import 'package:flutter/material.dart';
import 'package:gocomponent/utils/throttle.dart';

class ThrottleTestPage extends StatefulWidget {
  const ThrottleTestPage({super.key});

  @override
  State<ThrottleTestPage> createState() => _ThrottleTestPageState();
}

class _ThrottleTestPageState extends State<ThrottleTestPage> {
  final List<String> _logs = [];
  final ScrollController _scrollController = ScrollController();

  // 测试计数器
  int _saveCount = 0;
  int _searchCount = 0;

  // 创建节流函数
  late final void Function() throttledSave;
  late final void Function(String) throttledSearch;
  late final void Function() throttledSaveTrailing;
  late final void Function(String) throttledSearchTrailing;

  @override
  void initState() {
    super.initState();
    
    // 使用类方法创建节流函数
    
    // 无参数节流函数 (leading = true)
    void saveFunction() {
      _saveCount++;
      _addLog('保存操作执行 #$_saveCount (leading)');
    }
    throttledSave = Throttle.create(saveFunction, delay: 2000, leading: true);

    // 有参数节流函数 (leading = true)
    void searchFunction(String query) {
      _searchCount++;
      _addLog('搜索: "$query" #$_searchCount (leading)');
    }
    throttledSearch = Throttle.createWithParam(searchFunction, delay: 1000, leading: true);

    // 无参数节流函数 (leading = false)
    void saveTrailingFunction() {
      _addLog('延迟保存操作执行 (trailing)');
    }
    throttledSaveTrailing = Throttle.create(saveTrailingFunction, delay: 2000, leading: false);

    // 有参数节流函数 (leading = false)
    void searchTrailingFunction(String query) {
      _addLog('延迟搜索: "$query" (trailing)');
    }
    throttledSearchTrailing = Throttle.createWithParam(searchTrailingFunction, delay: 1000, leading: false);
  }

  void _addLog(String message) {
    setState(() {
      final time = DateTime.now().toString().substring(11, 19);
      _logs.add('[$time] $message');
    });
    
    // 自动滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
      _saveCount = 0;
      _searchCount = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Throttle 类方法测试'),
        actions: [
          IconButton(
            onPressed: _clearLogs,
            icon: const Icon(Icons.clear),
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 测试按钮区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Throttle 类方法测试',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '使用 Throttle.create() 和 Throttle.createWithParam() 语法',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                
                // Leading 模式测试
                const Text('Leading 模式 (首次立即执行):'),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: throttledSave,
                        child: const Text('保存 (2s节流)'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => throttledSearch('Flutter'),
                        child: const Text('搜索 (1s节流)'),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Trailing 模式测试
                const Text('Trailing 模式 (延迟执行):'),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: throttledSaveTrailing,
                        child: const Text('延迟保存 (2s)'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => throttledSearchTrailing('Dart'),
                        child: const Text('延迟搜索 (1s)'),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 内联函数测试
                const Text('内联函数测试:'),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Builder(
                        builder: (context) {
                          // 直接在 build 中创建节流函数
                          final inlineThrottled = Throttle.create(() {
                            _addLog('内联函数执行 (500ms节流)');
                          }, delay: 500);
                          
                          return ElevatedButton(
                            onPressed: inlineThrottled,
                            child: const Text('内联函数'),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Builder(
                        builder: (context) {
                          // 有参数的内联函数
                          final inlineParamThrottled = Throttle.createWithParam((String msg) {
                            _addLog('参数函数: $msg (500ms节流)');
                          }, delay: 500);
                          
                          return ElevatedButton(
                            onPressed: () => inlineParamThrottled('Hello'),
                            child: const Text('参数函数'),
                          );
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 复杂对象测试
                const Text('复杂对象参数测试:'),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () {
                    // 创建处理复杂对象的节流函数
                    final complexThrottled = Throttle.createWithParam((Map<String, dynamic> data) {
                      _addLog('复杂对象: ${data.toString()} (1s节流)');
                    }, delay: 1000);
                    
                    complexThrottled({
                      'id': DateTime.now().millisecondsSinceEpoch,
                      'name': 'Test User',
                      'data': [1, 2, 3],
                    });
                  },
                  child: const Text('复杂对象参数'),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // 日志显示区域
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '执行日志 (${_logs.length} 条):',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(8),
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              _logs[index],
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}

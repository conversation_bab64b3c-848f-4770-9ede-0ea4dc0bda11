import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gocomponent/theme/theme_manager.dart';

void main() {
  runApp(const MyApp());
  Get.put(ThemeManager());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {

  late final ThemeManager appTheme;
  
  @override
  void initState() {
    super.initState();

  }

  @override
  Widget build(BuildContext context) {
    return Obx(()=> GetMaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('GoComponent 示例'),
        ),
        body: HomePage()
      ),
      themeMode: ThemeManager.to.themeMode,
      theme: ThemeManager.to.theme,
    ));
  }
}
// HomePage
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: (){
            if (ThemeManager.to.themeMode == ThemeMode.light) {
              ThemeManager.to.changeTheme(ThemeMode.dark);
            } else {
              ThemeManager.to.changeTheme(ThemeMode.light);
            }
          },
          child: const Text('切换主题'),
        ),
        // 切换主题颜色
        ElevatedButton(
          onPressed: (){
            ThemeManager.to.buildThemeColor(const Color.fromARGB(255, 0, 59, 107));
          },
          child: const Text('蓝色'),
        ),
        // 重置默认颜色
        ElevatedButton(
          onPressed: (){
            ThemeManager.to.resetThemeColor();
          },
          child: const Text('重置默认颜色'),
        ),
        Container(
          width: 100,
          height: 50,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        Text('123')
      ],
    );
  }
}
import 'package:flutter/material.dart';
import 'package:gocomponent/components/appbar/appbar.dart';

class AppBarActionTestPage extends StatefulWidget {
  const AppBarActionTestPage({super.key});

  @override
  State<AppBarActionTestPage> createState() => _AppBarActionTestPageState();
}

class _AppBarActionTestPageState extends State<AppBarActionTestPage> {
  String _message = '点击 AppBar 右侧图标测试功能';

  void _onActionTap() {
    setState(() {
      _message = 'Action 被点击了！时间: ${DateTime.now().toString().substring(11, 19)}';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GoAppBar(
        title: const Text('AppBarAction 测试'),
        action: AppBarActionIcon(
          actionIcon: const Icon(Icons.settings),
          actionOnTap: _onActionTap,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'AppBarActionIcon 组件测试',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _message,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              '测试场景：',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            _buildTestScenario(
              '1. 基本功能',
              '点击右上角的设置图标，观察消息变化',
              Icons.check_circle,
              Colors.green,
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AppBarActionVariantsPage(),
                  ),
                );
              },
              child: const Text('查看更多测试场景'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestScenario(String title, String description, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class AppBarActionVariantsPage extends StatelessWidget {
  const AppBarActionVariantsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        appBar: const GoAppBar(
          title: Text('AppBarAction 变体测试'),
        ),
        body: Column(
          children: [
            const TabBar(
              labelColor: Colors.black,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.blue,
              tabs: [
                Tab(text: '有图标有回调'),
                Tab(text: '只有图标'),
                Tab(text: '只有回调'),
                Tab(text: '都为空'),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildVariantPage(
                    'AppBarActionIcon 完整功能',
                    '图标: ✅\n回调: ✅',
                    AppBarActionIcon(
                      actionIcon: const Icon(Icons.favorite),
                      actionOnTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('喜欢按钮被点击！')),
                        );
                      },
                    ),
                  ),
                  _buildVariantPage(
                    'AppBarActionIcon 只有图标',
                    '图标: ✅\n回调: ❌',
                    const AppBarActionIcon(
                      actionIcon: Icon(Icons.star),
                      actionOnTap: null,
                    ),
                  ),
                  _buildVariantPage(
                    'AppBarActionIcon 只有回调',
                    '图标: ❌\n回调: ✅',
                    AppBarActionIcon(
                      actionIcon: null,
                      actionOnTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('隐藏按钮被点击！')),
                        );
                      },
                    ),
                  ),
                  _buildVariantPage(
                    'AppBarActionIcon 都为空',
                    '图标: ❌\n回调: ❌',
                    const AppBarActionIcon(
                      actionIcon: null,
                      actionOnTap: null,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVariantPage(String title, String description, AppBarActionIcon action) {
    return Builder(
      builder: (context) {
        return Scaffold(
          appBar: GoAppBar(
            title: Text(title),
            action: action,
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    description,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  '测试说明：',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '观察右上角的 AppBar action 区域，测试点击行为。',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

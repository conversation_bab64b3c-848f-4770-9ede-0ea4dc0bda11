import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gocomponent/utils/to_opacity.dart';
import 'package:rk_package/rk_package.dart';

class AppBarIconOnTapAni extends StatefulWidget {
  const AppBarIconOnTapAni({
    super.key,
    required this.icon,
    required this.onTap,
    this.disable = false,
    this.backgroundColor
  });

  final Widget icon;

  final void Function()? onTap;

  final Color? backgroundColor;

  final bool disable;

  @override
  State<AppBarIconOnTapAni> createState() => _AppBarIconOnTapAniState();
}

class _AppBarIconOnTapAniState extends State<AppBarIconOnTapAni> {
  bool tapState = false;
  Timer? tapTimer;

  void updateTapState(bool newState) {
    setState(() {
      tapState = newState;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.disable) return;
        updateTapState(true);
        tapTimer?.cancel();
        tapTimer = Timer(const Duration(milliseconds: 50), () {
          updateTapState(false);
        });
      },
      onTapDown: (_) {
        if (widget.disable) return;
        updateTapState(true);
      },
      onTapUp:  (_) {
        if (widget.disable) return;
        tapTimer?.cancel();
        updateTapState(false);
        widget.onTap?.call();
      },
      onTapCancel: () {
        if (widget.disable) return;
        tapTimer?.cancel();
        updateTapState(false);
      },
      child: Container(
        width: 60.w,
        height: 60.w,
        alignment: Alignment.center,
        color: Colors.transparent,
        child: AnimatedContainer(
          width: 45.w,
          height: 45.w,
          duration: const Duration(milliseconds: 120),
          decoration: BoxDecoration(
            color: switch(widget.backgroundColor != null) {
              true => widget.backgroundColor?.toOpacity(tapState? 1 : 0),
              false => Color.fromRGBO(229, 229, 229, 1).toOpacity(tapState? 1 : 0),
            },
            borderRadius: BorderRadius.circular(40.w)
          ),
          child: widget.icon,
        ),
      )
    );
  }
}

import 'package:flutter/material.dart';
import 'package:gocomponent/components/appbar/appbar_icon_tap.dart';
import 'package:rk_package/rk_package.dart';

/// 自定义 AppBar 组件
///
/// 提供统一的导航栏样式和交互体验，支持：
/// - 自动返回按钮（根据路由栈判断）
/// - 便捷的单个 action 配置
/// - 完全自定义的 actions 和 leading
/// - 灵活的显示控制
class GoAppBar extends StatelessWidget implements PreferredSizeWidget {
  const GoAppBar({
    super.key,
    this.title,
    this.centerTitle = true,
    this.leadingOnTap,
    this.action,
    this.customLeading,
    this.customActions,
  }) : assert(
    !(customActions != null && action != null),
    'customActions 和 action 不能同时使用，请选择其中一种方式'
  );

  // ==================== 基础配置 ====================

  /// AppBar 标题
  final Widget? title;

  /// 是否居中显示标题，默认为 true
  final bool centerTitle;

  // ==================== 返回按钮配置 ====================

  /// 自定义返回按钮点击事件
  /// 如果为 null，则使用默认的 Get.back()
  final void Function()? leadingOnTap;

  // ==================== Action 配置 ====================

  /// 单个 action 图标（便捷配置）
  /// 如果为 null，则不显示 action 区域
  final AppBarActionIcon? action;

  // ==================== 自定义配置 ====================

  /// 自定义 actions 列表
  /// 如果提供此参数，将覆盖 action 的配置
  final List<Widget>? customActions;

  /// 自定义 leading Widget
  /// 如果提供此参数，将覆盖默认的返回按钮逻辑
  final Widget? customLeading;
  
  @override
  Size get preferredSize => Size.fromHeight(60.w);

  @override
  Widget build(BuildContext context) {
    // 检查是否可以返回（即是否有上级路由）
    final bool canPop = Navigator.of(context).canPop();

    return AppBar(
      title: title,
      centerTitle: centerTitle,
      // Leading 优先级：自定义 leading > 自动返回按钮 > null
      leading: customLeading ?? switch(canPop) {
        true => AppBarIconOnTapAni(
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            size: 20.w,
            color: Colors.black,
          ),
          onTap: switch(leadingOnTap != null) {
            true => leadingOnTap,
            false => ()=> Get.back(),
          },
        ),
        false => null,
      },

      // Actions 优先级：自定义 actions > 默认 action 配置 > null
      actions: customActions ?? (action != null ? [
        AppBarIconOnTapAni(
          icon: action!.actionIcon ?? SizedBox.shrink(),
          onTap: action!.actionOnTap,
          disable: action!.actionOnTap == null,
        ),
      ] : null),
    );
  }
}

class AppBarActionIcon {
  const AppBarActionIcon({
    this.actionIcon,
    this.actionOnTap
  });

  final Widget? actionIcon;
  final void Function()? actionOnTap;
}
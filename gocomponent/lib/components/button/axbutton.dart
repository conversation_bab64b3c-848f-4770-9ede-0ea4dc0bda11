import 'package:flutter/material.dart';
import 'package:gocomponent/components/ontap/ontap_animation.dart';
import 'package:gocomponent/theme/theme_manager.dart';
import 'package:rk_package/rk_package.dart';

import '../axtext/axtext.dart';

class AxButton extends StatelessWidget {
  const AxButton({
    super.key,
    required this.text,
    required this.onTap,
    this.width,
    this.height,
    this.color,
    this.textColor,
    this.fontSize,
    this.borderRadius,
    this.onTapType = OnTapAnimationType.toScAndOp
  });

  final String text;
  final double? width;
  final double? height;
  final Color? color;
  final Color? textColor;
  final double? fontSize;
  final double? borderRadius;
  final OnTapAnimationType onTapType;

  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return OnTapAnimation(
      type: onTapType,
      onTap: onTap,
      child: Container(
        width: width,
        height: height ?? 56.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: color ?? ThemeManager.to.theme.colorScheme.primary,
          borderRadius: BorderRadius.circular(
            borderRadius ?? 10.w
          )
        ),
        child: AxText(
          text: text,
          color: textColor ?? ThemeManager.to.theme.colorScheme.surface,
          fontSize: fontSize ?? 16.w,
        ),
      ),
    );
  }
}
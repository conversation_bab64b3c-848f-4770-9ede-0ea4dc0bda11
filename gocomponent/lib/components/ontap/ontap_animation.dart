import 'dart:async';

import 'package:flutter/widgets.dart';

/// 定义点击动画类型的枚举类
enum OnTapAnimationType {
  /// 仅进行缩放动画
  toScale,
  /// 仅进行透明度动画
  toOpacity,
  /// 同时进行缩放和透明度动画
  toScAndOp,
}

class OnTapAnimation extends StatefulWidget {
  const OnTapAnimation({
    super.key,
    required this.child,
    this.scaleFactor = 0.97,
    this.opacity = 0.7,
    this.onTap,
    this.duration = const Duration(milliseconds: 150),
    this.curve = Curves.linear,
    this.type = OnTapAnimationType.toScale,
    this.borderRadius = 360
  });

  final void Function()? onTap;
  final Widget child;
  final double scaleFactor;
  final double opacity;
  final Duration duration;
  final Curve curve;
  final OnTapAnimationType type;
  final double borderRadius;

  @override
  State<OnTapAnimation> createState() => _OnTapAnimationState();
}

class _OnTapAnimationState extends State<OnTapAnimation> {
  bool tapState = false;
  Timer? tapTimer;

  void updateTapState(bool newState) {
    setState(() {
      tapState = newState;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: switch(widget.onTap != null) {
        true => () async {
          updateTapState(true);
          tapTimer?.cancel();
          tapTimer = Timer(const Duration(milliseconds: 50), () {
            updateTapState(false);
          });
        },
        false => null,
      },
      onTapDown: switch(widget.onTap != null) {
        true => (_) {
          updateTapState(true);
        },
        false => null,
      },
      onTapUp: switch(widget.onTap != null) {
        true => (_) {
          tapTimer?.cancel();
          updateTapState(false);
          widget.onTap?.call();
        },
        false => null,
      },
      onTapCancel: switch(widget.onTap != null) {
        true => () {
          tapTimer?.cancel();
          updateTapState(false);
        },
        false => null,
      },
      child: switch (widget.type) {
        OnTapAnimationType.toOpacity => AnimatedOpacity(
          opacity: tapState ? widget.opacity : 1,
          duration: widget.duration,
          curve: widget.curve,
          child: widget.child,
        ),
        OnTapAnimationType.toScale => AnimatedScale(
          scale: tapState ? widget.scaleFactor : 1,
          duration: widget.duration,
          curve: widget.curve,
          child: widget.child,
        ),
        OnTapAnimationType.toScAndOp => AnimatedScale(
          scale: tapState ? widget.scaleFactor : 1,
          duration: widget.duration,
          curve: widget.curve,
          child: AnimatedOpacity(
            opacity: tapState ? widget.opacity : 1,
            duration: widget.duration,
            curve: widget.curve,
            child: widget.child,
          ),
        ),
      },
    );
  }
}
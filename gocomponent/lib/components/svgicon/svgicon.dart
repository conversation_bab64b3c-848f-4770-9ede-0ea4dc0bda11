import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:rk_package/rk_package.dart';

class SvgAssetIcon extends StatelessWidget {
  const SvgAssetIcon({
    super.key,
    required this.path,
    this.size,
    this.color,
    this.colorMapper,
    this.matchTextDirection = false,
    this.fit = BoxFit.contain,
    this.alignment = Alignment.center,
    this.allowDrawingOutsideViewBox = false,
    this.clipBehavior = Clip.hardEdge,
    this.svgTheme,
    this.package,
  });

  final double? size;
  final String path;
  final Color? color;
  final ColorMapper? colorMapper;
  final bool matchTextDirection;
  final BoxFit fit;
  final Alignment alignment;
  final bool allowDrawingOutsideViewBox;
  final Clip clipBehavior;
  final SvgTheme? svgTheme;
  final String? package;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size ?? 30.w,
      height: size ?? 30.w,
      alignment: Alignment.center,
      child: SvgPicture.asset(
        path,
        width: size ?? 30.w,
        height: size ?? 30.w,
        matchTextDirection: matchTextDirection,
        fit: fit,
        alignment: alignment,
        allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
        clipBehavior: clipBehavior,
        errorBuilder: (context, error, stackTrace) => Text('error image url'),
        theme: svgTheme,
        colorMapper: colorMapper,
        package: package,
        colorFilter: switch(color) {
          Color() => ColorFilter.mode(
            color!, 
            BlendMode.srcIn
          ),
          _ => null
        },
      )
    );
  }
}
import 'package:flutter/material.dart';
import 'package:gocomponent/theme/theme_manager.dart';
import 'package:rk_package/rk_package.dart';

// ignore: constant_identifier_names
const AXTEXT_PACKAGE = 'gocomponent';

enum AxTextType {
  medium('Medium'),
  bold('Bold');

  final String value;
  const AxTextType(this.value);
}

class AxText extends StatelessWidget {
  const AxText({
    super.key,
    required this.text,
    this.fontSize,
    this.fontFamily,
    this.color,
    this.letterSpacing,
    this.fontWeight,
    this.softWrap = true,
    this.overflow,
    this.package = AXTEXT_PACKAGE
  });

  final String text;

  final double? fontSize;

  final bool softWrap;

  final AxTextType? fontFamily;

  final Color? color;

  final double? letterSpacing;

  final FontWeight? fontWeight;

  final TextOverflow? overflow;

  final String? package;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      softWrap: softWrap,
      style: TextStyle(
        color: color ?? ThemeManager.to.theme.colorScheme.primary,
        fontSize: fontSize ?? 20.w,
        fontFamily: fontFamily?.value ?? AxTextType.medium.value,
        fontWeight: fontWeight,
        letterSpacing: letterSpacing,
        package: package,
        overflow: overflow
      ),
    );
  }
}
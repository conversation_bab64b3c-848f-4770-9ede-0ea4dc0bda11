import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

/// 主题管理器
///
/// 负责管理应用的主题模式（亮色/暗色）和动态颜色配置
/// 使用 GetxService 确保在整个应用生命周期内保持单例状态
///
/// 功能特性：
/// - 支持亮色/暗色主题切换
/// - 支持基于主色调的动态主题生成
/// - 支持主题重置功能
/// - 响应式主题更新
class ThemeManager extends GetxService {

  /// 获取 ThemeManager 单例实例
  static ThemeManager get to => Get.find<ThemeManager>();

  // ==================== 主题模式 ====================

  /// 当前主题模式（亮色/暗色）
  final Rx<ThemeMode> _themeMode = ThemeMode.light.obs;

  /// 获取当前主题模式
  ThemeMode get themeMode => _themeMode.value;

  /// 获取当前应用的主题数据
  /// 根据当前主题模式返回对应的主题配置
  ThemeData get theme {
    return _themeMode.value == ThemeMode.light
        ? lightThemeData.value
        : darkThemeData.value;
  }

  ColorScheme get colorTo => theme.colorScheme;

  // ==================== 生命周期方法 ====================

  @override
  void onInit() {
    super.onInit();
    resetThemeColor();
  }

  // ==================== 主题切换方法 ====================

  /// 切换主题模式
  ///
  /// [themeMode] 要切换到的主题模式
  /// - ThemeMode.light: 亮色模式
  /// - ThemeMode.dark: 暗色模式
  /// - ThemeMode.system: 跟随系统模式
  void changeTheme(ThemeMode themeMode) {
    _themeMode.value = themeMode;
  }

  // ==================== 主题数据 ====================

  /// 亮色主题数据（响应式）
  /// 可以通过 buildThemeColor 方法动态修改
  final Rx<ThemeData> lightThemeData = ThemeData().obs;

  /// 暗色主题数据（响应式）
  /// 可以通过 buildThemeColor 方法动态修改
  final Rx<ThemeData> darkThemeData = ThemeData().obs;

  /// 默认亮色主题配置
  ///
  /// 使用灰度色系作为基础配色方案：
  final ThemeData defaultLightTheme = ThemeData(
    colorScheme: ColorScheme.light(
      primary: Color.fromRGBO(18, 18, 18, 1),
      secondary: Color.fromRGBO(128, 128, 128, 1),
      onSecondary: Color.fromRGBO(166, 166, 166, 1),
      tertiary: Color.fromRGBO(229, 229, 229, 1),
      surface: Color.fromRGBO(242, 242, 242, 1),
    ),
    scaffoldBackgroundColor: Color.fromRGBO(255, 255, 255, 1),
    appBarTheme: AppBarTheme(
      backgroundColor: Color.fromRGBO(255, 255, 255, 1),
    ),
  );

  /// 默认暗色主题配置
  ///
  /// 使用反转的灰度色系作为暗色模式配色方案：
  final ThemeData defaultDarkTheme = ThemeData(
    colorScheme: ColorScheme.dark(
      primary: Color.fromRGBO(242, 242, 242, 1),
      secondary: Color.fromRGBO(229, 229, 229, 1),
      onSecondary: Color.fromRGBO(166, 166, 166, 1),
      tertiary: Color.fromRGBO(128, 128, 128, 1),
      surface: Color.fromRGBO(18, 18, 18, 1),
    ),
    scaffoldBackgroundColor: Color.fromRGBO(0, 0, 0, 1),
    appBarTheme: AppBarTheme(
      backgroundColor: Color.fromRGBO(0, 0, 0, 1),
    ),
  );

  // ==================== 主题操作方法 ====================

  /// 重置主题为默认颜色
  ///
  /// 将亮色和暗色主题都恢复为初始的默认配置
  /// 适用于用户想要撤销所有自定义颜色修改的场景
  void resetThemeColor() {
    lightThemeData.value = defaultLightTheme;
    darkThemeData.value = defaultDarkTheme;
  }

  /// 基于主色调构建动态主题
  ///
  /// 根据提供的主色调自动生成一套完整的颜色方案
  /// 使用渐变算法生成从深到浅的5个层次颜色
  ///
  /// [primaryColor] 主色调，用于生成整套颜色方案的基础颜色
  ///
  /// 颜色分配策略：
  /// - 亮色模式：从深到浅 (colors[0] -> colors[4])
  /// - 暗色模式：从浅到深 (colors[4] -> colors[0])
  void buildThemeColor(Color primaryColor) {
    final List<Color> colors = _generateGradientColors(primaryColor);

    if (themeMode == ThemeMode.light) {
      // 亮色模式：使用深色作为主色调，浅色作为背景
      lightThemeData.value = ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.light(
          primary: colors[0],        // 最深色 - 主要元素
          secondary: colors[1],      // 次深色 - 次要元素
          onSecondary: colors[2],    // 中间色 - 辅助元素
          tertiary: colors[3],       // 次浅色 - 装饰元素
          surface: colors[4],        // 最浅色 - 表面颜色
        ),
        scaffoldBackgroundColor: colors[4],
        appBarTheme: AppBarTheme(
          backgroundColor: colors[4],
          foregroundColor: colors[0],
        ),
      );
    } else {
      // 暗色模式：使用浅色作为主色调，深色作为背景
      darkThemeData.value = ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.dark(
          primary: colors[4],        // 最浅色 - 主要元素（在暗色背景下突出）
          secondary: colors[3],      // 次浅色 - 次要元素
          onSecondary: colors[2],    // 中间色 - 辅助元素
          tertiary: colors[1],       // 次深色 - 装饰元素
          surface: colors[0],        // 最深色 - 表面颜色
        ),
        scaffoldBackgroundColor: colors[0],
        appBarTheme: AppBarTheme(
          backgroundColor: colors[0],
          foregroundColor: colors[4],
        ),
      );
    }
  }

  // ==================== 私有辅助方法 ====================

  /// 基于主色调生成渐变颜色列表
  ///
  /// 使用线性插值算法，将主色调与白色进行混合，生成5个不同深浅的颜色
  ///
  /// [primaryColor] 基础主色调
  ///
  /// 返回值：包含5个颜色的列表，按深度排序：
  /// - colors[0]: 100% 主色调（最深）
  /// - colors[1]: 53.7% 主色调 + 46.3% 白色
  /// - colors[2]: 37.6% 主色调 + 62.4% 白色
  /// - colors[3]: 13.7% 主色调 + 86.3% 白色
  /// - colors[4]: 9.8% 主色调 + 90.2% 白色（最浅）
  ///
  /// 算法说明：
  /// 使用公式 `finalColor = primaryColor * percentage + white * (1 - percentage)`
  /// 实现主色调向白色的平滑过渡
  List<Color> _generateGradientColors(Color primaryColor) {
    // 预设的混合比例，经过设计优化，确保颜色层次分明
    const List<double> percentageList = [1.0, 0.537, 0.376, 0.137, 0.098];

    return percentageList.map((percentage) {
      // 确保百分比在有效范围内
      final double clampedPercentage = percentage.clamp(0.0, 1.0);

      // 计算每个颜色通道的混合值
      final int red = ((primaryColor.r * 255) * clampedPercentage +
                      255 * (1 - clampedPercentage)).floor();
      final int green = ((primaryColor.g * 255) * clampedPercentage +
                        255 * (1 - clampedPercentage)).floor();
      final int blue = ((primaryColor.b * 255) * clampedPercentage +
                       255 * (1 - clampedPercentage)).floor();

      // 确保颜色值在有效范围内并返回颜色对象
      return Color.fromARGB(
        255,
        red.clamp(0, 255),
        green.clamp(0, 255),
        blue.clamp(0, 255)
      );
    }).toList();
  }
}


import 'dart:ui';

/// Color 扩展工具类
///
/// 提供颜色透明度相关的便捷方法
extension ColorToOpacityExtension on Color {
  /// 设置颜色的透明度
  ///
  /// 保持原有的 RGB 值不变，只修改透明度
  ///
  /// 参数：
  /// - [opacity] 透明度值，范围 0.0-1.0
  ///   - 0.0 = 完全透明
  ///   - 1.0 = 完全不透明
  ///
  /// 返回：
  /// - 具有指定透明度的新 Color 对象
  ///
  /// 示例：
  /// ```dart
  /// Color red = Colors.red;
  /// Color semiTransparentRed = red.toOpacity(0.5); // 50% 透明度的红色
  /// ```
  Color toOpacity(double opacity) {
    return Color.fromRGBO(
      (r * 255).floor(),
      (g * 255).floor(),
      (b * 255).floor(),
      opacity,
    );
  }
}
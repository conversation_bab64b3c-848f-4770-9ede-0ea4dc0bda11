import 'dart:async';

/// 节流工具类
///
/// 用于限制函数的执行频率，在指定时间内只执行一次
/// 常用于防止按钮重复点击、搜索输入等场景
///
/// 提供两种创建方式：
/// - `Throttle.create()` - 无参数函数节流
/// - `Throttle.createWithParam()` - 有参数函数节流
class Throttle {
  // 私有构造函数，防止实例化
  Throttle._();

  /// 创建无参数节流函数
  ///
  /// 参数：
  /// - [func] 要节流的无参函数
  /// - [delay] 延迟时间，默认 200 毫秒
  /// - [leading] 是否在首次调用时立即执行，默认 true
  ///
  /// 返回：
  /// - 节流后的无参函数
  ///
  /// 示例：
  /// ```dart
  /// void save() => print('保存');
  /// final throttledSave = Throttle.create(save, delay: 1000);
  /// throttledSave(); // 立即执行
  /// throttledSave(); // 被忽略
  /// ```
  static void Function() create(
    void Function() func, {
    int delay = 200,
    bool leading = false,
  }) {
    bool isFirstCall = true;
    Timer? timer;

    return () {
      if (leading && isFirstCall) {
        func();
        isFirstCall = false;
      }

      timer ??= Timer(Duration(milliseconds: delay), () {
        if (!leading) {
          func();
        }
        timer = null;
        if (leading) {
          isFirstCall = true;
        }
      });
    };
  }

  /// 创建有参数节流函数
  ///
  /// 参数：
  /// - [func] 要节流的有参函数
  /// - [delay] 延迟时间，默认 200 毫秒
  /// - [leading] 是否在首次调用时立即执行，默认 true
  ///
  /// 返回：
  /// - 节流后的有参函数
  ///
  /// 示例：
  /// ```dart
  /// void search(String query) => print('搜索: $query');
  /// final throttledSearch = Throttle.createWithParam<String>(search, delay: 500);
  /// throttledSearch('hello'); // 立即执行
  /// throttledSearch('world'); // 被忽略
  /// ```
  static void Function(T) createWithParam<T>(
    void Function(T) func, {
    int delay = 200,
    bool leading = false,
  }) {
    bool isFirstCall = true;
    Timer? timer;
    T? lastParam; // 保存最后一次调用的参数

    return (T param) {
      lastParam = param; // 总是更新最后的参数

      if (leading && isFirstCall) {
        func(param);
        isFirstCall = false;
      }

      timer ??= Timer(Duration(milliseconds: delay), () {
        if (!leading && lastParam != null) {
          func(lastParam as T); // 使用最后一次的参数
        }
        timer = null;
        if (leading) {
          isFirstCall = true;
        }
      });
    };
  }
}


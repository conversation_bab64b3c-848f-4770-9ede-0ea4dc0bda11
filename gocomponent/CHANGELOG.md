## 1.0.4 (2025-6-12)

### 新增
- **gocomponent.dart** 添加export各组件导出

## 1.0.3

### 新增
- **GoAppBar**: 新增 `useLeading` 参数，默认为 true，用于控制 leading 区域的显示
- **GoAppBar**: 新增 `leading` 参数，支持自定义 leading Widget
- **GoAppBar**: 添加参数冲突检查断言，防止 `leading` 与 `useLeading` 同时使用

## 1.0.2

### 新增
- **GoAppBar**: 新增 `useAction` 参数，默认为 true，用于控制 action 区域的显示
- **GoAppBar**: 新增 `customActions` 参数，支持完全自定义的 actions 列表
- **GoAppBar**: 新增自定义 `customLeading` 参数，支持完全自定义的 leading Widget
- **GoAppBar**: 添加参数冲突检查断言，防止 `customActions` 与 `action/actionOnTap` 同时使用
- **GoAppBar**: 添加完整的代码注释和 API 文档，包含使用示例和优先级说明
- **ColorToOpacityExtension**: 为 Color 扩展工具类添加完整的文档注释，包含参数说明、返回值说明和使用示例
- **Throttle**: 为节流工具类添加完整的文档注释，包含类说明、方法说明和使用示例
- **Throttle**: 重构为类方法 API，提供 `Throttle.create()` 和 `Throttle.createWithParam()` 两个静态方法，支持完整的 IDE 代码提示
- **AppBarActionIcon**: 新增 AppBar action 配置类，支持图标和回调的独立配置
- **AppBarActionTestPage**: 新增 AppBarActionIcon 组件的完整测试页面

### 修改
- **GoAppBar**: 优化返回按钮显示逻辑，现在只在有上级路由时自动显示返回按钮
- **GoAppBar**: 优化返回按钮的交互反馈，现在点击返回按钮时会有动画效果
- **GoAppBar**: 重构 actions 逻辑，优先级为：`customActions` > `action` > `null`
- **GoAppBar**: 重构 leading 逻辑，优先级为：`自定义 customLeading` > `自动返回按钮` > `null`
- **GoAppBar**: 优化代码结构，按功能模块分组参数（基础配置、返回按钮配置、Action 配置、自定义配置）
- **GoAppBar**: 移除冗余的 `useAction` 参数，简化 API 设计（通过 `action: null` 控制是否显示）

### 修复
- **GoAppBar**: 修复在首页也显示返回按钮的问题
- **Throttle**: 修复静态 timer 变量导致多个节流函数相互干扰的严重问题

## 1.0.1

### 修改
- 合并了点击组件统一使用OnTapAnimation，使用type切换点击效果

## 1.0.0
- **基础组件库**：在 `components/` 目录下新增按钮（`Button`）、文本（`Text`）等核心基础组件，支持自定义样式、交互反馈和响应式布局。
- **主题系统**：新增 `theme/` 模块，内置统一的颜色方案（含主色、辅助色、中性色）和字体配置（D-DIN系列字体，包含Medium和Bold字重），支持全局主题切换功能。
- **字体文字组件**：基于项目字体（D-DIN.otf/D-DIN-Bold.otf）开发专用文字组件 `AxText`，提供 `medium` 和 `bold` 两种字重，自动继承主题字体配置，确保视觉一致性。
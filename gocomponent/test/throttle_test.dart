import 'package:flutter_test/flutter_test.dart';
import 'package:gocomponent/utils/throttle.dart';

void main() {
  group('Throttle Class Tests', () {
    
    // 测试无参数函数
    group('Throttle.create() 测试', () {
      test('基本无参数函数', () async {
        int callCount = 0;
        void testFunction() {
          callCount++;
        }
        
        final throttled = Throttle.create(testFunction, delay: 100);
        
        // 快速调用多次
        throttled();
        throttled();
        throttled();
        
        expect(callCount, 1); // leading = true，应该立即执行一次
        
        // 等待延迟时间过去
        await Future.delayed(Duration(milliseconds: 150));
        
        // 再次调用
        throttled();
        expect(callCount, 2); // 应该再执行一次
      });
      
      test('trailing 模式无参数函数', () async {
        int callCount = 0;
        void testFunction() {
          callCount++;
        }
        
        final throttled = Throttle.create(testFunction, delay: 100, leading: false);
        
        // 快速调用多次
        throttled();
        throttled();
        throttled();
        
        expect(callCount, 0); // leading = false，不应该立即执行
        
        // 等待延迟时间过去
        await Future.delayed(Duration(milliseconds: 150));
        
        expect(callCount, 1); // 应该在延迟后执行一次
      });
      
      test('匿名函数', () async {
        int callCount = 0;
        
        final throttled = Throttle.create(() {
          callCount++;
        }, delay: 100);
        
        throttled();
        throttled();
        
        expect(callCount, 1);
      });
    });
    
    // 测试有参数函数
    group('Throttle.withParam() 测试', () {
      test('String 参数函数', () async {
        String lastValue = '';
        int callCount = 0;
        
        void testFunction(String value) {
          lastValue = value;
          callCount++;
        }
        
        final throttled = Throttle.createWithParam(testFunction, delay: 100);
        
        throttled('first');
        throttled('second');
        throttled('third');
        
        expect(callCount, 1);
        expect(lastValue, 'first'); // leading = true，使用第一次的参数
        
        await Future.delayed(Duration(milliseconds: 150));
        
        throttled('fourth');
        expect(callCount, 2);
        expect(lastValue, 'fourth');
      });
      
      test('int 参数函数', () async {
        int lastValue = 0;
        int callCount = 0;
        
        void testFunction(int value) {
          lastValue = value;
          callCount++;
        }
        
        final throttled = Throttle.createWithParam(testFunction, delay: 100);
        
        throttled(1);
        throttled(2);
        throttled(3);
        
        expect(callCount, 1);
        expect(lastValue, 1);
      });
      
      test('自定义对象参数', () async {
        Map<String, dynamic>? lastValue;
        int callCount = 0;
        
        void testFunction(Map<String, dynamic> data) {
          lastValue = data;
          callCount++;
        }
        
        final throttled = Throttle.createWithParam(testFunction, delay: 100);
        
        throttled({'id': 1, 'name': 'Alice'});
        throttled({'id': 2, 'name': 'Bob'});
        
        expect(callCount, 1);
        expect(lastValue?['id'], 1);
        expect(lastValue?['name'], 'Alice');
      });
      
      test('trailing 模式有参数函数', () async {
        String lastValue = '';
        int callCount = 0;
        
        void testFunction(String value) {
          lastValue = value;
          callCount++;
        }

        final throttled = Throttle.createWithParam(testFunction, delay: 100, leading: false);
        
        throttled('first');
        throttled('second');
        throttled('third');
        
        expect(callCount, 0); // leading = false，不应该立即执行
        
        await Future.delayed(Duration(milliseconds: 150));
        
        expect(callCount, 1);
        expect(lastValue, 'third'); // trailing 模式使用最后一次的参数
      });
    });
    
    // 测试边界情况
    group('边界情况测试', () {
      test('delay = 0', () async {
        int callCount = 0;
        void testFunction() {
          callCount++;
        }
        
        final throttled = Throttle.create(testFunction, delay: 0);
        
        throttled();
        throttled();
        
        expect(callCount, 1);
        
        // 即使 delay = 0，也需要等待下一个事件循环
        await Future.delayed(Duration(milliseconds: 1));
        
        throttled();
        expect(callCount, 2);
      });
      
      test('多个独立的节流函数', () async {
        int count1 = 0;
        int count2 = 0;
        
        void func1() { count1++; }
        void func2() { count2++; }
        
        final throttled1 = Throttle.create(func1, delay: 100);
        final throttled2 = Throttle.create(func2, delay: 100);
        
        throttled1();
        throttled2();
        throttled1();
        throttled2();
        
        expect(count1, 1);
        expect(count2, 1);
        
        await Future.delayed(Duration(milliseconds: 150));
        
        throttled1();
        throttled2();
        
        expect(count1, 2);
        expect(count2, 2);
      });
      
      test('异步函数', () async {
        int callCount = 0;
        
        void asyncFunction() async {
          await Future.delayed(Duration(milliseconds: 10));
          callCount++;
        }
        
        final throttled = Throttle.create(asyncFunction, delay: 100);
        
        throttled();
        throttled();
        
        // 等待异步函数执行完成
        await Future.delayed(Duration(milliseconds: 20));
        
        expect(callCount, 1);
      });
    });
    
    // 性能测试
    group('性能测试', () {
      test('大量调用性能', () async {
        int callCount = 0;
        void testFunction() {
          callCount++;
        }
        
        final throttled = Throttle.create(testFunction, delay: 50);
        
        // 模拟大量快速调用
        for (int i = 0; i < 1000; i++) {
          throttled();
        }
        
        expect(callCount, 1); // 只应该执行一次
        
        await Future.delayed(Duration(milliseconds: 100));
        
        // 再次大量调用
        for (int i = 0; i < 1000; i++) {
          throttled();
        }
        
        expect(callCount, 2); // 总共执行两次
      });
    });
  });
}

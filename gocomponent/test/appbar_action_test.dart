import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gocomponent/components/appbar/appbar.dart';

void main() {
  group('AppBarActionIcon Tests', () {
    
    testWidgets('AppBarActionIcon 基本功能测试', (WidgetTester tester) async {
      bool actionCalled = false;
      
      final action = AppBarActionIcon(
        actionIcon: const Icon(Icons.settings),
        actionOnTap: () {
          actionCalled = true;
        },
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: GoAppBar(
              title: const Text('测试'),
              action: action,
            ),
          ),
        ),
      );
      
      // 验证图标是否显示
      expect(find.byIcon(Icons.settings), findsOneWidget);
      
      // 点击图标
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pump();
      
      // 验证回调是否被调用
      expect(actionCalled, true);
    });
    
    testWidgets('AppBarActionIcon 为 null 时的行为', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: const GoAppBar(
              title: Text('测试'),
              action: null,
            ),
          ),
        ),
      );
      
      // 验证没有显示任何图标
      expect(find.byType(Icon), findsNothing);
    });
    
    testWidgets('AppBarActionIcon 只有图标没有回调', (WidgetTester tester) async {
      final action = AppBarActionIcon(
        actionIcon: const Icon(Icons.settings),
        actionOnTap: null,
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: GoAppBar(
              title: const Text('测试'),
              action: action,
            ),
          ),
        ),
      );
      
      // 验证图标显示
      expect(find.byIcon(Icons.settings), findsOneWidget);
      
      // 点击图标（应该不会出错）
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pump();
      
      // 测试通过说明没有异常
    });
    
    testWidgets('action = null 时不显示 action', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: const GoAppBar(
              title: Text('测试'),
              action: null,
            ),
          ),
        ),
      );

      // 验证图标不显示
      expect(find.byIcon(Icons.settings), findsNothing);
    });
    
    testWidgets('customActions 优先于 action', (WidgetTester tester) async {
      final action = AppBarActionIcon(
        actionIcon: const Icon(Icons.settings),
        actionOnTap: () {},
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: GoAppBar(
              title: const Text('测试'),
              action: action,
              customActions: const [
                Icon(Icons.search),
                Icon(Icons.more_vert),
              ],
            ),
          ),
        ),
      );
      
      // 验证显示的是 customActions 而不是 action
      expect(find.byIcon(Icons.search), findsOneWidget);
      expect(find.byIcon(Icons.more_vert), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsNothing);
    });
    
    testWidgets('AppBarActionIcon 只有回调没有图标', (WidgetTester tester) async {
      bool actionCalled = false;
      
      final action = AppBarActionIcon(
        actionIcon: null,
        actionOnTap: () {
          actionCalled = true;
        },
      );
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: GoAppBar(
              title: const Text('测试'),
              action: action,
            ),
          ),
        ),
      );
      
      // 验证显示了 SizedBox.shrink()
      expect(find.byType(SizedBox), findsWidgets);
      
      // 尝试点击（虽然没有可见的图标，但区域还在）
      await tester.tap(find.byType(SizedBox).first);
      await tester.pump();
      
      // 验证回调被调用
      expect(actionCalled, true);
    });
  });
  
  group('AppBarActionIcon 边界情况测试', () {
    
    test('AppBarActionIcon 构造函数测试', () {
      // 测试所有参数都为 null
      const action1 = AppBarActionIcon();
      expect(action1.actionIcon, null);
      expect(action1.actionOnTap, null);
      
      // 测试只有图标
      const action2 = AppBarActionIcon(actionIcon: Icon(Icons.settings));
      expect(action2.actionIcon, isA<Icon>());
      expect(action2.actionOnTap, null);
      
      // 测试只有回调
      final action3 = AppBarActionIcon(actionOnTap: () {});
      expect(action3.actionIcon, null);
      expect(action3.actionOnTap, isA<Function>());
    });
  });
}

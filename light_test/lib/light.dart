import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get_core/get_core.dart';
import 'package:get/get_navigation/get_navigation.dart';

class CinPage extends StatefulWidget {
  const CinPage({super.key});

  @override
  State<CinPage> createState() => _CinPageState();
}

class _CinPageState extends State<CinPage> {

  final Map<String, dynamic> args = Get.arguments;
  late final BluetoothDevice device;
  List<BluetoothService> deviceService = [];

  @override
  void initState()  {
    super.initState();
    device = (args['device'] as ScanResult).device;
    device.discoverServices().then((services) {
      for (var service in services) {
        setState(() {
          deviceService.add(service);
        });
      }
    });
  }

  @override
  void dispose() {
    device.disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CinPage'),
      ),
      // floatingActionButton: ElevatedButton(
      //   onPressed: ()  async {
          
      //   },
      //   child: const Text('services'),
      // ),
      body: Column(
        children: [
          for (var service in deviceService)
            Column(
              children: [
                for (var characteristic in service.characteristics)
                  Visibility(
                    visible: (characteristic.properties.write || characteristic.properties.writeWithoutResponse) && characteristic.properties.read,
                    child: Column(
                      children: [
                        switch ((characteristic.properties.write || characteristic.properties.writeWithoutResponse)) {
                          true => Column(
                            children: [
                              Text(
                                'WRITE: -- ${characteristic.characteristicUuid.toString()}',
                                style: TextStyle(
                                  fontSize: 10
                                ),
                              ),
                              ElevatedButton(
                                onPressed: () async {
                                  characteristic.write([0x00, 0x00, 0x00, 0x00, 0x00, 0x00]);
                                },
                                child: const Text('WRITE'),
                              ),
                            ],
                          ),
                          _ => Text('null'),
                        },
                        switch (characteristic.properties.read) {
                          true => Column(
                            children: [
                              Text(
                                'READ: -- ${characteristic.characteristicUuid.toString()}',
                                style: TextStyle(
                                  fontSize: 10
                                ),
                              ),
                              ElevatedButton(
                                onPressed: () async {
                                  List<int> result = await characteristic.read();
                                  print(result);
                                  // print(ascii.decode(result));
                                  // print(ascii.decode(result));
                                  // SYNCVID - [BweeSync]
                                  // LCX001 - [3D65, 5124]
                                  // LXR002 - [A9DC]
                                  // LXB001 - [665D]
                                  // LUX  
                                },
                                child: const Text('READ'),
                              ),
                              Text('--------done--------'),
                            ],
                          ),
                          _ => Text('null'),
                        },
                        
                        // Text(characteristic.properties.toString()),
                      ],
                    )
                  ),
              ],
            )
        ],
      ),
    );
  }
}
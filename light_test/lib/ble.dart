
import 'dart:async';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class Ble {
  Ble() {
    FlutterBluePlus.setLogLevel(
      LogLevel.error, 
      color: true
    );
    setBleStateStream();
  }

  static Timer? tts;
  static Set<ScanResult> devices = {};
  static StreamSubscription<List<ScanResult>>? srl;
  static StreamSubscription? bleStream;

  void setBleStateStream() {
    bleStream = FlutterBluePlus.adapterState.listen((BluetoothAdapterState state) {});
  }

  static Future<void> scan({
    light = true,
  }) async {
    if (tts != null) {
      tts?.cancel();
      tts = null;
    }

    devices.clear();

    FlutterBluePlus.startScan(
      withServices: [
        if (light) Guid('bae55b96-7d19-458d-970c-50613d801bc9')
      ],
    );

    tts = Timer(const Duration(seconds: 6),(){
      offScan();
      tts?.cancel();
      tts = null;
    });
    
  }

  static Future<void> offScan() async {
    if (!FlutterBluePlus.isScanningNow) return;
    await FlutterBluePlus.stopScan();
  }

  static listen(void Function(ScanResult results) callback) {
    srl = FlutterBluePlus.onScanResults.listen((results) async {
      if (results.isEmpty) return;
      final result = results.last;

      if (result.rssi < -80) return;
      if (result.device.platformName.isEmpty) return;
      if (devices.contains(result)) return;
      devices.add(result);
      callback(result);
    }) ;
  }

  static dispose() {
    tts?.cancel();
    tts = null;
    srl?.cancel();
    srl = null;
    bleStream?.cancel();
    bleStream = null;
    devices.clear();
  }
}
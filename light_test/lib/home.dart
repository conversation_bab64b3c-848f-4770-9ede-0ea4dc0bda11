import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/route_manager.dart';
import 'package:light_test/ble.dart';

import 'routes.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {

  final List<ScanResult> devices = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      Ble.listen((result) {
        setState(() {
          devices.add(result);
        });
        print('''
          ${result}
        '''.trim());
        print('------------');
      });
    });
  }

  @override
  void dispose() {
    Ble.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Home'),
      ),
      floatingActionButton: ElevatedButton(
        onPressed: (){
          Ble.scan();
        },
        child: const Text('Scan'),
      ),
      body: Column(
        children: [
          for (var dev in devices)
            GestureDetector(
              onTap: () async {
                dev.device.connect().then((_){
                  Get.toNamed(
                    Routes.light,
                    arguments: {
                      'device': dev
                    }
                  );
                });
              },
              child: ListTile(
                title: Text(dev.device.platformName),
                subtitle: Text(dev.device.remoteId.toString()),
              ),
            )
        ],
      )
    );
  }
}
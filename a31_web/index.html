<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta name="referrer" content="no-referrer" />
	<title>Web Management</title>
	<!-- Introduce CSS Style(引入css样式) -->
	<link rel="stylesheet" href="./style/style.css?v=1.2">
	<link rel="icon" href="./static/WebIcon.ico?v=1.2" type="image/x-icon">
	<link rel="shortcut icon" href="./static/WebIcon.ico?v=1.2" type="image/x-icon">
	<!-- Introduction of core module: Vue(引入核心模块：VUE) -->
	<script src="./js/vue.js"></script>
	<script src="./js/script.js"></script>
	<!-- Introducing the core module JQuery(引入核心模块JQuery) -->
	<script src="./js/JQuery_2.2.js"></script>
	<!-- Components:top navigation barr(组件：顶部导航栏) -->
	<script src="./common/topBar_2.2.js?v=1.2"></script>
	<!-- Component: current playback information(组件：当前播放信息) -->
	<script src="./common/currentPlay_2.2.js?v=1.2"></script>
	<!-- Components: current playlist -->
	<script src="./common/currentPlaylist_2.2.js?v=1.2"></script>
	<!-- Components: bottom function bar(组件：底部功能栏) -->
	<script src="./common/bottomPlay_2.2.js?v=1.2"></script>
	<!-- (组件：在线电台) -->
	<script src="./common/onlineRadio_2.2.js?v=1.2"></script>
	<script src="./static/images_2.2.js?v=1.2"></script>
	<script src="./js/Getmethods_2.2.js?v=1.2"></script>
	<script src="https://radio.arylic.com/md5.js?v=1.2"></script>
</head>

<body>
	<div id="app">
		<!-- form page -->
		<div class="iemask">
			<div class="iemessage_body">
				<span>Error</span>
				<div class="ie_content">We are very sorry that we have not solved the compatibility problem of IE
					browser, please try to use Google Browser</div>
			</div>
		</div>
		<div class="FormPage">
			<div class="tipsMessage"></div>
			<div class="player_body">
				<div class="loadingMask">
					<div class="loader"></div>
					<div class="loadtitle">loading...</div>
				</div>
				<div class="login_mask">
					<div class="login_page">
						<div class="login_title">Web Management</div>
						<div class="login_input">
							<input type="password" placeholder="Password" class="loginput" v-model="login_pas">
						</div>
						<div class="login_btn"><button @click="user_login">login</button></div>
					</div>
				</div>
				<div class="loginOut_mask">
					<div class="loginOut_page">
						<div class="loginOut_title">Are you sure you want to log out?</div>
						<div class="loginOut_btn">
							<button @click="$('.loginOut_mask').css('display','none')">Cancel</button>
							<button @click="login_outSure">Out</button>
						</div>
					</div>
				</div>
				<!-- Fuzzy background(模糊的背景) -->
				<div class="Fuzzy_background">
					<img :src="nowplaying_State.url">
					<!-- <canvas id="canvas" :width="canvasW+'px'" :height="canvasH+'px'"></canvas> -->
					<div class="screen"></div>
					<div id="grad"></div>
				</div>
				<!-- play Content(播放器内容) -->
				<div class="content">
					<!-- Top navigation bar(顶部导航栏) -->
					<div class="top_bar">
						<top-bar :top_title="top_title" :data="this" ref="topChild"></top-bar>
					</div>
					<!-- Middle(播放器中间一块) -->
					<div v-show="SwitchVideo" class="theBodyOfPlay">
						<div class="mind_PlayContent">
							<div class="eqmask" @click="shadoweqmask" :class="showEqbody == false?'eqmaskOn':''"></div>
							<!-- listmask -->
							<div class="PrivacyPolicy_mask">
								<div class="PrivacyPolicy_body">
									<div class="Privacy_Title">Terms and Conditions</div>
									<div id="Privacy_content" @scroll="iframeScroll">
										<iframe class="iframes" src="https://radio.arylic.com/privacy" frameborder="0"></iframe>
										<!-- src="https://radio.arylic.com/privary_policy.html" -->
									</div>
									<div class="Privacy_bottom">
										<div class="pri_right">
											<div class="pri_btn pri_btn_yes"
												:class="btn_yesFalse == false?'btn_yesFalse':''"
												@click="open_onlineRadio">I agree</div>
											<div class="pri_btn pri_close" @click="Close_primask">I decline</div>
										</div>
									</div>
								</div>
							</div>
							<div class="list_mask" :class="show_list == true?'show_listmask':''" @click="list_mask">
							</div>
							<div class="radio_mask">
								<div class="login_outPage">
									<div class="loginOut_popUp">
										<div class="loginOutTitle">Are you sure to log out?</div>
										<div class='outBtnPage'>
											<button @click="canceloutindex">Cancel</button>
											<button @click="login_outsure">Out</button>
										</div>
									</div>
								</div>
							</div>
							<div class="play_infoBody">
								<!-- 播放信息 -->
								<current-play :$fatherdata="this"></current-play>
								<!-- 播放的列表 -->
								<current-playlist :data="this" ref="current_child"></current-playlist>
							</div>
							<!-- 侧边栏 -->
							<div class="play_slidebody">
								<div class="slide_container">
									<div class="show_RightlisT">
										<div title="Input Source">
											<svg class="icon" aria-hidden="true" @click="show_Source('Sound_Source')">
												<use xlink:href="#icon-yinyuan"></use>
											</svg>
										</div>
										<div title="Online Radio">
											<svg class="icon" aria-hidden="true" @click="show_Source('oneline_radio')">
												<use xlink:href="#icon-diantai"></use>
											</svg>
										</div>
										<div title="Preset List">
											<svg class="icon" aria-hidden="true" @click="show_Source('Preset_list')">
												<use xlink:href="#icon-yushe"></use>
											</svg>
										</div>
									</div>
									<div class="Sound_Source" style="color: white;" shownavtable="false">
										<div class="sourceTitle">
											input source
										</div>
										<div @click="SwitchAsource(item)" class="sourceContent"
											:class="[item == AsourceName?'sourceSelect':'',item == 'USB'?havaUSB == 0?'disabledUSB':'':'']"
											v-for="(item,index) in Input_sourceLsit" :key="index">
											{{item}}
										</div>
									</div>
									<div class="oneline_radio ShowOneline_radio" shownavtable="false">
										<online-radios :$fatherdata="this" ref="oneline_radioC"></online-radios>
									</div>
									<div class="Preset_list" shownavtable="false">
										<div class="List_content h1">
											Preset List
										</div>
										<div class="preset_scrollview">
											<div class="list_table"
												:class="(NowplaylistIndex * 1) === item.class?'Select_list_table':''"
												@click="List_play(item,index)" v-for="(item,index) of preset_playlist"
												:key="index">
												<img :src="item.PicUrl == undefined?'./static/defaultbackground.png':item.PicUrl">
												<div class="preset_name">{{item.Name ||'Empty'}}</div>
												<div class="delectSonCon"
													:style="{display:item.Name == undefined?'none':''}"
													@click.stop="delectThisSong(item)">×</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="Equalizer_body" ref="eqbody">
								<div class="balancer">Audio Settings</div>
								<div class="Trebleclass">
									<div class="treble">
										<span>Treble</span>
										<span style="float: right;">{{trebleVal}}dB</span>
										<div class="treble_rangeContent">
											<div class="treble_tc"></div>
											<input class="treble_slider slider_thumb" type="range" value="treble_value"
												step="1" max="100" min="0" v-model="treble_value"
												@input="select_trebleValue" @change="setTreVal">
											<div class="treble_tcright"></div>
										</div>
									</div>
									<div class="medium">
										<span>Medium</span>
										<span style="float: right;">{{MIDVal}}dB</span>
										<div class="medium_rangeContent">
											<div class="medium_tc"></div>
											<input class="medium_slider slider_thumb" type="range" value="medium_value"
												step="1" max="100" min="0" v-model="medium_value"
												@input="select_mediumValue" @change="setMidVal">
											<div class="medium_tcrihgt"></div>
										</div>
									</div>
									<div class="bass">
										<span>Bass</span>
										<span style="float: right;">{{BassVal}}dB</span>
										<div class="bass_rangeContent">
											<div class="bass_tc"></div>
											<input class="bass_slider slider_thumb" type="range" value="bass_value"
												step="1" max="100" min="0" v-model="bass_value"
												@input="select_bassValue" @change="setBassVal">
											<div class="bass_tcright"></div>
										</div>
									</div>
								</div>
								<div class="deppbass">
									<div style="float: right;">Deep Bass</div>
									<div class="Switch_bass" @click="DeepSwitch">
										<span class="bass_btn"></span>
									</div>
								</div>
								<div class="eqmode">
									<div>Equalizer</div>
									<div class="scrollFLAT">
										<div class="NowEQtitle" @click="showMyEQ">{{NowEQ}}</div>
									</div>
									<div class="flatList" v-if="showEQlist">
										<span v-for="item of EQlist" :class="NowEQ == item.name?'gaoliang':''"
											@click="SwitchEQ(item)">{{item.name}}</span>
									</div>
								</div>
								<div class="MaxVolume">
									<div class="MaxVolume_top">
										<div>Max Volume</div>
										<div style="padding-right:3px;">{{MXV}}%</div>
									</div>
									<div class="MaxVolume_bottom">
										<svg class="icon" aria-hidden="true">
											<use xlink:href="#icon-volume100"></use>
										</svg>
										<div class="MaxVolume_Content">
											<input class="MaxVolume_range slider_thumb" value="MaxVolume_value"
												v-model="MaxVolume_value" max="100" min="0" step="1" type="range"
												@input="set_MaxVolume_value" @change="setMXV">
											<div class="MaxVolume_tc"></div>
										</div>
									</div>
								</div>
								<div class="balance">
									<div class="balance_top">
										<div>Balance</div>
										<div>{{BALVal}}%</div>
									</div>
									<div class="balance_bottom">
										<span>L</span>
										<div class="balance_Content">
											<div class="balance_tc"></div>
											<input class="balance_range slider_thumb" value="balance_value"
												v-model="balance_value" max="100" min="0" step="1" type="range"
												@input="set_balance_value" @change="setbvals">
											<div class="balance_tcright"></div>
										</div>
										<span>R</span>
									</div>
								</div>
							</div>
						</div>
						<!-- 底部播放按钮类 -->
						<div class="Botm_playmodel">
							<bottom-play :dataday="this" ref="botm_play"></bottom-play>
						</div>
					</div>
					<div class="SwitchForm theBodyOfPlay" v-show="SwitchForm">
						<div class="reset_mask">
							<div class="reset_page">
								<div class="reset_message">Restore to factory settings?</div>
								<div class="reset_btnpage"><button @click="factory_reset(true)">Yes</button><button
										@click="factory_reset(false)">No</button></div>
							</div>
						</div>
						<!-- project ID -->
						<div class="form-item">
							<div class="ladel">Project ID</div>
							<div class="rightbox">{{info.ProjectID}}</div>
						</div>
						<!-- MAC address -->
						<div class="form-item">
							<div class="ladel">MAC address</div>
							<div class="rightbox">{{info.MACaddress}}</div>
						</div>
						<!-- firmware version -->
						<div class="form-item">
							<div class="ladel">Firmware Version</div>
							<div class="col-box">
								<div class="form-box">
									<div class="rightbox">
										{{info.FirmwareVersion}}
										<!-- 自动升级 -->
										<div v-if="info.newVersion != '0.0'" @click="ota_upgrade" class="icontip flash"
											:style="animations">
											<i class="iconfont icon-update icon-big"></i>
										</div>
									</div>
									<!-- 手动升级 TODO -->
									<form id="uploadFiles" method="post" @change="fw_upload($event)"
										enctype="multipart/form-data" target="targetFrame">
										<label for="file" form="uploadFiles" class="apply-btn" style="margin-left:0px;">Manual</label>
										<input type="file" id="file" ref="inputer" class="f-hidden" />
									</form>

								</div>
							</div>
							<div class="col-box" style="justify-content: center;" title="OTA Upgrade Automatically">
								<div class="Switch_bass2" :class="ota?'switchon':''" @click="opaUpgrade">
									<span class="bass_btn2"></span>
								</div>
							</div>
						</div>
						<!-- ota name -->
						<!-- <div class="form-item">
							<div class="ladel">Auto Upgrade</div>
							
						</div> -->
						<!-- device name -->
						<div class="form-item">
							<div class="ladel" for="DeviceName">Device Name</div>
							<form class="form-box" target="targetFrame">
								<input v-model="form.DeviceName" name="DeviceName" class="input"
									:placeholder="info.DeviceName" />
								<div v-if="form.DeviceName" @click="set_device_name" class="btn" style="width:50px;">
									Save</div>
							</form>
						</div>
						<!-- connect wifi -->
						<div v-if="info.wifiEnabled == '1'" class="form-item">
							<div class="ladel">Connect to AP</div>
							<div class="form-box">
								<div class="rightbox wifi_rightbox">
									{{info.ConnectAP | SSID }}
								</div>
								<div v-if="info.ConnectAP" class="icontip wifi_icon">
									<i class="icon-big iconfont icon-WiFixinhao"></i>
								</div>
								<button class="btn showaplist_btn"
									:style="{'margin-left':info.ConnectAP == ''?'30px':'4px'}"
									@click="wifi_show_aplist">Connect</button>
							</div>
						</div>
						<!-- set wifi IP -->
						<div v-if="info.wifiEnabled == '1'" class="form-item">
							<div class="ladel">IP Settings (WiFi)</div>
							<div class="col-box">
								<label class="select-box">
									<input type="radio" name="ik" v-model="info.wifiStatic" value="0" checked="checked"
										@click="set_wifi_ip_mode">
									<div>DHCP</div>
									<input type="radio" name="like" v-model="info.wifiStatic" value="1"
										@click="set_wifi_ip_mode">
									<div>MANUAL</div>
								</label>
								<div class="form-box" style="position: relative;">
									<div v-if="info.wifiStatic == '0'" class="rightbox"
										style="position: absolute;top: 3px;padding-left: 0px;">{{info.wifiIP}}</div>
									<input v-if="info.wifiStatic == '1'" v-model="form.wifiIPInput"
										:disabled="info.wifiStatic=='0'" class="input" :placeholder="info.wifiIP" />
									<button v-if="info.wifiStatic == '1' && form.wifiIPInput"
										:disabled="!form.wifiMaskInput || !form.wifiGatewayInput || !form.wifiDNS1Input"
										type="submit" @click="set_wifi_static_ip" class="btn">Save</button>
								</div>
								<div class="form-box" v-if="info.wifiStatic == '1'">
									<input v-model="form.wifiMaskInput" class="input" :placeholder="info.staticMask" />
								</div>
								<div class="form-box" v-if="info.wifiStatic == '1'">
									<input v-model="form.wifiGatewayInput" class="input"
										:placeholder="info.staticGateway" />
								</div>
								<div class="form-box" v-if="info.wifiStatic == '1'">
									<input v-model="form.wifiDNS1Input" class="input" :placeholder="info.staticDNS1" />
								</div>
								<div class="form-box" v-if="info.wifiStatic == '1'">
									<input v-model="form.wifiDNS2Input" class="input" :placeholder="info.staticDNS2" />
								</div>
							</div>
						</div>
						<!-- set ETH IP -->
						<div v-if="info.ethEnabled == '1'" class="form-item">
							<div class="ladel">IP Settings (ETH)</div>
							<div class="col-box">
								<label class="select-box">
									<input type="radio" name="like" v-model="info.ethStatic" value="0" checked="true"
										@click="set_eth_ip_mode">
									<div>DHCP</div>
									<input type="radio" name="like" v-model="info.ethStatic" value="1"
										@click="set_eth_ip_mode">
									<div>MANUAL</div>
								</label>
								<div class="form-box" style="position: relative;">
									<div v-if="info.ethStatic =='0'" class="rightbox"
										style="position: absolute;top: 3px;padding-left: 0px;">{{info.ethIP}}</div>
									<input v-if="info.ethStatic =='1'" v-model="form.ethIPInput"
										:disabled="info.ethStatic =='0'" class="input" :placeholder="info.ethIP" />
									<button v-if="info.ethStatic =='1' && form.ethIPInput"
										:disabled="!form.ethMaskInput || !form.ethGatewayInput || !form.ethDNS1Input"
										type="submit" @click="set_eth_static_ip" class="btn">Save</button>
								</div>
								<div class="form-box" v-if="info.ethStatic =='1'">
									<input v-model="form.ethMaskInput" class="input" :placeholder="info.staticMask" />
								</div>
								<div class="form-box" v-if="info.ethStatic =='1'">
									<input v-model="form.ethGatewayInput" class="input"
										:placeholder="info.staticGateway" />
								</div>
								<div class="form-box" v-if="info.ethStatic =='1'">
									<input v-model="form.ethDNS1Input" class="input" :placeholder="info.staticDNS1" />
								</div>
								<div class="form-box" v-if="info.ethStatic =='1'">
									<input v-model="form.ethDNS2Input" class="input" :placeholder="info.staticDNS2" />
								</div>
							</div>
						</div>

						<!-- password -->
						<div class="form-item" style="margin: 25px auto;">
							<div class="ladel" style="margin-top: 8px;">Admin Password</div>
							<div class="col-box">
								<div class="form-box">
									<div class="icon-box">
										<input class="input" :type="eye?'text':'password'" v-model="form.pas_new"
											placeholder="New password" />
										<div class="icontip">
											<i v-if="eye" class="iconfont icon-yan" @click="eye=!eye"></i>
											<i v-else class="iconfont icon-biyan" @click="eye=!eye"></i>
										</div>
									</div>
									<div style="margin-left: 44px;" title="Limit With Password">
										<div class="Switch_bass2" :class="Nopasw?'switchon':''" @click="TableNopsw">
											<span class="bass_btn2"></span>
										</div>
									</div>
								</div>
								<div class="form-box">
									<div class="icon-box">
										<input class="input" :type="eye?'text':'password'" v-model="form.pas_cfm"
											placeholder="Confirm password" />
										<div class="icontip">
											<i v-if="eye" class="iconfont icon-yan" @click="eye=!eye"></i>
											<i v-else class="iconfont icon-biyan" @click="eye=!eye"></i>
										</div>
									</div>
									<button v-if="form.pas_new && form.pas_cfm" class="btn"
										@click="set_password">Save</button>
								</div>
							</div>
						</div>
						<!-- factory reset -->
						<form class="form-item">
							<label class="ladel"></label>
							<button type="button" class="reset-btn" @click="show_reset_page"
								style="margin-bottom: 0;">Factory Reset</button>
						</form>
						<iframe frameborder="0" style="display:none" name="targetFrame"></iframe>
					</div>
					<div v-if="showTable" class="table-content">
						<!-- <div class="wlan-list">WIFI LIST</div> -->
						<div class="table-top">
							<!-- 刷新设备列表 -->
							<button class="btn" @click="wifi_refresh_aplist" style="margin-left: 0px ;"><i
									class="iconfont icon-refresh p5r"></i>Refresh</button>
							<div class="flex1"></div>
							<button class="btn" @click="wifi_hide_aplist"><i
									class="iconfont icon-go-back-new p5r"></i>Back</button>
						</div>
						<table id="tablewifi" align="center">
							<thead class="theader">
								<tr class="tr-row">
									<th class="th-header" v-for="(item,index) in thList">{{item}}</th>
								</tr>
							</thead>
							<tbody class="scrollWifi">
								<tr :class="item.ssid === info.ConnectAP?'tr-row-focus':'tr-row'"
									v-for="(item,index) in wifiList" :key="index"
									@click="wifi_show_connect(index,item)">
									<td class="td-col" style="text-align:left">
										<svg v-if="item.ssid" class="icon">
											<use v-if="item.rssi<=25 && item.rssi>=0" xlink:href="#rkwifi"></use>
											<use v-if="item.rssi<=50 && item.rssi>=26" xlink:href="#rkwifi1"></use>
											<use v-if="item.rssi<=80 && item.rssi>=51" xlink:href="#rkwifi2"></use>
											<use v-if="item.rssi<=100 && item.rssi>=81" xlink:href="#rkwifi3"></use>
										</svg>
										<div style="display: inline-block;margin-left: 10px;">
											{{ item.ssid | SSID }}
										</div>
									</td>
									<td class="td-col">{{item.bssid}}</td>
									<td class="td-col"><i class="iconfont" :class="item.auth === 'OPEN'?'':'icon-huaban'"></i>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<!-- wifi login page -->
			<div v-if="showLoginWifi || showAlert" class="mask">
				<div v-if="showLoginWifi" class="login-wifi">
					<button class="closein" @click="wifi_hide_connect">×</button>
					<div class="logintitle">{{wifi.ssid | SSID}}</div>
					<div class="relbox">
						<input class="loginput" v-model="wifiPassword" type="password" required />
						<button class="loginbtn" @click="wifi_connect">Connect</button>
					</div>
				</div>
			</div>
			<!-- wifi login page -->
		</div>
	</div>
</body>
<script>
	/* 浏览器兼容性 */
	function IEVersion() { let userAgent = navigator.userAgent; let isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; let isEdge = userAgent.indexOf("Edge") > -1 && !isIE; let isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1; if (isIE) { let reIE = new RegExp("MSIE (\\d+\\.\\d+);"); reIE.test(userAgent); let fIEVersion = parseFloat(RegExp["$1"]); if (fIEVersion == 7) { return 7; } else if (fIEVersion == 8) { return 8; } else if (fIEVersion == 9) { return 9; } else if (fIEVersion == 10) { return 10; } else { return 6; } } else if (isEdge) { return 'edge'; } else if (isIE11) { return 11; } else { return -1; } } var llqbb = IEVersion(); if (llqbb != -1) { $('.iemask').css('display', 'block'); }
</script>
<script type="text/javascript" charset="utf-8">
	var debugging = true;
	var HOST_IP = "";
	if (debugging) HOST_IP = "http://**************/";
	var vm = new Vue({
		el: "#app",
		/* 注册组件 */
		components: {
			/* 顶部导航栏模块 */
			topBar,
			/* 播放大图模块 */
			currentPlay,
			/* 播放列表模块 */
			currentPlaylist,
			/* 底部栏模块 */
			bottomPlay,
			/* 在线电台模块 */
			onlineRadios,
		},
		data() {
			return {
				login_pas: '', // 登录密码
				device_info: {}, //	设备信息
				top_title: 'Now Playing', //	左上角标题
				showTable: false,	// wifi列表开关选项 false:关闭
				showLoginWifi: false,
				showAlert: false,
				SwitchVideo: true,
				SwitchForm: false,
				eye: false, // 密码显示开关
				animations: "", // animation:flash 1s .2s ease-out both infinite
				infinite: 'infinite',
				loading: false,
				LoadText: "Loading",
				thList: ["SSID", "BSSID", "Encrypt"], // "RSSI","CHANNEL","AUTH","EXTCH"
				indexs: null,
				bar: 0,
				wifi: {},
				wifiList: [],
				ota_check_interval: null,
				// gradientDeg: 0,
				// preshow: false,
				Playback_mode: 1,	// 播放模式
				show_list: false,	// 记录右侧列表开启状态 false：关闭
				shou_listname: '',	// 传入名字弹出相应右侧列表
				playing_songs: false,	// 是否正在播放歌曲
				preset_playlist: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
				info: {
					upgrading: false,
					ProjectID: "",
					MACaddress: "",
					newVersion: "0.0",	// 新版本提示
					FirmwareVersion: "",
					DeviceName: "",
					ConnectAP: "",
					wifiIP: "",
					ethIP: "",
					staticMask: "Subnet Mask",
					staticGateway: "Gateway",
					staticDNS1: "Prefered DNS",
					staticDNS2: "Alternate DNS",
					wifiEnabled: 1,
					ethEnabled: 1,
					wifiStatic: 0,
					ethStatic: 0,
				},
				form: {
					DeviceName: '',
					wifiIPInput: "",
					wifiMaskInput: "",
					wifiGatewayInput: "",
					wifiDNS1Input: "",
					wifiDNS2Input: "",
					ethIPInput: "",
					ethMaskInput: "",
					ethGatewayInput: "",
					ethDNS1Input: "",
					ethDNS2Input: "",
					// playUrl: "",
					pas_new: "",
					pas_cfm: "",
				},
				wifiPassword: '',
				nowplaying_list: [],
				nowplaying_State: {
					name: '',
					url: '',
					album: ''
				},
				playing_index: 0,
				upnpUrl: '',
				Current_status: {},
				// now_song_obj: {
				// 	Album: '',
				// 	Artist: '',
				// 	Title: ''
				// },
				Input_sourceLsit: [],
				CStatue: '',
				Nowsource: '',
				Asource_list: [
					{
						name: 'Line in',
						mode: '40'
					},
					{
						name: 'Bluetooth',
						mode: '41'
					},
					{
						name: 'Optical',
						mode: '43'
					},
					{
						name: 'USBDAC',
						mode: '51'
					},
					{
						name: 'USB',
						mode: '11'
					},
					{
						name: 'Line in2',
						mode: '47'
					}
				],
				AsourceName: '',
				PListempty: true,
				showEqbody: true,
				DeepS: false,
				directionOF: null,
				treble_value: 50,
				medium_value: 50,
				bass_value: 50,
				MaxVolume_value: 0,
				balance_value: 50,
				PlayMedium: '',
				// ShowPasswordStatus: false,
				// login_message: '',
				play_status: 'STOP',
				NoMusicSelect: true, /* true:未选择歌曲 , false:选择了歌曲 */
				radio_maskOpen: false,
				Login_Status: false,
				trebleVal: 0,
				MIDVal: 0,
				BassVal: 0,
				MXV: 0,
				DeepBas: 0,
				BALVal: 0,
				NowEQ: '',
				EQlist: [],
				showEQlist: false,
				btn_yesFalse: false,
				NowTrack: {}, // 当前播放信息
				NowInput: 'UNKNOWN',
				NowPlaylistName: '',
				NowplaylistIndex: null,
				sound_data: {
					vol: 0,
					mute: 0,
					loop: Number
				},
				havaUSB: null,
				AsourceIMG: '#icon-a-lujing1',
				Nopasw: false,
				ota: true
			}
		},
		filters: {
			SSID(hexCharCodeStr) {
				// 16进制转字符串
				var trimedStr = hexCharCodeStr.trim();
				var rawStr = trimedStr.substr(0, 2).toLowerCase() === "0x" ? trimedStr.substr(2) : trimedStr;
				var len = rawStr.length;
				if (len % 2 !== 0) {
					this.showMessage(1, 'Illegal Format ASCII Code!');
					return "";
				}
				var curCharCode;
				var resultStr = [];
				for (var i = 0; i < len; i = i + 2) {
					curCharCode = parseInt(rawStr.substr(i, 2), 16); // ASCII Code Value
					resultStr.push(String.fromCharCode(curCharCode));
				}
				return resultStr.join("");
			},
		},
		watch: {
			NowPlaylistName: function (v, l) {
				if (v != 'spotify') {
					this.preset_playlist.forEach((item, index) => {
						if (item.Name + item.id == v) {
							this.NowplaylistIndex = item.class;
						}
					})
				}
			},
			NowTrack: function (v, l) {
				if (v == null) {
					for (var i in this.nowplaying_State) {
						this.nowplaying_State[i] == '';
					}
				} else {
					if (v.meta != null) {
						
						this.nowplaying_State.name = v.meta.title;
						if (v.meta.image != 'un_known') {
							this.nowplaying_State.url = v.meta.image;
						} else {
							this.nowplaying_State.url = '';
						}
						if (v.meta.artist != '') {
							this.nowplaying_State.album = v.meta.album + ',' + v.meta.artist;
						} else {
							this.nowplaying_State.album = v.meta.album + v.meta.artist;
						}
						this.$refs.botm_play.curpos = v.elapsed;
						this.$refs.botm_play.totlen = v.duration;
					}
				}
			},
			NowInput: function (v) {
				this.Get_current_status();
			},
			NowEQ: function (v, l) {
				if (v == '') {
					$('.eqmode').attr('readonly', 'readonly');
				} else {
					$('.eqmode').removeAttr('readonly');
				}
			},
			// 检测音源
			PlayMedium: function (v, l) {
				if (v == 'NONE' || v == 'UNKNOWN' || v == 'SPOTIFY' || v == 'AIRPLAY' || v == 'THIRD-DLNA' || v == 'RADIO-NETWORK') {
					$('.Nowplay_list').css('display', 'none');
				} else {
					setTimeout(() => {
						$('.Nowplay_list').css('display', 'block');
					}, 600)
				}
			},
			/* 监听音源改变状态 */
			Nowsource: function (v, l) {
				// console.log('音源状态是',v)
				this.Asource_list.forEach(i => {
					if (v == i.mode) {
						this.AsourceName = i.name;
					}
				});
				if (v == '0' || v == '1' || v == '2' || v == '10' || v == '16' || v == '20' || v == '31' || v == '32' || v == '99') {
					this.AsourceName = 'WiFi';
					this.AsourceIMG = '#icon-a-lujing1';
					if (this.NowTrack.meta == null) {
						document.querySelector('.Song_NameA').innerHTML = 'No Music Select';
					}
				} else if (v == '40') {
					this.AsourceIMG = '#icon-line';
					if (this.NowTrack.meta == null) {
						document.querySelector('.Song_NameA').innerHTML = 'LINE - IN';
					}
				} else if (v == '41') {
					this.AsourceIMG = '#icon-ble';
					if (this.NowTrack.meta == null) {
						document.querySelector('.Song_NameA').innerHTML = 'BLUETOOTH';
					}
				} else if (v == '51') {
					this.AsourceIMG = '#icon-a-USBAudio';
					if (this.NowTrack.meta == null) {
						document.querySelector('.Song_NameA').innerHTML = 'USBDAC';
					}
				} else if (v == '43') {
					this.AsourceIMG = '#icon-a-ziyuan5';
					if (this.NowTrack.meta == null) {
						document.querySelector('.Song_NameA').innerHTML = 'OPTICAL';
					}
				} else if (v == '45') {
					this.AsourceIMG = '#icon-co';
					if (this.NowTrack.meta == null) {
						document.querySelector('.Song_NameA').innerHTML = 'COAXIAL';
					}
				} else if (v == '47') {
					this.AsourceIMG = '#icon-line';
					if (this.NowTrack.meta == null) {
						document.querySelector('.Song_NameA').innerHTML = 'LINE - IN2';
					}
				}
			},
			playing_index: function (v, l) {
				this.$nextTick(function () {
					this.get_list_location();
				})
			},
			trebleVal: function (v, l) {
				this.treble_value = 50 + (v * 5);
				if (this.treble_value < 51) {
					$('.treble_tcright').width(0);
					var pyl = 50 - (this.treble_value * 1);
					$('.treble_tc').width(pyl * 2.5);
				} else {
					$('.treble_tc').width(0);
					var pyl = (this.treble_value * 1) - 50;
					$('.treble_tcright').width(pyl * 2.5);
				}
			},
			MIDVal: function (v, l) {
				this.medium_value = 50 + (v * 5);
				if (this.medium_value < 51) {
					$('.medium_tcrihgt').width(0);
					var pyl = 50 - (this.medium_value * 1);
					$('.medium_tc').width(pyl * 2.5);
				} else {
					$('.medium_tc').width(0);
					var pyl = (this.medium_value * 1) - 50;
					$('.medium_tcrihgt').width(pyl * 2.5);
				}
			},
			BassVal: function (v, l) {
				this.bass_value = 50 + (v * 5);
				if (this.bass_value < 51) {
					$('.bass_tcright').width(0);
					var pyl = 50 - (this.bass_value * 1);
					$('.bass_tc').width(pyl * 2.5);
				} else {
					$('.bass_tc').width(0);
					var pyl = (this.bass_value * 1) - 50;
					$('.bass_tcright').width(pyl * 2.5);
				}
			},
			BALVal: function (v, l) {
				this.balance_value = (v * 1 + 100) / 2;
			},
			balance_value: function (v, l) {
				if (this.balance_value < 51) {
					$('.balance_tcright').width(0);
					pyl = 50 - (this.balance_value * 1);
					$('.balance_tc').width(pyl * 2.2);
				} else {
					$('.balance_tc').width(0);
					pyl = (this.balance_value * 1) - 50;
					$('.balance_tcright').width(pyl * 2.2);
				}
			},
			MXV: function (v, l) {
				this.MaxVolume_value = v;
				$('.MaxVolume_tc').width(v * 2.25);
			},
			DeepS: function (v, l) {
				if (v == false) {
					$('.Switch_bass').css('background-color', 'rgb(57, 62, 66)');
					$('.bass_btn').css('left', '2px');
				} else {
					$('.Switch_bass').css('background-color', 'rgb(62 162 133)');
					$('.bass_btn').css('left', '22px');
				}
			},
			DeepBas: function (v, l) {
				if (v == 0) {
					this.DeepS = false;
				} else {
					this.DeepS = true;
				}
			},
			Nopasw: function (v) {
				if (v) {
					$('.loginIcon').removeClass('loginIconDisable');
					$('.theBodyOfPlay').removeClass('theBodyOfPlayIStrue');
					localStorage.setItem('Nopas', 1);
				} else {
					$('.loginIcon').addClass('loginIconDisable');
					$('.theBodyOfPlay').addClass('theBodyOfPlayIStrue');
					localStorage.setItem('Nopas', 0);
				}
			}
		},
		mounted() {
			window.addEventListener('keydown', event => {
				if (event.keyCode === 9) {
					event.preventDefault();
				}
				if (event.keyCode === 13) {
					if (document.activeElement.className == 'loginput') {
						this.user_login();
					}
				}
			})
			$('.content').click(() => {
				if (this.Nopasw) {
					if (sessionStorage.getItem('login_status') == null) {
						this.$refs.topChild.show_loginMask();
					}
				}
			})
			$('.login_page').click(function () {
				event.stopPropagation();
			})
			$('.loginOut_page').click(function () {
				event.stopPropagation();
			})
			if (this.Nopasw) {
				$('.login_mask').click(() => {
					this.$refs.topChild.show_loginMask();
				})
			}
		},
		async created() {
			/* 获取当前设备信息 */
			await this.get_device_info();
			/* 获取当前网络信息 */
			await this.get_network_info();
			this.Get_current_status();
			if(localStorage.getItem('NopasFirst') == null){
				localStorage.setItem('NopasFirst',true);
				localStorage.setItem('Nopas','0');
				$('.loginIcon').addClass('loginIconDisable');
				$('.theBodyOfPlay').addClass('theBodyOfPlayIStrue');
			}
			if (localStorage.getItem('Nopas') == '0') {
				this.Nopasw = false;
				$('.loginIcon').addClass('loginIconDisable');
				$('.theBodyOfPlay').addClass('theBodyOfPlayIStrue');
				// if (sessionStorage.getItem('login_status') == '1') {
				// 	this.get_login_state(3);
				// }
			}else{
				this.Nopasw = true;
			}
			this.$refs.botm_play.socket.send('#OTA');
		},
		methods: {
			opaUpgrade(){
				if(this.ota){
					this.$refs.botm_play.socket.send('#OTA:0');
				}else{
					this.$refs.botm_play.socket.send('#OTA:1');
				}
			},
			showMyEQ(){
				// console.log(this.showEQlist);
				this.showEQlist = !this.showEQlist;
			},
			TableNopsw() {
				this.Nopasw = !this.Nopasw;
			},
			iframeScroll(e) {
				if (e.target.scrollTop + e.target.clientHeight > e.target.scrollHeight - 10) {
					this.btn_yesFalse = true;
				} else {
					this.btn_yesFalse = false;
				}
			},
			SwitchEQ(item) {
				this.NowEQ = item.name;
				this.$refs.botm_play.socket.send('EQS:' + item.num);
				this.showEQlist = false;
			},
			canceloutindex() {
				this.$refs.oneline_radioC.cancelLoginOut();
			},
			login_outsure() {
				this.$refs.oneline_radioC.login_out();
			},
			showMessage(or, message, timer = 3000) {
				if (or == 1) {
					var orname = 'errTips';
					var iconname = 'icon-duicuo';
				} else if (or == 0) {
					var orname = 'okiTips';
					var iconname = 'icon-duicuo-';
				}
				var str = $('<div class="istips ' + orname + '"></div>');
				var orIcon = $('<svg class="icon ' + orname + '"><use xlink:href="#' + iconname + '"></use></svg>');
				str.append(orIcon);
				var strMsg = $('<div class="strMsg">' + message + '</div>');
				str.append(strMsg);
				$('.tipsMessage').append(str);
				setTimeout(() => {
					str.addClass('showTips');
				}, 100)
				setTimeout(() => {
					str.addClass('closeTips');
					setTimeout(() => {
						str.remove();
					}, 400)
				}, timer);
			},
			open_onlineRadio() {
				// return console.log(1);
				$('.PrivacyPolicy_mask').css('display', 'none');
				this.show_Source('ShowOneline_radio');
				this.$refs.oneline_radioC.getGenreid();
				localStorage.setItem('through', true);
			},
			Close_primask() {
				$('.PrivacyPolicy_mask').css('display', 'none');
			},
			set_MaxVolume_value() {
				if (this.MaxVolume_value > 30) {
					$('.MaxVolume_tc').width(this.MaxVolume_value * 2.25);
					this.MXV = this.MaxVolume_value;
				} else {
					this.MaxVolume_value = 30;
					$('.MaxVolume_tc').width(this.MaxVolume_value * 2.25);
					this.MXV = this.MaxVolume_value;
				}
			},
			setMXV() {
				this.$refs.botm_play.socket.send('MXV:' + this.MaxVolume_value);
			},
			scope(sv) {
				let kd = sv;
				var num = 0, db = 0, oldDb = -10;
				for (var i = 1; i < 21; i++) {
					if (kd > (i * 5) - 5 && kd < (i * 5) + 1) {
						num = i * 5;
						oldDb = i + oldDb;
					}
				}
				return { num, oldDb }
			},
			scope_old(sv) {
				var num = 0, oldDb = -10;
				for (var i = 1; i < 21; i++) {
					if (sv > (i * 10) - 10 && sv < (i * 10) + 1) {
						num = i * 10;
						oldDb = i * 2 + oldDb;
					}
				}
				// 0 10 20 30 40 50 60 70 80 90 100
				return { num, oldDb };
			},
			// 绑定treble滑块值和填充量
			select_trebleValue(e) {
				if (this.$refs.botm_play.EQAPI == '#API:AP8064' || this.$refs.botm_play.EQAPI == '') {
					scope = this.scope_old(this.treble_value);
				} else {
					scope = this.scope(this.treble_value);
				}
				this.treble_value = scope.num;
				this.trebleVal = scope.oldDb;
			},
			setTreVal() {
				// console.log(this.trebleVal);
				this.$refs.botm_play.socket.send("TRE:" + this.trebleVal);
				// if (this.$refs.botm_play.EQAPI == '#API:AP8064' || this.$refs.botm_play.EQAPI == '') {
				// 	this.$refs.botm_play.socket.send("TRE");
				// }
			},
			select_mediumValue() {
				var scope = this.scope(this.medium_value);
				this.medium_value = scope.num;
				this.MIDVal = scope.oldDb;
			},
			setMidVal() {
				this.$refs.botm_play.socket.send("MID:" + this.MIDVal);
			},
			select_bassValue() {
				if (this.$refs.botm_play.EQAPI == '#API:AP8064' || this.$refs.botm_play.EQAPI == '') {
					scope = this.scope_old(this.bass_value);
				} else {
					scope = this.scope(this.bass_value);
				}
				this.bass_value = scope.num;
				this.BassVal = scope.oldDb;
			},
			setBassVal() {
				this.$refs.botm_play.socket.send("BAS:" + this.BassVal);
				if (this.$refs.botm_play.EQAPI == '#API:AP8064' || this.$refs.botm_play.EQAPI == '') {
					this.$refs.botm_play.socket.send("EQG");
				}
			},
			set_balance_value() {
				this.BALVal = this.balance_value * 2 - 100;
			},
			setbvals() {
				this.$refs.botm_play.socket.send("BAL:" + this.BALVal);
			},
			// deep按钮开关
			DeepSwitch() {
				this.DeepS = !this.DeepS;
				if (this.DeepS == false) {
					this.$refs.botm_play.socket.send("VBS:0");
				} else {
					this.$refs.botm_play.socket.send("VBS:1");
				}
			},
			// 关闭eq界面
			shadoweqmask() {
				this.$refs.botm_play.showEq();
			},
			// /* 获取url */
			// getURL() {
			// 	if (debugging) {
			// 		var ip = HOST_IP.substring(0, HOST_IP.length - 1);
			// 		this.upnpUrl = ip + ':59152/upnp/control/';
			// 	} else {
			// 		if (this.device_info.eth2 != '' && this.device_info.eth2 != '0.0.0.0') {
			// 			this.upnpUrl = 'http://' + this.device_info.eth2 + ':59152/upnp/control/';
			// 		} else if (this.device_info.apcli0 == '' || this.device_info.apcli0 == '0.0.0.0') {
			// 			this.upnpUrl = 'http://' + this.device_info.ra0 + ':59152/upnp/control/';
			// 		} else {
			// 			this.upnpUrl = 'http://' + this.device_info.apcli0 + ':59152/upnp/control/';
			// 		}
			// 	}
			// },
			/* 定位到当前播放歌曲的位置 */
			get_list_location() {
				if (this.nowplaying_list.length != 0) {
					var location = this.playing_index - 1;
					var mas = $('.scrllo_playlist').find('.Playlist_songs:eq(' + location + ')');
					if ($('.scrllo_playlist').length != 0) {
						$('.scrllo_playlist').scrollTop(mas.offset().top - $('.scrllo_playlist').offset().top + $('.scrllo_playlist').scrollTop() - 8);
					}
				}
			},
			/* 获取当前信息 */
			Get_current_status() {
				fetch(HOST_IP + 'httpapi.asp?command=getPlayerStatus').then(res => {
					res.json().then(data => {
						// console.log('当前消息是',data);
						// this.Current_status = data;
						this.Nowsource = data.mode;
						this.sound_data.vol = data.vol;
						this.sound_data.mute = data.mute;
						this.sound_data.loop = data.loop * 1;
					})
				}).catch(err => {
					console.log(err);
				})
			},
			/* 显示侧边栏 */
			show_Source(name) {
				if (!this.Login_Status) {
					if (!this.Nopasw) {
						if (name == 'oneline_radio') {
							if (localStorage.getItem('through') == 'true') {
								this.$refs.oneline_radioC.getGenreid();
								this.show_Source('ShowOneline_radio');
							} else {
								$('.PrivacyPolicy_mask').css('display', 'flex');
							}
						} else {
							this.show_list = true;
							this.shou_listname = name;
							$('.' + name).css('transform', 'translateX(-240px)');
							$('.' + name).css('opacity', '1');
							$('.show_RightlisT').addClass('show_RightlisTON');
							$('.play_infoBody').css('width', '75%');
							$('.' + name).attr('showNavTable', true);
						}
					} else {
						return console.log('未登录');
					}
				} else {
					if (name == 'oneline_radio') {
						if (localStorage.getItem('through') == 'true') {
							this.$refs.oneline_radioC.getGenreid();
							this.show_Source('ShowOneline_radio');
						} else {
							$('.PrivacyPolicy_mask').css('display', 'flex');
						}
					} else {
						this.show_list = true;
						this.shou_listname = name;
						$('.' + name).css('transform', 'translateX(-240px)');
						$('.' + name).css('opacity', '1');
						$('.show_RightlisT').addClass('show_RightlisTON');
						$('.play_infoBody').css('width', '75%');
						$('.' + name).attr('showNavTable', true);
					}
				}
			},
			// 关闭侧边栏
			list_mask() {
				this.show_list = false;
				$('.' + this.shou_listname).css('transform', 'translateX(0px)');
				$('.show_RightlisT').removeClass('show_RightlisTON');
				$('.play_infoBody').css('width', '100%');
				$('.' + this.shou_listname).attr('showNavTable', false);
				setTimeout(() => {
					$('.' + this.shou_listname).css('opacity', '.3');
				}, 200)
			},
			/* 点击预设播放列表加载当前播放列表 */
			List_play(item, index) {
				if (item.Name != undefined) {
					this.nowplaying_list = [];
					fetch(HOST_IP + 'httpapi.asp?command=MCUKeyShortClick:' + (index + 1)).then(res => {
						this.NowplaylistIndex = item.class;
						localStorage.setItem('Pindex', this.NowplaylistIndex);
					})
				} else {
					this.$refs.botm_play.socket.send('#CMD:PLAYLIST_CURRENT');
					if (this.NowTrack.source == '' || this.NowTrack.source == 'airplay' || this.NowTrack.source == 'QPLAY') {
						return false
					} else {
						setTimeout(() => {
							var nowClass = index + 1;
							var nname = this.NowPlaylistName;
							var purl = this.nowplaying_State.url;
							var nsource = this.NowTrack.source;
							this.$refs.botm_play.socket.send('#CMD:PRESET_ADD:' + nowClass + '#$@' + nname + '#$@' + purl + '#$@' + nsource);
							this.$refs.botm_play.socket.send('#CMD:PRESET');
						}, 300)
					}
				}
			},
			delectThisSong(item) {
				this.$refs.botm_play.socket.send('#CMD:PRESET_DEL:' + item.class);
				this.$refs.botm_play.socket.send('#CMD:PRESET');
			},
			/* 切换音源 */
			SwitchAsource(item) {
				if (item == 'Line in') mode = 'line-in';
				if (item == 'Bluetooth') mode = 'bluetooth';
				if (item == 'Optical') mode = 'optical';
				if (item == 'USBDAC') mode = 'PCUSB';
				if (item == 'USB') mode = 'udisk';
				if (item == 'Line in2') mode = 'line-in2';
				if (item == 'Coaxial') mode = 'co-axial';
				if (item == 'WiFi') mode = 'wifi';
				fetch(HOST_IP + 'httpapi.asp?command=setPlayerCmd:switchmode:' + mode).then(() => {
					this.Get_current_status();
				}).catch(err => {
					console.log(err);
				})
			},
			ota_state_progress() {
				fetch(HOST_IP + "httpapi.asp?command=getMvRomBurnPrecent").then(res => {
					res.json().then(r => {
						this.bar = r.progress;
						if (r == "100") {
							clearInterval(this.ota_check_interval);
						}
					})
				}).catch(err => {
					console.log(err);
					clearInterval(this.ota_check_interval);
				})
			},
			ota_stage_state_check() {
				// read UpdateStatus
				// 查询固件下载状态	"httpapi.asp?command=getMvRemoteUpdateStatus" 
				// 10 20没有更新  22下载出错  25正在下载  31校验失败  32开始升级  40下载完成
				fetch(HOST_IP + "httpapi.asp?command=getMvRemoteUpdateStatus").then(res => {
					res.text().then(r => {
						// console.log("ota state: ", r);
						if (r == '25' || r == '0' || r == '40') {
							console.log(r);
						} else if (r == '32') {
							let _this = this;
							clearInterval(this.ota_check_interval);
							this.ota_check_interval = setInterval(_this.ota_state_progress, 1000);
						} else {
							clearInterval(this.ota_check_interval);
						}
					})
				}).catch(err => {
					console.log(err);
					clearInterval(this.ota_check_interval);
				})
			},
			ota_upgrade() {
				fetch(HOST_IP + "httpapi.asp?command=getMvRemoteUpdateStatus").then(res => {
					res.text().then(r => {
						console.log("ota state: ", r);
						if (r != '32' && r != '25') {
							let clicked = confirm("Upgrade to version: " + this.info.newVersion + "?")
							if (clicked == true) {
								let _this = this
								fetch(HOST_IP + "httpapi.asp?command=getMvRemoteUpdateStart").then(res => {
									res.text().then(r => {
										this.ota_check_interval = setInterval(_this.ota_stage_state_check, 1000);
									})
								}).catch(err => { console.log(err) })
							}
						}
						else {
							this.showMessage(1, 'Already Upgrading!');
						}
					})
				})
			},
			fw_upload_progress(evt) {
				this.bar = evt.percent;
				console.log("progress:" + evt.percent);
				if (evt.percent == 100) {
					let _this = this;
					// upload finished, show progress
					this.info.upgrading = true;
					this.ota_check_interval = setInterval(_this.ota_stage_state_check, 1000);
				}
			},
			fw_upload(evt) {
				let files = evt.srcElement.files;
				fetch(HOST_IP + "httpapi.asp?command=getMvRemoteUpdateStatus").then(res => {
					res.text().then(r => {
						console.log("ota state: ", r);
						if (r != '32' && r != '25') {
							let _this = this;
							console.log('upgrade with ' + files[0].name);
							// notify update type
							fetch(HOST_IP + "httpapi.asp?command=NotifyUpgradeType:" + files[0].name).then(res => {
								res.text().then(r => {
									if (r == 'OK') {
										console.log('start uploading')
										const url = HOST_IP + "cgi-bin/upload.cgi";
										let formData = new FormData();
										let _this = this;
										const xhr = new XMLHttpRequest();
										formData.append("file", files[0]);
										xhr.upload.addEventListener('progress', (e) => {
											e.percent = e.loaded / e.total * 100;
											_this.fw_upload_progress(e);
										});
										xhr.open('POST', url);
										xhr.send(formData);
									}
								})
							})
						}
						else {
							this.showMessage(1, 'Already Upgrading!');
						}
					})
				})
			},
			get_login_state(num) {
				fetch(HOST_IP + "httpapi.asp?command=getLoginResult").then(res => {
					res.json().then(li => {
						if (li.login == '1') {
							// console.log('通过');
							$('.login_mask').css('display', 'none');
							this.Login_Status = true;
							sessionStorage.setItem('login_status', 1);
							if (num != 3) {
								this.showMessage(0, 'Login successful!');
							}
						} else {
							this.Login_Status = false;
							if (num == 1) {
								$('.loginOut_mask').css('display', 'none');
								this.showMessage(0, 'Logout!');
								sessionStorage.removeItem('login_status');
								$('.theBodyOfPlay').css('pointer-events', 'none');
							} else {
								this.showMessage(1, 'The password is wrong!');
							}
							this.login_pas = '';
						}
					}).catch(err => {
						if (!debugging) {
							this.showMessage(1, 'Please check the network connection!');
						} else {
							// $('.login_mask').css('display', 'none');
						}
					})
				}).catch(err => {
					this.login_pas = '';
				})
			},
			async user_login() {
				if (this.login_pas) {
					await fetch(HOST_IP + "httpapi.asp?command=checkUserLogin:admin:" + this.login_pas).then(res => {
						this.get_login_state();
					}).catch(err => {
						this.showMessage(1, 'Error, please check network connection!');
						this.$refs.topChild.show_loginMask();
					})
				} else {
					this.showMessage(1, 'Please input your password!');
				}
			},
			login_outSure() {
				fetch(HOST_IP + "httpapi.asp?command=checkUserLogin:admin:").then(res => {
					this.get_login_state(1);
				})
			},
			async get_device_info() {
				await fetch(HOST_IP + "httpapi.asp?command=getStatusEx").then(res => {
					res.json().then(li => {
						console.log('device_info', li);
						this.device_info = li;
						this.info.ProjectID = li.project;

						this.info.MACaddress = li.MAC;
						this.info.FirmwareVersion = li.firmware + '.' + li.mcu_ver;
						this.info.DeviceName = li.DeviceName;
						if (li.DeviceName) {
							$('title').html(li.DeviceName);
						}
						this.info.ConnectAP = li.essid;
						this.info.wifiIP = li.apcli0;
						this.form.wifiIPInput = this.info.wifiIP;
						var plm_support = parseInt(li.plm_support, 16).toString(2);
						plm_support = plm_support.split('').reverse().join('');
						this.Input_sourceLsit.push('WiFi');
						for (var i = 0; i < plm_support.length; i++) {
							if (plm_support.substring(i, i + 1) == '1') {
								if (i == 1) {
									setTimeout(() => {
										this.Input_sourceLsit.push('Line in');
									}, 10)
								}
								if (i == 2) this.Input_sourceLsit.push('Bluetooth');
								if (i == 3) {
									setTimeout(() => {
										this.Input_sourceLsit.push('USB');
									}, 30)
								}
								if (i == 4) {
									setTimeout(() => {
										this.Input_sourceLsit.push('Optical');
									}, 40)
								}
								if (i == 6) this.Input_sourceLsit.push('Coaxial');
								if (i == 8) {
									setTimeout(() => {
										this.Input_sourceLsit.push('Line in2');
									}, 20);
								}
								if (i == 15) {
									setTimeout(() => {
										this.Input_sourceLsit.push('USBDAC');
									}, 50);
								}
							}
						}
						if (li.eth_dhcp == '1')
							this.info.ethIP = li.eth2;
						else
							this.info.ethIP = li.eth_static_ip;
						this.info.newVersion = "0.0";
						this.form.ethIPInput = this.info.ethIP;
						ver = li.NewVer;
						if (ver == "0") {
							ver = li.firmware;
							ver = ver + ".";
						}
						if (li.mcu_ver_new != "0") {
							ver = ver + li.mcu_ver_new;
						} else {
							ver = ver + li.mcu_ver;
						}
						if (ver != this.info.FirmwareVersion) this.info.newVersion = ver;  // 新版本								

					})
				}).catch(err => {
					console.log(err);
				})
				// get upgrading info
				this.ota_stage_state_check();
			},
			async get_network_info() {
				await fetch(HOST_IP + "httpapi.asp?command=getStaticIP").then(res => {
					res.json().then(li => {
						// console.log('getStaticIP', li)
						if (li.wifi == 0) {
							this.info.wifiEnabled = 1;
							this.info.wifiStatic = 1;
							this.info.wifiIP = li.wifi_static_ip;
							this.info.staticMask = li.wifi_static_mask;
							this.info.staticGateway = li.wifi_static_gateway;
							this.info.staticDNS1 = li.wifi_static_dns1;
							this.info.staticDNS2 = li.wifi_static_dns2;
							this.form.wifiIPInput = li.wifi_static_ip;
							this.form.wifiMaskInput = li.wifi_static_mask;
							this.form.wifiGatewayInput = li.wifi_static_gateway;
							this.form.wifiDNS1Input = li.wifi_static_dns1;
							this.form.wifiDNS2Input = li.wifi_static_dns2;
						} else if (li.wifi == 1) {
							this.info.wifiEnabled = 1;
							this.info.wifiStatic = 0;
						} else {
							this.info.wifiEnabled = 0;
							this.info.wifiStatic = 0;
						} if (li.eth == 0) {
							this.info.ethEnabled = 1;
							this.info.ethStatic = 1;
							this.info.ethIP = li.eth_static_ip;
							this.info.staticMask = li.eth_static_mask;
							this.info.staticGateway = li.eth_static_gateway;
							this.info.staticDNS1 = li.eth_static_dns1;
							this.info.staticDNS2 = li.eth_static_dns2;
							this.form.ethMaskInput = li.eth_static_mask;
							this.form.ethGatewayInput = li.eth_static_gateway;
							this.form.ethDNS1Input = li.eth_static_dns1;
							this.form.ethDNS2Input = li.eth_static_dns2;
						} else if (li.eth == 1) {
							this.info.ethEnabled = 1;
							this.info.ethStatic = 0;
						} else {
							this.info.ethEnabled = 0;
							this.info.ethStatic = 0;
						} if (this.info.wifiEnabled == 0 && this.info.ethEnabled == 0) {
							// might be connecting directly, enable WIFI
							this.info.wifiEnabled = 1;
							this.info.wifiStatic = 0;
						}
					})
				}).catch(err => {
					this.info.wifiEnabled = 1;
					this.info.ethEnabled = 1;
				})
			},
			wifi_refresh_aplist() {
				setTimeout(() => {
					$('.tr-row').eq(1).addClass('trLoading');
				}, 0)
				this.wifiList = [{ ssid: '', bssid: 'Loading...', auth: 'OPEN' }];
				fetch(HOST_IP + 'httpapi.asp?command=wlanGetApListEx').then(res => {
					$('.tr-row').eq(1).removeClass('trLoading');
					res.json().then(r => {
						this.wifiList = r.aplist;
					})
				}).catch(err => {
					console.log('Err>>', err);
					this.wifiList = [{ ssid: '', bssid: "null link", auth: 'OPEN', },];
				})
			},
			wifi_show_aplist() {
				this.wifi_refresh_aplist();
				document.querySelector('.SwitchForm').classList.add('FormPagehide');
				this.showTable = true;
			},
			wifi_hide_aplist() {
				document.querySelector('.SwitchForm').classList.remove('FormPagehide');
				this.showTable = false;
			},
			wifi_show_connect(e, r) {
				//this.wifiList[0].bssid != 'Loading...'
				if ($('.tr-row').eq(1).hasClass('trLoading') != true) {
					console.log('Row>>', e, r)
					this.indexs = e;
					this.wifi = {
						ssid: r.ssid,
						channel: r.channel,
						auth: r.auth,
						encry: r.encry,
						// pwd: password,
						extch: r.extch,
					}
					this.wifiPassword = '';
					this.showLoginWifi = true;
					setTimeout(() => {
						if (r.auth != 'OPEN') {
							$('.loginput').removeClass('disableWifinput');
						} else {
							$('.loginput').addClass('disableWifinput');

						}
					}, 0)
				}
			},
			wifi_hide_connect() {
				this.showLoginWifi = false;
			},
			stringToHex(str) {
				var val = "";
				for (var i = 0; i < str.length; i++) {
					if (val == "") {
						val = str.charCodeAt(i).toString(16);
					} else {
						val += str.charCodeAt(i).toString(16);
					}
				}
				return val;
			},
			wifi_connect() {
				// this.loading = true;
				let pwd = this.stringToHex(this.wifiPassword);
				let wifi = this.wifi;
				let wAPI = "httpapi.asp?command=wlanConnectApEx:";
				let postData = 'ssid=' + wifi.ssid + ':ch=' + wifi.channel + ':auth=' + wifi.auth + ':encry=' + wifi.encry + ':pwd=' + pwd + ':chext=' + wifi.extch;
				let url = HOST_IP + wAPI + postData;
				console.log(url);
				fetch(url).then(response => {
					let result = response.text();
					result.then(res => {
						// this.loading = false;
						this.showLoginWifi = false;
					})
				})
			},
			play_url(item) {
				console.log(item);
				if (item.url) {
					var pos = item.url.lastIndexOf(".");
					console.log(pos);
					var t = item.url.substring(pos, item.url.length).toLowerCase();
					console.log(t);
					let play_api = 'httpapi.asp?command=setPlayerCmd:play:' + item.url;
					if (t == ".m3u")
						play_api = 'httpapi.asp?command=setPlayerCmd:m3u:play:' + item.url;
					fetch(HOST_IP + play_api).then(res => {
						res.text().then(r => {
							console.log(r);
							// this.loading = false;
						})
					}).catch(err => {
						console.log(err);
					})
				} else {
					return
				}
			},
			set_password() {
				// verify password
				if (this.form.pas_new != this.form.pas_cfm) {
					$('.message_password').css('bottom', '0px');
					setTimeout(() => {
						$('.message_password').css('bottom', '-60px');
					}, 3000)
					this.form.pas_new = "";
					this.form.pas_cfm = "";
				} else if (this.form.pas_new == "") {
					this.showMessage(1, "The password can't be blank");
					this.form.pas_new = "";
					this.form.pas_cfm = "";
				} else {
					// set password
					var reg = new RegExp(/^[0-9a-zA-Z_-]{1,8}$/);
					if (reg.test(this.form.pas_new)) {
						let pwd = this.stringToHex(this.form.pas_cfm);
						let url = HOST_IP + "httpapi.asp?command=saveUserPwd:" + pwd;
						fetch(url).then(response => {
							let result = response.text();
							result.then(res => {
								console.log(res);
								// this.loading = false;
								this.form.pas_new = "";
								this.form.pas_cfm = "";
							})
						})
					} else {
						this.showMessage(1, 'The password only accept alphabit, numbers, "-" and "_", up to 8 characters.');
					}
				}
			},
			set_device_name() {
				console.log(this.form.DeviceName);
				var dname = this.form.DeviceName;
				var spC = /[+\-*\/%`:;~!@#$^&*()_={}[\];':".,<>\/?\\|]/;
				if(spC.test(dname)){
					console.log('包含字符');
					this.showMessage(1, 'Illegal character input!');
				}else{
					let devapi = "httpapi.asp?command=setDeviceName:" + this.form.DeviceName;
					fetch(HOST_IP + devapi).then(res => {
						console.log(res);
						res.text().then(li => {
							console.log(li);
							this.info.DeviceName = this.form.DeviceName;
							this.form.DeviceName = null;
						})
					}).catch(err => {
						console.log(err);
						this.info.DeviceName = this.form.DeviceName;
						this.form.DeviceName = null;
					})
				}
			},
			set_wifi_static_ip() {
				var reg = new RegExp(/((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/);
				if (reg.test(this.form.wifiIPInput) && reg.test(this.form.wifiMaskInput) && reg.test(this.form.wifiGatewayInput) && reg.test(this.form.wifiDNS1Input)) {
					var param = '{"type":"wifi","ip":"' + this.form.wifiIPInput + '","mask":"' + this.form.wifiMaskInput + '","gateway":"' + this.form.wifiGatewayInput + '","dns":[{"service":"' + this.form.wifiDNS1Input
					if (reg.test(this.form.wifiDNS2Input))
						param = param + '"},{"service":"' + this.form.wifiDNS2Input + '"}]}'
					else
						param = param + '"}]}'
					console.log(HOST_IP + "httpapi.asp?command=setStaticIP:" + param);
					fetch(HOST_IP + "httpapi.asp?command=setStaticIP:" + param).then(res => {
						res.text().then(r => {
							if (r == 'OK') {
								this.showMessage(0, 'OK');
							}
						})
					}).catch(err => {
						console.log(err);
					})
				} else {
					console.log(2);
					this.showMessage(1, 'Please enter the correct format');
				}
			},
			set_eth_static_ip() {
				var reg = new RegExp(/((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/)
				if (reg.test(this.form.ethIPInput) &&
					reg.test(this.form.ethMaskInput) &&
					reg.test(this.form.ethGatewayInput) &&
					reg.test(this.form.ethDNS1Input)) {
					var param = '{"type":"eth","ip":"' + this.form.ethIPInput + '","mask":"' + this.form.ethMaskInput + '","gateway":"' + this.form.ethGatewayInput + '","dns":[{"service":"' + this.form.ethDNS1Input
					if (reg.test(this.form.ethDNS2Input))
						param = param + '"},{"service":"' + this.form.ethDNS2Input + '"}]}'
					else
						param = param + '"}]}'
					fetch(HOST_IP + "httpapi.asp?command=setStaticIP:" + param).then(res => {
						res.text().then(r => {
							if (r == 'OK') {
								this.showMessage(0, 'OK');
								this.form.ethMaskInput = '';
								this.form.ethGatewayInput = '';
								this.form.ethDNS1Input = '';
								this.form.ethDNS2Input = '';
							}
						})
					}).catch(err => {
						console.log(err);
					})
				} else {
					this.showMessage(1, 'Please enter the correct format');
				}
			},
			set_wifi_ip_mode(m) {
				if (m.target.value == 0) {
					// DHCP	
					fetch(HOST_IP + "httpapi.asp?command=setDhcp:wifi").then(res => {
						res.text().then(r => {
							console.log(r)
						});
					})
				}
			},
			set_eth_ip_mode(m) {
				if (m.target.value == 0) {
					// DHCP
					fetch(HOST_IP + "httpapi.asp?command=setDhcp:eth").then(res => {
						res.text().then(r => {
							console.log(r);
						})
					})
				}
			},
			factory_reset(value) {
				if (value == true) {
					fetch(HOST_IP + "httpapi.asp?command=restoreToDefault").then(res => {
						res.text().then(json => {
							setTimeout(() => {
								$('.reset_mask').css('display', 'none');
							}, 0)
						})
					}).catch(err => {
						$('.reset_mask').css('display', 'flex');
					})
				} else {
					setTimeout(() => {
						$('.reset_mask').css('display', 'none');
					}, 0)
				}
			},
			show_reset_page() {
				$('.reset_mask').css('display', 'flex');
			}
		},
	})
</script>

</html>
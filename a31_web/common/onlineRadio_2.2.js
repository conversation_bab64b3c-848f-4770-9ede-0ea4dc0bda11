
const onlineRadios = {
    template: `
    <div>
        <div class="switch_radioBody">
            <div class="Public_radio" :class="radioMode == 0?'showRadios':''"  @click="PublicClick">Public</div>
            <div class="Personal_radio" :class="radioMode == 1?'showRadios':''"  @click="PersonalClick">My Station</div>
            <span class="p_title_background">
                <span class="huakuai" :class="radioMode == 1?'huakuaiRight':''"></span>
            </span>
        </div>
        <div class="Radio_body">
            <div class="nav_body">
                <div class="genreContent" v-if="radioMode == 0">
                    <div class="filterName" :class="'is' + item.name" v-for="(item,index) of filterS"  @mouseover="filterS[index].filter = 1" @mouseout="filterS[index].filter = 0">
                        <span >{{item.NowName}}</span>
                        <div :class="filterS[index].filter == 1?'showFilter':''" style="scrollbar-width: none;">
                            <div v-if="item.content.length == 0" class="filterEmpty">Empty</div>
                            <div v-else >
                                <ul class="filtersUl" >
                                    <ol  @click="tableGenre(item,item2,index)" v-for="(item2,index2) of item.content" :class="item2.name == item.NowName?'NowSelcetGener':''">
                                        <svg :class="item2.name != item.NowName?'disabledcont':''"  class="icon generIcon" @click="iconClick(item.name)" aria-hidden="true">
                                            <use xlink:href="#icon-zuoshangjiao-tuijian">
                                            </use>
                                        </svg>
                                        <svg :class="item2.name != item.NowName?'disabledcont':''"  class="icon generIcongg" @click="iconClick(item.name)" aria-hidden="true">
                                            <use xlink:href="#icon-icon_gougou"></use>
                                        </svg>
                                        {{item2.name}}
                                    </ol>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="loginBtn_body" v-else>
                    <div class="icon_content" v-if="loginStatus"  :class="item.iconStatus == false?'icon_disable':'icon_open'" v-for="(item,index) of loginBtnlist">
                        <svg class="icon" @click="iconClick(item.name)" style="width: calc(var(--icon_width18) - 0.3px);" aria-hidden="true">
                            <use :xlink:href="'#' + item.icon_url"></use>
                        </svg>
                    </div>
                </div>
            </div>
            <div v-if="radioMode == 0" class="Public_Radio_body">
                <div v-if="allRadios != ''" class="Radio_Content">
                    <div class="Public_type_body" v-for="(item,index) in allRadios" @click="PlayUserSong(item)" >
                        <img class="Public_Radio_image" :src="item.image_url?item.image_url:'static/defaultbackground.png'" onerror="src='static/defaultbackground.png'">
                        <div class="Public_text">
                            <div>{{item.title}}</div>
                        </div>
                        <i class="moreInfo" @click.stop="ShowRadio_Info(item)">...</i>
                    </div>
                </div>
                <div v-else class="emptyPublic">
                    <div class="emptyRadio">
                        <svg class="icon" aria-hidden="true">
                            <use xlink:href="#icon-zanwubofangjilu"></use>
                        </svg>
                        <span>Share your first radio song with your friends!</span>
                    </div>
                </div>
            </div>
            <div v-else class="Private_Radio_body">
                <div class="Private_Page" v-if="loginStatus">
                    <div class="Radio_Content">
                        <div style="height:100%;">
                            <div class="emptyRadio" v-if="userRadios == ''">
                                <svg class="icon" aria-hidden="true">
                                    <use xlink:href="#icon-zanwubofangjilu"></use>
                                </svg>
                                <span>You haven't added your private radio station yet!</span>
                            </div>
                            <div class="userRadiobox" v-else>
                                <div class="LikeStation" v-if="!userActivated">
                                    <p class="Public_type_body Activated" @click.stop="resendActivated">Your account is not activated yet. You could click here to resend the activation mail if it's expired.</p>
                                </div>
                                <div class="LikeStation" v-if="likesStation !=''">
                                    <p class="sitionTitle">Like Station</p>
                                    <div class="Public_type_body"  v-for="(item,index) in likesStation" @click.stop="PlayUserSong(item)">
                                        <img class="Public_Radio_image" :src="item.image_url == ''?'static/defaultbackground.png':item.image_url" onerror="src='static/defaultbackground.png'">
                                        <div class="Public_text">
                                            <div>{{item.title}}</div>
                                        </div>
                                        <i class="moreInfo" @click.stop="ShowRadio_Info(item)">...</i>
                                    </div>
                                </div>
                                <div class="PublicStation" v-if="publicStation !=''">
                                    <p class="sitionTitle">Public Station</p>
                                    <div class="Public_type_body"  v-for="(item,index) in publicStation" @click.stop="PlayUserSong(item)">
                                        <img class="Public_Radio_image" :src="item.image_url == ''?'static/defaultbackground.png':item.image_url" onerror="src='static/defaultbackground.png'">
                                        <div class="Public_text">
                                            <div>{{item.title}}</div>
                                        </div>
                                        <i class="moreInfo" @click.stop="ShowRadio_Info(item)">...</i>
                                    </div>
                                </div>
                                <div class="MyStation" v-if="mineStation !=''">
                                    <p class="sitionTitle">Station</p>
                                    <div class="Public_type_body"  v-for="(item,index) in mineStation" @click.stop="PlayUserSong(item)">
                                        <img class="Public_Radio_image" :src="item.image_url == ''?'static/defaultbackground.png':item.image_url"  @onerror="src='static/defaultbackground.png'">
                                        <div class="Public_text">
                                            <div>{{item.title}}</div>
                                        </div>
                                        <i class="moreInfo" @click.stop="ShowRadio_Info(item)">...</i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="add_content" v-if="addRadio">
                            <div class="out_addContent">
                                <div style="display:flex;">
                                    <svg class="icon cancelinfomation" aria-hidden="true" @click="add_out">
                                        <use xlink:href="#icon-fanhui"></use>
                                    </svg>
                                    <p class="radiosinfoTile">Add Station</p>
                                </div>
                            </div>
                            <div class="addText_content">
                                <p><span>Name*</span><input class="addtext addRadioName" type="text" placeholder="" v-model="addRadioData.RadioName"></p>
                                <p><span>Play Url*</span><input class="addtext addPlayUrl" type="text" placeholder="" v-model="addRadioData.PlayUrl"></p>
                                <p><span>Image Url</span><input class="addtext addImageUrl" type="text" placeholder="" v-model="addRadioData.ImageUrl"></p>
                                <p>
                                    <span>Genre</span>
                                    <div class="searchcontent">
                                        <div class="addtext addGenreId" :class="Sgenre1?'showselect':''" @click="showThisfilter(1)">{{addRadioData.GenreId}}</div>
                                        <span class="selectGenre Sgenre1" v-if="Sgenre1">
                                            <input class="searchGenreput" type="text" placeholder="Search" v-model="searchGtet.Genretext" @input="findBrother(1)">
                                            <div class="scorllSelectGenre">
                                                <div v-for="item of screening.genres" @click="SelectGenreid(item,1)" :class="GenreSearch.genres == true?item.search == true?'':'disableGenres':''">{{item.name}}</div>
                                            </div>
                                        </span>
                                    </div>
                                </p>
                                <p>
                                    <span>Language</span>
                                    <div class="searchcontent">
                                        <div class="addtext addGenreId" :class="Sgenre2?'showselect':''" @click="showThisfilter(2)">{{addRadioData.LanguageId}}</div>
                                        <span class="selectGenre Sgenre2" v-if="Sgenre2">
                                            <input class="searchGenreput addLanguage" type="text" placeholder="Search" v-model="searchGtet.Languagetext"  @input="findBrother(2)">
                                            <div class="scorllSelectGenre">
                                                <div v-for="item of screening.language" @click="SelectGenreid(item,2)" :class="GenreSearch.language == true?item.search == true?'':'disableGenres':''">{{item.name}}</div>
                                            </div>
                                        </span>
                                    </div>
                                </p>
                                <p>
                                    <span>Country</span>
                                    <div class="searchcontent">
                                        <div class="addtext addGenreId" :class="Sgenre3?'showselect':''" @click="showThisfilter(3)">{{addRadioData.CountryId}}</div>
                                        <span class="selectGenre Sgenre3" v-if="Sgenre3">
                                            <input class="searchGenreput addCountry" type="text" placeholder="Search" v-model="searchGtet.Countrytext" @input="findBrother(3)">
                                            <div class="scorllSelectGenre">
                                                <div v-for="item of screening.country" @click="SelectGenreid(item,3)" :class="GenreSearch.country == true?item.search == true?'':'disableGenres':''">{{item.name}}</div>
                                            </div>
                                        </span>
                                    </div>
                                </p>
                                <p><span>Description</span><textarea class="addtext RadioDesc" name="RadioDesc" v-model="addRadioData.RadioDesc"></textarea></p>
                                <p><button class="addRadiobtn" @click="addSubmit">Add</button></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="login_Page" v-else>
                    <div class="Register_container" v-if="loginMode == 0">
                    <div class="Private_Radio_login_body">
                        <input class="Register_username" @input="check" type="text" placeholder="Username" v-model="user.userName">
                        <input class="Register_email" @input="check" type="text" placeholder="Email" v-model="user.userEmail">
                        <input class="Register_psw" @input="check" type="password" placeholder="Password" v-model="user.userPsw">
                        <input class="Register_qpsw" @input="check" type="password" placeholder="Confirm Password" v-model="user.qruserPsw">
                    </div>
                    <div class="clause">
                        By registering, you agree to the <a href="https://radio.arylic.com/terms" target="_blank">Conditions of Use</a> and <a target="_blank" href="https://radio.arylic.com/privacy">Privacy Notice</a>.
                    </div>
                    <div class="Register_btn_body allbtn_body">
                        <button class="Close_btn"  @click="switching(1)">Back</button>
                        <button class="Create_btn" @click="user_Create" :class="checkbtn == false?'disableSubmit':''">Create</button>
                    </div>
                </div>
                <div class="login_container" v-if="loginMode == 1">
                    <div class="Private_Radio_login_body">
                        <input @input="check" type="text" class="login_Email"  placeholder="Email" v-model="user.userEmail">
                        <input @input="check" type="password" class="login_PassWord" placeholder="Password" v-model="user.userPsw">
                    </div>
                    <div class="Switch_bar">
                        <span @click="switching(0)">Register</span>
                        <span @click="switching(2)">Forgot?</span>
                    </div>
                    <div class="login_btn_body allbtn_body">
                        <button class="signin_btn" @click="user_login" :class="checkbtn == false?'disableSubmit':''">Sign In</button>
                    </div>
                </div>
                <div class="reset_container" v-if="loginMode == 2">
                    <div class="Private_Radio_login_body">
                        <div class="Reset_Prompt">Change Password</div>
                        <input @input="check" type="text" class="online_Radio_Email" placeholder="Enter your email address" v-model="user.userEmail">
                    </div>
                    <div class="reset_btn_body allbtn_body">
                        <button class="Close_btn" @click="switching(1)">Back</button>
                        <button class="Create_btn"@click="user_PswReset" :class="checkbtn == false?'disableSubmit':''">Send</button>
                    </div>
                </div>
                </div>
            </div>
        </div>
        <div class="RadioInfomation" v-if="radioInfomation">
            <div style="display:flex;">
                <svg class="icon cancelinfomation" aria-hidden="true" @click="closeRadioInfo">
                    <use xlink:href="#icon-fanhui"></use>
                </svg>
                <p class="radiosinfoTile">Station Info</p>
                
            </div>
            <div class="scorllInfo" >
                <div class="infoClass1">
                    <img class="infoimage" :src="searchInfo.image_url?searchInfo.image_url:'static/defaultbackground.png'" :data-src="searchInfo.image_url" onerror="src='static/defaultbackground.png'">
                    <div class="theHeat">
                        <p>
                            <span>Likes:</span>
                            <span style="margin-left:4px;">{{searchInfo.fav_count}}</span>
                        </p>
                        <p>
                            <span>Create:</span>
                            <span style="margin-left:4px;">{{searchInfo.user_name}}</span>
                        </p>
                        <p style="font-size:12px;">
                            {{searchInfo.created}}
                        </p>
                        <div class="likeBTN"  @click="LikeRaios">
                            <svg class="icon" :class="searchInfo.like == '1'?'onlike':''" aria-hidden="true">
                                <use :xlink:href="searchInfo.like == '1'?'#icon-like':'#icon-sq-like'"></use>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="infoClass2">
                    <div class="infoblock selectShare">
                        <span>Shared</span>
                        <div class="ChooseShare" @click="switchShare">
                            <div>Public</div>
                            <div class="SharedBass" :class="shared==true?'sharedTrue':''">
                                <span class="SharedBass_btn" :class="shared==true?'sharedTruebas':''"></span>
                            </div>
                        </div>
                    </div>
                    <p class="infoblock">
                        <span>Name</span>
                        <input v-model="searchInfo.title"/>
                    </p>
                    <p class="infoblock">
                        <span>Play URL</span>
                        <input v-model="searchInfo.play_url"/>
                    </p>
                    <p class="infoblock">
                        <span>Image URL</span>
                        <input v-model="searchInfo.image_url"/>
                    </p>
                    <div class="infoblock searchbody">
                        <span>Genre</span>
                        <div class="addtext addGenreId" :class="Sgenre1?'showselect':''" @click="showThisfilter(1)">{{searchInfo.genre}}</div>
                        <span class="selectGenre Sgenre1" v-if="Sgenre1">
                            <input class="searchGenreput" type="text" placeholder="Search" v-model="searchGtet.Genretext" @input="findBrother(1)">
                            <div class="scorllSelectGenre">
                                <div v-for="item of screening.genres" @click="SelectGenreid2(item,1)" :class="GenreSearch.genres == true?item.search == true?'':'disableGenres':''">{{item.name}}</div>
                            </div>
                        </span>
                    </div>
                    <div class="infoblock searchbody">
                        <span>Language</span>
                        <div class="addtext addGenreId" :class="Sgenre2?'showselect':''" @click="showThisfilter(2)">{{searchInfo.language}}</div>
                        <span class="selectGenre Sgenre2" v-if="Sgenre2">
                            <input class="searchGenreput addLanguage" type="text" placeholder="Search" v-model="searchGtet.Languagetext"  @input="findBrother(2)">
                            <div class="scorllSelectGenre">
                                <div v-for="item of screening.language" @click="SelectGenreid2(item,2)" :class="GenreSearch.language == true?item.search == true?'':'disableGenres':''">{{item.name}}</div>
                            </div>
                        </span>
                    </div>
                    <div class="infoblock searchbody">
                        <span>Country</span>
                        <div class="addtext addGenreId" :class="Sgenre3?'showselect':''" @click="showThisfilter(3)">{{searchInfo.country}}</div>
                        <span class="selectGenre Sgenre3" v-if="Sgenre3">
                            <input class="searchGenreput addCountry" type="text" placeholder="Search" v-model="searchGtet.Countrytext" @input="findBrother(3)">
                            <div class="scorllSelectGenre">
                                <div v-for="item of screening.country" @click="SelectGenreid2(item,3)" :class="GenreSearch.country == true?item.search == true?'':'disableGenres':''">{{item.name}}</div>
                            </div>
                        </span>
                    </div>
                    <p class="infoblock">
                        <span>Description</span>
                        <textarea class="RadioDesc" v-model="searchInfo.desc" @input="updateDesc"></textarea>
                    </p>
                    <p style="display:flex;justify-content: space-between;margin:8px 0 ;">
                        <button class="deleteBTN" v-if="mystation" @click="qrDeleteMask = true">Delete</button>
                        <button class="ModifyBTN" v-if="mystation" @click="ModifyRadio(false)">Modify</button>
                    </p>
                </div>
            </div>
            <div class="InfoBtn">
                <button @click="closeRadioInfo">Cancel</button>
                
            </div>
            <div class="qrDeleteMask" v-if="qrDeleteMask">
                <div class="qrDelete">
                    <div class="qrTitle">Confirm delete?</div>
                    <div class="qrBtnbody">
                        <button @click="qrDeleteMask = false">No</button>
                        <button @click="deleteSong" class="qrdelete">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>`,
    props: {
        $fatherdata: {}
    },
    data() {
        return {
            // 电台模式
            radioMode: 0,
            user: {
                userName: '',
                // 用户邮箱
                userEmail: '',
                // 用户密码
                userPsw: '',
                // 用户确认密码
                qruserPsw: '',
            },
            // 注册
            loginMode: 1,
            loginStatus: false,
            userActivated: false,
            allRadios: [],
            loginTime: null,
            filterS: [{
                title: 'genres',
                name: 'genre',
                NowName:'genre',
                filter: 0,
                content:[],
            }, {
                title: 'languages',
                name: 'language',
                NowName:'language',
                filter: 0,
                content:[],
            }, {
                title: 'countries',
                name: 'country',
                NowName:'country',
                filter: 0,
                content:[],
            }],
            loginBtnlist: [
                {
                    icon_url: 'icon-tianjia',
                    iconStatus: true,
                    name: 'addRadio'
                },
                {
                    icon_url: 'icon-tuichudenglu',
                    iconStatus: true,
                    name: 'loginOut'
                }
            ],
            inputNumber: -1,
            checkbtn: false,
            addRadio: false,
            addRadioData: {
                RadioName: '',
                PlayUrl: '',
                ImageUrl: '',
                GenreId: 'Not Specified',
                LanguageId: 'Not Specified',
                CountryId: 'Not Specified',
                RadioDesc: ''
            },
            screening: {
                genres: {},
                language: {},
                country: {}
            },
            GenreSearch: {
                genres: false,
                language: false,
                country: false
            },
            DetectionTimer: null,
            addSubmitBtn: false,
            getuserRadio: null,
            userRadios: [],
            searchGtet: {
                Genretext: '',
                Languagetext: '',
                Countrytext: ''
            },
            Sgenre1: false,
            Sgenre2: false,
            Sgenre3: false,
            radioInfomation: false,
            searchInfo: {},
            isSrc:null,
            mystation:false,
            qrDeleteMask:false,
            searchGenerId:{
                genre:null,
                language:null,
                country:null
            },
            nowSongItem:{},
            updateDescBoolean:false,
            shared:false,
            likesStation:[],
            publicStation:[],
            mineStation:[]
        }
    },
    watch: {
        loginStatus: function (v) {
            console.log('登录状态',v);
            if (v) {
                this.getuserR();
                this.loginStatus_refresh(1);
            }
        },
    },
    mounted() {
        var showinput = false;
        window.addEventListener('keydown', event => {
            if (event.keyCode === 9) {
                if ($('.oneline_radio').attr('shownavtable') == 'true' && $(".Private_Radio_body").length == 1) {
                    setTimeout(() => {
                        showinput = true;
                    }, 300);
                    if (this.addRadio == false) {
                        let inputs = $('.Private_Radio_login_body').find('input').eq().prevObject;
                        if (document.activeElement.tagName == 'INPUT') {
                            var nowInputName = document.activeElement.className;
                            if (showinput == true) {
                                if ($('.' + nowInputName).next().length != 0) {
                                    $('.' + nowInputName).next().focus();
                                } else {
                                    inputs[0].focus();
                                }
                            }
                        } else {
                            if (this.loginStatus == false) {
                                if (showinput == true) {
                                    inputs[0].focus();
                                }
                            }
                        }
                    } else {
                        if ($('.show_addContent').length != 0) {
                            var addname = document.activeElement.className.slice(8);
                            if (addname == 'RadioDesc') {
                                $('.addtext')[0].focus();
                            } else {
                                if (document.activeElement.className.slice(0, 7) == 'addtext') {
                                    $('.' + addname).parent().next().find('.addtext').focus();
                                } else {
                                    $('.addtext')[0].focus();
                                }
                            }
                        } else {
                            seting = false;
                        }
                    }
                } else {
                    showinput = false;
                }
            }
            if (event.keyCode == 13) {
                if(document.activeElement.className == 'login_PassWord'){
                    this.user_login();
                }
            }
        })
    },
    mounted() {
        if (sessionStorage.getItem('token') != '' && sessionStorage.getItem('token') != null) {
            this.loginStatus = true;
            this.user_info(sessionStorage.getItem('token'));
        }
        this.getRadio();
    },
    methods: {
        // 图片错误时修改成空
        handleImageError(list,index){
            item = list[index];
            this.$set(list,index,{
                id: item.id,
                image_url: "static/defaultbackground.png",
                is_public: item.is_public,
                play_url: item.play_url,
                title: item.title,
            });
        },
        handleImage(item){
            item.image_url = "static/defaultbackground.png";
        },
        switchShare(){
            this.shared = !this.shared;
            this.ModifyRadio(true);
            this.$fatherdata.showMessage(0,'Modified to complete.');
        },
        tableGenre(fitem,mitem,findex){
            
            var GenerArr = Object.keys(this.searchGenerId);
            GenerArr.forEach(items=>{
                if(fitem.name == items){
                    if (fitem.name == 'genre') {
                        this.searchGenerId[items] = mitem.id;
                    } else {
                        this.searchGenerId[items] = mitem.code;
                    }
                }
            })
            let data = {
                count:9999,
                genre:this.searchGenerId.genre,
                language:this.searchGenerId.language,
                country:this.searchGenerId.country
            }
            this.ajax(data, 'station/query' , 'post' ).then(res => {
                this.allRadios = res.stations;
                if(mitem.name != 'All'){
                    this.filterS[findex].NowName = mitem.name;
                } else {
                    this.filterS[findex].NowName = fitem.name;
                }
            }).catch(err => {
                console.log(err);
            });
        },
        LikeRaios(){
            if(this.searchInfo.like == 1){ like = 0 } else { like = 1 };
            data = {
                sid:this.searchInfo.uid,
                like:like,
                token:sessionStorage.getItem('token')
            }
            this.ajax(data,'event/like','post').then( res =>{
                this.searchInfo.like = like;
                this.getuserR();
                if (like == 1) {
                    this.$fatherdata.showMessage(0, 'Already collected!');
                } else {
                    this.$fatherdata.showMessage(0, 'Cancel the collection!');
                }
                // this.closeRadioInfo();
            }).catch(err=>{
                console.log(err);
            })
        },
        getGenreSX(){
            var Arr1 = ['genres','languages','countries'];
            var Arr2 = ['genres','language','country'];
            this.ajax(null, 'station/summary', 'post').then(res=>{
                Arr1.forEach((key,index)=>{
                    this.filterS[index].content = [];
                    this.filterS[index].content.push({id:'',name:'All'})
                    if(this.filterS[index].title == Arr1[index]){
                        for(let i = 0 ; i < res[key].length ; i++){
                            setTimeout(()=>{
                                this.screening[Arr2[index]].forEach(item=>{
                                    if (item.id == res[key][i] || item.code == res[key][i]) {
                                        if (item.id == 0 || item.code == 0) {
                                            this.filterS[index].content.push(item);
                                        }
                                        if (item.id == 0 || item.code == 0) {
                                            return
                                        } else {
                                            this.filterS[index].content.push(item);
                                        }
                                    }
                                })
                            },200)
                        }
                    }
                })
            });
        },
        getRadio(){
            let data = { count:9999 }
            this.ajax(data, 'station/query' , 'post' ).then(res => {
                this.allRadios = res.stations;
            }).catch(err => {
                console.log(err);
            });
        },
        ModifyRadio(sharedBoolean){
            // 设置分享还是私人
            if (this.shared == true) {
                this.searchInfo.public = 1;
            } else {
                this.searchInfo.public = 0;
            }
            // 转换分类id
            var arr = ['genres','language','country'];
            var arr2 = ['','',''];
            for(let i = 0; i < arr.length; i++){
                this.screening[arr[i]].forEach(item=>{
                    if (arr[i] == 'genres') {
                        if (this.searchInfo.genre == item.name){
                            arr2[i] = item.id;
                        }
                    } else {
                        if (this.searchInfo[arr[i]] == item.name){
                            arr2[i] = item.code;
                        }
                    }
                })
            }
            // 校验播放链接
            if (!UrlZZ.test(this.searchInfo.play_url)){
                this.searchInfo.play_url = '';
                return this.$fatherdata.showMessage(1, 'Play links must start with Http/ HTTPS!');
            }
            // 校验图片
            var imager = this.searchInfo.image_url;
            if (imager != '') {
                ImageXXX.map(item => {
                    if (imager.indexOf(item) != -1) {
                        imager = imager.slice(0, imager.indexOf(item) + item.length);
                    }
                })
                Yzimage(imager).then(() => {
                    this.searchInfo.image_url = imager;
                }).catch(() => {
                    this.searchInfo.image_url = '';
                    return this.$fatherdata.showMessage(1, 'The picture is invalid!');
                })
            }
            // 校验长度
            if (this.searchInfo.title.length > 255) {
                return this.$fatherdata.showMessage(1, 'The Name is too long!');
            }
            if (this.searchInfo.play_url.length > 1024) {
                return this.$fatherdata.showMessage(1, 'The play url is too long!');
            }
            if (this.searchInfo.image_url.length > 1024) {
                return this.$fatherdata.showMessage(1, 'The Image Url is too long!');
            }
            let data = {
                token:sessionStorage.getItem('token'),
                sid:this.searchInfo.uid,
                title:this.searchInfo.title,
                desc:this.searchInfo.desc,
                play_url:this.searchInfo.play_url,
                image_url:imager,
                genre_id:arr2[0],
                language:arr2[1],
                country:arr2[2],
                public:this.searchInfo.public
            }
            if(data.token && data.title && data.play_url){
                this.ajax(data,'station/modify','post').then(res=>{
                    if(res.status == 0){
                        this.getuserR();
                        this.getRadio();
                        this.getGenreSX();
                        this.UpdateNowplay(data);
                        if(!sharedBoolean){
                            this.closeRadioInfo()
                            this.$fatherdata.showMessage(0,'Modified to complete.');
                        }
                    } else {
                        return this.$fatherdata.showMessage(1, res.msg);
                    }
                    // 刷新我的列表和公共列表
                }).catch(err=>{
                    console.log(err);
                })
                
            }
        },
        updateDesc(){
            this.updateDescBoolean = true;
        },
        UpdateNowplay(data){
            var nowplay = this.$fatherdata.nowplaying_State;
            if(localStorage.getItem('%g/nfho*s') == this.nowSongItem.id){
                switch (true) {
                    case nowplay.name != data.title:
                        nowplay.name = data.title;
                        break;
                    case this.updateDescBoolean == true:
                        nowplay.album = this.searchInfo.user_name + ',' + data.desc;
                        break;
                    case nowplay.url != data.image_url:
                        nowplay.url = data.image_url
                        break;
                    default:
                        break;
                }
                let updateplayss = nowplay.name != data.title ||
                this.updateDescBoolean == true ||
                nowplay.url != data.image_url || 
                nowplay.play_urls != data.play_url
                if(updateplayss){
                    var item = {
                        desc:data.desc,
                        id:data.sid,
                        image_url: data.image_url,
                        is_public:data.public,
                        title:data.title,
                        user_name:this.searchInfo.user_name,
                        play_url:data.play_url
                    }
                    this.PlayUserSong(item);
                }
            }
        },
        deleteSong(){
            this.updateDescBoolean = false;
            let data = {token:sessionStorage.getItem('token'),sid:this.searchInfo.uid}
            this.ajax(data,'station/delete','post').then(res=>{
                if(res.status == 0){
                    this.getuserR();
                    this.getRadio();
                    this.closeRadioInfo();
                    this.qrDeleteMask = false;
                    this.$fatherdata.showMessage(0,'Deleting completed.');
                }
            })
        },
        closeRadioInfo(){
            this.updateDescBoolean = false;
            $('.RadioInfomation').removeClass('show_RadioIfomation');
            setTimeout(()=>{
                $('.Radio_body').css('opacity', '1');
            },100)
            setTimeout(()=>{
                this.radioInfomation = false;
            },230)
            this.guanbisearch();
        },
        guanbisearch(){
            this.closethisFilter(1);
            this.closethisFilter(2);
            this.closethisFilter(3);
        },
        ShowRadio_Info(item) {
            this.updateDescBoolean = false;
            this.nowSongItem = item;
            this.radioInfomation = true;
            if (sessionStorage.getItem('token')) {
                data = {sid: item.id,token:sessionStorage.getItem('token')}
            } else {
                data = {sid: item.id}
            }
            userName = unCompileStr(sessionStorage.getItem(compileStr('isUsername')));
            userId = unCompileStr(sessionStorage.getItem(compileStr('isUserID')));
            var arr = ['genres','language','country'];
            this.ajax(data, 'station/info', 'post').then(res => {
                if( res.user_name != userName){
                    this.mystation = false;
                    var ary = ['.ChooseShare','.scorllInfo input','.theHeat','.RadioDesc','.searchbody','.selectShare'];
                    setTimeout(()=>{
                        ary.forEach(item=>{
                            $(item).attr('readonly','readonly');
                        })
                        $('.selectShare').css('display','none');
                    },10)
                } else {
                    this.mystation = true;
                }
                this.searchInfo = res;
                this.searchInfo.uid = item.id;
                if (res.public == 1) {
                    this.shared = true;
                } else {
                    this.shared = false;
                }
                for(let i = 0; i < 3; i++){
                    this.screening[arr[i]].forEach(item=>{
                        if(this.searchInfo.genre == item.id){
                            this.searchInfo.genre = item.name;
                        } else if(this.searchInfo.language == item.code){
                            this.searchInfo.language = item.name;
                        } else if(this.searchInfo.country == item.code){
                            this.searchInfo.country = item.name;
                        }
                    })
                }
                setTimeout(()=>{
                    $('.RadioInfomation').addClass('show_RadioIfomation');
                    $('.Radio_body').css('opacity', '0');
                },10)
            }).catch(err => {
                console.log(err);
            })
        },
        PlayUserSong(item){
            // console.log(123);
            localStorage.setItem('%g/nfho*s',item.id);
            data = {sid:item.id}
            if(sessionStorage.getItem('token')){
                data.token = sessionStorage.getItem('token');
            }
            this.ajax(data,'station/info','post').then(res=>{
                item.user_name = res.user_name;
                this.screening.genres.forEach((item)=>{
                    if ( item.id*1 == res.genre ){
                        Showgenre = item.name;
                    } 
                })
                console.log('#CMD:PLAYLIST_CREATE:'+res.title+'#$@'+res.id+'#$@'+res.title+'#$@'+Showgenre+'#$@'+res.user_name+'#$@'+res.image_url+'#$@'+res.play_url);
                console.log('#CMD:PLAYLIST_PLAY:'+res.title+'#$@1');
                this.$fatherdata.$refs.botm_play.socket.send('#CMD:PLAYLIST_CREATE:'+res.title+'#$@'+res.id+'#$@'+res.title+'#$@'+Showgenre+'#$@'+res.user_name+'#$@'+res.image_url+'#$@'+res.play_url);
                this.$fatherdata.$refs.botm_play.socket.send('#CMD:PLAYLIST_PLAY:'+res.title+'#$@1');
                this.eventPlay(item.id);
            }).catch(err=>{
                console.log(err);
            })
        },
        eventPlay(sid){
            let data = {
                token: sessionStorage.getItem('token'),
                sid
            }
            this.ajax(data,'event/play','post').then(res=>{
                console.log(res);
            })
        },
        getuserR() {
            this.likesStation = [];
            this.publicStation = [];
            this.mineStation = [];
            this.ajax({ token: sessionStorage.getItem('token') }, 'user/station', 'post').then( res=>{
                if(res.stations != null){
                    res.stations.forEach(item=>{
                        if(item.is_mine == 0){
                            this.likesStation.push(item);
                        } else if (item.is_public == 1) {
                            this.publicStation.push(item);
                        } else{
                            this.mineStation.push(item);
                        }
                    })
                    this.userRadios = res.stations;
                }
            })
        },
        addSubmit() {
            if (this.addSubmitBtn == true) {
                if (UrlZZ.test(this.addRadioData.PlayUrl)) {
                    var genreid, counteyid, languageid;
                    this.screening.genres.forEach(item => {
                        if (item.name == this.addRadioData.GenreId) {
                            genreid = item.id;
                        }
                    })
                    this.screening.language.forEach(item => {
                        if (item.name == this.addRadioData.LanguageId) {
                            languageid = item.code;
                        }
                    })
                    this.screening.country.forEach(item => {
                        if (item.name == this.addRadioData.CountryId) {
                            counteyid = item.code;
                        }
                    })
                    // 校验图片
                    var imager = this.addRadioData.ImageUrl;
                    if (imager != '') {
                        ImageXXX.map(item => {
                            if (imager.indexOf(item) != -1) {
                                imager = imager.slice(0, imager.indexOf(item) + item.length);
                            }
                        })
                        Yzimage(imager).then(() => {
                            this.addRadioData.ImageUrl = imager;
                        }).catch(() => {
                            this.addRadioData.ImageUrl = '';
                            return this.$fatherdata.showMessage(1, 'The picture is invalid!');
                        })
                    }
                    var data = {
                        token: sessionStorage.getItem('token'),
                        title: this.addRadioData.RadioName,
                        desc: this.addRadioData.RadioDesc,
                        play_url: this.addRadioData.PlayUrl,
                        image_url: imager,
                        country: counteyid,
                        genre_id: genreid,
                        language: languageid
                    }
                    if (data.title.length > 255) {
                        return this.$fatherdata.showMessage(1,'The Name is too long!');
                    }
                    if (data.play_url.length > 1024) {
                        return this.$fatherdata.showMessage(1,'The Play Url is too long!');
                    }
                    if (data.image_url.length > 1024) {
                        return this.$fatherdata.showMessage(1,'The Image Url is too long!');
                    }
                    this.ajax(data,'station/create','post').then( res => {
                        if (res.status == 0) {
                            this.getuserR();
                            this.add_out();
                        } else {
                            return this.$fatherdata.showMessage(1, 'You account is not activated!');
                        }
                    })
                } else {
                    this.$fatherdata.showMessage(1, 'Play links must start with Http/ HTTPS!');
                    this.addRadioData.PlayUrl = '';
                }
                this.guanbisearch();
            }
        },
        findBrother(or) {
            var arr = ['Genretext' , 'Languagetext' , 'Countrytext'];
            var arr2 = ['genres' , 'language' , 'country'];
            var index = or - 1;
            if (this.searchGtet[arr[index]].length != 0) {
                this.GenreSearch[arr2[index]] = true;
                this.screening[arr2[index]].forEach(item => {
                    if (item.id == 0 || item.code == '00') {
                        item.search = true;
                    } else {
                        if (item.name.toLowerCase().indexOf(this.searchGtet[arr[index]].toLowerCase()) != -1) {
                            item.search = true;
                        } else {
                            item.search = false;
                        }
                    }
                })
            } else {
                this.GenreSearch[arr2[index]] = false;
            }
        },
        SelectGenreid(item , or) {
            var arr = ['GenreId', 'LanguageId', 'CountryId'];
            var arr2 = ['Genretext', 'Languagetext', 'Countrytext'];
            var arr3 = ['genres', 'language', 'country'];
            var index = or - 1;
            this.addRadioData[arr[index]] = item.name;
            this.searchGtet[arr2[index]] = '';
            this.screening[arr3[index]].forEach(items => {
                items.search = true;
            })
            this.showThisfilter(or);
        },
        SelectGenreid2(item, or) {
            var arr = ['genre', 'language', 'country'];
            var arr2 = ['Genretext', 'Languagetext', 'Countrytext'];
            var arr3 = ['genres', 'language', 'country'];
            var index = or - 1;
            this.searchInfo[arr[index]] = item.name;
            this.searchGtet[arr2[index]] = '';
            this.screening[arr3[index]].forEach(items => {
                items.search = true;
            })
            this.showThisfilter(or);
        },
        showThisfilter(or) {
            if (this[('Sgenre') + or] == true) {
                $('.Sgenre' + or).removeClass('showaddGenre');
                $('.Sgenre' + or).addClass('closeSelectGenre');
                setTimeout(() => {
                    this[('Sgenre') + or] = false;
                }, 230)
            } else {
                this[('Sgenre') + or] = true;
                setTimeout(() => {
                    $('.Sgenre' + or).addClass('showaddGenre');
                }, 0)
                switch (true) {
                    case or == 1:
                        this.closethisFilter(2);
                        this.closethisFilter(3);
                        break;
                    case or == 2:
                        this.closethisFilter(1);
                        this.closethisFilter(3);
                        break;
                    case or == 3:
                        this.closethisFilter(1);
                        this.closethisFilter(2);
                        break;
                }
            }
        },
        closethisFilter(or) {
            if (this[('Sgenre') + or] == true) {
                $('.Sgenre' + or).removeClass('showaddGenre');
                $('.Sgenre' + or).addClass('closeSelectGenre');
                setTimeout(() => {
                    this[('Sgenre') + or] = false;
                }, 230)
            }
        },
        add_out() {
            if (this.addRadio == true) {
                $('.loginBtn_body').css('opacity', '1');
                $('.emptyRadio').css('opacity', '1');
                $('.userRadiobox').css('opacity', '1');
                $('.add_content').removeClass('show_addContent');
                setTimeout(() => {
                    this.addRadio = false;
                    clearInterval(this.DetectionTimer);
                }, 300)
            }
            this.guanbisearch();
        },
        iconClick(name) {
            if (name == 'loginOut') {
                this.$fatherdata.radio_maskOpen = true;
                $('.radio_mask').css('display', 'block');
                setTimeout(() => {
                    $('.loginOut_popUp').addClass('showloginOut');
                }, 0)
            }
            if (name == 'addRadio' && this.userActivated) {
                this.addRadio = true;
                var radioData = Object.keys(this.addRadioData);
                radioData.forEach(item => {
                    switch (true) {
                        case item == 'GenreId': break;
                        case item == 'LanguageId':   break;
                        case item == 'CountryId': break;
                        default:
                            this.addRadioData[item] = '';
                            break;
                    }
                })
                $('.loginBtn_body').css('opacity', '0');
                $('.emptyRadio').css('opacity', '0');
                $('.userRadiobox').css('opacity', '0');
                setTimeout(() => {
                    $('.add_content').addClass('show_addContent');
                }, 0);
                this.DetectionTimer = setInterval(() => {
                    var detection = this.addRadioData;
                    if (detection.RadioName != '' && detection.PlayUrl != '' && detection.GenreId != '' && detection.LanguageId != '' && detection.CountryId != '') {
                        $('.addRadiobtn').removeClass('disableSubmit');
                        this.addSubmitBtn = true;
                    }   else {
                        $('.addRadiobtn').addClass('disableSubmit');
                        this.addSubmitBtn = false;
                    }
                }, 100);
            }else if(name == 'addRadio' && !this.userActivated){
                this.$fatherdata.showMessage(1,'The account is not activated, please click Send email to activate the account!',10000);
            }
        },
        resendActivated(){
            
            let data = {
                token: sessionStorage.getItem('token')
            }
            this.ajax(data, 'user/register_resend', 'post').then(res => {
                console.log(res);
                if(res.status == 0){
                    this.$fatherdata.showMessage(0,'The email has been sent. Please click in the email to activate the account!',8000);
                }else{
                    this.$fatherdata.showMessage(1,res.msg);
                }
            })
        },
        cancelLoginOut() {
            this.$fatherdata.radio_maskOpen = false;
            $('.loginOut_popUp').removeClass('showloginOut');
            $('.radio_mask').css('display', 'none');
        },
        login_out() {
            let data = { token: sessionStorage.getItem('token') }
            this.ajax(data, 'user/logout', 'post').then(res => {
                if(res.status == 0) {
                    sessionStorage.removeItem('token');
                    sessionStorage.removeItem('TokenTimer');
                    sessionStorage.removeItem(compileStr('isUsername'));
                    sessionStorage.removeItem(compileStr('isUserID'));
                    this.switching(1);
                    this.loginStatus_refresh(0);
                    this.$fatherdata.showMessage(0, 'Log out');
                    this.cancelLoginOut();
                    this.loginStatus = false;
                }
            })
        },
        check() {
            switch (true) {
                case this.loginMode == 0:
                    if (this.user.userEmail != '' && this.user.userPsw != '' && this.user.qruserPsw != '') {
                        this.checkbtn = true;
                    } else {
                        this.checkbtn = false;
                    }
                    break;
                case this.loginMode == 1:
                    if (this.user.userEmail != '' && this.user.userPsw != '') {
                        this.checkbtn = true;
                    } else {
                        this.checkbtn = false;
                    }
                    break;
                case this.loginMode == 2:
                    if (this.user.userEmail != '') {
                        this.checkbtn = true;
                    } else {
                        this.checkbtn = false;
                    }
                    break;
                default:
                    this.checkbtn = false;
                    break;
            }
        },
        PersonalClick() {
            this.radioMode = 1;
            this.switching(1);
            this.closeRadioInfo();
        },
        PublicClick() {
            this.radioMode = 0;
            this.switching(4);
            this.add_out();
            this.closeRadioInfo();
        },
        async getGenreid() {
            let arr1 = ['genre', 'language', 'country'];
            let arr2 = ['genres','language','country'];
            arr1.forEach((key,index)=>{
                this.ajax(null,key,'get').then(res=>{
                    if (res.status == 0){
                        this.screening[arr2[index]] = (res[arr2[index]]);
                        this.screening[arr2[index]].forEach((item2)=>{
                            item2.search = false;
                            if (key == 'country') {
                                if (item2.name.indexOf(',') != -1) {
                                    item2.name = item2.name.slice(0, item2.name.indexOf(','));
                                }
                            }
                        })
                    } 
                })
            })
            setTimeout(() => {
                this.getGenreSX();
            }, 500)
        },
        user_login() {
            let data = {
                mail: this.user.userEmail,
                password: hex_md5(this.user.userPsw)
            }
            var ok = data.mail != '' && data.password != '';
            var tips_F = this.$fatherdata.showMessage;
            if (ok) {
                var emailtest = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
                if (emailtest.test(data.mail)) {
                    if (data.password.length >= 6) {
                        this.ajax(data, 'user/login', 'post').then(res => {
                            console.log(res);
                            if (res.status == 0) {
                                this.loginStatus = true;
                                tips_F(0, 'Login successful');
                                sessionStorage.setItem('token', res.token);
                                sessionStorage.setItem('TokenTimer', new Date().getMinutes());
                                sessionStorage.setItem(compileStr('isUsername'),compileStr(res.name));
                                sessionStorage.setItem(compileStr('isUserID'),res.id);
                                this.user_info(res.token);
                                this.loginStatus_refresh(1);

                            } else {
                                tips_F(1, res.msg);
                            }
                        })
                    } else {
                        tips_F(1, 'The password must be at least 6 digits!');
                    }
                } else {
                    tips_F(1, 'The mailbox is invalid!');
                }
            }
        },
        user_info(token){
            let data = {
                token,
            }
            this.ajax(data, 'user/info', 'post').then(res => {
                if(res.activated == 1){
                    this.userActivated = true;
                }else{
                    this.userActivated = false;
                }
                console.log(this.userActivated);
            })
        },
        loginStatus_refresh(state) {
            if (sessionStorage.getItem('token')) {
                if (state == 1) {
                    this.loginTime = setInterval(() => {
                        var tokenDX
                        if (sessionStorage.getItem('TokenTimer') < 30) {
                            tokenDX = sessionStorage.getItem('TokenTimer') * 1 + 30;
                        } else {
                            tokenDX = 30 - (60 - (sessionStorage.getItem('TokenTimer') * 1));
                        }
                        if (tokenDX == new Date().getMinutes()) {
                            var data = { token: sessionStorage.getItem('token') }
                            this.ajax(data, 'user/refresh', 'post').then(res => {
                                sessionStorage.setItem('token', res.token);
                                sessionStorage.setItem('TokenTimer', new Date().getMinutes());
                            })
                        }
                    }, 30000);
                } else {
                    clearInterval(this.loginTime);
                    sessionStorage.removeItem('token');
                    sessionStorage.removeItem('TokenTimer');
                }
            } else {
                this.switching(1);
            }
        },
        user_Create() {
            var ok = this.user.userName != '' && this.user.userEmail != '' && this.user.userPsw != '' && this.user.qruserPsw != '';
            var tips_F = this.$fatherdata.showMessage;
            if (ok) {
                var emailtest = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
                if (emailtest.test(this.user.userEmail)) {
                    if (this.user.userPsw == this.user.qruserPsw) {
                        if (this.user.userPsw.length >= 6) {
                            let data = {
                                mail: this.user.userEmail,
                                password: hex_md5(this.user.userPsw),
                                name: this.user.userName,
                                desc: 'absolutely empty',
                            }
                            this.ajax(data, 'user/register', 'post').then(res => {
                                console.log(res);
                                if (res.status == 0) {
                                    tips_F(0, 'Registration successful,please go to the mailbox to activate.');
                                    this.switching(1);
                                } else {
                                    tips_F(1, res.msg);
                                }
                            })
                        } else {
                            tips_F(1, 'The password must be at least 6 digits!');
                        }
                    } else {
                        tips_F(1, 'Inconsistent passwords!');
                    }
                } else {
                    tips_F(1, 'The mailbox is invalid!');
                }
            } else {
                tips_F(1, 'Please submit your information!');
            }
        },
        user_PswReset() {
            var emailtest = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
            if (emailtest.test(this.user.userEmail)){
                this.ajax({mail:this.user.userEmail},'user/recovery','post').then(res=>{
                    if(res.status == 0){
                        this.$fatherdata.showMessage(0,'The email has been sent to your mailbox, please check it.');
                    } else {
                        this.$fatherdata.showMessage(1,res.msg);
                    }
                })
            } else {
                this.$fatherdata.showMessage(1,'The mailbox format is incorrect!')
            }
            this.user.userEmail = '';
            this.checkbtn = false;
        },
        // 切换登录页面
        switching(mode) {
            let newuser = this.user;
            newuser.qruserPsw = '', newuser.userEmail = '', newuser.userPsw = '', newuser.userName = '', this.checkbtn = false;
            this.loginMode = mode;
            var name = 'show_loginList';
            if (mode == 0) {
                setTimeout(() => {
                    $('.Register_container').addClass(name);
                }, 50)
            } else {
                $('.Register_container').removeClass(name);
            }
            if (mode == 1) {
                setTimeout(() => {
                    $('.login_container').addClass(name);
                }, 50)
            } else {
                $('.login_container').removeClass(name);
            }
            if (mode == 2) {
                setTimeout(() => {
                    $('.reset_container').addClass(name);
                }, 50)
            } else {
                $('.reset_container').removeClass(name);
            }
        },
        // 请求方法
        ajax(data, Request, fn) {
            var url = 'https://radio.arylic.com/' + Request;
            return new Promise(function (resolve, reject) {
                var xml = new XMLHttpRequest();
                xml.open(fn, url);
                xml.setRequestHeader('Content-type', 'application/json');
                xml.send(JSON.stringify(data));
                xml.onreadystatechange = function () {
                    if (xml.readyState == 4 && xml.status == 200) {
                        try {
                            var response = xml.responseText;
                            resolve(JSON.parse(response));
                        } catch (e) {
                            reject(e);
                        }
                    }
                }
            })
        },
    }
}
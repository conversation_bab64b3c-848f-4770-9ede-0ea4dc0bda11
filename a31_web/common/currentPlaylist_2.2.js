const currentPlaylist = {
    template: `
    <div class="Nowplay_list" v-if="data.nowplaying_list.length != 0">
        <div class="list_playName">Play List</div>
        <div class="scrllo_playlist">
            <div class="Playlist_songs" :class="item.class == data.playing_index?'nowplay_select':''" v-for="(item,index) in data.nowplaying_list" :key="index" @click="Choose_song(item)">
                <div class="dynamic_body">
                    <div class="dynamic"  :class="item.class == data.playing_index?(data.play_status == 'PLAY'?'show_palying':''):''">
                        <span class="s1" style="height: 10px;"></span>
                        <span class="s2" style="height: 20px;"></span>
                        <span class="s3" style="height: 24px;"></span>
                        <span class="s2" style="height: 20px;"></span>
                        <span class="s1" style="height: 10px;"></span>
                    </div>
                    <img class="song_PictureB" :class="item.class == data.playing_index?'nowplay_selectP':''" :src="item.image"  onerror="src='static/defaultbackground.png'">
                    </img>
                </div> 
                <div class="Song_titleB">
                    <div class="Song_NameB" :title="item.title">{{item.title}}</div>
                    <div class="Song_singerB" v-html="item.album == ''?'':item.album + ',' + item.artist"></div>
                </div>
            </div>
        </div>
    </div>
    `,
    props: {
        data: {}
    },
    methods:{
        Choose_song(item){
            this.data.$refs.botm_play.socket.send('#CMD:PLAYLIST_PLAY:' + this.data.NowPlaylistName + '#$@' + item.class);
        }
    }
}


const topBar = {
    template:`
    <div class="top_navigation">
		<div class="top_logo h1" >{{top_title}}</div>
        <div class="loginIcon">
            <svg class="icon loginIconsvg" aria-hidden="true" @click.stop="show_loginMask">
                <use :xlink:href="LoginSrc"></use>
            </svg>
        </div>
		<div class="top_set_up" @click="tablehoutai">
            <svg class="icon SetIcon" aria-hidden="true" >
                <use :xlink:href="setSrc"></use>
            </svg>
        </div>
	</div>
    `,
    //@click="user_login"
    props:{
        data:{},
        top_title:{
            type:String,
            default:null,
        }
    },
    data(){
        return{
            LoginSrc:'#icon-wode1',
            setSrc:'#icon-shezhi-01',
            loginMaskStatus:false,
        }
    },
    watch:{
        'data.Login_Status':function(v,l){
            console.log(v);
            if (v == true) {
                this.LoginSrc = '#icon-wode2';
                $('.theBodyOfPlay').css('pointer-events','initial');
            } else {
                this.LoginSrc = '#icon-wode1';
            }
        }
    },
    methods:{
        // 切换到后台
        tablehoutai(){
            if(this.data.show_list == true){
                this.$parent.list_mask();
            }
            if(this.data.radio_maskOpen == true){
                this.$parent.canceloutindex();
            }
            if(this.data.showTable == true) {
                this.data.showTable = false;
                document.querySelector('.SwitchForm').classList.remove('FormPagehide');
            }
            this.data.SwitchVideo = !this.data.SwitchVideo;
			this.data.SwitchForm = !this.data.SwitchForm;
            if (this.data.top_title == 'Now Playing') {
                this.data.top_title = 'Settings';
                this.setSrc = '#icon-shezhi2-01';
			} else {
				this.data.top_title = 'Now Playing';
                this.setSrc = '#icon-shezhi-01';
			}
        },
        show_loginMask(){
            this.data.login_pas = '';
            this.loginMaskStatus = !this.loginMaskStatus;
            if (this.data.Login_Status == false) {
                if (this.loginMaskStatus) {
                    $('.login_mask').css('display','flex');
                    $('.loginput').focus();
                } else {
                    $('.login_mask').css('display','none');
                }
            } else {
                $('.loginOut_mask').css('display','flex');
            }
        }
    }
}
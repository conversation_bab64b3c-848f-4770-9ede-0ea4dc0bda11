// dataday.play_status == 'STOP'?'playsNone':''
const bottomPlay = {
    template: `
    <div class="palys_jindu">
        <div class="top_palys">
        <div class="play_btnbody">
            <div class="paly_btn">
            <div class="random" @click="debounce(Switch_randommode())">
                <svg class="icon" :class="dataday.sound_data.loop == '2' || dataday.sound_data.loop == '3' || dataday.sound_data.loop == '5'? '':'noRandom'" aria-hidden="true">
                    <use xlink:href="#icon-randow"></use>
                </svg>
            </div>
            <div class="perv" @click="debounce(up_down('prev'))">
                <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-perv"></use>
                </svg>
            </div>
            <div class="playsOrstop" :class="" @click="debounce(play_pause())">
                <div class="click_playbody">
                    <svg v-if="dataday.Nowsource == 41" class="icon" style="width: 36px;margin-left:2px;" aria-hidden="true">
                        <use xlink:href="#icon-a-ziyuan1"></use>
                    </svg>
                    <svg v-else  class="icon" aria-hidden="true">
                        <use :xlink:href="play_btnName"></use>
                    </svg>
                </div>
                <div class="dynamic loading" :class="dataday.play_status == 'TRANSITION'?'loading_music':''">
                    <span class="s1" style="height: 10px;"></span>
                    <span class="s2" style="height: 20px;"></span>
                    <span class="s3" style="height: 24px;"></span>
                    <span class="s2" style="height: 20px;"></span>
                    <span class="s1" style="height: 10px;"></span>
                </div>
            </div>
            <div class="next" id="next" @click="debounce(up_down('next'))">
                <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-next"></use>
                </svg>
            </div>
            <div class="loop"  @click="debounce(Switch_loopmode())">
                <svg class="icon" aria-hidden="true">
                    <use :xlink:href="loop_name"></use>
                </svg>
            </div>
        </div>
        <div class="right_elm">
            <div class="volume">
            <svg class="icon" aria-hidden="true" @click="Mute_volume" v-if="dataday.sound_data.mute == '0'">
                <use :xlink:href="volume_Name"></use>
            </svg>
            <svg class="icon" aria-hidden="true" @click="Mute_volume" v-else>
                <use xlink:href="#icon-volumeX"></use>
            </svg>
            </div>
            <div class="vol__" >
                <input class="volume_"  type="range" max="100" min="0" step="1" value="0" @input="Adjust_volume" @change="set_volume"/>
            </div>
        </div>
        <div class="eqbtn">
            <svg class="icon" aria-hidden="true" @click="showEq">
                <use xlink:href="#icon-eq"></use>
            </svg>
        </div>
        </div>
    </div>
        <div class="jindutiao">
            <div class="curpos">{{Has_been}}</div>
            <div class="progress_bar">
                <input class="Completed_bar" type="range" max="totlen" min="0" step="1"  @change="Setting_Progress_bar" @input="AdjustArticle" @mousedown="set_curpos = 1" @mouseup="set_curpos = 0"/>
            </div>
            <div class="totlen">{{Chief}}</div>
        </div>
    </div>`,
    props: {
        dataday: {},
    },
    data() {
        return {
            curpos: 0,
            totlen: 0,
            Chief: '00:00',
            Has_been: '00:00',
            set_curpos: 0,
            The_offset: 0,
            loop_name: '#icon-no_loop',
            volume_Name: '#icon-volume10',
            play_btnName: '#icon-bofang',
            socket: null,
            EQAPI: '',
            offsetValTimer:null
        }
    },
    watch: {
        curpos: function (v) {
            if (this.set_curpos == 0) {
                var pyl = Math.floor((this.curpos / this.totlen) * 100);
                if (isNaN(pyl) == false) {
                    document.querySelector('.Completed_bar').style.backgroundSize = pyl + '% 100%';
                } else {
                    document.querySelector('.Completed_bar').style.backgroundSize = pyl + '% 100%';
                }
            }
            this.Has_been = this.Timetion(v);
        },
        totlen: function (v) {
            if ( v == 0){
                document.querySelector('.Completed_bar').style.backgroundSize =  '0% 100%';
            }
            this.Chief = this.Timetion(v);
        },
        'dataday.sound_data.vol': function (v) {
            var Volmue = document.querySelector('.volume_');
            Volmue.value = v;
            Volmue.style.backgroundSize = v + '% 100%';
            this.Bind_Volume_Icon(v);
        },
        'dataday.show_list': function () {
            if (document.querySelector('.Preset_list').getAttribute('shownavtable') == 'true') {
                this.socket.send('#CMD:PRESET');
            }
        },
        'dataday.info.wifiIP': function (v) {
            //console.log('======',v);
            if (v != '') {
                this.socketlianjie(v);
            }
        },
        'dataday.info.ethIP': function (v) {
            if (v != '') {
                this.socketlianjie(v);
            }
        },

        'dataday.sound_data.loop': function (v, l) {
            if (v != l) {
                switch (true) {
                    case v == '3' || v == '4':
                        this.loop_name = '#icon-no_loop';
                        break;
                    case v == '0' || v == '2':
                        this.loop_name = '#icon-loop';
                        break;
                    case v == '1' || v == '5':
                        this.loop_name = '#icon-single';
                        break;
                    default:
                        this.loop_name = '#icon-no_loop';
                        break;
                }
            }
        },
        'dataday.play_status': function (v) {
            switch (true) {
                case v == 'PAUSE' || v == 'STOP':
                    this.play_btnName = '#icon-bofang';
                    break;
                case v == 'PLAY':
                    this.play_btnName = '#icon-zanting';
                    break;
                case v == 'TRANSITION':
                    this.play_btnName = '#icon-load';
                    break;
                default:
                    this.play_btnName = '#icon-bofang';
                    break;
            }
            if ( v == 'PLAY') {
                if ( this.curpos > this.totlen -1 ){
                    if ( this.offsetValTimer != null ) {
                        clearInterval(this.offsetValTimer);
                    }
                } else {
                    this.offsetValTimer = setInterval(()=>{
                        if(this.curpos < this.totlen) this.curpos++
                    },1000)
                }
            } else {
                if ( this.offsetValTimer != null ) {
                    clearInterval(this.offsetValTimer);
                }
            }
        },
        EQAPI: function (v) {
            if (v == '#API:AP8064') {
                document.querySelector('.medium').setAttribute('readonly','readonly');
                document.querySelector('.eqmode').setAttribute('readonly','readonly');
                document.querySelector('.balance').setAttribute('readonly','readonly');
                this.socket.send('EQG');
                setTimeout(() => {
                    this.socket.send('VBS');
                    this.socket.send('MXV');
                }, 300)
            } else if (v == '#API:BP10XX') {
                this.socket.send('PEQ');
                setTimeout(() => {
                    this.socket.send('AUS');
                }, 300)
            }
        }
    },
    beforeDestroy() {
        this.socket.close();
    },
    methods: {
        Timetion(time) {
            var f = Math.floor(time / 60);
            var m = Math.floor(time % 60);
            if (f < 10) {
                f = '0' + f
            };
            if (m < 10) {
                m = '0' + m
            };
            var song_time = f + ":" + m;
            return song_time
        },
        /* 弹出eq界面 */
        showEq() {
            var eqmask = document.querySelector('.eqmask');
            var Equalizer_body = document.querySelector('.Equalizer_body');
            this.dataday.showEqbody = !this.dataday.showEqbody;
            if (this.dataday.show_list == true) {
                this.dataday.list_mask();
            }
            if (this.dataday.showEqbody == false) {
                eqmask.style.display = 'block';
                Equalizer_body.style.display = 'block';
                setTimeout(() => {
                    //console.log(this.EQAPI);
                    if (this.EQAPI == '') {
                        var arr = ['.medium', '.eqmode', '.balance', '.deppbass', '.eqmode', '.MaxVolume'];
                        arr.forEach(item => {
                            document.querySelector(item).setAttribute('readonly','readonly');
                        })
                        this.socket.send('MID');
                        this.socket.send('TRE');
                        this.socket.send('BAS');
                    } else if(this.EQAPI == 'BP10XX'){
                        this.socket.send('PEQ');
                        setTimeout(()=>{
                            this.socket.send('AUS');
                        },300)
                    }
                }, 300);
            } else {
                eqmask.style.display = 'none';
                Equalizer_body.style.display = 'none';
            }
        },
        socketlianjie(url) {
            SocketUrl = 'ws://' + url + ':8888';
            
            var self = this;
            this.socket = new WebSocket(SocketUrl);
            this.socket.onopen = function (res) {
                //console.log('已连接ws');
                self.socket.send('#CMD:STATUS');
                self.socket.send('#CMD:PRESET');
                self.socket.send('#CMD:USB');
                setTimeout(() => {
                    $('.loadingMask').css('display','none');
                    self.socket.send('#API');
                }, 200)
            }
            this.socket.onmessage = function (res) {
                // console.log(res);
                if (res.data.indexOf('cmd') != -1) {
                    var dRegex =  /[\x00-\x1F\x7F-\x9F]/g;// 匹配所有不可见字符的正则表达式
                    
                    try{
                        // 使用正则表达式替换不可见字符为空格
                        var resData = res.data.replace(dRegex, '');
                        var cmdData = JSON.parse(resData);
                    }catch(e){
                        //console.log('问题出现在',res.data);
                    }
                    
                    switch (true) {
                        case cmdData.cmd == 'STATUS':
                            // UNKNOWN
                            // SONGLIST-NETWORK
                            // RADIO-NETWORK 
                            // "NETWORK::PLAYLIST"
                            self.dataday.NowInput = cmdData.input;
                            if(cmdData.input == "UNKNOWN"){
                                self.Has_been = '00:00';
                                self.Chief = '00:00';
                                return;
                            }
                            if (cmdData.track.state != null) {
                                
                                // //console.log('当前NowInput是',self.dataday.NowTrack);
                                // //console.log('当前track是',cmdData.track);
                                self.dataday.NowTrack = cmdData.track;

                                // 启动播放
                                if (cmdData.input != 'UNKNOWN' ){
                                    self.Has_been = self.Timetion(cmdData.track.elapsed);
                                    self.Chief = self.Timetion(cmdData.track.duration);
                                } else {
                                    self.Has_been = '00:00';
                                    self.Chief = '00:00';
                                }

                                // 是否正在播放
                                if (cmdData.track.state != '') {
                                    self.dataday.play_status = cmdData.track.state;
                                    // //console.log('self.dataday.play_status',self.dataday.play_status);
                                } else {
                                    self.curpos = 0;
                                    self.dataday.play_status = 'STOP';
                                    self.Has_been = '00:00';
                                    self.Chief = '00:00';
                                    setTimeout(()=>{
                                        self.dataday.nowplaying_State.name = '';
                                        self.dataday.nowplaying_State.album = '';
                                        self.dataday.nowplaying_State.url = '';
                                    },300)
                                }

                                if (cmdData.track.source =='Personal Radio' || cmdData.track.source == 'Radio' ) {
                                    $('.perv').attr('readonly','readonly');
                                    $('.next').attr('readonly','readonly');
                                    $('.jindutiao').attr('readonly','readonly');
                                    $('.loop').attr('readonly','readonly');
                                    $('.random').attr('readonly','readonly');
                                } else {
                                    $('.perv').removeAttr('readonly');
                                    $('.next').removeAttr('readonly');
                                    $('.jindutiao').removeAttr('readonly');
                                    $('.loop').removeAttr('readonly');
                                    $('.random').removeAttr('readonly');
                                }
                                // if (cmdData.track.duration == 0 && cmdData.track.elapsed == 0) {
                                //         if (cmdData.track.source != 'QQFM'){
                                //             $('.perv').attr('readonly','readonly');
                                //             $('.next').attr('readonly','readonly');
                                //             $('.jindutiao').attr('readonly','readonly');
                                //             $('.loop').attr('readonly','readonly');
                                //             $('.random').attr('readonly','readonly');
                                //         } else {
                                //             $('.perv').removeAttr('readonly');
                                //             $('.next').removeAttr('readonly');
                                //             $('.jindutiao').removeAttr('readonly');
                                //             $('.loop').removeAttr('readonly');
                                //             $('.random').removeAttr('readonly');
                                //         }
                                //     } else {
                                //         $('.perv').removeAttr('readonly');
                                //         $('.next').removeAttr('readonly');
                                //         $('.jindutiao').removeAttr('readonly');
                                //         $('.loop').removeAttr('readonly');
                                //         $('.random').removeAttr('readonly');
                                // }
                                if (cmdData.track.source == 'NETWORK::SPOTIFY' || cmdData.track.source == 'DLNA' 
                                    || cmdData.track.source == 'NETWORK::RADIO' || cmdData.track.source == 'NETWORK::AIRPLAY' 
                                    || cmdData.input == 'UNKNOWN' || cmdData.track.source =='Personal Radio') {
                                    self.dataday.nowplaying_list = [];
                                } else {
                                    setTimeout(()=>{
                                        self.socket.send('#CMD:PLAYLIST_BROWSE:CurrentQueue');
                                        self.socket.send('#CMD:PLAYLIST_CURRENT');
                                    },800);
                                }
                            } else {

                                if (cmdData.input == 'LINE-IN') {
                                    $('.perv').attr('readonly','readonly');
                                    $('.next').attr('readonly','readonly');
                                    $('.jindutiao').attr('readonly','readonly');
                                    $('.loop').attr('readonly','readonly');
                                    $('.random').attr('readonly','readonly');
                                }

                                var nowPlaydata = self.dataday.nowplaying_State;
                                nowPlaydata.name = '';
                                nowPlaydata.album = '';
                                nowPlaydata.url = '';
                                self.dataday.nowplaying_list = [];
                                self.dataday.play_status = 'STOP';  
                                self.Has_been = '00:00';
                                self.Chief = '00:00';
                                document.querySelector('.Completed_bar').style.backgroundSize =  '0% 100%';
                            }
                            break;
                        case cmdData.cmd == 'PRESET':
                            if (cmdData.content != null) {
                                self.dataday.preset_playlist = [{},{},{},{},{},{},{},{},{},{}];
                                cmdData.content.forEach((item) => {
                                    self.dataday.preset_playlist[item.index -1].Name = item.name;
                                    self.dataday.preset_playlist[item.index -1].PicUrl = item.image;
                                    self.dataday.preset_playlist[item.index -1].id = item.id;
                                    self.dataday.preset_playlist[item.index -1].class = item.index;
                                })
                            }
                            break;
                        case cmdData.cmd == 'PLAYLIST_BROWSE':
                            self.dataday.playing_index = cmdData.index;
                            self.dataday.nowplaying_list = cmdData.content;
                            self.dataday.nowplaying_list.forEach((item, index) => {
                                item.class = index + 1;
                            })
                            break;
                        case cmdData.cmd == 'PLAYLIST_CURRENT':
                            if (self.dataday.NowTrack.source == 'spotify') {
                                self.dataday.NowPlaylistName = 'spotify';
                            } else if (self.dataday.NowTrack.meta == undefined) {
                                self.dataday.NowplaylistIndex = null;
                            } else {
                                self.dataday.NowPlaylistName = cmdData.name;
                            }
                            break;
                        case cmdData.cmd == 'META':
                            // self.dataday.NowPlaylistName = cmdData.title;
                            break;
                        case cmdData.cmd == 'NONE':
                            if ( cmdData.mode != undefined ) {
                                self.dataday.sound_data.loop = cmdData.mode;
                            } else if (cmdData.vol != undefined) {
                                self.dataday.sound_data.vol = cmdData.vol;
                            }
                            break;
                        case cmdData.cmd == 'RAKOIT':
                            if(cmdData.msg == 'TRE'){
                                self.dataday.trebleVal  = cmdData.param * 1;
                            }else if(cmdData.msg == 'BAS'){
                                self.dataday.BassVal  = cmdData.param * 1;
                            }else if(cmdData.msg == 'MID'){
                                self.dataday.MIDVal  = cmdData.param * 1;
                            }else if(cmdData.msg == 'VBS'){
                                self.dataday.DeepBas = cmdData.param * 1;
                            }else if(cmdData.msg == 'MXV'){
                                self.dataday.MXV = cmdData.param * 1;
                            }else if(cmdData.msg == 'BAL'){
                                self.dataday.BALVal = cmdData.param * 1;
                            }else if(cmdData.msg == 'PEQ'){
                                var arr = cmdData.param.split(',');
                                arr.forEach((item, i) => {
                                    arr[i] = {};
                                    arr[i].num = item.substring(0, 1);
                                    arr[i].name = item.substring(2);
                                })
                                self.dataday.EQlist = arr;
                                //console.log(self.dataday.EQlist);
                            }else if(cmdData.msg == 'AUS'){
                                //console.log(cmdData.param.split(','));
                                var arr = cmdData.param.split(',');
                                self.dataday.trebleVal = arr[0] * 1;
                                self.dataday.MIDVal = arr[1] * 1;
                                self.dataday.BassVal =  arr[2] * 1;
                                self.dataday.DeepBas = arr[3] * 1;
                                setTimeout(()=>{
                                    self.dataday.EQlist.forEach(item => {
                                        if (item.num == arr[4]) {
                                            self.dataday.NowEQ = item.name;
                                        }
                                    });
                                },200);
                                self.dataday.MXV = arr[5] * 1;
                                self.dataday.BALVal = arr[6] * 1;
                                // self.dataday.BassVal  = cmdData.param * 1;
                            }
                            break;
                        case cmdData.cmd == 'API':
                            self.EQAPI = cmdData.msg;
                            break;
                        case cmdData.cmd == 'OTA':
                            console.log(cmdData);
                            if(cmdData.value == '1'){
                                self.dataday.ota = true;
                            }else{
                                self.dataday.ota = false;
                            }
                            console.log(self.dataday.ota);
                            break;
                        case cmdData.cmd == 'AXX':
                            if(cmdData.msg == 'USB'){
                                // self.dataday.havaUSB = cmdData.param * 1;
                                // //console.log('usb',self.dataday.havaUSB);
                                if(cmdData.param * 1 == 1){
                                    if(self.dataday.Input_sourceLsit.indexOf('USB') == -1){
                                        self.dataday.Input_sourceLsit.push('USB');
                                    }
                                }else if(cmdData.param * 1 == 0){
                                    var indexToRemove = self.dataday.Input_sourceLsit.indexOf('USB');
                                    if (indexToRemove !== -1) {
                                        // 使用splice方法删除该项
                                        self.dataday.Input_sourceLsit.splice(indexToRemove, 1);
                                    }
                                }
                            }else if(cmdData.msg == 'volume'){
                                //console.log(cmdData.param * 1,'音量');
                                self.dataday.sound_data.vol = cmdData.param * 1;
                            }else if(cmdData.msg == 'input'){

                            }
                            // self.dataday.havaUSB = cmdData.status;
                            break
                    }
                }
                if (res.data.indexOf('#API:') != -1) {
                    self.EQAPI = res.data;
                }
                if (res.data.indexOf('TRE:') == 0) {
                    self.dataday.trebleVal = res.data.replace('TRE:', '');
                }
                if (res.data.indexOf('BAS:') == 0) {
                    self.dataday.BassVal = res.data.replace('BAS:', '');
                }
                if (res.data.indexOf('VBS:') == 0) {
                    self.dataday.DeepBas = res.data.replace('VBS:', '');
                }
                if (res.data.indexOf('MXV:') == 0) {
                    self.dataday.MXV = res.data.replace('MXV:', '');
                }
                if (res.data.indexOf('AUS:') == 0) {
                    var arr = res.data.replace('AUS:', '').split(',');
                    var arr2 = ['TRE' , 'MID' , 'BAS' , 'VBS' , 'EQS' , 'MXV' , 'BAL'];
                    var obj = {};
                    for (var i = 0; i < arr.length; i++) {
                        obj[arr2[i]] = arr[i];
                    }
                    self.dataday.trebleVal = obj.TRE;
                    self.dataday.MIDVal = obj.MID;
                    self.dataday.BassVal = obj.BAS;
                    self.dataday.DeepBas = obj.VBS;
                    self.dataday.MXV = obj.MXV;
                    self.dataday.BALVal = obj.BAL;
                    self.dataday.EQlist.forEach(item => {
                        if (item.num == obj.EQS) {
                            self.dataday.NowEQ = item.name;
                        }
                    })
                }
                if (res.data.indexOf('PEQ:') == 0) {
                    var arr = res.data.replace('PEQ:', '').split(',');
                    arr.forEach((item, i) => {
                        arr[i] = {};
                        arr[i].num = item.substring(0, 1);
                        arr[i].name = item.substring(2);
                    })
                    self.dataday.EQlist = arr;
                }
            }
        },
        /* 设置进度条填充条*/
        AdjustArticle(e) {
            var val = e.target.value;
            var Completed_bar = document.querySelector('.Completed_bar');
            if (this.dataday.play_status == 'PLAY' || this.dataday.play_status == 'PAUSE') {
                Completed_bar.style.backgroundSize = val + '% 100%';
            }
        },
        /* 设置进度条(结束触发) */
        Setting_Progress_bar(e) {
            var val = e.target.value;
            this.The_offset = Math.round(this.totlen * (val / 100));
            if (this.dataday.play_status == 'PLAY' || this.dataday.play_status == 'PAUSE') { 
                fetch(HOST_IP + 'httpapi.asp?command=setPlayerCmd:seek:' + this.The_offset).then(()=>{
                    this.dataday.Get_current_status();
                    this.socket.send('#CMD:STATUS');
                }).catch(err => {
                    //console.log(err);
                });
            }
        },
        /* Volume_mute(音量静音)*/
        Mute_volume() {
            if (this.dataday.sound_data.mute == '0') {
                var muet = '1';
            } else if (this.dataday.sound_data.mute == '1') {
                var muet = '0';
            }
            fetch(HOST_IP + 'httpapi.asp?command=setPlayerCmd:mute:' + muet).then(() => {
                this.dataday.Get_current_status();
            });
        },
        /* Adjust_volume(调整音量) */
        Adjust_volume(e) {
            var val = e.target.value;
            document.querySelector('.volume_').style.backgroundSize = val + '% 100%';
            this.Bind_Volume_Icon(val);
        },
        /* set_volume(设置音量) */
        set_volume(e) {
            var val = e.target.value;
            //console.log(val);
            fetch(HOST_IP + 'httpapi.asp?command=setPlayerCmd:vol:' + val).then(() => {
                //console.log('音量设置完成');
                this.dataday.Get_current_status();
            })
        },
        /* Bind volume change icon(绑定音量改变图标) */
        Bind_Volume_Icon(val) {
            val = val * 1;
            if (val == 0) {
                this.volume_Name = '#icon-volumeX';
            } else if (val >= 1 && val <= 20) {
                this.volume_Name = '#icon-volume10';
            } else if (val >= 21 && val <= 50) {
                this.volume_Name = '#icon-volume50';
            } else if (val >= 51 && val <= 100) {
                this.volume_Name = '#icon-volume100';
            }
        },
        /* prev song (上一曲/下一曲) */
        up_down(action) {
            if (action == 'prev'){
                this.socket.send('PRE');
            } else{
                this.socket.send('NXT');
            }
        },
        /* Play or pause(播放or暂停) */
        play_pause() {
            this.socket.send('POP');
            // if (this.dataday.play_status != 'TRANSITION') {
            //     if (this.dataday.play_status == 'PAUSE') { ml = 'resume' } else if (this.dataday.play_status == 'PLAY') { ml = 'pause' }
            //     fetch(HOST_IP + 'httpapi.asp?command=setPlayerCmd:' + ml);
            // }
        },
        /* 设置循环模式 */
        Switch_randommode() {
            let loop = this.dataday.sound_data.loop;
            if (loop == '0') this.cyclemode('2');
            if (loop == '1') this.cyclemode('5');
            if (loop == '2') this.cyclemode('0');
            if (loop == '3') this.cyclemode('4');
            if (loop == '4') this.cyclemode('3');
            if (loop == '5') this.cyclemode('1');
        },
        Switch_loopmode() {
            let loop = this.dataday.sound_data.loop;
            if (loop == '0') this.cyclemode('1');
            if (loop == '1') this.cyclemode('4');
            if (loop == '2') this.cyclemode('5');
            if (loop == '3') this.cyclemode('2');
            if (loop == '4') this.cyclemode('0');   
            if (loop == '5') this.cyclemode('3');
        },
        cyclemode(mode) {
            fetch(HOST_IP + 'httpapi.asp?command=setPlayerCmd:loopmode:' + mode).then(() => {
                this.dataday.Get_current_status();
            });
        },
        debounce(fn) {
            var t = null;
            return function () {
                var firstClick = !t;
                if (t) { clearTimeout(t); }
                if (firstClick) {
                    fn.apply(this, arguments);
                }
                t = setTimeout(() => {
                    t = null;
                }, 800)
            }
        }
    }
}
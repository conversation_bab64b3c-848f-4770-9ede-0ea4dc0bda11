/*
 * @Author: your name
 * @Date: 2021-12-03 11:08:32
 * @LastEditTime: 2022-04-16 17:11:07
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \组件c:\Users\<USER>\Desktop\a31_web\common\currentPlay_2.2.js
 */

const currentPlay = {
    template:`
    <div class="Play_information">
        <div class="Song_picture">
            <svg class="icon IconMor" v-if="$fatherdata.nowplaying_State.url == undefined || $fatherdata.nowplaying_State.url == ''" aria-hidden="true" :class="$fatherdata.nowplaying_State.name == ''?'NoMusic':''">
                <use :xlink:href="$fatherdata.AsourceIMG"></use>
            </svg>
            <img class="Song_PictureA" v-else :src="$fatherdata.nowplaying_State.url" onerror="src='static/defaultbackground.png'"></img>
        </div>
        <div class="Song_NameA h1" :title="$fatherdata.nowplaying_State.name">No Music Select</div>
        <div class="Song_singerA h2" ></div>
    </div>
    `,
    props:{
        $fatherdata:{},
        
    },
    data(){
        return{

        }
    },
    watch:{
        '$fatherdata.nowplaying_State.name':function(v) {
            // {{$fatherdata.nowplaying_State.name == ''?'':$fatherdata.nowplaying_State.name}}
            if (v == '') {
                TitleName = 'No Music Select';
            } else {
                TitleName = v;
            }
            document.querySelector('.Song_NameA').innerHTML = TitleName;
        },
        '$fatherdata.nowplaying_State.album':function(v) {
            if (v == '') {
                Infoalbum = '';
            } else {
                Infoalbum = v;
            }
            document.querySelector('.Song_singerA').innerHTML = Infoalbum;
        },
        
    }
} 
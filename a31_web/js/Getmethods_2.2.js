
const ImageXXX = ['.xbm', '.tif', 'pjp', '.svgz', '.jpg','.jpeg', '.ico','.tiff', '.gif', '.svg', '.jfif', '.webp', '.png', '.bmp', '.pjpeg', '.avif'];

const UrlZZ = /(http|https):\/\/([\w.]+\/?)\S*/;

const Yzimage = function(imgurl) {
    return new Promise(function (resolve, reject) {
        var ImgObj = new Image();
        ImgObj.src = imgurl;
        ImgObj.onload = function (res) {
            resolve(res);
        }
        ImgObj.onerror = function (err) {
            reject(err);
        }
    })
}

// 加密
const compileStr = function(code){
    var c = String.fromCharCode(code.charCodeAt(0) + code.length);
    for(var i = 1; i < code.length ; i++){
        c += String.fromCharCode(code.charCodeAt(i) + code.charCodeAt(i - 1));
    }
    return escape(c)
}
// 解密
const unCompileStr = function (code){
    code = unescape(code);
    var c = String.fromCharCode(code.charCodeAt(0) - code.length);
    for(var i = 1; i < code.length; i++){
        c += String.fromCharCode(code.charCodeAt(i) - c.charCodeAt(i - 1));
    }
    return c;
}


!function (t) { var e, i, n, l, o, a, c = '<svg><symbol id="rkwifi1" viewBox="0 0 1024 1024"><path d="M970.24 382.72c-10.24 0-20.48-3.84-28.16-11.52C704 133.12 317.44 133.12 79.36 371.2 64 386.56 38.4 386.56 23.04 371.2c-15.36-15.36-15.36-39.68 0-55.04 268.8-268.8 705.28-268.8 975.36 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 14.08-21.76 23.04-37.12 23.04z" fill="#E0E0E0" ></path><path d="M837.12 515.84c-10.24 0-20.48-3.84-28.16-11.52-165.12-165.12-432.64-165.12-599.04 0-10.24 10.24-24.32 14.08-38.4 10.24-14.08-3.84-24.32-14.08-28.16-28.16-3.84-14.08 0-28.16 10.24-38.4 195.84-195.84 514.56-195.84 710.4 0 11.52 11.52 14.08 28.16 8.96 43.52-5.12 14.08-19.2 24.32-35.84 24.32z" fill="#E0E0E0" ></path><path d="M705.28 647.68c-10.24 0-20.48-3.84-28.16-11.52-92.16-92.16-241.92-92.16-334.08 0-15.36 15.36-39.68 15.36-55.04 0-15.36-15.36-15.36-39.68 0-55.04 122.88-122.88 322.56-122.88 445.44 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 12.8-20.48 23.04-37.12 23.04zM638.72 802.56c0 61.44-43.52 113.92-103.68 125.44-60.16 11.52-120.32-20.48-143.36-76.8-23.04-56.32-3.84-121.6 47.36-154.88 51.2-33.28 119.04-26.88 161.28 15.36 24.32 24.32 38.4 57.6 38.4 90.88z" fill="#03B18C" ></path></symbol><symbol id="rkwifi" viewBox="0 0 1024 1024"><path d="M970.24 382.72c-10.24 0-20.48-3.84-28.16-11.52C704 133.12 317.44 133.12 79.36 371.2 64 386.56 38.4 386.56 23.04 371.2c-15.36-15.36-15.36-39.68 0-55.04 268.8-268.8 705.28-268.8 975.36 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 14.08-21.76 23.04-37.12 23.04z" fill="#E0E0E0" ></path><path d="M837.12 515.84c-10.24 0-20.48-3.84-28.16-11.52-165.12-165.12-432.64-165.12-599.04 0-10.24 10.24-24.32 14.08-38.4 10.24-14.08-3.84-24.32-14.08-28.16-28.16-3.84-14.08 0-28.16 10.24-38.4 195.84-195.84 514.56-195.84 710.4 0 11.52 11.52 14.08 28.16 8.96 43.52-5.12 14.08-19.2 24.32-35.84 24.32z" fill="#E0E0E0" ></path><path d="M705.28 647.68c-10.24 0-20.48-3.84-28.16-11.52-92.16-92.16-241.92-92.16-334.08 0-15.36 15.36-39.68 15.36-55.04 0-15.36-15.36-15.36-39.68 0-55.04 122.88-122.88 322.56-122.88 445.44 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 12.8-20.48 23.04-37.12 23.04z" fill="#E0E0E0" ></path><path d="M638.72 802.56c0 61.44-43.52 113.92-103.68 125.44-60.16 11.52-120.32-20.48-143.36-76.8-23.04-56.32-3.84-121.6 47.36-154.88 51.2-33.28 119.04-26.88 161.28 15.36 24.32 24.32 38.4 57.6 38.4 90.88z" fill="#03B18C" ></path></symbol><symbol id="rkwifi2" viewBox="0 0 1024 1024"><path d="M970.24 382.72c-10.24 0-20.48-3.84-28.16-11.52C704 133.12 317.44 133.12 79.36 371.2 64 386.56 38.4 386.56 23.04 371.2c-15.36-15.36-15.36-39.68 0-55.04 268.8-268.8 705.28-268.8 975.36 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 14.08-21.76 23.04-37.12 23.04z" fill="#E0E0E0" ></path><path d="M837.12 515.84c-10.24 0-20.48-3.84-28.16-11.52-165.12-165.12-432.64-165.12-599.04 0-10.24 10.24-24.32 14.08-38.4 10.24-14.08-3.84-24.32-14.08-28.16-28.16-3.84-14.08 0-28.16 10.24-38.4 195.84-195.84 514.56-195.84 710.4 0 11.52 11.52 14.08 28.16 8.96 43.52-5.12 14.08-19.2 24.32-35.84 24.32z" fill="#03B18C" ></path><path d="M705.28 647.68c-10.24 0-20.48-3.84-28.16-11.52-92.16-92.16-241.92-92.16-334.08 0-15.36 15.36-39.68 15.36-55.04 0-15.36-15.36-15.36-39.68 0-55.04 122.88-122.88 322.56-122.88 445.44 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 12.8-20.48 23.04-37.12 23.04zM638.72 802.56c0 61.44-43.52 113.92-103.68 125.44-60.16 11.52-120.32-20.48-143.36-76.8-23.04-56.32-3.84-121.6 47.36-154.88 51.2-33.28 119.04-26.88 161.28 15.36 24.32 24.32 38.4 57.6 38.4 90.88z" fill="#03B18C" ></path></symbol><symbol id="rkwifi3" viewBox="0 0 1024 1024"><path d="M970.24 382.72c-10.24 0-20.48-3.84-28.16-11.52C704 133.12 317.44 133.12 79.36 371.2 64 386.56 38.4 386.56 23.04 371.2c-15.36-15.36-15.36-39.68 0-55.04 268.8-268.8 705.28-268.8 975.36 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 14.08-21.76 23.04-37.12 23.04z" fill="#03B18C" ></path><path d="M837.12 515.84c-10.24 0-20.48-3.84-28.16-11.52-165.12-165.12-432.64-165.12-599.04 0-10.24 10.24-24.32 14.08-38.4 10.24-14.08-3.84-24.32-14.08-28.16-28.16-3.84-14.08 0-28.16 10.24-38.4 195.84-195.84 514.56-195.84 710.4 0 11.52 11.52 14.08 28.16 8.96 43.52-5.12 14.08-19.2 24.32-35.84 24.32z" fill="#03B18C" ></path><path d="M705.28 647.68c-10.24 0-20.48-3.84-28.16-11.52-92.16-92.16-241.92-92.16-334.08 0-15.36 15.36-39.68 15.36-55.04 0-15.36-15.36-15.36-39.68 0-55.04 122.88-122.88 322.56-122.88 445.44 0 11.52 11.52 14.08 28.16 8.96 43.52-6.4 12.8-20.48 23.04-37.12 23.04zM638.72 802.56c0 61.44-43.52 113.92-103.68 125.44-60.16 11.52-120.32-20.48-143.36-76.8-23.04-56.32-3.84-121.6 47.36-154.88 51.2-33.28 119.04-26.88 161.28 15.36 24.32 24.32 38.4 57.6 38.4 90.88z" fill="#03B18C" ></path></symbol></svg>', d = (d = document.getElementsByTagName("script"))[d.length - 1].getAttribute("data-injectcss"); if (d && !t.__iconfont__svg__cssinject__) { t.__iconfont__svg__cssinject__ = !0; try { document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>") } catch (t) { console && console.log(t) } } function s() { o || (o = !0, n()) } e = function () { var t, e, i; (i = document.createElement("div")).innerHTML = c, c = null, (e = i.getElementsByTagName("svg")[0]) && (e.setAttribute("aria-hidden", "true"), e.style.position = "absolute", e.style.width = 0, e.style.height = 0, e.style.overflow = "hidden", t = e, (i = document.body).firstChild ? (e = i.firstChild).parentNode.insertBefore(t, e) : i.appendChild(t)) }, document.addEventListener ? ~["complete", "loaded", "interactive"].indexOf(document.readyState) ? setTimeout(e, 0) : (i = function () { document.removeEventListener("DOMContentLoaded", i, !1), e() }, document.addEventListener("DOMContentLoaded", i, !1)) : document.attachEvent && (n = e, l = t.document, o = !1, (a = function () { try { l.documentElement.doScroll("left") } catch (t) { return void setTimeout(a, 50) } s() })(), l.onreadystatechange = function () { "complete" == l.readyState && (l.onreadystatechange = null, s()) }) }(window);
/* #03b18c #122127 #b5b7b8 */
:root {
	--backgruond_black_opacity_095: rgb(20 20 20 /98%);
	--icon_width18: 18px;
	--icon_color: 255, 255, 255;
	--icon_color_gray: 235, 235, 235;
	--icon_active: scale(.95);
	--icon_transition: .1s ease-out;
	--text_h1_color: 255, 255, 255;
	--text_h2_color: 230, 230, 230;
	--text_color_gray: 200, 200, 200;
	--opacity70: .7;
	--text_selcetionStyle: 122, 122, 122, 0.6;
	--scroll_color: rgb(180, 180, 180);
	--grayinputColor: #40404065;
	--shadow1: 0 0 16px 16px #ffffff10 inset;
}

* {
	padding: 0;
	margin: 0;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	text-transform: capitalize;
	font-family: "Source Sans Pro", sans-serif !important;
}

svg {
	color: rgb(var(--icon_color));
	transition: color var(--icon_transition);
	-moz-transition: color var(--icon_transition);
	-o-transition: color var(--icon_transition);
	-webkit-transition: color var(--icon_transition);
}

img {
	pointer-events: none;
	object-fit: cover;
}

input {
	-webkit-user-select: text !important;
	/* user-select:text !important; */
}

.h1 {
	color: rgb(var(--text_h1_color));
}

.h2 {
	color: rgb(var(--text_h2_color));
}

.volume_,
.Completed_bar,
.MaxVolume_range,
.treble_slider,
.medium_slider,
.bass_slider,
.balance_range {
	-webkit-user-select: none !important;
}

.volume_,
.Completed_bar,
.balance_range,
.MaxVolume_range,
.select-box input,
.volume_::-webkit-slider-thumb,
.SwitchForm::-webkit-scrollbar,
.slider_thumb::-webkit-slider-thumb,
.SwitchForm::-webkit-scrollbar-thumb,
.treble_slider,
.medium_slider,
.bass_slider,
.progress_bar .Completed_bar::-webkit-slider-thumb,
.Radio_Content::-webkit-scrollbar,
.filterName>div::-webkit-scrollbar,
.selectGenre::-webkit-scrollbar {
	appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	-o-appearance: none;
}

.Radio_Content::-webkit-scrollbar {
	display: none;
}

.icon {
	width: 1.5em;
	height: 1.5em;
	vertical-align: -0.2em;
	fill: currentColor;
	overflow: hidden;
}

@font-face {
	font-family: "iconfont";
	src: url('data:application/x-font-woff2;charset=utf-8;base64, 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') format('woff2');
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;

}

.icon-new:before {
	content: "\e610";
}

.icon-update:before {
	content: "\e6dc";
}

.icon-banben:before {
	content: "\e7a5";
}

.icon-huaban:before {
	content: "\e603";
}

.icon-refresh:before {
	content: "\e600";
}

.icon-WiFixinhao:before {
	content: "\e6a1";
}

.icon-yan:before {
	content: "\e975";
}

.icon-biyan:before {
	content: "\e681";
}

.icon-go-back-new:before {
	content: "\e60f";
}

.between {
	justify-content: space-between;
}

.item-mid {
	align-items: center;
}

.t-mid {
	text-align: center;
}

.t-left {
	text-align: left;
}

.p5r {
	padding-right: 5px;
}

.cr-red {
	color: red !important;
}

.cr-sub {
	color: #b5b7b8;
}

.f-hidden {
	display: none;
}

.content {
	width: 1000px;
	height: 600px;
	margin: 0 auto;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	box-sizing: border-box;
	overflow: hidden;
	position: relative;
	background-color: rgb(0 0 0 / 10%);
}

.SwitchForm {
	padding: 18px;
	height: 504px;
	border-top: none;
	overflow-y: scroll;
	background-color: rgb(0 0 0 / 40%);
}

.SwitchForm::-webkit-scrollbar {
	max-width: 6px;
}

.SwitchForm::-webkit-scrollbar-thumb {
	background-color: var(--scroll_color);
	border-radius: 12px;
}

.form-item {
	display: flex;
	justify-content: flex-start;
	margin: 15px auto;
}

.ladel {
	width: 150px;
	height: 30px;
	line-height: 30px;
	font-size: 15px;
	font-weight: 600;
	text-align: left;
	margin-left: 40px;
	color: rgba(255, 255, 255, 1);
}

.form-box {
	display: flex;
	align-items: center;
	width: 310px;
	text-transform: none;
}

.wifi_rightbox {
	min-width: 214px !important;
}

.rightbox {
	position: relative;
	min-width: 200px;
	height: 30px;
	font-size: 15px;
	box-sizing: border-box;
	text-align: left;
	color: #cccccc;
	line-height: 30px;
	padding-left: 2px;
	overflow: hidden;
}

.input {
	width: 200px;
	min-width: 200px;
	height: 30px;
	font-size: 14px;
	border-bottom: solid 1px !important;
	background: none;
	color: #ffffff;
	outline: none;
	border: none;
	margin: 4px 0;
	text-transform: none;
	padding-right: 30px;
	box-sizing: border-box;
}

.wifi_icon {
	right: 0px !important;
	top: 0px !important;
	position: inherit !important;
}

.wifi_icon i {
	cursor: default;
}

.icon-box {
	position: relative;
	user-select: text !important;
}

.icontip {
	position: absolute;
	z-index: 999;
	float: right;
	right: 5px;
	top: 6px;
	color: #BCBCBC;
	font-size: 16px;
	cursor: pointer;
}

.trLoading:hover {
	cursor: auto;
	color: #a5a5a5;
	background-color: transparent;
}

.icon-big {
	font-size: 26px !important;
	color: #03b18c;
}

.btn {
	background-color: #ffffff;
	border: none;
	min-width: 72px;
	height: 28px;
	font-size: 15px;
	line-height: 28px;
	text-align: center;
	padding: 2px 12px;
	margin-left: 44px;
	box-sizing: content-box;
	border-radius: 6px;
	color: #000000;
	cursor: pointer;
	font-weight: bold;
}

.btn:active {
	transform: scale(.95);
}

.showaplist_btn {
	margin-left: 4px;
	background-color: #ffffff;
	border: none;
	min-width: 60px;
	height: 28px;
	font-size: 15px;
	line-height: 28px;
	text-align: center;
	padding: 3px 18px;
	margin-left: 44px;
	box-sizing: content-box;
	border-radius: 4px;
	color: #000000;
	cursor: pointer;
	font-weight: bold;
}

.apply-btn {
	background-color: #ffffff;
	border: none;
	min-width: 60px;
	height: 28px;
	line-height: 28px;
	font-size: 15px;
	text-align: center;
	padding: 7px 20px;
	box-sizing: content-box;
	margin-left: 44px;
	color: #000000;
	cursor: pointer;
	border-radius: 4px;
	font-weight: bold;
}

.btn:disabled {
	display: none;
}

.btn:active,
.btn:hover,
.apply-btn:active,
.apply-btn:hover,
.reset-btn:hover,
.loginbtn:hover {
	opacity: var(--opacity70);
}

.reset-btn {
	background-color: #ffffff;
	border: none;
	height: 34px;
	width: 200px;
	font-size: 14px;
	line-height: 34px;
	box-sizing: content-box;
	color: #000000;
	margin-bottom: 40px;
	cursor: pointer;
	margin-left: 0px;
	border-radius: 4px;
	font-weight: bold;
}

.col-box {
	display: flex;
	flex-direction: column;
}

.select-box {
	color: rgba(166, 166, 166, 1);
	font-size: 14px;
	display: flex;
	margin-top: 8px;
}

.select-box div {
	display: inline-block;
	margin-right: 25px;
}

.select-box input {
	width: 15px;
	height: 15px;
	border: solid 2px rgba(166, 166, 166, 1);
	border-radius: 50%;
	margin-right: 5px;
	cursor: pointer;
}

.select-box input:checked {
	background-color: white;
}

.wifi-name {
	font-size: 17px;
	color: #03b18c;
}

.th-header {
	color: white;
	border-bottom: 1px solid #efefef;
	padding: 12px 30px;
}

.th-header:first-child {
	text-align: left;
	padding-left: 72px;
}

.tr-row-focus,
.tr-row-focus:hover {
	background-color: rgba(142, 142, 142, 0.25);
	color: white;
}

.td-col {
	height: 30px;
	text-align: center;
	padding: 8px 35px;
}

#tablewifi {
	width: 100%;
	border-collapse: collapse;
	color: #a5a5a5;
}

#tablewifi,
#tablewifi tr,
#tablewifi tr td {
	border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.table-content {
	width: 100%;
	height: 90%;
	margin: 0 auto;
	overflow: hidden;
	overflow-y: scroll;
	-ms-overflow-style: none;
	scrollbar-width: none;
	box-sizing: border-box;
	background-color: rgba(0 0 0 / 50%);
}

.table-content::-webkit-scrollbar {
	display: none;
	/* width: 4px !important; */
}

.table-content::-webkit-scrollbar-thumb {
	border-radius: 15px;
	/* background-color: #00CED1; */
}

.table-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 10px;
	font-size: 17px;
}

tbody tr:hover {
	cursor: pointer;
	color: #fdfdfd;
	background-color: rgb(142 142 142 / 20%)
}

.p-enter,
.p-leave-to {
	opacity: 0;
}

.p-enter-active,
p-leave-active {
	transition: all 0.3s ease-out;
	-moz-transition: all 0.3s ease-out;
	-webkit-transition: all 0.3s ease-out;
	-o-transition: all 0.3s ease-out;
}

.align-self {
	align-self: flex-start;
	padding-left: 50px;
}

.mask {
	position: absolute;
	z-index: 80;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background-color: rgb(90 90 90 / 50%);
}

.login-tip {
	display: flex;
	justify-content: space-around;
	width: 350px;
	margin: 0 auto;
	color: #bdbdbe;
	font-size: 15px;
	margin-top: -30px;
	padding-bottom: 20px;
	text-align: right;
}

.login-to {
	color: #03B18C;
	font-size: 17px;
	font-weight: 700;
	background: none;
	padding: 6px;
	border: none;
	cursor: pointer;
}

.relbox {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 6px;
}

.loginput,
.loginbtn {
	width: 100%;
	height: 34px;
	font-size: 15px;
	text-align: center;
	padding: 2px 12px;
	box-sizing: border-box;
	border-radius: 6px;
	outline: none;
	border: none;
	background-color: rgb(255 255 255 / 10%);
	color: #d1d1d1;
}

.loginbtn {
	cursor: pointer;
	margin-top: 12px;
	/* width: 300px;
			height: 50px;
			font-size: 17.5px;
			padding: 12px;
			
			box-sizing: border-box;
			border-radius: 6px;
			border: none;
			outline: none;
			background: #ffffff;
			color: #000000;
			cursor: pointer;
			font-weight: bold; */
}

.ladeler {
	position: absolute;
	left: 15px;
	bottom: 14px;
	color: #929293;
	pointer-events: none;
	transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
}

.v-enter,
.v-leave-to {
	opacity: 0;
	transform: translateY(-150px);
}

.v-enter-active,
v-leave-active {
	transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
}

.main {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9999;
	width: 100vw;
	height: 100vh;
	box-sizing: border-box;
	background: rgba(0, 0, 0, 0.1);
	opacity: 1;
	text-align: center;
}

.loadEffect {
	width: 100px;
	height: 100px;
	position: relative;
	margin: 25% auto;
	border-radius: 50px;
	transform: scale(0.6);
}

.loadEffect span {
	display: inline-block;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	background: #00EBC0;
	position: absolute;
	animation: load 1.04s ease infinite;
	-webkit-animation: load 1.04s ease infinite;
}

@keyframes load {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0.2;
	}
}

@-webkit-keyframes load {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0.2;
	}
}

.loadEffect span:nth-child(1) {
	left: 0;
	top: 50%;
	margin-top: -8px;
	animation-delay: 0.13s;
	-webkit-animation-delay: 0.13s;
}

.loadEffect span:nth-child(2) {
	left: 14px;
	top: 14px;
	animation-delay: 0.26s;
	-webkit-animation-delay: 0.26s;
}

.loadEffect span:nth-child(3) {
	left: 50%;
	top: 0;
	margin-left: -8px;
	animation-delay: 0.39s;
	-webkit-animation-delay: 0.39s;
}

.loadEffect span:nth-child(4) {
	top: 14px;
	right: 14px;
	animation-delay: 0.52s;
	-webkit-animation-delay: 0.52s;
}

.loadEffect span:nth-child(5) {
	right: 0;
	top: 50%;
	margin-top: -8px;
	animation-delay: 0.65s;
	-webkit-animation-delay: 0.65s;
}

.loadEffect span:nth-child(6) {
	right: 14px;
	bottom: 14px;
	animation-delay: 0.78s;
	-webkit-animation-delay: 0.78s;
}

.loadEffect span:nth-child(7) {
	bottom: 0;
	left: 50%;
	margin-left: -8px;
	animation-delay: 0.91s;
	-webkit-animation-delay: 0.91s;
}

.loadEffect span:nth-child(8) {
	bottom: 14px;
	left: 14px;
	animation-delay: 1.04s;
	-webkit-animation-delay: 1.04s;
}

.bar {
	position: relative;
	height: 2px;
	width: 200px;
	background-color: #f5f5f5;
	overflow: hidden;
}

.progress {
	position: absolute;
	width: 200px;
	height: 2px;
	border-radius: 5px;
	background: #03B18C;
	top: 0;
	left: -200px;
}

@keyframes flash {

	0%,
	50%,
	100% {
		transform: translateY(0);
	}

	25%,
	75% {
		transform: translateY(-10px);
	}
}

@-webkit-keyframes flash {

	0%,
	50%,
	100% {
		transform: translateY(0);
	}

	25%,
	75% {
		transform: translateY(-10px);
	}
}

.FormPage {
	height: 100vh;
	min-width: 1024px;
	min-height: 800px;
	box-sizing: border-box;
	background-image: linear-gradient(0deg, rgb(17 24 27) 30%, rgb(35 70 74) 100%);
	-moz-background-image: linear-gradient(0deg, rgb(17 24 27) 30%, rgb(35 70 74) 100%);
	-ms-background-image: linear-gradient(0deg, rgb(17 24 27) 30%, rgb(35 70 74) 100%);
	-o-background-image: linear-gradient(0deg, rgb(17 24 27) 30%, rgb(35 70 74) 100%);
	-webkit-background-image: linear-gradient(0deg, rgb(17 24 27) 30%, rgb(35 70 74) 100%);
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	background-position: center center;
	background-size: cover;
}

.FormPagehide {
	display: none;
}

.top_navigation {
	width: 1000px;
	height: 60px;
	background-color: var(--backgruond_black_opacity_095);
	margin: 0 auto;
	display: flex;
	justify-content: space-between;
	box-sizing: border-box;
	backdrop-filter: blur(1.5px);
	-o-backdrop-filter: blur(1.5px);
	-moz-backdrop-filter: blur(1.5px);
	border-bottom: solid 2px rgb(21 21 21 / 80%);
}

.top_logo {
	margin-left: 35px;
	width: 150px;
	height: 60px;
	font-size: 22px;
	line-height: 60px;
	font-weight: bold;
}

.top_set_up,
.loginIcon {
	display: flex;
	align-items: center;
}

.top_set_up {
	margin-right: 25px;
}

.loginIcon {
	transform: translateX(320px);
	position: relative;
}

.loginIconsvg,
.SetIcon {
	cursor: pointer;
}

.is_play {
	opacity: 1 !important;
	background-color: #d4d4d426 !important;
	border-right: solid 1px #313131;
	transition: background-color .3s ease-in-out, opacity .2s ease-out;
	-o-transition: background-color .3s ease-in-out, opacity .2s ease-out;
	-webkit-transition: background-color .3s ease-in-out, opacity .2s ease-out;
	-moz-transition: background-color .3s ease-in-out, opacity .2s ease-out;
}

.is_play .preset_name {
	color: white;
}

.mind_PlayContent {
	height: 470px;
	position: relative;
	border-bottom: none !important;
	border-top: none !important;
	box-sizing: border-box;
	width: 1000px;
	overflow: hidden;
}

.Play_information {
	width: 380px;
	height: 470px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.Nowplay_list {
	width: 300px;
	height: 470px;
}

.Botm_playmodel {
	width: 100%;
	height: 70px;
}

.theBodyOfPlay {
	pointer-events: none;
	z-index: 1;
}

.preset_name {
	height: 45px;
	width: 155px;
	line-height: 45px;
	overflow: hidden;
	color: rgb(191, 191, 191);
	padding: 0 10px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
	font-size: 13px;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-weight: 400;
}

.slide_container {
	position: relative;
	width: 240px;
	height: 470px;
	transform: translateX(0px);
}

.Sound_Source,
.oneline_radio,
.Preset_list {
	width: 100%;
	height: 100%;
	transition: transform .3s ease-in-out, opacity .2s ease-in-out;
	-moz-transition: transform .3s ease-in-out, opacity .2s ease-in-out;
	-webkit-transition: transform .3s ease-in-out, opacity .2s ease-in-out;
	-o-transition: transform .3s ease-in-out, opacity .2s ease-in-out;
	position: absolute;
	top: 0;
	left: 0;
	background: rgb(18 18 18 / 90%);
	scrollbar-color: #585858 #fbfbff00;
	scrollbar-width: thin;
	box-sizing: border-box;
	opacity: 0;
}

.oneline_radio::-webkit-scrollbar {
	display: none;
}

.show_RightlisT {
	position: absolute;
	top: 0;
	left: -48px;
	display: flex;
	flex-direction: column;
	height: 100%;
	justify-content: center;
	transition: left .35s linear, opacity .3s ease-in;
	-webkit-transition: left .35s linear, opacity .3s ease-in;
	-o-transition: left .35s linear, opacity .3s ease-in;
	-moz-transition: left .35s linear, opacity .3s ease-in;
}

.show_RightlisT svg {
	width: calc(var(--icon_width18) + 4px);
	margin: 12px 0;
	cursor: pointer;
	color: rgb(var(--icon_color_gray));
}

.show_RightlisT svg:hover {
	color: rgb(var(--icon_color));
}

.show_RightlisTON {
	opacity: 0;
	left: -275px;
}

.NoMusic {
	color: rgba(255, 255, 255, 0.1) !important;
}

.play_infoBody {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	transition: width .25s linear, filter .2s linear;
	-moz-transition: width .25s linear, filter .2s linear;
	-webkit-transition: width .25s linear, filter .2s linear;
	-o-transition: width .25s linear, filter .2s linear;
}

.Song_picture {
	width: 275px;
	height: 275px;
	margin-top: 68px;
	border-radius: 15px;
	overflow: hidden;
	background-color: rgb(28, 33, 36);
	box-shadow: 0px 0px 35px 1px rgb(0 0 0 / 45%);

	display: flex;
	align-items: center;
	justify-content: center;
}

.IconMor {
	width: 150px;
	height: 150px;
	color: rgba(255, 255, 255, 0.35);
}

.Song_PictureA {
	width: inherit;
	height: inherit;
}

.Song_PictureA[src=""] {
	opacity: 0;
}

.Song_NameA {
	width: 280px;
	height: 28px;
	text-align: center;
	font-size: 18px;
	margin-top: 14px;
	font-weight: 600;
	white-space: nowrap;
	text-overflow: ellipsis;
	line-height: 28px;
	padding: 0 35px;
	box-sizing: border-box;
	overflow: hidden;
	text-shadow: 0 0 2px #000;
}

.Song_singerA {
	width: 280px;
	height: 20px;
	font-size: 14px;
	text-align: center;
	font-weight: 100;
	margin-top: 4px;
	white-space: nowrap;
	text-overflow: ellipsis;
	line-height: 20px;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}

.list_playName {
	width: 100%;
	height: 24px;
	margin: 64px 0 10px 0;
	color: rgba(255, 255, 255, 1);
	font-size: 20px;
	font-weight: 500;
	padding-left: 5px;
	box-sizing: border-box;
}

.list_mask {
	width: 760px;
	height: 470px;
	background: #00000000;
	position: absolute;
	top: 0;
	left: 0px;
	cursor: pointer;
	display: none;
	z-index: 1;
}

.show_listmask {
	display: block;
}

.player_body {
	width: 1000px;
	height: 600px;
	margin: 0 auto;
	box-shadow: 0px 2px 8px 2px #0000006e;
	overflow: hidden;
	position: relative;
	border-radius: 5px;
	z-index: 1;
}

.screen {
	position: absolute;
	right: 0px;
	top: 0px;
	height: 100%;
	width: 100%;
	background-color: rgb(0 0 0 / 40%);
}

.play_slidebody {
	position: absolute;
	top: 0;
	right: -240px;
}

.scrllo_playlist {
	width: 300px;
	height: 315px;
	overflow: hidden;
	overflow-y: scroll;
	padding: 12px 0px 0px 6px;
	box-sizing: border-box;
}

.scrllo_playlist::-webkit-scrollbar {
	width: 6px !important;
	display: block !important;
}

.scrllo_playlist::-webkit-scrollbar-thumb {
	background-color: var(--scroll_color);
	border-radius: 12px;
}

.nowplay_select {
	opacity: 1 !important;
	text-shadow: 0px 0px 2px #000000;
}

.nowplay_select .Song_NameB {
	color: rgb(var(--text_h1_color));
}

.nowplay_select .Song_singerB {
	color: rgb(var(--text_h2_color));
}

.Playlist_songs {
	width: 280px;
	margin-bottom: 12px;
	opacity: calc(var(--opacity70) - 0.1);
	cursor: pointer;
	max-height: 50px;
	display: flex;
}

.Playlist_songs:last-child {
	margin-bottom: inherit;
}

.Song_titleB {
	display: flex;
	flex-wrap: wrap;
	align-content: center;
}

.Playlist_songs:hover {
	opacity: 1;
}

.Playlist_songs:active {
	transform: scale(.99);
}

.song_PictureB {
	width: 50px;
	height: 50px;
	border-radius: 6px;
	box-shadow: 0px 0px 3px 1px rgb(0 0 0 / 10%);
	margin-right: 8px;
}

.dynamic_body {
	position: relative;
	margin-right: 4px;
}

.dynamic {
	position: absolute;
	top: 0px;
	left: 10px;
	width: 32px !important;
	height: 50px !important;
	display: none;
	justify-content: space-evenly;
	align-items: center;
	padding-top: 1px;
}

.show_palying {
	display: flex !important;
	/* transform: scale(1.15);
			margin-left: 3px;
			z-index: 2; */
}

.dynamic span {
	display: inline-block;
	width: 3px;
	background-color: #ffffffa8;
	border-radius: 15px;
	line-height: 100px;
}

.dynamic .s1 {
	animation: s1 .4s linear infinite;
	-webkit-animation: s1 .4s linear infinite;
	-moz-animation: s1 .4s linear infinite;
	-ms-animation: s1 .4s linear infinite;
	animation-direction: alternate-reverse;
}

.dynamic .s2 {
	animation: s2 .4s linear infinite;
	-webkit-animation: s2 .4s linear infinite;
	-moz-animation: s2 .4s linear infinite;
	-ms-animation: s2 .4s linear infinite;
	animation-direction: alternate-reverse;
}

.dynamic .s3 {
	animation: s3 .4s ease-in infinite;
	-webkit-animation: s3 .4s ease-in infinite;
	-moz-animation: s3 .4s ease-in infinite;
	-ms-animation: s3 .4s ease-in infinite;
	animation-direction: alternate-reverse;
}

@keyframes s1 {
	to {
		height: 20px;
	}

	form {
		height: 10px;
	}
}

@-webkit-keyframes s1 {
	to {
		height: 20px;
	}

	form {
		height: 10px;
	}
}

@-moz-keyframes s1 {
	to {
		height: 20px;
	}

	form {
		height: 10px;
	}
}

@-ms-keyframes s1 {
	to {
		height: 20px;
	}

	form {
		height: 10px;
	}
}

@keyframes s2 {
	to {
		height: 15px;
	}

	form {
		height: 25px;
	}
}

@-webkit-keyframes s2 {
	to {
		height: 15px;
	}

	form {
		height: 25px;
	}
}

@-moz-keyframes s2 {
	to {
		height: 15px;
	}

	form {
		height: 25px;
	}
}

@-ms-keyframes s2 {
	to {
		height: 15px;
	}

	form {
		height: 25px;
	}
}

@keyframes s3 {
	to {
		height: 10px;
	}

	form {
		height: 20px;
	}
}

@-webkit-keyframes s3 {
	to {
		height: 10px;
	}

	form {
		height: 20px;
	}
}

@-moz-keyframes s3 {
	to {
		height: 10px;
	}

	form {
		height: 20px;
	}
}

@-ms-keyframes s3 {
	to {
		height: 10px;
	}

	form {
		height: 20px;
	}
}

.Song_NameB {
	width: 180px;
	height: 22px;
	color: rgb(var(--text_color_gray));
	font-size: 15px;
	overflow: hidden;
	box-sizing: border-box;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-weight: 500;
}

.Song_singerB {
	width: 190px;
	height: 20px;
	color: rgb(var(--text_color_gray));
	font-size: 12px;
	font-weight: 100;
	overflow: hidden;
	box-sizing: border-box;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.playing {
	opacity: 1 !important;
	border-radius: 4px;
}

.palys_jindu {
	width: 1000px;
	height: 70px;
	background-color: var(--backgruond_black_opacity_095);
	box-sizing: border-box;
	box-shadow: 0px -5px 80px 15px #000000bf, -6px 0px 6px 0px #00000000, 6px 0px 6px 0px #00000000, 0px 5px 19px 0px #00000000;
	backdrop-filter: blur(1.5px);
	-o-backdrop-filter: blur(1.5px);
	-moz-backdrop-filter: blur(1.5px);
	border-top: solid 2px rgb(21 21 21 / 80%);
	transition: background-color .5s linear;
	/* background-image: repeating-linear-gradient(90deg, #0000008a, #0000003d 3px); */
}

.lsit_mask {
	width: 777px;
	height: 540px;
	background: #00000000;
	position: absolute;
	top: 0;
	left: -1001px;
	cursor: pointer;
}

.Fuzzy_background {
	width: 1000px !important;
	height: 1000px !important;
	position: absolute;
	top: -180px;
	left: 0;
	filter: blur(12px);
	transform: scale(1.05);
}

.Fuzzy_background img {
	width: 100%;
	height: 100%;
	transform: translateY(-30px);
}

.paly_btn {
	position: absolute;
	left: calc(50% - 140px);
	width: 280px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding-top: 6px;
}

.playing_music {
	display: inline-block !important;
}

.loading_music {
	display: flex !important;
}

.loading span {
	width: 2px;
}

.top_palys {
	height: 56px;
	width: 100%;
	position: relative;
}

.play_btnbody {
	height: 56px;
}

.play_btnbody svg {
	cursor: pointer;
}

.jindutiao {
	display: flex;
	justify-content: center;
}

.progress_bar {
	width: 400px;
	height: 2px;
	position: relative;
	border-radius: 4px;
}

.progress_bar>div {
	padding: 0 8px;
}

.Completed_bar {
	position: absolute;
	top: -1px;
	left: 0;
	width: 100%;
	height: 3px;
	background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) no-repeat, rgba(255, 255, 255, 0.4);
	background-size: 0% 100%;
	border-radius: 4px;
	cursor: pointer;
}

.volume_,
.Completed_bar {
	box-shadow: 0 0 1px 1px #0a0a0a80;
}

.Completed_bar:hover,
.volume_:hover {
	opacity: var(--opacity70);
}

.progress_bar .Completed_bar::-webkit-slider-thumb {
	height: 5px;
	width: 5px;
	background-color: rgba(0, 0, 0, 0);
}

input[type="range"]::-moz-range-thumb {
	width: 14px;
	height: 14px;
	background-color: #fdfdfd;
	border-radius: 50%;
	cursor: pointer;
	border: none;
}

input[type="range"]::-moz-range-thumb:hover {
	border: solid 1px rgb(75 255 202) !important;
}

.scrllo_playlist,
.scorllInfo,
.addText_content,
.SwitchForm {
	scrollbar-color: #535353 #f7f7f900;
	scrollbar-width: thin;
}

.Radio_Content,
.userRadiobox,
.filtersUl,
.Radio_Content {
	scrollbar-width: none;
}

.Completed_bar::-moz-range-thumb,
.volume_::-moz-range-thumb {
	border: none !important;
	height: 0px !important;
	width: 0px !important;
	background-color: transparent !important;
	background-position: 0 0 !important;
	background-size: cover !important;
	cursor: pointer !important;
}

.Completed_bar:focus,
.volume_:focus,
.searchGenreput:focus,
.treble_slider:focus,
.medium_slider:focus,
.bass_slider:focus,
.allbtn_body>button:focus,
.play_urlContent input:focus,
.balance_range:focus,
.MaxVolume_range:focus {
	outline: none;
}

.fenzu img {
	width: 20px;
	height: 20px;
	transition: transform 1s ease-in-out;
	-webkit-transition: transform 1s ease-in-out;
	-moz-transition: transform 1s ease-in-out;
	-o-transition: transform 1s ease-in-out;
	cursor: pointer;
}

.fenzu img:hover {
	transform: rotate(360deg) scale(1.05);
	opacity: 1;
}

.right_elm {
	width: 135px;
	height: 31px;
	position: absolute;
	top: 0;
	right: 0;
	display: flex;
	align-items: center;
	padding-top: 23px;
}

.vol__ {
	display: flex;
	align-items: center;
}

.volume {
	width: 22px;
	margin-right: 4px;
	height: 100%;
	display: flex;
	align-items: center;
}

.vol_table {
	width: 80px;
	height: 2px;
	background-color: rgba(255, 255, 255, 0.4);
	border-radius: 4px;
}

.volume_pas {
	width: 50px;
	height: 2px;
	background-color: rgba(255, 255, 255, 1);
	border-radius: 4px;
}

.List_content,
.sourceTitle {
	margin: 22px 0 0 0;
	font-size: 18px;
	padding-left: 14px;
	height: 30px;
	color: rgba(255, 255, 255, 1);
	font-weight: 300;
}

.url_playtitile {
	margin-bottom: 6px;
}

.list_table {
	display: flex;
	align-items: center;
	cursor: pointer;
	opacity: var(--opacity70);
	border-radius: 4px;
	margin-bottom: 12px;
	background-color: var(--grayinputColor);
	position: relative;
}

.list_table:active,
.Public_type_body:active {
	transform: scale(.99);
}

.list_table:nth-last-child(1) {
	margin-bottom: 0px !important;
}

.list_table:hover {
	opacity: 1;
	background-color: rgb(55, 55, 55);
}

.list_table img {
	width: 45px;
	height: 45px;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
	box-shadow: 0px 0px 8px 2px rgb(0 0 0 / 35%);
}

.preset_scrollview {
	padding: 12px;
	overflow-y: scroll;
	height: 420px;
	box-sizing: border-box;
	scrollbar-color: #535353 #f7f7f900;
	scrollbar-width: none;
}

.preset_scrollview::-webkit-scrollbar {
	display: none;
}

.volume_ {
	width: 80px;
	height: 3px;
	border-radius: 12px;
	background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) no-repeat, rgba(255, 255, 255, 0.4);
	background-size: 0% 100%;
	cursor: pointer;
}

.volume_:active,
.Completed_bar:active {
	opacity: 1;
}

.volume_::-webkit-slider-thumb {
	height: 5px;
	width: 5px;
	background-color: rgba(255, 255, 255, 0);
}

.perv svg,
.next svg {
	width: calc(var(--icon_width18) - 0.5px);
}

.loop svg {
	width: calc(var(--icon_width18) + 0.5px);
}

.random svg {
	width: calc(var(--icon_width18) - 0.1px);
}

.loop {
	transform: translateX(18px);
}

.random {
	transform: translateX(-18px);
}

.showloop {
	display: inline-block !important;
}

.noRandom {
	color: rgb(80, 80, 80);
}

.sourceContent {
	padding: 0px 10px;
	text-align: center;
	margin: 12px;
	font-size: 14px;
	color: rgb(120, 120, 120);
	height: 45px;
	line-height: 45px;
	width: 90%;
	box-sizing: border-box;
	border-radius: 4px;
	opacity: var(--opacity70);
}

.sourceContent:hover {
	cursor: pointer;
	background-color: #71717135;
}

.sourceContent:active {
	transform: scale(.98);
}

.sourceSelect,
.sourceSelect:hover {
	color: rgb(255, 255, 255);
	background: #5e5e5e7d;
	opacity: 1;
}

.playsOrstop {
	position: relative;
}

.playsOrstop img {
	display: none;
	width: 27px;
	height: 27px;
}

.load {
	position: relative;
}

.loading {
	top: -5px !important;
	left: 6px !important;
	width: 28px !important;
}

.loading span {
	background-color: #141414e5 !important;
}

.boxdy {
	position: absolute;
	top: 0px;
	left: 0px;
}

.Equalizer {
	position: relative;
}

.Equalizer_body {
	width: 280px;
	position: absolute;
	bottom: 8px;
	right: 8px;
	background-color: rgb(26 27 28);
	border-radius: 10px;
	padding: 10px;
	z-index: 2;
	display: none;
	animation: showEQbody .3s ease-in-out 1;
	box-shadow: 1px 0px 22px #00000090;
	;
}

@keyframes showEQbody {
	from {
		opacity: 0;
		transform: translateY(200px);
	}

	to {
		opacity: 1;
		transform: translateY(0px);
	}
}

.Equalizer_conten {
	position: absolute;
	top: -2px;
	left: -3px;
	width: 20px;
	height: 20px;
	background-color: #1c212400;
	cursor: pointer;
}

.eqmask {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 1;
	display: none;
	cursor: pointer;
	background-color: #0003;
	transition: backdrop-filter .3s ease-out;
	-o-transition: backdrop-filter .3s ease-out;
	-moz-transition: backdrop-filter .3s ease-out;
	-webkit-transition: backdrop-filter .3s ease-out;
	backdrop-filter: blur(0px);
	-moz-backdrop-filter: blur(0px);
	-o-backdrop-filter: blur(0px);
}

.eqmaskOn {
	backdrop-filter: blur(2px);
	-moz-backdrop-filter: blur(2px);
	-o-backdrop-filter: blur(2px);
}

.balancer {
	text-align: center;
	color: white;
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 12px;
}

.Trebleclass {
	background-color: rgb(28, 33, 36);
	border-radius: 4px;
	box-shadow: 0 0 5px #00000030;
	padding: 16px 0 2px 0;
}

.treble {
	margin-top: initial !important;
}

.treble,
.medium,
.bass {
	width: 90%;
	margin: 3% 5%;
	height: 40px;
	color: white;
	font-size: 13px;
	position: relative;
}

.bass {
	margin-bottom: 8px;
}

.treble_rangeContent,
.medium_rangeContent,
.bass_rangeContent {
	transform: translateY(-4px);
	position: relative;
	z-index: 1;
}

.treble_slider,
.medium_slider,
.bass_slider {
	top: 0px;
	left: 0px;
	width: 100%;
	height: 2px;
	border-radius: 4px;
	background-color: rgb(61 61 61 / 50%);
	z-index: 2;
	cursor: pointer;
}

.slider_thumb::-webkit-slider-thumb {
	width: 12px;
	height: 12px;
	background: #ffffff;
	border-radius: 50%;
	transition: transform .1s ease-in-out;
	-webkit-transition: transform .1s ease-in-out;
	-moz-transition: transform .1s ease-in-out;
	-o-transition: transform .1s ease-in-out;
}

.slider_thumb::-webkit-slider-thumb:hover {
	transform: scale(1.18);
	border: solid 1px rgb(75 255 202);
}

.treble_tc,
.medium_tc,
.bass_tc {
	width: 0px;
	height: 2px;
	background-color: rgb(75 255 202);
	position: relative;
	top: 14px;
	left: 0px;
	transform: rotate(180deg);
	transform-origin: left;
	margin-left: 126px;
	z-index: -1;
}

.treble_tcright,
.medium_tcrihgt,
.bass_tcright {
	width: 0px;
	height: 2px;
	background-color: rgb(75 255 202);
	position: relative;
	top: -5px;
	right: 0px;
	transform-origin: left;
	margin-left: 126px;
	z-index: -1;
}

.deppbass,
.eqmode {
	width: 100%;
	height: 40px;
	border-radius: 4px;
	background-color: rgb(28, 33, 36);
	box-shadow: 0 0 5px #00000030;
	margin-top: 8px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 14px;
	box-sizing: border-box;
	font-size: 13px;
	color: white;
}

.theBodyOfPlayIStrue {
	pointer-events: inherit !important;
}

.loginIconDisable {
	opacity: 0.1;
	pointer-events: none !important;
}

.bass_btn,
.SharedBass_btn,
.bass_btn2 {
	width: 16px;
	height: 16px;
	background: rgb(255, 255, 255);
	display: inline-block;
	border-radius: 50%;
	position: absolute;
	top: 2px;
	left: 2px;
	transition: left .24s ease-in-out;
	-webkit-transition: left .24s ease-in-out;
	-moz-transition: left .24s ease-in-out;
	-o-transition: left .24s ease-in-out;
	box-shadow: 0 0 2px 1px #3f3f3f4a;
}

.MaxVolume,
.balance {
	width: 100%;
	height: 60px;
	background-color: rgb(28, 33, 36);
	box-shadow: 0 0 5px #00000030;
	border-radius: 4px;
	margin-top: 8px;
	padding-top: 10px;
	box-sizing: border-box;
	font-size: 13px;
	color: white;
}

.SharedBass {
	margin-top: 1px;
}

.Switch_bass,
.SharedBass,
.Switch_bass2 {
	width: 40px;
	height: 20px;
	border-radius: 15px;
	background-color: rgba(57, 62, 66, 1);
	cursor: pointer;
	position: relative;
	transition: background-color .3s ease-in-out, transform .2s ease-in-out;
	-moz-transition: background-color .3s ease-in-out, transform .2s ease-in-out;
	-webkit-transition: background-color .3s ease-in-out, transform .2s ease-in-out;
	-o-transition: background-color .3s ease-in-out, transform .2s ease-in-out;
	box-shadow: 0px 0px 2px 1px #1d1d1d;
}

.Switch_bass2 {
	box-shadow: 0px 0px 2px 1px #2b2b2b5e !important;
	backdrop-filter: blur(1px);
	opacity: .9;
}

.gaoliang {
	background-color: rgb(91 108 117 / 35%);
	box-shadow: 0 0 1px 1px #00000060 inset;
	color: white !important;
}

.gaoliang:hover {
	background-color: rgb(91 108 117 / 35%);
	color: white !important;
}

.switchon {
	background-color: rgb(62, 162, 133);
}

.switchon>span {
	left: 22px;
}

.Switch_bass2 .sharedTrue {
	background-color: rgb(62, 162, 133);
}

.sharedTruebas {
	left: 22px;
}

.ChooseShare {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.curpos,
.totlen {
	font-size: 12px;
	color: rgb(199, 199, 199);
	transform: translateY(-8px);
	width: 50px;
	text-align: center;
	overflow: hidden;
	text-overflow: clip;
	white-space: nowrap;
}

.MaxVolume_top,
.balance_top {
	padding: 0 14px;
	display: flex;
	justify-content: space-between;
}

.MaxVolume_bottom,
.balance_bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px 0 12px;
	margin-top: 8px;
}

.balance_bottom {
	padding: 0 14px !important;
}

.MaxVolume_range {
	width: 225px;
	height: 2px;
	background-color: rgb(61 61 61 / 50%);
	border-radius: 4px;
	cursor: pointer;
}

.disableStoping:hover {
	opacity: 1 !important;
	cursor: no-drop;
}

.balance_bottom span {
	font-size: 12px;
}

.balance_range {
	width: 220px;
	height: 2px;
	background-color: rgb(61 61 61 / 35%);
	border-radius: 4px;
	cursor: pointer;
}

.Switch_bass:hover,
.SharedBass:hover {
	opacity: var(--opacity70);
}

.MaxVolume_Content,
.balance_Content {
	position: relative;
	display: flex;
	z-index: 2;
	position: relative;
}

.MaxVolume_range {
	z-index: 2;
}

.MaxVolume_Content::after {
	content: '';
	position: absolute;
	width: 70px;
	height: 2px;
	background-color: rgb(34, 34, 34);
	z-index: 1;
}

.MaxVolume_tc {
	position: absolute;
	top: 0;
	left: 0px;
	width: 0px;
	height: 2px;
	background-color: rgb(75 255 202);
	z-index: -1;
}

.balance_tc {
	position: absolute;
	top: 0px;
	left: 0px;
	width: 6px;
	height: 2px;
	background-color: rgb(75 255 202);
	transform: rotate(180deg);
	transform-origin: left;
	margin-left: 110px;
	z-index: -1;
}

.balance_tcright {
	width: 0px;
	height: 2px;
	background-color: rgb(75 255 202);
	position: absolute;
	top: 0px;
	left: 110px;
	transform-origin: left;
	z-index: -1;
}

.login_mask,
.loginOut_mask {
	width: 100%;
	height: calc(100% - 60px);
	background-color: rgba(0, 0, 0, 0.35);
	position: absolute;
	top: 60px;
	left: 0px;
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
	display: none;
}

.loginIconsvg {
	width: 22px;
}

/* 弹窗Class */
.login_page,
.reset_page {
	height: 200px;
	padding: 0px !important;
	transform: translateY(-35px);
}

/* 1. */
.loginOut_page,
.loginOut_popUp,
.login_page,
.reset_page,
.login-wifi,
.qrDelete {
	width: 260px;
	background-color: rgb(12 12 12 / 75%);
	border-radius: 8px;
	box-sizing: border-box;
	padding: 12px;
	backdrop-filter: blur(14px);
	box-shadow: 0 0 8px 0 #00000035;
}

.login-wifi {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: 320px auto;
	position: relative;
	width: 230px !important;
	padding: 18px;
	animation: showLoginwifi .2s ease-in-out 1;
}

@keyframes showLoginwifi {
	from {
		opacity: 0;
		margin: 300px auto;
		transform: rotateX(-20deg);
	}

	to {
		opacity: 1;
		transform: scale(1);
		margin: 320px auto;
		transform: rotateY(0deg);
	}
}

.logintitle {
	color: white;
	font-size: 16px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	width: 80%;
	margin: 8px 0 24px 0;
	text-align: center;
}

.closein {
	position: absolute;
	right: 2px;
	top: 2px;
	background: none !important;
	border: none !important;
	font-size: 22px;
	color: #ffffff91;
	cursor: pointer;
	width: 20px;
	height: 20px;
}

.closein:hover {
	opacity: .8;
}

/* 2. */
.login_page {
	animation: showlogin_page .2s ease-in-out 1;
}

.loginOut_page {
	animation: showloginOut_page .2s ease-in-out 1;
}

.loginOut_popUp {
	transform: translateY(-30px);
	opacity: 0;
	transition: transform .2s ease-in-out, opacity .3s ease-in-out;
}

@keyframes showloginOut_page {
	from {
		opacity: 0;
		transform: rotateX(-30deg) translateY(-35px);
	}

	to {
		opacity: 1;
		transform: rotateX(0deg) translateY(-0px);
	}
}

@keyframes showlogin_page {
	from {
		opacity: 0;
		transform: rotateX(-30deg) translateY(-60px);
	}

	to {
		opacity: 1;
		transform: rotateX(0deg) translateY(-35px);
	}
}

.login_title {
	text-align: center;
	font-size: 20px;
	color: rgba(255, 255, 255, 1);
	margin-top: 30px;
}

/*  */
.loginOutTitle,
.loginOut_title,
.qrTitle {
	text-align: left;
	font-size: 16px;
	font-weight: 100;
	color: white;
	text-transform: none;
}

.loginOutTitle {
	margin-bottom: 28px;
}

/*  */
.loginOut_btn,
.outBtnPage,
.reset_btnpage {
	display: flex;
	justify-content: space-between;
	padding-left: 45%;
	margin-top: 12px;
}

.loginOut_btn>button,
.outBtnPage>button,
.reset_btnpage>button {
	width: 60px;
	height: 28px;
	font-size: 12px;
	border: none;
	background-color: rgb(255 255 255 / 10%);
	color: #c1c1c1;
	border-radius: 4px;
	cursor: pointer;
}

.loginOut_btn>button:hover,
.outBtnPage>button:hover,
.login_btn button:hover {
	background-color: rgb(255 255 255 / 20%);
	color: #dad8d8;
}

.outBtnPage>button:active,
.loginOut_btn>button:active,
.login_btn button:active,
.loginbtn:active {
	transform: scale(0.95);
}

/*  */
.login_input {
	text-align: center;
	margin-top: 38px;
	position: relative;
}

.login_input input {
	width: 80%;
	height: 34px;
	font-size: 14px;
	text-align: left;
	box-sizing: border-box;
	border-radius: 4px;
	border: none;
	background: rgb(250 250 250 / 10%);
	outline: none;
	color: white;
	/* user-select: text !important; */
}

.login_btn {
	text-align: center;
}

.login_btn button {
	width: 80%;
	height: 34px;
	font-size: 14px;
	margin-top: 12px;
	margin-bottom: 0px;
	box-sizing: border-box;
	border-radius: 4px;
	border: none;
	outline: none;
	background-color: rgb(255 255 255 / 10%);
	color: #d1d1d1;
	cursor: pointer;
}

.iconfont_eyas {
	color: white;
	position: absolute;
	top: 7px;
	right: 25px;
	transform: scale(1);
	cursor: pointer;
}

.message_password {
	width: 99.8%;
	height: 50px;
	background-color: rgb(34, 34, 34);
	/* border: solid 1px #d4d4d4; */
	position: absolute;
	bottom: -60px;
	left: 1px;
	text-align: center;
	color: white;
	font-size: 18px;
	line-height: 50px;
	transition: bottom .3s ease-in-out;
	-webkit-transition: bottom .3s ease-in-out;
	-moz-transition: bottom .3s ease-in-out;
	-o-transition: bottom .3s ease-in-out;
}

.reset_mask {
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 60px;
	left: 0px;
	width: 100%;
	height: calc(100% - 60px);
	background-color: #25252527;
	z-index: 9999;
	display: none;
}

.reset_page {
	height: 100px;
	padding: 12px !important;
	animation: showreset_page .2s ease-in-out 1;
}

@keyframes showreset_page {
	from {
		opacity: 0;
		transform: translateY(-60px);
	}

	to {
		opacity: 1;
		transform: translateY(-35px);
	}
}

.reset_message {
	text-align: left;
	font-size: 16px;
	font-weight: 100;
	color: white;
	text-transform: none;
	margin-bottom: 26px;
}

.play_urlContent {
	display: flex;
	padding: 0 6px;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	user-select: text !important;

}

.play_urlContent input {
	border: none;
	background-color: #52525240;
	width: 70%;
	height: 42px;
	margin: 6px 0;
	color: rgb(191 191 191);
	font-size: 13px;
	border-top-left-radius: 5px;
	border-bottom-left-radius: 5px;
	padding-left: 10px;
	user-select: text !important;
}

.play_urlContent div {
	width: 20%;
	height: 42px;
	line-height: 42px;
	text-align: center;
	border-top-right-radius: 5px;
	border-bottom-right-radius: 5px;
	background-color: #52525240;
	color: rgb(191 191 191);
	;
	font-size: 13px;
	cursor: pointer;
}

.play_urlContent div:hover {
	color: rgb(255, 255, 255);
}

.url_scrollview {
	overflow-y: scroll;
	height: 420px;
}

.url_scrollview::-webkit-scrollbar {
	display: none;
}

.iemask {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background-image: linear-gradient(0deg, rgba(28, 33, 36, 1) 30%, rgba(26, 62, 66, 1) 100%);
	z-index: 99999;
	display: none;
}

.iemessage_body {
	position: absolute;
	width: 340px;
	background-color: rgba(12, 12, 12, .9);
	top: 50%;
	left: 50%;
	margin-left: -250px;
	margin-top: -110px;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	padding: 18px;
	border: solid 4px rgba(167, 166, 166, 0.2);
}

.ie_content {
	font-size: 16px;
	box-sizing: border-box;
	border-radius: 8px;
	margin-top: 12px;
}

.iemessage_body span {
	text-align: center;
	font-size: 24px;
	color: white;
}

.iemessage_body div {
	text-align: center;
	color: white;
	width: 100%;
}

.click_playbody {
	width: 40px;
	height: 40px;
	margin-bottom: 6px;
}

.click_playbody svg {
	width: 100%;
	height: 100%;
}

.click_playbody:hover {
	transform: scale(1.06);
	transition: transform .1s ease-in-out;
}

.eqbtn {
	width: 20px;
	height: 20px;
	position: absolute;
	top: 28px;
	right: 150px;
}

.eqbtn svg {
	width: calc(var(--icon_width18) + 1.5px);
	height: calc(var(--icon_width18) + 1.2px);
}

.eqbtn svg:hover,
.volume svg:hover,
.top_set_up svg:hover,
.click_playbody svg:hover,
.icon_open>svg:hover {
	color: rgb(var(--icon_color_gray));
}

.perv svg:hover,
.next svg:hover {
	opacity: .6;
}

.random svg:hover,
.loop svg:hover {
	opacity: var(--opacity70);
}

#icon-no_loop {
	color: rgb(80, 80, 80);
}

.eqbtn svg:active,
.volume svg:active,
.paly_btn svg:active,
.show_RightlisT svg:active,
.volume svg:active,
.top_set_up svg:active,
.click_playbody svg:active,
.icon_open>svg:active,
.loginIcon>svg:active {
	transform: var(--icon_active);
}

.PrivacyPolicy_mask {
	position: absolute;
	background-color: #0000005c;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9;
	display: none;
	align-items: center;
	justify-content: center;
	transition: all .3s ease-in;
	-moz-transition: all .3s ease-in;
	-o-transition: all .3s ease-in;
	-webkit-transition: all .3s ease-in;
	backdrop-filter: blur(2px);
	-moz-backdrop-filter: blur(2px);
	-o-backdrop-filter: blur(2px);
	-webkit-backdrop-filter: blur(2px);
}

.PrivacyPolicy_body {
	width: 560px;
	height: 350px;
	background: #fff;
	border-radius: 8px;
	padding: 10px;
	box-sizing: border-box;
	overflow: hidden;
	animation: showPrivacyPolicy .2s ease-in-out 1;
}

@keyframes showPrivacyPolicy {
	from {
		opacity: 0;
		transform: rotateX(-25deg) translateY(-40px);
	}

	to {
		opacity: 1;
		transform: rotateX(0deg) translateY(0px);
	}
}

.pri_left {
	display: flex;
	align-items: center;
}

.pri_right {
	display: flex;
}

.pri_btn {
	width: 80px;
	height: 32px;
	margin-left: 10px;
	border-radius: 4px;
	text-align: center;
	line-height: 32px;
	font-size: 13px;
	font-weight: 400;
}

.pri_btn:active {
	transform: scale(.95);
}

.pri_btn:hover {
	opacity: .8;
	cursor: pointer;
}

.btn_yesFalse {
	opacity: .4;
	pointer-events: none;
}

.pri_btn_yes {
	background-color: rgba(229, 229, 229, 1);
	color: rgb(66, 66, 66);
}

.pri_close {
	color: #fff;
	background-color: #ff0000a8;
	background-image: linear-gradient(115deg, rgb(114 43 43 / 20%), rgb(109 9 9 / 20%));
}

.pri_disabled {
	opacity: .5;
}

.Privacy_Title {
	height: 24px;
	text-align: center;
	line-height: 24px;
	font-size: 18px;
	font-weight: bold;
	color: #111;
	margin-bottom: 5px;
}

#Privacy_content {
	width: 100%;
	height: 260px;
	overflow-y: scroll;
}

#Privacy_content::-webkit-scrollbar {
	appearance: none;
	-o-appearance: none;
	-moz-appearance: none;
	-webkit-appearance: none;
	width: 5px;
}

#Privacy_content::-webkit-scrollbar-thumb {
	background-color: #ccc;
	border-radius: 12px;
}

.iframes {
	width: 100%;
	height: 300%;
}

.Privacy_bottom {
	display: flex;
	height: 32px;
	align-items: center;
	justify-content: flex-end;
	margin-top: 8px;
}

.pri_click {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	border: solid 1px rgba(166, 166, 166, 1);
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	margin-top: 3px;
}

.pri_yuan {
	height: 8px;
	width: 8px;
	background-color: rgba(166, 166, 166, 1);
	border-radius: 50%;
}

.pri_title {
	font-size: 14px;
	margin-left: 5px;
	padding-top: 1px;
	color: rgba(166, 166, 166, 1);
}

.switch_radioBody {
	display: flex;
	position: relative;
	font-weight: 100;
	letter-spacing: 0.5px;
}

.switch_radioBody div {
	width: 50%;
	height: 45px;
	text-align: center;
	line-height: 45px;
	color: rgb(122, 122, 122);
	opacity: var(--opacity70);
	font-size: 15px;
	cursor: pointer;
	z-index: 2;
	transition: color .15s cubic-bezier(0.44, 0.12, 0.63, 0.87);
	-moz-transition: color .15s cubic-bezier(0.44, 0.12, 0.63, 0.87);
	-webkit-transition: color .15s cubic-bezier(0.44, 0.12, 0.63, 0.87);
	-o-transition: color .15s cubic-bezier(0.44, 0.12, 0.63, 0.87);
}

.p_title_background {
	position: absolute;
	top: 43px;
	left: 12px;
	width: 89%;
	height: 2px;
	box-sizing: border-box;
	z-index: 1;
}

.showRadios {
	color: #fff !important;
	opacity: 1 !important;
}

.Radio_body {
	padding: 12px 12px 0 12px;
	height: 413px;
	width: 216px;
	overflow: hidden;
	transition: opacity .15s ease-in-out;
}

.nav_body {
	height: 35px;
	margin-bottom: 12px;
}

.search {
	width: 100%;
	background: rgba(26, 30, 31, 1);
	outline: none;
	border: none;
	height: 30px;
	border-radius: 20px;
	box-sizing: border-box;
	color: rgba(128, 128, 128, 1);
	text-align: center;
	font-size: 14px;
}

.Public_Radio_body {
	overflow: hidden;
	height: 366px;
}

.Public_type_body {
	height: 45px;
	display: flex;
	margin-bottom: 12px;
	cursor: pointer;
	opacity: calc(var(--opacity70) + .05);
	background-color: var(--grayinputColor);
	border-radius: 4px;
	position: relative;
}

.Activated{
	align-items: center;
	color: gray;
	font-size: 12px;
	text-transform: none;
	padding: 10px;
}

.Public_type_body:hover {
	opacity: 1;
	background-color: #40404080;
}

.Public_type_body .Public_Radio_image {
	max-width: 45px;
	max-height: 45px;
	min-width: 45px;
	height: 45px;
	width: 45px;
	margin-right: 10px;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}

.Public_text {
	display: flex;
	flex-direction: column;
	width: 65%;
	line-height: 45px;
	font-size: 14px;
	color: white;
}

.Public_text>div {
	width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.Private_Radio_login_body>input {
	width: 100%;
	height: 38px;
	margin-bottom: 12px;
	padding: 10px;
	box-sizing: border-box;
	border-radius: 4px;
	border: none;
	outline: none;
	background-color: var(--grayinputColor);
	color: white;
	font-size: 14px;
	text-transform: none;
}

.Private_Radio_login_body>input::placeholder {
	color: #999;
}

.Private_Radio_login_body>input:focus::placeholder {
	color: rgb(90, 90, 90);
}

.Switch_bar {
	height: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 7px;
	font-size: 12px;
	color: #999;
	margin-bottom: 120px;
}

.Switch_bar span:hover {
	cursor: pointer;
	opacity: var(--opacity70);
}

.allbtn_body {
	display: flex;
}

.login_btn_body {
	justify-content: space-between;
}

.login_btn_body>button {
	width: 100%;
}

.Register_btn_body,
.reset_btn_body {
	justify-content: space-between;
	margin-top: 44px;
}

.Register_btn_body>button,
.reset_btn_body>button {
	width: 47%;
}

.allbtn_body>button {
	height: 38px;
	background-color: var(--grayinputColor);
	color: white;
	border: none;
	border-radius: 4px;
	font-size: 14px;
	font-weight: 100;
}

.allbtn_body>button:hover {
	opacity: var(--opacity70);
	cursor: pointer;
}

.allbtn_body>button:active {
	transform: scale(0.95);
}

.disableSubmit {
	opacity: .3;
	cursor: auto !important;
}

.disableSubmit:hover {
	opacity: .3 !important;
}

.disableSubmit:active {
	transform: scale(1) !important;
}

.genreContent {
	display: flex;
	justify-content: space-evenly;
	background-color: var(--grayinputColor);
	border-radius: 4px;
}

.genreContent>div {
	font-size: 12.5px;
	padding: 0px 8px;
	height: 35px;
	line-height: 35px;
	color: #ffffffb3;
	z-index: 2;
	font-weight: 100;
}

.filterName>span {
	width: 58px;
	cursor: pointer;
	display: inline-block;
	text-align: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.genreContent>div:hover {
	color: rgb(230, 230, 230);
}

input::selection {
	background-color: rgba(var(--text_selcetionStyle));
}

.huakuai {
	width: 50px;
	background-color: #ebeaea;
	display: inline-block;
	height: 2px;
	position: absolute;
	left: 24px;
	border-radius: 20px;
	transition: width .15s cubic-bezier(0.55, 0.09, 0.68, 0.53), left .2s ease-in-out;
	-o-transition: width .15s cubic-bezier(0.55, 0.09, 0.68, 0.53), left .2s ease-in-out;
	-moz-transition: width .15s cubic-bezier(0.55, 0.09, 0.68, 0.53), left .2s ease-in-out;
	-webkit-transition: width .15s cubic-bezier(0.55, 0.09, 0.68, 0.53), left .2s ease-in-out;
}

.huakuaiRight {
	width: 75px;
	left: 130px;
}

.filterName {
	position: relative;
}

.filterName>div {
	position: absolute;
    background-color: rgb(56 56 56 / 70%);
    display: none;
    border-radius: 4px;
    color: white;
    max-height: 220px;
    max-width: 100px;
    min-width: 100px;
    font-size: 12px;
    overflow-y: scroll;
    text-align: center;
    top: 32px;
    box-shadow: 0px 1px 2px 1px rgb(0 0 0 / 35%);
    backdrop-filter: blur(9px);
    -moz-backdrop-filter: blur(12px);
    -o-backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    animation: showGener .3s ease-in-out 1;
    z-index: 9;
}

/*Firefox*/
@-moz-document url-prefix() {
	.filterName>div::after {
		content: '';
		top: 0;
		left: 0;
		position: absolute;
		width: 100%;
		height: 100%;
		background-color: rgba(44, 44, 44, 0.9);
		filter: blur(1px);
		z-index: -1;
	}
}

@keyframes showGener {
	from {
		opacity: 0;
		transform: translateY(-10px);
	}

	to {
		opacity: 1;
		transform: translateY(0px);
	}
}

.filterName>div::-webkit-scrollbar {
	display: none;
}

.Radio_Content {
	height: 366px;
	overflow-y: scroll;
	overflow-x: hidden;
}

.showFilter {
	display: block !important;
}

.filtersUl ol {
	height: 22px;
	line-height: 22px;
	padding: 4px 10px 4px 10px;
	cursor: pointer;
	max-width: 98px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	color: #ffffff;
}

.filtersUl ol:hover {
	background-color: #727272;
}

.filterEmpty {
	padding: 0 12px;
	border: solid 1px #000000ab;
}

.isgenre>div {
	left: 10px;
}

.iscountry>div {
	right: 10px;
}

.loginBtn_body {
	display: flex;
	height: 100%;
	align-items: center;
	justify-content: space-between;
	padding: 0 2px;
	transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-o-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-moz-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-webkit-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
}

.icon_content {
	display: flex;
	justify-content: center;
	align-items: center;
}

.icon_content>svg {
	width: calc(var(--icon_width18) + 0.6px);
	color: rgb(var(--icon_color_gray));
}

.icon_open>svg {
	color: white;
	cursor: pointer;
}

.icon_disable>svg {
	color: rgba(90 90 90 / 30%);
}

.Private_Radio_body {
	position: relative;
}

.login_container,
.Register_container,
.reset_container {
	width: 216px;
	height: 366px;
	box-sizing: border-box;
	position: absolute;
	top: 0;
	left: -228px;
}

.show_loginList {
	left: 0px !important;
	transition: left .3s ease-in-out;
	-moz-transition: left .3s ease-in-out;
	-o-transition: left .3s ease-in-out;
	-webkit-transition: left .3s ease-in-out;
}

.closeLogin_body {
	height: 0px !important;
}

.Reset_Prompt {
	color: #c9c9c9;
	text-align: center;
	font-size: 16px;
	margin-bottom: 20px;
	text-transform: initial;
}

.tipsMessage {
	z-index: 999;
	position: absolute;
	top: 0px;
	min-width: 25%;
	padding: 0px 12px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: center;
	transition: height .3s ease-in-out;
	overflow: hidden;
	max-height: 100%;
}

.istips {
	height: 40px;
	width: 100%;
	box-sizing: border-box;
	border-radius: 4px;
	padding: 0 15px;
	margin: 8px 0;
	display: flex;
	align-items: center;
	transform: translateY(-20px) rotateX(50deg);
	opacity: 0;
	transition: opacity .3s linear, transform .3s ease-in-out;
	font-size: 14px;
}

.strMsg {
	text-transform: initial;
}

.istips svg {
	margin-right: 6px;
}

.errTips {
	background-color: #f9d7d7;
	color: #ff2626;
}

.okiTips {
	background-color: #e4f1dd;
	color: #6cc142;
}

.showTips {
	opacity: 1;
	transform: translateY(0px) rotateX(0deg);
}

.closeTips {
	opacity: 0;
	transform: translateY(-10px) rotateX(30deg);
}

.emptyRadio {
	display: flex;
	width: 100%;
	height: 75%;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: rgb(65, 65, 65) !important;
	font-size: 14px;
	text-align: center;
	box-sizing: border-box;
	opacity: var(--opacity70);
	transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-o-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-moz-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-webkit-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
}

.emptyRadio>span {
	text-transform: initial;
}

.userRadiobox {
	transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-o-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-moz-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	-webkit-transition: opacity .15s cubic-bezier(0.35, 0.16, 0.83, 0.33);
	overflow-y: scroll;
	height: 366px;
}

.userRadiobox::-webkit-scrollbar {
	display: none;
}

.emptyRadio>svg {
	width: 85px;
	height: 85px;
	/* margin-top: 12%; */
	color: rgb(65, 65, 65) !important;
}

.radio_mask {
	position: absolute;
	width: 1000px;
	height: 470px;
	background-color: #00000080;
	z-index: 3;
	display: none;
}

.login_outPage {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.showloginOut {
	transform: translateY(0px);
	opacity: 1;
}

.add_content {
	width: 100%;
	height: 414px;
	position: absolute;
	top: -48px;
	left: calc(100% + 12px);
	background-color: transparent;
	transition: left .3s ease-in-out;
}

.show_addContent {
	left: 0;
}

.addText_content,
.scorllInfo {
	width: 99.5%;
	height: 366px;
	overflow-y: scroll;
	padding-right: 14px;
}

.addText_content::-webkit-scrollbar,
.scorllInfo::-webkit-scrollbar {
	appearance: none;
	width: 5px;
}

.addText_content::-webkit-scrollbar-thumb,
.scorllInfo::-webkit-scrollbar-thumb {
	background-color: var(--grayinputColor);
	border-radius: 4px;
}

.addText_content>p {
	position: relative;
}

.selectGenre {
	width: 93%;
	display: inline-block;
	border-radius: 4px;
	z-index: 2;
	text-align: center;
	color: #9b9b9b !important;
	white-space: nowrap;
	padding: 6px;
	position: relative;
	transition: max-height .25s ease-in-out;
	overflow: hidden;
	max-height: 0;
	background-color: var(--grayinputColor);
	border: solid 1px #4040400f;
}

.showaddGenre {
	max-height: 140px;
}

.closeSelectGenre {
	max-height: 0px;
}

.scorllSelectGenre {
	overflow-y: scroll;
	max-height: 100px;
	font-size: 13px;
	width: 102%;
}

.scorllSelectGenre::-webkit-scrollbar {
	appearance: none;
	width: 5px;
}

.scorllSelectGenre::-webkit-scrollbar-thumb {
	border-radius: 12px;
	background-color: #71717144;
}

.searchGenreput {
	width: calc(100% - 24px);
	height: 26px;
	text-align: center;
	background-color: transparent;
	border: none;
	color: #999;
	border-bottom: solid 1px #282b2c;
	border-radius: 0px;
	font-size: 14px;
	padding: 0 0 3px 0;
	text-transform: initial;
}

.searchGenreput::placeholder {
	color: rgb(61, 61, 61);
}

.scorllSelectGenre>div {
	margin: 4px 0;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 4px 8px;
}

.scorllSelectGenre>div:hover {
	cursor: pointer;
	background: #5e5e5e33;
	border-radius: 2px;
	color: #565656;
}

.addText_content>p>span {
	color: #999;
	font-size: 14px;
}

.addText_content>p>input {
	width: 100%;
	height: 32px;
	margin: 4px 0;
	padding: 10px;
	box-sizing: border-box;
	border-radius: 4px;
	border: none;
	outline: none;
	color: white;
	font-size: 14px;
	text-transform: initial;
	background-color: var(--grayinputColor);
	/* border: solid 1px #4040400f; */
}

.addtext {
	width: 100%;
	height: 32px;
	margin: 4px 0;
	line-height: 32px;
	padding: 0 10px;
	font-size: 13px;
	color: white;
	border-radius: 4px;
	box-sizing: border-box;
	background-color: var(--grayinputColor);
	background-image: linear-gradient(115deg, rgb(27 27 27 / 20%), rgb(28 28 28 / 20%));
	border: none;
}

.RadioDesc {
	max-width: 100%;
	min-width: 100%;
	height: 80px;
	margin: 4px 0;
	padding: 6px 10px;
	box-sizing: border-box;
	border-radius: 4px;
	outline: none;
	color: white;
	font-size: 14px;
	text-transform: none;
	resize: none;
	line-height: 20px;
	background-color: #71717126;
	background-image: linear-gradient(115deg, rgb(27 27 27 / 20%), rgb(28 28 28 / 20%));
	border: none;
}

.RadioDesc::selection {
	background: rgba(153, 153, 153, 0.623);
}

.RadioDesc::-webkit-scrollbar {
	display: none;
}

.addRadiobtn {
	height: 38px;
	color: white;
	border: none;
	border-radius: 4px;
	font-size: 14px;
	font-weight: 100;
	width: 100%;
	cursor: pointer;
	font-weight: 100;
	background-color: var(--grayinputColor);
	margin-bottom: 6px;
}

.addRadiobtn:hover,
.InfoBtn>button:hover {
	opacity: var(--opacity70);
}

.addRadiobtn:active,
.InfoBtn>button:active {
	transform: scale(.95);
}

.disableGenres {
	display: none;
}

.moreInfo {
	width: 30px;
	height: 30px;
	color: white;
	transform: rotate(90deg) translate(8px, -2px);
	text-align: center;
	letter-spacing: 1.5px;
	display: none;
}

.moreInfo:active {
	opacity: .4;
}

.addthisSong {
	transform: translateY(10.5px);
	height: 24px;
	position: relative;
}

.addthisSong>svg {
	width: 18px;
	color: #7a7a7a;
}

.Radioreadonly>.addthisSong {
	display: none;
}

.duidui {
	width: 12px !important;
	color: white !important;
	position: absolute;
	top: 0;
	left: 0px;
	margin-left: 3px;
	display: none;
}

.duidui[selecteds=true] {
	display: block;
	animation: showduidui .1s ease-in-out;
}

@keyframes showduidui {
	0% {
		transform: scale(.5);
	}

	100% {
		transform: scale(1.2);
	}
}

.Public_type_body:hover .moreInfo {
	display: block;
}

.addGenreId {
	position: relative;
	cursor: pointer;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.addGenreId::after {
	content: '';
	width: 5px;
	height: 5px;
	border-top: solid 1px rgb(165, 165, 165);
	border-right: solid 1px rgb(165, 165, 165);
	transform: rotate(135deg) scale(1.15);
	position: absolute;
	top: 12px;
	right: 10px;
	transition: transform .25s linear, top .2s ease-in-out;
}

.showselect::after {
	top: 15px;
	transform: rotate(315deg) scale(1.15);
}

.RadioInfomation {
	width: 100%;
	height: 425px;
	position: absolute;
	top: 45px;
	right: -100%;
	z-index: 2;
	transition: right .32s cubic-bezier(0.39, 0.04, 0.64, 0.93);
	box-sizing: border-box;
	padding: 12px 12px 0 12px;
}

.show_RadioIfomation {
	right: 0%;
}

.infoimage {
	width: 65px;
	height: 65px;
	border-radius: 8px;
	margin-right: 8px;
}

.radiosinfoTile,
.out_addContent {
	height: 35px;
	line-height: 35px;
	margin-bottom: 12px;
	color: white;
	font-weight: 100;
	font-size: 17px;
}

.infoClass1 {
	display: flex;
	color: white;
	margin-bottom: 4px;
}

.theHeat {
	display: flex;
	color: #999;
	font-size: 13px;
	border-radius: 6px;
	padding: 4px 0px 0 6px;
	background-color: var(--grayinputColor);
	border: solid 1px #4040400f;
	flex-direction: column;
	position: relative;
}

.theHeat>p {
	display: flex;
}

.theHeat>p>span {
	width: 45px;
}

.theHeat>p>span:last-child {
	border-radius: 4px;
	color: #d8d8d8;
	font-weight: 100;
	font-size: 12px;
	line-height: 19px;
	text-align: left;
	width: 80px !important;
	overflow: hidden;
	text-overflow: ellipsis;

}

.infoblock {
	position: relative;
}

.infoblock>span {
	color: #999;
	font-size: 14px;
}

.infoblock>input {
	width: 100%;
	height: 33px;
	margin: 4px 0;
	padding: 10px;
	box-sizing: border-box;
	border-radius: 4px;
	outline: none;
	color: white;
	font-size: 14px;
	text-transform: initial;
	background-color: var(--grayinputColor);
	background-image: linear-gradient(115deg, rgb(27 27 27 / 20%), rgb(28 28 28 / 20%));
	border: solid 1px #4040400f;
}

.infoblock input[readonly],
.ChooseShare[readonly],
.RadioDesc[readonly],
.searchbody[readonly],
.likeRadio,
.disableWifinput,
.jindutiao[readonly],
.perv[readonly],
.next[readonly],
.loop[readonly],
.random[readonly] {
	cursor: auto !important;
	opacity: 0.3;
	pointer-events: none;
}

.infoblock input[readonly],
.RadioDesc[readonly] {
	pointer-events: initial;
	color: transparent;
	text-shadow: 0 0 0 rgb(255, 255, 255);
}

.medium[readonly],
.eqmode[readonly],
.balance[readonly],
.deppbass[readonly],
.MaxVolume[readonly] {
	cursor: auto !important;
	opacity: 0.2;
	pointer-events: none;
}

.theHeat[readonly] {
	opacity: 0.6 !important;
}

.selectShare {
	display: inline-block;
	width: 100%;
}

.selectShare>.ChooseShare {
	width: 100%;
	height: 33px;
	line-height: 33px;
	margin: 4px 0;
	padding: 0 10px;
	box-sizing: border-box;
	border-radius: 4px;
	color: white;
	font-size: 14px;
	text-transform: initial;
	cursor: pointer;
	position: relative;
	background-color: var(--grayinputColor);
	background-image: linear-gradient(115deg, rgb(27 27 27 / 20%), rgb(28 28 28 / 20%));
}

.InfoBtn {
	display: flex;
	justify-content: space-between;
	margin-top: 12px;
	margin-right: 7px;
}

.InfoBtn>button {
	height: 36px;
	color: white;
	border: none;
	border-radius: 4px;
	font-size: 14px;
	font-weight: 100;
	cursor: pointer;
	width: 47%;
	background-color: var(--grayinputColor);
	background-image: linear-gradient(115deg, rgb(27 27 27 / 20%), rgb(28 28 28 / 20%));
	border: solid 1px #4040400f;
}

.TheChoose {
	position: relative;
}

.TheChoose::after {
	content: '';
	display: inline-block;
	position: absolute;
	top: 10px;
	right: 8px;
	width: 10px;
	height: 10px;
	background-color: rgb(62, 162, 133);
	border-radius: 50%;
}

.paging {
	display: flex;
	justify-content: space-between;
	font-size: 14px;
	color: white;
}

.deleteBTN,
.ModifyBTN {
	height: 36px;
	color: #eeeeee;
	border-radius: 4px;
	font-size: 13px;
	cursor: pointer;
	width: 47%;
	background-color: #69696965;
	background-image: linear-gradient(115deg, rgb(27 27 27 / 20%), rgb(28 28 28 / 20%));
	border: solid 1px #2c2c2c;

}

.deleteBTN,
.qrdelete {
	color: #ffffff !important;
	background-color: #ff0000a8 !important;
	border: solid 1px #640e0e !important;
	background-image: linear-gradient(115deg, rgb(114 43 43 / 20%), rgb(109 9 9 / 20%)) !important;
}

.qrDeleteMask {
	width: 100%;
	height: 111%;
	position: absolute;
	top: -45px;
	left: 0px;
	background-color: rgba(0, 0, 0, 0.8);
	color: white;
}

.qrDelete {
	width: 85% !important;
	height: 90px;
	position: absolute;
	top: calc(50% - 40px);
	left: 8%;
	padding: 10px;
	background-color: rgb(35 35 35 / 40%);
	animation: showloginOut_page .2s ease-in-out 1;
}

.qrBtnbody {
	display: flex;
	margin-left: 35%;
	justify-content: space-between;
	margin-top: 22px;
}

.qrBtnbody>button {
	width: 55px;
	height: 28px;
	font-size: 12px;
	border: none;
	background-color: rgb(255 255 255 / 10%);
	color: #c1c1c1;
	border-radius: 4px;
	cursor: pointer;
}

.qrDelete>div>button:hover,
.deleteBTN:hover,
.ModifyBTN:hover {
	opacity: var(--opacity70);
}

.qrDelete>div>button:active {
	transform: scale(.99);
}

.NowSelcetGener {
	position: relative;
}

.NowSelcetGener>.generIcon {
	display: block !important;
}

.disabledAllAfter {
	display: block;
}

.disabledcont {
	display: none;
}

.generIcon {
	color: #727272;
	display: none;
	position: absolute;
	top: 0px;
	left: 0px;
}

.generIcongg {
	color: white;
	position: absolute;
	top: -4px;
	left: -3px;
	transform: scale(.4);
}

.emptyPublic {
	height: 100%;
}

.loadingMask {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	z-index: 99999;
	background-color: #1f1f1fb5;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.loadingMask[on] {
	display: flex;
}

.loader {
	animation: spin 1s linear infinite;
	-webkit-animation: spin 1s linear infinite;
	-moz-animation: spin 1s linear infinite;
	-o-animation: spin 1s linear infinite;
	border: 2px solid rgba(221, 221, 221, 0.692);
	border-top: 2px solid #47a6fffa;
	border-radius: 50%;
	height: 20px;
	width: 20px;
	display: inline-block;
	position: relative;
}

@keyframes spin {
	to {
		transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
	}
}

@-webkit-keyframes spin {
	to {
		transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
	}
}

@-moz-keyframes spin {
	to {
		transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
	}
}

@-o-keyframes spin {
	to {
		transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
	}
}

.loadtitle {
	margin-top: 8px;
	color: #d1d1d166;
	animation: flashs 2s ease-in-out infinite;
	-webkit-animation: flashs 2s linear infinite;
	-moz-animation: flashs 2s linear infinite;
	-o-animation: flashs 2s linear infinite;
}

@keyframes flashs {
	0% {
		color: #d1d1d166;
	}

	50% {
		color: #ffffffbf;
	}

	100% {
		color: #d1d1d166;
	}
}

@-webkit-keyframes flashs {
	0% {
		color: #d1d1d166;
	}

	50% {
		color: #ffffffbf;
	}

	100% {
		color: #d1d1d166;
	}
}

@-moz-keyframes flashs {
	0% {
		color: #d1d1d166;
	}

	50% {
		color: #ffffffbf;
	}

	100% {
		color: #d1d1d166;
	}
}

@-o-keyframes flashs {
	0% {
		color: #d1d1d166;
	}

	50% {
		color: #ffffffbf;
	}

	100% {
		color: #d1d1d166;
	}
}

.vol2 {
	width: 80px;
	height: 3px;
	background-color: #cccccc40;
	border-radius: 12px;
}

.vol2>div {
	width: 0;
	height: inherit;
	border-radius: inherit;
	background-color: #ffffff;
}

#canvas {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 999;
	transform: scale(.1);
	display: none;
}

.cancelinfomation {
	width: 15px;
	margin: 6px 12px 0 2px;
	cursor: pointer;
	opacity: 1;
}

.cancelinfomation:hover {
	opacity: .5;
	transition: .2s ease-in-out;
}

.sitionTitle {
	text-align: center;
	font-size: 15px;
	color: rgb(120, 120, 120);
	margin-bottom: 12px;
}

.scrollFLAT {
	height: inherit;
	line-height: 40px;
	position: relative;
}

.eqmode {
	position: relative;
}

.NowEQtitle {
	text-transform: uppercase;
	text-align: right;
	cursor: pointer;
}

.flatList {
	position: absolute;
    top: 40px;
    right: 0px;
    border-radius: 4px;
    height: 150px;
    z-index: 3;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    font-size: 13px;
    overflow: scroll;
    background-color: rgb(28 33 36);
    box-shadow: 0px 0px 5px #00000030;
    width: 100%;
    animation: showEQlist .2s ease-in-out;
}

.flatList::-webkit-scrollbar {
    width: 0px !important;
    display: none !important;
}

.flatList::-webkit-scrollbar-thumb {
    background-color: var(--scroll_color);
    border-radius: 12px;
}

@keyframes showEQlist {
	from {
		transform: translateY(-10px);
		opacity: 0;
	}

	to {
		transform: translateY(0px);
		opacity: 1;
	}
}

.flatList>span {
	height: 32px;
	line-height: 32px;
	padding: 4px 14px;
	cursor: pointer;
	text-transform: uppercase;
	text-align: center;
	color: #505050;
}

.flatList>span:hover {
	background-color: rgb(91 108 117 / 15%);
	box-shadow: 0 0 1px 1px #00000060 inset;
	color: white;
}

.flatList>.gaoliang:hover {
	background-color: rgb(91 108 117 / 35%);
	box-shadow: 0 0 1px 1px #00000060 inset;
	color: white;
}

.imagesbody {
	position: relative;
}

.likeBTN {
	position: absolute;
	top: 3px;
	right: 3px;
	cursor: pointer;
}

.likeBTN>svg:active {
	transform: scale(.95);
}

.likeBTN>svg {
	width: 14px;
	height: 14px;
}

.onlike {
	color: #ff4545 !important;
}

.playsNone {
	pointer-events: none;
}

.Select_list_table {
	opacity: 1;
	background-color: rgb(55, 55, 55);
}

.Select_list_table>.preset_name {
	color: rgb(120, 216, 120) !important;
	position: relative;
	overflow: hidden;
}

.Select_list_table>.delectSonCon {
	background-color: rgb(55 55 55);
}

.delectSonCon {
	display: none;
}

.list_table:hover .delectSonCon {
	position: absolute;
	top: -5px;
	right: -5px;
	width: 25px;
	height: 25px;
	line-height: 23px;
	text-align: center;
	color: rgb(210 210 210);
	background-color: rgb(55, 55, 55);
	font-weight: bold;
	display: block;
	font-size: 20px;
	border-radius: 22%;
}

.disabledUSB {
	pointer-events: none;
	opacity: .3;
}

.clause {
	font-size: 12px;
	text-align: center;
	color: #999999;
	text-shadow: -1px 1px 1px #97979723;
}

.clause>a {
	color: #3f6ac1;
	text-decoration: none;
}

.clause>a:hover {
	opacity: .8;
}
{"name": "music-manage", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.3", "crypto-js": "^4.2.0", "element-plus": "^2.9.6", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuex": "^4.1.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@vitejs/plugin-vue": "^5.2.1", "sass": "^1.85.1", "vite": "^6.2.0"}}
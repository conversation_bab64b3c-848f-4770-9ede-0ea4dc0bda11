<template>
    <div class="login-container">
        <div class="login-card">
            <el-header class="login-header">
                <div class="login-title">Music Manage</div>

            </el-header>
            <el-form v-if="isLogin" class="login-form"  :model="loginForm" :rules="rules" label-width="auto">
                <el-form-item prop="username" class="login-form-item">
                    <el-input 
                        v-model="loginForm.username" 
                        placeholder="Email" 
                        :prefix-icon="User">
                    </el-input>
                </el-form-item>
                <el-form-item prop="password" class="login-form-item">
                    <el-input 
                        placeholder="Password" 
                        :type="showPassword ? 'text' : 'password'" 
                        v-model="loginForm.password"
                        :prefix-icon="Lock"
                        >
                        <template #suffix>
                            <el-icon @click.prevent="showPasswordFun">
                                <View v-if="showPassword"/>
                                <Hide v-else/>
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>

                <el-form-item class="login-form-item">
                    <el-button type="primary" class="login-button" @click="login()">Sign In</el-button>
                </el-form-item>

                <el-form-item class="chekckbox">
                    <el-form-item class="rememberBody">
                        <el-switch
                            class="remember-switch"
                            v-model="remberVal"
                            style="margin-left: 24px"
                            @change="rememberChange"
                        />
                        <span class="remember">Remember</span>
                    </el-form-item>

                    <el-form-item>
                        <span class="forgot">Forgot Password?</span>
                    </el-form-item>
                </el-form-item>
                
                <el-form-item class="privacy">
                    <el-checkbox-group v-model="checkList">
                        <el-checkbox label="" value="privacyVal"/>
                    </el-checkbox-group>
                    <a @click="dialogVisible = true">Privacy Policy</a>
                </el-form-item>
            </el-form>

            <!-- 注册 -->
            <el-form v-else class="login-form" :model="registerForm" :rules="rules2" label-width="auto">
                <el-form-item prop="username" class="login-form-item">
                    <el-input 
                        v-model="registerForm.username" 
                        placeholder = "UserName" 
                        :prefix-icon="User">
                    </el-input>
                </el-form-item>

                <el-form-item prop="mail" class="login-form-item">
                    <el-input 
                        v-model="registerForm.mail" 
                        placeholder = "Email" 
                        :prefix-icon="User">
                    </el-input>
                </el-form-item>
                <el-form-item prop="password" class="login-form-item">
                    <el-input 
                        placeholder = "password" 
                        v-model = "registerForm.password"
                        :type = "showPassword ? 'text' : 'password'" 
                        :prefix-icon = "Lock"
                        >
                        <template #suffix>
                            <el-icon @click.prevent="showPasswordFun">
                                <View v-if="showPassword"/>
                                <Hide v-else/>
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="password2" class="login-form-item">
                    <el-input 
                        placeholder = "Confirm password" 
                        v-model = "registerForm.password2"
                        :type = "showPassword ? 'text' : 'password'" 
                        :prefix-icon = "Lock"
                        >
                        <template #suffix>
                            <el-icon @click.prevent="showPasswordFun">
                                <View v-if="showPassword"/>
                                <Hide v-else/>
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>

                <el-form-item class="login-form-item">
                    <el-button type="primary" class="login-button" @click="login(loginForm, checkList)">Register</el-button>
                </el-form-item>
                
                <el-form-item class="privacy">
                    <el-checkbox-group v-model="checkList">
                        <el-checkbox label="" value="privacyVal"/>
                    </el-checkbox-group>
                    <a @click="dialogVisible = true">Privacy Policy</a>
                </el-form-item>
            </el-form>
        </div>
    </div>

    <el-dialog
        class="login-dialog"
        v-model="dialogVisible"
        title=""
        width="50%"
        top="30vh"
        :draggable=true
        :show-close=false
        :close-on-click-modal=false
    >
        <div v-if="!iframeLoaded" 
            class="iframe-loading" 
            style="height: 40vh;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;"
        >
            <div style="transform: scale(2);">
                <el-icon class="is-loading">
                    <Loading />
                </el-icon>
            </div>
            <div 
                style="margin-top: 10px; 
                font-size: 16px;"
            >
                Loading...
            </div>
        </div>
        
        <div v-show="iframeLoaded" class="iframe-body" style="height: 40vh;">
            <iframe 
                src="https://radio.arylic.com/privacy" 
                width="100%" 
                height="100%"
                style="border: none;"
                @load="iframeLoaded = true"
            ></iframe>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">Cancel</el-button>
                <el-button type="primary" @click="confirmPrivacy">Confirm</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
    import { reactive, ref } from 'vue';
    import AuthClass from '../api/auth';
    import { User, Lock, View , Hide, Loading } from '@element-plus/icons-vue';
    import { ElMessage } from 'element-plus';
    import { onBeforeMount } from 'vue';
    import router from '../router/index.js';

    onBeforeMount(() => {
        let remVal = AuthClass.getRmember();
        remberVal.value = remVal ?? false;

        const loginInfo = AuthClass.getUserLogin();
        if (loginInfo) {
            loginForm.username = loginInfo.username;
            loginForm.password = loginInfo.password;
        }
    
    });

    const isLogin = ref(true);
    const showPassword = ref(false);
    const remberVal = ref(false);
    const dialogVisible = ref(false);
    const iframeLoaded = ref(false);
    const checkList = ref(['privacyVal']);

    const loginForm = reactive({
        username: '',
        password: ''
    });

    const registerForm = reactive({
        username: '',
        password: '',
        password2: '',
        mail: '',
    });

    const rules = {
        username: [{ required: true, message: 'Please enter your email', trigger: 'blur' }],
        password: [{ required: true, message: 'Please enter your password', trigger: 'blur' }],
    };

    const rules2 = {
        username: [{ required: true, message: 'Please enter your username', trigger: 'blur' }],
        password: [{ required: true, message: 'Please enter your password', trigger: 'blur' }],
        password2: [{ required: true, message: 'Please confirm your password', trigger: 'blur' }],
        mail: [{ required: true, message: 'Please confirm your email', trigger: 'blur' }]
    };
    

    // 切换密码可见性
    const showPasswordFun = () => {
        console.log(showPassword.value);
        showPassword.value = !showPassword.value;
    };

    // 切换登录方式
    const rememberChange = () => {
        console.log(remberVal.value);
        AuthClass.saveRmember(remberVal.value);
    };

    // 同意隐私政策
    const confirmPrivacy = () => {
        dialogVisible.value = false;
        checkList.value = ['privacyVal'];
    };

    // 登录
    const login = async () => {
        const isUsername = loginForm.username.trim();
        const isPassword = loginForm.password.trim();

        if (!isUsername || !isPassword) {
            ElMessage({
                message: 'Please enter your username and password',
                type: 'error',
            })
            return;
        }

        const isPrivacy = checkList.value.includes('privacyVal');
        if (!isPrivacy) {
            ElMessage({
                message: 'Please agree to the privacy policy',
                type: 'error',
            })
            return;
        }

        AuthClass.saveRmember(remberVal.value);
        if (remberVal.value) {
            AuthClass.saveUserLogin(loginForm);
        } else {
            AuthClass.removeUserLogin();
        }
        

        AuthClass.loginFun(
            loginForm.username,
            loginForm.password,
        ).then((result) => {
            console.log('login页面登录成功', result);
            AuthClass.saveUserInfo({
                name: result.name,
                id: result.id,
                token: result.token,
            });
            ElMessage({
                message: 'Login successful',
                type: 'success',
            });
            setTimeout(() => {
                console.log('login页面前往home');
                router.push({path: '/home'})
            }, 300);
        }).catch((err) => {
            console.error('请求失败', err);
            ElMessage({
                message: err.msg,
                type: 'error',
            });
        });
    };
</script>

<style lang="scss" scoped>

.login-container {
    @include flex-center;
    min-height: 100vh;
   
    .login-form {
        min-width: 250px;
        width: 100%;
    }

    .login-header {
        @include flex-center;
        height: 100px;
        font-size: 25px;
    }

    .login-title {
        color: #fff;
    }
    
    .login-card {
        width: 320px;
        max-width: 320px;

        :deep(.el-input__prefix),
        :deep(.el-input__suffix) {
            .el-icon {
                font-size: 18px;
            }
        }

        .login-form-item{
            position: relative;
            height: 70px;
            margin-bottom: 12px;

            :deep(.el-input__wrapper) {
                caret-color: #fff;
                height: 45px !important;
                line-height: 45px !important;
                border-radius: 45px;
                background: rgb(255 255 255 / 20%);
                padding: 0px 20px;
                font-size: 15px;
                box-shadow: 0 0 0 0 transparent inset !important;
                border-color: transparent !important;
                margin-top: 8px;
                
                input::placeholder {
                    color: $placeholder-color;
                }
                
                .el-input__inner {
                    color: $text-color;
                }
            }

            :deep(.el-icon) {
                color: $text-color;
            }

            :deep(.el-form-item__error) {
                position: absolute;
                bottom: -22px;
                left: 15px;
                margin: 0;
                height: 20px;
                line-height: 20px;
                padding: 0 10px;
                background: rgba(255, 0, 0, 0.1);
                border-radius: 4px;
            }
        }
    }
    
    .login-button {
        width: 100%;
        height: 45px !important;
        line-height: 45px !important;
        border-radius: 45px;
        margin-top: 12px;
        background: rgb(123, 189, 255);
        border: none;
    }

    .chekckbox {
        margin-bottom: 0;

        :deep(.el-form-item__content) {
            justify-content: space-between;
        }

        :deep(.el-switch) {
            margin-left: 0px !important;
            --el-switch-on-color: rgb(123, 189, 255);
            margin-right: 12px;
        }
        
        .rememberBody {
            align-items: start;
            margin-right: 12px !important;
        }

        .remember,
        .forgot {
            font-size: 12px;
            color: $text-color;
        }

    }

    .privacy {
        text-align: center;

        a {
            font-weight: normal;
            color: $text-color;
            font-size: 11px;
            cursor: pointer;
            text-decoration: underline;
        }
    }

}
</style>
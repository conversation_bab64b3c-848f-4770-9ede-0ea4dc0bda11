<template>
    <div class="devices-body">
        <iframe
            class="device_iframe"
            :src="currentDevice" 
            loading="lazy"
        ></iframe>
    </div>
</template>

<script setup>
    import { ref, reactive } from 'vue';

    const devices  = reactive([
        {
            'name': 'device',
            'src': 'http://*************/index.html'
        }
    ]);

    const currentDevice = ref(devices[0].src);
    
</script>

<style scoped lang="scss">
    .devices-body {
        width: 100%;
        height: 100%;
        background: #212332;
        overflow: hidden;
        color: #fff;

        .device_iframe {
            min-width: 100%; 
            min-height: 100%; 
            border: none;
        }
    }
</style>
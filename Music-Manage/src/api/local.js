import { encodeAES, decodeAES } from "../utils/utils";

export default class LocalClass {
    static _namesapce = 'MUSIC_MANAGE';
    // 遍历localStorage，删除过期的项
    static init() {
        for (let i = 0; i < localStorage.length; i++) {
            try {
                const key = localStorage.key(i);
                // 解密
                const decodeData = decodeAES(localStorage.getItem(key));
                const decodeKey = decodeAES(key);
                const data = JSON.parse(decodeData);
                // 不是本项目的项
                if (!decodeKey.startsWith(this._namesapce)) continue;
                // 无过期时间
                if (data.timeOut == null) {
                    let n1 = '保存时间是' + new Date(data.saveTime).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
                    console.log('无过期时间', decodeKey, n1, '内容', data.value);
                    continue;
                }
                // 判断是否过期
                if (data.timeOut && data.timeOut < Date.now()) {
                    let n1 = '过期时间是' + new Date(data.timeOut).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
                    console.log('删除过期项', decodeKey, n1, '内容', data.value);
                    localStorage.removeItem(key);
                } else {
                    let n1 = '保存时间是' + new Date(data.saveTime).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
                    let n2 = '过期时间是' + new Date(data.timeOut).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
                    console.log('未过期', decodeKey, n1, n2, '内容', data.value);
                }
            } catch(_) {}
        }
    }
    
    static setItem(key, value, timeOut) {
        try {
            const saveKey = this._namesapce + '_' + key;
            const data = {
                value: value,
                saveTime: Date.now(),
                timeOut: timeOut ? this._calcExpiration(timeOut): null
            }
            // 加密
            const str = JSON.stringify(data);
            const encodeStr = encodeAES(str);
            const encodeKey = encodeAES(saveKey);
            localStorage.setItem(encodeKey, encodeStr);
        } catch(e) {
            console.error(e);    
        }
    }

    static getItem(key) {
        try {
            const saveKey = this._namesapce + '_' + key;
            // 先检查是否有这个key
            if (!localStorage.getItem(encodeAES(saveKey))) {
                return null;
            }
            const str = localStorage.getItem(encodeAES(saveKey));
            const decodeData = decodeAES(str);
            const data = JSON.parse(decodeData);
            return data.value ?? null;
        } catch(e) {
            console.error(e);
            return null;
        }
    }

    static removeItem(key) {
        try {
            const saveKey = this._namesapce + '_' + key;
            localStorage.removeItem(encodeAES(saveKey));
        } catch(e) {
            console.error(e);
        }
    }

    static setSession(key, value) {
        try {
            const saveKey = this._namesapce + '_' + key;
            const data = {
                value: value,
                saveTime: Date.now(),
                timeOut: null
            }
            // 加密
            const str = JSON.stringify(data);
            const encodeStr = encodeAES(str);
            const encodeKey = encodeAES(saveKey);
            sessionStorage.setItem(encodeKey, encodeStr);
        } catch(e) {
            console.error(e);    
        }
    }

    static getSession(key) {
        try {
            const saveKey = this._namesapce + '_' + key;
            // 先检查是否有这个key
            if (!sessionStorage.getItem(encodeAES(saveKey))) {
                return null;
            }
            const str = sessionStorage.getItem(encodeAES(saveKey));
            const decodeData = decodeAES(str);
            const data = JSON.parse(decodeData);
            return data.value ?? null;
        } catch(e) {
            console.error(e);
            return null;
        }
    }
    
    static _calcExpiration(minutes) {
        return Date.now() + minutes * 60 * 1000;
    }
}
<template>
    <div class="dialog-container">
        <el-dialog 
            v-model="store.state.showDialog" 
            :title="'...loading'"
            width="60vw"
            style="max-width: 650px;"
        >
            <div>
                123
            </div>
            <div>
                图片
            </div>
            <div>
                播放链接
            </div>
            <div>
                分类
            </div>
            <div>
                描述
            </div>
            <div>
                右上角爱心
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import store from '../../store';


</script>

<style scoped lang="scss">
    :deep(.el-dialog) {
        background-color: $page-bg-color;

        .el-dialog__title {
            color: $text-color;
        }

        .el-dialog__body {
            color: $text-sec-color;
        }
    }
</style>
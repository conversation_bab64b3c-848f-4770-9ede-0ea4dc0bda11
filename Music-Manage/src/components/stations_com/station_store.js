import { ref, reactive } from 'vue';
import StationClass from '../../api/station.js';
export const stationsSotre = () => {
    const FILTER_TYPES = {
        GENRE: 'genre',
        LANGUAGE: 'language',
        COUNTRY: 'country',
    }

    const PAGE_MODE = {
        PUBLIC: 0,
        PRIVATE: 1
    }

    const genreValue = ref('');
    const genreList = reactive([]);

    const languageValue = ref('');
    const languageList = reactive([]);

    const countryValue = ref('');
    const countryList = reactive([]);

    const musicSongs = reactive([]);
    const noMusicSongs = ref(false);

    const _myFlag = () => {
        const fl1 = genreValue.value == '' || genreValue.value == undefined;
        const fl2 = languageValue.value == '' || languageValue.value == undefined;
        const fl3 = countryValue.value == '' || countryValue.value == undefined;
        return fl1 && fl2 && fl3;
    }

    const clearFilter = () => {
        if (_myFlag()) {
            return console.log('请选择过滤条件');
        }
        noMusicSongs.value = false;
        genreValue.value = '';
        languageValue.value = '';
        countryValue.value = '';
        StationClass.setAllSongsAndPublic(musicSongs);
    }

    const getFilterSongs = async () => {
        noMusicSongs.value = false;
        
        if (_myFlag()) {
            StationClass.setAllSongsAndPublic(musicSongs);
            return console.log('全部删除');
        }

        try {
            const genreId = genreList.find((item) => item?.name === genreValue.value)?.id || '';
            const languageId = languageList.find((item) => item?.name === languageValue.value)?.code || '';
            const countryId = countryList.find((item) => item?.name === countryValue.value)?.code || '';
            musicSongs.splice(0, musicSongs.length);
            const result = await StationClass.getQueryOnFilter(genreId, languageId, countryId);
            
            if (result.stations.length === 0) {
                console.log('暂无数据');
                noMusicSongs.value = true;
            } else {
                musicSongs.push(...result.stations);
                noMusicSongs.value = false;
            }
        } catch (error) {
            console.log(error);
        }
    }

    return {
        FILTER_TYPES,
        PAGE_MODE,
        genreValue,
        genreList,
        languageValue,
        languageList,
        countryValue,
        countryList,
        musicSongs,
        noMusicSongs,
        clearFilter,
        getFilterSongs,
    }
};
$bg-color: 
  linear-gradient(to bottom right, rgb(225 173 255 / 37%), #ffb5d3), 
  linear-gradient(to bottom left, #e1ff71fa, rgb(69 143 255 / 74%));

$text-color: #fff;
$text-sec-color: #a2a2a2;
$page-bg-color: #212332;
$second-color: rgba(42, 45, 62, 1);
$placeholder-color: rgba(255, 255, 255, 0.5);

@function blue-color($opacity: 1) {
  @return rgba(102, 182, 255, $opacity);
}

@mixin height-equal-line-height($size) {
  height: $size;
  line-height: $size;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-row {
  display: flex;
  flex-direction: row;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

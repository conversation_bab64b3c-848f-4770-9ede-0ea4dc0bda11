// 导入 axios 库，用于发起 HTTP 请求
import axios from 'axios';

/**
 * AxiosClass 类封装了 axios 实例，用于简化 HTTP 请求的操作。
 * 提供了 get 和 post 方法，分别用于发起 GET 和 POST 请求。
 */
class AxiosClass {
    // 定义一个 axios 实例属性
    i_axios;
    
    constructor() {
        // 设置请求的基础 URL
        const baseURL = 'https://radio.arylic.com';
        // 创建一个 axios 实例
        this.i_axios = axios.create({
            baseURL,
            headers: {
                // 设置请求头的内容类型为 JSON
                'Content-Type': 'application/json'
            }
        });
    }

    setToken(token) {
        this.i_axios.defaults.headers.common['Authorization'] = token;
        console.log(this.i_axios.defaults.headers.common['Authorization']);
        return this;
    }

    /**
     * 发起 GET 请求。
     * @param {string} url - 请求的 URL。
     * @param {any} params - 请求的参数，默认为空字符串。
     * @returns {Promise<any>} - 一个 Promise，成功时返回响应数据，失败时返回错误信息。
     */
    get(url, params = '') {
        return new Promise((resolve, reject) => {
            // 发起 GET 请求
            this.i_axios.get(url, { params }).then(response => {
                // 请求成功，解析响应数据
                resolve(response.data);
            }).catch(error => {
                // 请求失败，返回错误信息
                reject({ error: true, message: error.message || 'GET请求错误' });
            });
        });
    }


    /**
     * 发起 POST 请求。
     * @param {string} url - 请求的 URL。
     * @param {Object} data - 请求的数据，默认为空对象。
     * @returns {Promise<any>} - 一个 Promise，成功时返回响应数据，失败时返回错误对象。
     */
    post(url, data = {}) {
        return new Promise((resolve, reject) => {
            // 发起 POST 请求
            this.i_axios.post(url, data).then(response => {
                // 请求成功，解析响应数据
                resolve(response.data);
            }).catch(error => {
                // 请求失败，返回错误对象
                reject({ error: true, message: error.message || 'POST请求错误' });
            });
        });
    }
}

// 导出 AxiosClass 的一个实例
export default new AxiosClass();
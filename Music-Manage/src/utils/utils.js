import CryptoJS from 'crypto-js';

const theKey1 = 'RAKOIT';
const theKey2 = 'ARYLIC';

/**
 * 计算字符串的MD5哈希值。
 * 
 * @param {string} str - 需要计算MD5哈希值的字符串。如果输入不是字符串类型，则返回null。
 * @returns {string|null} - 返回输入字符串的MD5哈希值（16进制格式）。如果输入无效或加密失败，则返回null。
 */
function encodeMd5(str) {
	// 检查输入是否为字符串类型，如果不是则直接返回null
	if (typeof str !== 'string') return null;

	try {
		// 创建MD5哈希，并将结果以16进制字符串形式返回
		return CryptoJS.MD5(str).toString();
	} catch (error) {
		// 捕获加密过程中可能出现的错误，并打印错误信息
		console.error('MD5加密失败:', error);
		return null;
	}
}

function encodeAES(str) {
	if (typeof str !== 'string') return null;

	try {
		const key = CryptoJS.enc.Utf8.parse(toAscii(theKey1));
		const iv = CryptoJS.enc.Utf8.parse(toAscii(theKey2));

		const encrypted = CryptoJS.AES.encrypt(
			CryptoJS.enc.Utf8.parse(str),
			key,
			{
				iv: iv,
				mode: CryptoJS.mode.CBC,
				padding: CryptoJS.pad.Pkcs7
			}
		);

		return encrypted.toString();
	} catch (error) {
		console.error('AES加密失败:', error);
		return null;
	}
}

function decodeAES(str) {
	if (typeof str !== 'string') return null;

	try {
		const key = CryptoJS.enc.Utf8.parse(toAscii(theKey1));
		const iv = CryptoJS.enc.Utf8.parse(toAscii(theKey2));

		const decrypted = CryptoJS.AES.decrypt(
			str,
			key,
			{
				iv: iv,
				mode: CryptoJS.mode.CBC,
				padding: CryptoJS.pad.Pkcs7
			}
		);

		return decrypted.toString(CryptoJS.enc.Utf8);
	} catch (error) {
		// console.error('AES解密失败:', error, str);
		return null;
	}
}


/**
 * @param {string} str - 输入
 * @returns {string} - 16位长度的ASCII数字
 */
function toAscii(str) {
	try {
		let result = '';

		for (let i = 0; i < Math.min(str.length, 8); i++) {
			const code = str.charCodeAt(i).toString().padStart(2, '0');
			result += code;
		}

		return result.padEnd(16, '0');
	} catch (error) {
		console.error('Error in toAscii:', error);
		return null;
	}
}

export {
	encodeMd5,
	encodeAES,
	decodeAES
};
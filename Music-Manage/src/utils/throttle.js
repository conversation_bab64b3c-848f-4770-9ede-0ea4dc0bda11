/**
 * 节流函数
 * 用于限制函数的执行频率，确保函数在指定时间内只执行一次
 * 这对于防止在快速变化的输入（如窗口调整大小、滚动或键盘事件）时频繁调用函数非常有用
 * 
 * @param {Function} func 要节流的函数
 * @param {number} delay 执行间隔时间（毫秒），在此时间范围内函数只会执行一次
 * @returns {Function} 返回一个新的节流函数
 */
function throttle(func, delay = 200) {
    // 记录上一次函数执行的时间戳
    let lastCall = 0;
    return function (...args) {
        // 获取当前时间戳
        const now = new Date().getTime();
        // 检查是否达到执行间隔
        if (now - lastCall < delay) {
            return;
        }
        // 更新上一次执行时间戳为当前时间
        lastCall = now;
        // 执行传入的函数，并传递当前上下文和参数
        return func.apply(this, args);
    };
}

/**
 * 创建一个请求监听器函数
 * 该函数用于管理请求的状态，避免重复请求
 * 
 * @returns {Function} 返回一个闭包函数，用于监听和管理请求
 */
class RequestController {
    /**
     * 创建请求控制器实例
     * @param {Object} [options] 配置选项
     * @param {Function} [options.onError] 错误处理回调函数
     */
    constructor(options = {}) {
        this.isRequesting = false;
        this.onError = options.onError || ((error) => console.error('请求错误:', error));
    }

    /**
     * 发起新请求
     * @param {Function} requestFunc 返回Promise的请求函数
     * @returns {Promise|void} 返回Promise或undefined
     */
    request(requestFunc) {
        console.log('当前请求状态:', this.isRequesting ? '正在处理请求' : '空闲');

        if (this.isRequesting) {
            console.log('请求被阻止');
            return;
        }

        this.isRequesting = true;
        console.log('开始处理新请求');

        return requestFunc().then(result => {
            this.isRequesting = false;
            console.log('请求处理完成');
            return result;
        }).catch(error => {
            this.isRequesting = false;
            this.onError(error);
            throw error;
        });
    }

    /**
     * 手动重置请求状态
     */
    reset() {
        this.isRequesting = false;
        console.log('请求状态已重置');
    }
}


export { throttle, RequestController };
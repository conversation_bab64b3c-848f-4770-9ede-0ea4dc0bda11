
import 'package:test_interface/src/instances/bluetooth/device_type/libre/action_equal_mixin.dart';
import 'package:test_interface/src/instances/bluetooth/device_type/libre/msg/i_libre_send_action.dart';
import 'package:test_interface/src/instances/bluetooth/mixin/ble_channcel_mixin.dart';

import '../../../../interface/actions/i_action.dart';
import '../../../../mixins/libre_msg_convert/libre_send_msg_encoder.dart';
import '../../mixin/ble_send_mixin.dart';

mixin LibreSendMixin on BLESendMixin,BLEChanncel,LibreActionEqualMixin  {

  @override
  void send2Channel(ISendAction action){
     assert( write!= null);
    assert(action is ILibreSendAction);
    write!
        .write(LibreSendMsgEncoder.commandEncode(action: action as ILibreSendAction));
  }

  
}
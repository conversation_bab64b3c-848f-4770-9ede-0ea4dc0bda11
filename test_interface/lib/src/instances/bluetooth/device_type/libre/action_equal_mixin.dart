

import '../../../../interface/actions/i_action.dart';
import '../../../../interface/communication/i_send.dart';
import 'msg/i_libre_receive_action.dart';
import 'msg/i_libre_send_action.dart';

mixin LibreActionEqualMixin implements ISend {
  @override
  bool checkEqual(ISendAction send, IReceiveAction receive) {
    assert(send is ILibreSendAction);
    assert(receive is ILibreReceiveAction);
    final libreSend = send as ILibreSendAction;
    final libreReceive = receive as ILibreReceiveAction;

    //当command不能满足需求时,需要额外的加判断
    switch (libreSend.runtimeType) {
      case _:
        return libreSend.command == libreReceive.command;
    }
  }


}
import 'package:test_interface/src/instances/bluetooth/i_receive_msg_parser.dart';

import '../../../../interface/actions/i_action.dart';
import '../../../../mixins/libre_msg_convert/receive_msg_decoder_mixin.dart';

mixin LibreReceiveParserMixin on ReceiveMsgDecoder implements IReceiveMsgParser {
  @override
  List<IReceiveAction> parse(List<int> value) {
    return parseMessage(value).cast<IReceiveAction>();
  }
}

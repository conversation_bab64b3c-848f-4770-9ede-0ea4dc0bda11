import 'package:test_interface/src/instances/bluetooth/device_type/b50/msg/b50_action_struct.dart';

import '../i_b50_receive_action.dart';

class GetName extends IB50ReceiveAction {
  GetName._();
  @override
  bool checkReceive(String content) {
    /*
    这里主要是为了处理特殊情况

    // 设备可用输入源
    if(data.contains('LST:') && !data.contains('CEQ:LST') && !(data.contains('DSK:LST'))){xxx}
    
    */
    return content.contains(key);
  }

  @override
  B50ActionStruct copyWith({String content = ''}) {
    return GetName._()..content = content;
  }

  @override
  String get key => 'NAM:';
}

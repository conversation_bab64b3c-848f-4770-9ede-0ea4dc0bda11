import 'dart:convert';

import '../../../../interface/actions/i_action.dart';
import '../../../../mixins/receive_actions_mixin.dart';
import '../../i_receive_msg_parser.dart';
import 'msg/i_b50_receive_action.dart';

mixin B50ReceiveParserMixin on ReceiveActionsMixin
    implements IReceiveMsgParser {
  @override
  List<IReceiveAction> parse(List<int> value) {
    final data = ascii.decode(value);
    // 截取内容
    String str;

    if (!data.contains(';')) {
      str = data.substring(data.indexOf(':') + 1, data.length);
    } else {
      str = data.substring(data.indexOf(':') + 1, data.length - 1);
    }
    
    for (final item in allReceiveActions) {
      final cAction = item as IB50ReceiveAction;
      if (cAction.checkReceive(str)) {
        return [cAction.copyWith(content: str) as IReceiveAction];
      }
    }
    //todo 收到无效消息应该记录
    return [];
  }
}

import 'dart:convert';

import '../../../../interface/actions/i_action.dart';
import '../../mixin/ble_channcel_mixin.dart';
import '../../mixin/ble_send_mixin.dart';
import 'action_equal_mixin.dart';
import 'msg/i_b50_send_action.dart';

mixin B50SendMixin on BLESendMixin, BLEChanncel, B50ActionEqualMixin {
  @override
  void send2Channel(ISendAction action) {
    assert(write != null);
    assert(action is IB50SendAction);
    write!.write(ascii.encode((action as IB50SendAction).content));
  }
}

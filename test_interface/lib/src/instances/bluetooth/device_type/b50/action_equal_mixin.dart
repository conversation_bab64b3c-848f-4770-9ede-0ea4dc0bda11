import '../../../../interface/actions/i_action.dart';
import '../../../../interface/communication/i_send.dart';
import 'msg/i_b50_receive_action.dart';
import 'msg/i_b50_send_action.dart';

mixin B50ActionEqualMixin implements ISend {
  @override
  bool checkEqual(ISendAction send, IReceiveAction receive) {
    assert(send is IB50SendAction);
    assert(receive is IB50ReceiveAction);
    final libreSend = send as IB50SendAction;
    final libreReceive = receive as IB50ReceiveAction;

    //当command不能满足需求时,需要额外的加判断
    switch (libreSend.runtimeType) {
      case _:
        return libreSend.key == libreReceive.key;
    }
  }
}

import 'dart:async';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:test_interface/src/common/typedef.dart';
import 'package:test_interface/src/instances/bluetooth/mixin/ble_channcel_mixin.dart';
import 'package:test_interface/src/instances/bluetooth/msg/i_ble_send_action.dart';
import 'package:test_interface/src/interface/actions/i_action.dart';
import 'package:test_interface/src/interface/communication/i_communication.dart';
import 'package:test_interface/src/interface/device/i_device.dart';
import 'package:test_interface/src/mixins/receive_effect_manage_mixin.dart';
import 'package:test_interface/src/mixins/send_queue.dart';

import '../../mixins/receive_actions_mixin.dart';
import 'i_ble_device.dart';
import 'i_receive_msg_parser.dart';
import 'mixin/ble_send_mixin.dart';
import 'mixin/get_receive_by_send_mixin.dart';

abstract class IBLEService extends ICommunication
    with
        BLEChanncel,
        ReceiveEffectManager,
        BLESendMixin,
        SendQueue,
        IReceiveMsgParser,
        ReceiveActionsMixin,
        GetReceiveBySendMixin {
  @override
  Future<IReceiveAction?> send(ISendAction action,
      {Duration timeout = const Duration(seconds: 5)}) async {
    assert(write != null);
    assert(action is IBLESendAction);
    final Completer<IReceiveAction?> result = Completer<IReceiveAction?>();
    final receiveAction = getReceiveAction(action as IBLESendAction);
    effectCallback(IReceiveAction receive) {
      if (result.isCompleted) return;
      result.complete(receive);
    }

    //同时注册effect获取结果
    registerEffect(receiveAction.runtimeType, effectCallback);
    sendActions([action]);

    final future = result.future;

    return future.timeout(timeout, onTimeout: () {
      //todo 超时了是否需要记录啥的
      return null;
    }).whenComplete(() {
      unregisterEffect(receiveAction.runtimeType, effectCallback);
    });
  }

  @override
  Future<FutureVoidCallback> startUp(IDevice device) async {
    //建立蓝牙连接
    assert(device is IBLEDevice);
    final bleDevice = (device as IBLEDevice).device;
    //先建立连接
    await bleDevice.connect();
    //获取通道
    final services = await bleDevice.discoverServices();
    for (var service in services) {
      List<BluetoothCharacteristic> characteristics = service.characteristics;
      for (BluetoothCharacteristic c in characteristics) {
        if (c.properties.read) {
          try {
            await c.read();
          } catch (err) {
            return Future.error('read不可用');
          }
        }
        // 写的特征值
        if (c.properties.write || c.properties.writeWithoutResponse) write = c;
        // 读的特征值
        if (c.properties.notify) {
          notify = c;
          try {
            await c.setNotifyValue(true);
          } catch (err) {
            return Future.error('notify不可用');
          }
        }
      }
    }

    //创建监听
    notify!.onValueReceived.listen((value) {
      final actions = parse(value);
      //触发节流
      receiveMsg(actions);

      //触发影响
      super.executeEffect(actions);
    });

    return () async {
      await bleDevice.disconnect();
    };
  }
}

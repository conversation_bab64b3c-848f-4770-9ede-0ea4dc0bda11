import '../../../mixins/receive_actions_mixin.dart';
import '../msg/i_ble_receive_action.dart';
import '../msg/i_ble_send_action.dart';
import 'ble_send_mixin.dart';

mixin GetReceiveBySendMixin on BLESendMixin, ReceiveActionsMixin {
  IBLEReceiveAction? getReceiveAction(IBLESendAction sendAction) {
    for (var action in allReceiveActions) {
      if (checkEqual(sendAction, action as IBLEReceiveAction)) {
        return action;
      }
    }
    assert(false, 'no receive action found for $sendAction');

    return null;
  }
}

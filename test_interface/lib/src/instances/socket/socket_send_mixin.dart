import '../../interface/actions/i_action.dart';
import '../../interface/communication/i_send.dart';
import 'base_socket_mixin.dart';
import 'msg/i_receive_action.dart';
import 'msg/i_send_action.dart';

mixin SocketSend on BaseSocket implements ISend {
  @override
  void send2Channel(ISendAction action) {
    assert(a31Socket != null);
    assert(action is ISocketSendAction);
    a31Socket!.add(_toPackage((action as ISocketSendAction).content));
  }

  @override
  bool checkEqual(ISendAction send, IReceiveAction receive) {
    assert(send is ISocketSendAction);
    assert(receive is ISocketReceiveAction);
    final luciSend = send as ISocketSendAction;
    final luciReceive = receive as ISocketReceiveAction;

    //当command不能满足需求时,需要额外的加判断
    switch (send.runtimeType) {
      case _:
        return luciSend.key == luciReceive.key;
    }
  }

  // 转换需要发送的数据
  List<int> _toPackage(String data) {
    // 固定 header
    List<int> header = [0x18, 0x96, 0x18, 0x20];
    // 计算 payload 内容的长度
    int dataLen = data.length;
    // 计算 checksum，将 payload 中每个字节的 ASCII 值相加
    int checksum = 0;
    for (int i = 0; i < data.length; i++) {
      checksum += data.codeUnitAt(i);
    }
    // 构建 length 的 little endian 格式
    List<int> lengthBytes = [];
    for (int i = 0; i < 4; i++) {
      lengthBytes.add(dataLen & 0xFF);
      dataLen >>= 8;
    }
    // 构建 checksum 的 little endian 格式
    List<int> checksumBytes = [];
    for (int i = 0; i < 4; i++) {
      checksumBytes.add(checksum & 0xFF);
      checksum >>= 8;
    }
    // 固定 reserved
    List<int> reserved = [0, 0, 0, 0, 0, 0, 0, 0];
    // 将所有部分合并成整数列表 并返回
    return [
      ...header,
      ...lengthBytes,
      ...checksumBytes,
      ...reserved,
      ...data.codeUnits
    ];
  }
}

import 'dart:async';
import 'dart:io';

import 'package:test_interface/src/common/typedef.dart';
import 'package:test_interface/src/interface/actions/i_action.dart';
import 'package:test_interface/src/interface/communication/i_communication.dart';
import 'package:test_interface/src/interface/device/i_device.dart';
import 'package:test_interface/src/mixins/send_queue.dart';

import '../../common/lock.dart';
import '../../mixins/receive_actions_mixin.dart';
import '../../mixins/receive_effect_manage_mixin.dart';
import 'base_socket_mixin.dart';
import 'i_socket_device.dart';
import 'msg/a31_receive_actions_mixin.dart';
import 'msg/a31_receive_parser_minxi.dart';
import 'msg/get_receive_from_send.dart';
import 'msg/i_send_action.dart';
import 'socket_send_mixin.dart';

class A31Service extends ICommunication
    with
        ReceiveEffectManager,
        ReceiveActionsMixin,
        A31ReceiveParserMixin,
        BaseSocket,
        SocketSend,
        SendQueue,
        GetReceiveBySendMixin,
        A31ReceiveActionsMixin {
  StreamSubscription? streamSocket;

  final Lock _connectLock = Lock();

  @override
  Future<IReceiveAction?> send(ISendAction action,
      {Duration timeout = const Duration(seconds: 5)}) {
    assert(a31Socket != null);
    assert(action is ISocketSendAction);
    final Completer<IReceiveAction?> result = Completer<IReceiveAction?>();
    final receiveAction = getReceiveAction(action as ISocketSendAction);
    effectCallback(IReceiveAction receive) {
      if (result.isCompleted) return;
      result.complete(receive);
    }

    //同时注册effect获取结果
    registerEffect(receiveAction.runtimeType, effectCallback);
    sendActions([action]);

    return result.future.timeout(timeout, onTimeout: () {
      return null;
    }).whenComplete(() {
      unregisterEffect(receiveAction.runtimeType, effectCallback);
    });
  }

  @override
  Future<FutureVoidCallback> startUp(IDevice device) async {
    assert(device is ISocketDevice);
    final luciDevice = device as ISocketDevice;
    await _connectLock.mutex(() async {
      try {
        try {
          a31Socket = await Socket.connect(luciDevice.ip, luciDevice.port);
        } catch (e) {
          //todo socket连接失败记录日志?尝试重连?
        }

        assert(a31Socket != null);
        streamSocket = a31Socket!.listen((value) {
          final actions = parse(value);
          //触发节流
          receiveMsg(actions.cast<IReceiveAction>());

          //触发影响
          super.executeEffect(actions.cast<IReceiveAction>());
        }, onDone: () {
          // luciSocketState = false;
          // _connectStateController.add(SocketConnectState.disconnect);
        }, onError: (e) {
          // luciSocketState = false;
          // _connectStateController.add(SocketConnectState.disconnect);
        }, cancelOnError: true);
        // _connectStateController.add(SocketConnectState.connected);
      } catch (e) {}
    });

    return () async {};
  }
}


part of '../a31_receive_actions_mixin.dart';


class GetNameReceiveAction extends ISocketReceiveAction {
  GetNameReceiveAction._();
  @override
  String get key => 'NAM';

  @override
  bool checkReceive(String content) {
    /*
    这里主要是为了处理特殊情况

    // 设备可用输入源
    if(data.contains('LST:') && !data.contains('CEQ:LST') && !(data.contains('DSK:LST'))){xxx}
    
    */
    return content.contains(key);
  }

  @override
  SocketActionStruct copyWith({String content = ''}) {
   
    return GetNameReceiveAction._()..content = content;
  }




}
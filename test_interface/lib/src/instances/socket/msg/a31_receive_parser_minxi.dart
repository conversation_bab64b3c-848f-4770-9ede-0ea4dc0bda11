import 'dart:convert';
import 'dart:typed_data';

import '../../../interface/actions/i_action.dart';
import '../../../mixins/receive_actions_mixin.dart';
import 'i_receive_action.dart';

mixin A31ReceiveParserMixin on  ReceiveActionsMixin {
  List<IReceiveAction> parse(List<int> value) {
    final data = _parseData(value);
    List<int> payload = data['payload'];
    String msg;
    try {
      msg = utf8.decode(payload).trim();
    } catch (e) {
      msg = String.fromCharCodes(payload);
    }
    //todo 这里是不是应该注入别的实现
    /*
         if (project.contains('A100') && msg.contains('MCU+PAS+DSK:LST:') || 
            sourceInput.value == 'USBPLAY' && msg.contains('MCU+PAS+RAKOIT:DSK:LST')
            ) {
            msg = gbk_bytes.decode(payload);
          }
          */
    for (final item in allReceiveActions) {
      final cAction = item as ISocketReceiveAction;
      if (cAction.checkReceive(msg)) {
        return [cAction.copyWith(content: msg) as IReceiveAction];
      }
    }
    //todo 收到无效消息应该记录
    return [];
  }

  // 解析从socket拿到的数据
  Map<String, dynamic> _parseData(List<int> data) {
    // 转换字节高低位
    int bytesToIntLittleEndian(Uint8List bytes) {
      try {
        int result = 0;
        for (int i = 0; i < bytes.length; i++) {
          result += bytes[i] << (8 * i);
        }
        return result;
      } catch (e) {
        return 0;
      }
    }

    // 解析数据包
    try {
      Map<String, dynamic> result = {};
      Uint8List header = Uint8List.fromList(data.sublist(0, 4));
      result['header'] = header;
      Uint8List lengthBytes = Uint8List.fromList(data.sublist(4, 8));
      int length = bytesToIntLittleEndian(lengthBytes);
      result['length'] = length;
      Uint8List checksumBytes = Uint8List.fromList(data.sublist(8, 12));
      int checksum = bytesToIntLittleEndian(checksumBytes);
      result['checksum'] = checksum;
      Uint8List reserved = Uint8List.fromList(data.sublist(12, 20));
      result['reserved'] = reserved;
      Uint8List payload = Uint8List.fromList(data.sublist(20));
      result['payload'] = payload;
      return result;
    } catch (err) {
      //todo 日志
      // Log.e('解析数据时出现错误，错误信息:$err,所以返回空Map对象{}');
      return {};
    }
  }
}

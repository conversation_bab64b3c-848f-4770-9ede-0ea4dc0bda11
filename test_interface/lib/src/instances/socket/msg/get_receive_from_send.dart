



import '../../../interface/actions/i_action.dart';
import '../../../mixins/receive_actions_mixin.dart';
import '../socket_send_mixin.dart';
import 'i_receive_action.dart';

mixin GetReceiveBySendMixin on SocketSend,ReceiveActionsMixin {

  //还需要一个方法关联send与receive
  ISocketReceiveAction? getReceiveAction(ISendAction sendAction) {
    for (var action in allReceiveActions) {
      if (checkEqual(sendAction, action as ISocketReceiveAction)) {
        return action;
      }
    }
    assert(false, 'no receive action found for $sendAction');
    
    return null;
  }
}

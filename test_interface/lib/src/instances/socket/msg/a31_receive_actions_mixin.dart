import 'package:test_interface/src/instances/socket/msg/i_receive_action.dart';
import 'package:test_interface/src/instances/socket/msg/i_send_action.dart';
import 'package:test_interface/src/instances/socket/msg/socket_msg_struct.dart';

import '../../../interface/actions/i_action.dart';
import '../../../mixins/receive_actions_mixin.dart';

part 'receive/get_name.dart';
part 'send/get_name.dart';

mixin A31ReceiveActionsMixin on ReceiveActionsMixin {
  @override
  List<IReceiveAction> get allReceiveActions => [
        GetNameReceiveAction._(),
      ];
}

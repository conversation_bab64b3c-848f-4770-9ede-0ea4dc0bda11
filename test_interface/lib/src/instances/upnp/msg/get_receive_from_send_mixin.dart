import '../../../interface/actions/i_action.dart';
import '../../../mixins/receive_actions_mixin.dart';
import 'i_receive_action.dart';
import 'i_send_action.dart';

mixin GetReceiveBySendMixin on ReceiveActionsMixin {
  //还需要一个方法关联send与receive
  IUPnPReceiveAction? getReceiveAction(ISendAction sendAction) {
    assert(sendAction is IUPnPSendAction);
    for (var action in allReceiveActions) {
      if ((sendAction as IUPnPSendAction).key ==
          (action as IUPnPReceiveAction).key) {
        return action;
      }
    }
    assert(false, 'no receive action found for $sendAction');

    return null;
  }
}

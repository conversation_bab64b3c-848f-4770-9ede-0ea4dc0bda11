import 'dart:convert';
import 'dart:io';

import 'package:test_interface/src/common/typedef.dart';
import 'package:test_interface/src/interface/actions/i_action.dart';
import 'package:test_interface/src/interface/communication/i_communication.dart';
import 'package:test_interface/src/interface/device/i_device.dart';
import 'package:xml2json/xml2json.dart';

import '../../mixins/receive_actions_mixin.dart';
import 'base_channel_mixin.dart';
import 'msg/get_receive_from_send_mixin.dart';
import 'msg/i_send_action.dart';

class UpnpService extends ICommunication
    with BaseChannelMixin, ReceiveActionsMixin, GetReceiveBySendMixin {
  @override
  Future<IReceiveAction?> send(ISendAction action,
      {Duration timeout = const Duration(seconds: 5)}) async {
    if (client == null) {
      throw Exception("client is null");
    }
    assert(action is IUPnPSendAction);
    //判断协议
    //todo 暂时没有啥判断,http也走httpClinet
    //判断请求方式 get/post
    if (action is IUPnPSendPostAction) {
      //post请求
      final uri = Uri.tryParse(action.url);
      if (uri == null) {
        throw Exception("url is a invalid url");
      }
      final request = await client!.postUrl(uri);
      Map headers = {
        'SOAPACTION': action.hsa,
        'Content-Type': 'text/xml;charset="utf-8"',
      };
      // 添加请求头
      headers.forEach((key, value) {
        request.headers.add(key, value);
      });
      // 添加请求体数据
      request.write(action.dat);
      HttpClientResponse response = await request.close();
      String responseBody = await response.transform(utf8.decoder).join();
      if (response.statusCode == 200) {
        //获取receiveAction
        final receiveAction = getReceiveAction(action);
        if (receiveAction != null) {
          return null; //没有定制reveiveAction,代表不需要处理,直接返回null
        }
        // Log.p(responseBody);
        // 解码XML实体
        String xml = responseBody.replaceAll("\r", "").replaceAll("\n", "");
        Map data = _translateInfo(xml);
        if (data.containsKey('s:Envelope')) {
          if (data['s:Envelope'].containsKey('s:Body')) {
            return receiveAction!
                .copyWith(responseBody: data['s:Envelope']['s:Body']);
          }
        }

        return receiveAction!.copyWith(responseBody: data);
      } else {
        return Future.error(response.statusCode);
      }
    } else {
      final cAction = action as IUPnPSendAction;
      //get请求
      final uri = Uri.tryParse(cAction.url);
      if (uri == null) {
        throw Exception("url is a invalid url");
      }
      // 发起 HTTPS GET 请求
      HttpClientRequest request = await client!.getUrl(uri);
      HttpClientResponse response = await request.close();
      String responseBody = await response.transform(utf8.decoder).join();
      // 打印状态码和响应内容
      if (response.statusCode == 200) {
        //获取receiveAction
        final receiveAction = getReceiveAction(action);
        if (receiveAction != null) {
          return null; //没有定制reveiveAction,代表不需要处理,直接返回null
        }
        //todo 暂时业务没有用到返回值,但是还是返回了,用jsonDecode兼容格式
        return receiveAction!.copyWith(responseBody: jsonDecode(responseBody));
      } else {
        return Future.error(response.statusCode);
      }
    }
  }

  @override
  Future<FutureVoidCallback> startUp(IDevice device) async {
    //创建httpClient
    client = HttpClient()
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
    return () async {};
  }

  Map _translateInfo(data) {
    final Xml2Json xml2json = Xml2Json();
    xml2json.parse(data);
    return json.decode(xml2json.toParker());
  }
}

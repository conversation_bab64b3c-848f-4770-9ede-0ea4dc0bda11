import 'package:test_interface/src/instances/luci/msg/i_send_action.dart';
import 'package:test_interface/src/instances/luci/msg/send/get_info.dart';
import 'package:test_interface/src/interface/actions/i_action.dart';
import 'package:test_interface/src/interface/communication/i_send.dart';

import 'base_socket_mixin.dart';
import 'msg/i_receive_action.dart';
import 'msg/luci_receive_actions.dart' as receive_actions;
import '../../mixins/libre_msg_convert/libre_send_msg_encoder.dart';

mixin LuciSend on BaseSocket implements ISend {
  @override
  void send2Channel(ISendAction action) {
    assert(luciSocket != null);
    assert(action is ILuciSendAction);
    luciSocket!
        .add(LibreSendMsgEncoder.commandEncode(action: action as ILuciSendAction));
  }

  @override
  bool checkEqual(ISendAction send, IReceiveAction receive
      ) {
    assert(send is ILuciSendAction);
    assert(receive is ILuciReceiveAction);
    final luciSend = send as ILuciSendAction;
    final luciReceive = receive as ILuciReceiveAction;
   
    //当command不能满足需求时,需要额外的加判断,比如当同时发多条getInfo但是取的是不同的消息时
    switch (send.runtimeType) {
      case GetInfoAction _:
        if (receive is! receive_actions.GetInfoAction) {
          return false;
        }
        //通过判断content之类的判断收发是否是同一个action
        //例:
        if (luciSend.content.contains('READ_fwdownload_xml')) {
          return receive.content.contains('READ_fwdownload_xml');
        }
        return false;
      case _:
        return send.command == luciReceive.command;
    }
  }
}

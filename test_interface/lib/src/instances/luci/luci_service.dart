import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:test_interface/src/common/typedef.dart';
import 'package:test_interface/src/instances/luci/luci_device.dart';
import 'package:test_interface/src/mixins/receive_effect_manage_mixin.dart';
import 'package:test_interface/src/interface/actions/i_action.dart';
import 'package:test_interface/src/interface/communication/i_communication.dart';
import 'package:test_interface/src/interface/device/i_device.dart';

import '../../common/lock.dart';
import '../../mixins/receive_actions_mixin.dart';
import '../../mixins/send_queue.dart';
import 'base_socket_mixin.dart';
import 'luci_send_mixin.dart';
import 'msg/i_send_action.dart';
import '../../mixins/libre_msg_convert/receive_msg_decoder_mixin.dart';
import 'msg/get_receive_from_send.dart';
import 'msg/luci_receive_actions.dart';

class LuciService extends ICommunication
    with
        ReceiveEffectManager, //维护effect
        ReceiveActionsMixin,
        LuciReceiveActions, //维护收消息的所有类型
        ReceiveMsgDecoder, //解析收消息
        BaseSocket, //基础socket能力
        LuciSend, //发送与消息对比
        SendQueue, //发送节流
        GetReceiveBySendMixin //根据send获取对应的receive
//这里的顺序主要跟dispose相关,mixin的dispose是从后往前执行的
{
  StreamSubscription? streamLuci;

  bool luciSocketState = false;
  final Lock _connectLock = Lock();

  final StreamController<SocketConnectState> _connectStateController =
      StreamController.broadcast();

  @override
  Future<FutureVoidCallback> startUp(IDevice device) async {
    assert(device is LuciService);
    final luciDevice = device as LuciDevice;
    await _connectLock.mutex(() async {
      try {
        try {
          ByteData cert = await rootBundle.load('assets/cert/client.pem');
          ByteData key = await rootBundle.load('assets/cert/client.key');
          SecurityContext securityContext = SecurityContext();
          securityContext.useCertificateChainBytes(cert.buffer.asUint8List(),
              password: '');
          securityContext.usePrivateKeyBytes(key.buffer.asUint8List(),
              password: '');

          luciSocket = await SecureSocket.connect(
              luciDevice.ip, luciDevice.port,
              onBadCertificate: (X509Certificate cert) => true,
              context: securityContext);
        } catch (e) {
          luciSocket = await Socket.connect(luciDevice.ip, luciDevice.port);
        }

        assert(luciSocket != null);
        streamLuci = luciSocket!.listen((value) {
          final actions = parseMessage(value);
          //触发节流
          receiveMsg(actions.cast<IReceiveAction>());

          //触发影响
          super.executeEffect(actions.cast<IReceiveAction>());
        }, onDone: () {
          luciSocketState = false;
          _connectStateController.add(SocketConnectState.disconnect);
        }, onError: (e) {
          luciSocketState = false;
          _connectStateController.add(SocketConnectState.disconnect);
        }, cancelOnError: true);
        _connectStateController.add(SocketConnectState.connected);

        //创建完直接注册
        //todo
      } catch (e) {}
    });

    return () async {};
  }

  @override
  Future<void> dispose() async {
    super.dispose();
    _connectStateController.close();
    streamLuci?.cancel();
  }

  @override
  Future<IReceiveAction?> send(ISendAction action,
      {Duration timeout = const Duration(seconds: 5)}) {
    assert(luciSocket != null);
    assert(action is ILuciSendAction);
    final Completer<IReceiveAction?> result = Completer<IReceiveAction?>();
    final receiveAction = getReceiveAction(action as ILuciSendAction);
    effectCallback(IReceiveAction receive) {
      if (result.isCompleted) return;
      result.complete(receive);
    }

    //同时注册effect获取结果
    registerEffect(receiveAction.runtimeType, effectCallback);
    sendActions([action]);

    return result.future.timeout(timeout, onTimeout: () {
      return null;
    }).whenComplete(() {
      unregisterEffect(receiveAction.runtimeType, effectCallback);
    });
  }

  // VoidCallback listenDownloadProgress(
  //     OutValueCallback<Uint8List> downloadProgress) {
  //       registerEffect<DownloadProgress>((DownloadProgress action) {

  //       });
  //   return () {
  //     unregisterEffect();
  //   };
  // }
}

enum SocketConnectState {
  connected,
  disconnect,
}

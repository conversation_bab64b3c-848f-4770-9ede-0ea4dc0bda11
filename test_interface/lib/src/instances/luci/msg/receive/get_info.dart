// ignore_for_file: prefer_function_declarations_over_variables

part of '../luci_receive_actions.dart';



class GetInfoAction extends ILuciReceiveAction {
  GetInfoAction._();

  @override
  int get command => 0xD0; //208

  @override
  GetInfoAction copyWith({String content=''}) {
    return GetInfoAction._()..content = content;
  }
}

//-----------------------effects---------------

///获取fwDownload结果
/// action.content示例,固定格式:fwdownload_xml:http://xxxxxx/

final getFwDownloadEffect =
    (OutValueCallback<String> callback) => (ILuciReceiveAction action) {
          assert(action is GetInfoAction);
          if (action.content.startsWith("fwdownload_xml:")) {
            var url = action.content.substring(15);
            callback(url);
          }
        };

///获取Model结果
/// action.content示例,固定格式:Model:xxxxxx
final getModelEffect =
    (OutValueCallback<String> callback) => (ILuciReceiveAction action) {
          assert(action is GetInfoAction);
          if (action.content.startsWith("Model:")) {
            var model = action.content.substring(6);
            callback(model);
          }
        };

///获取HardwarePlatform结果
/// action.content示例,固定格式:HardwarePlatform:xxxxxx
final getHardwarePlatformEffect =
    (OutValueCallback<String> callback) => (ILuciReceiveAction action) {
          assert(action is GetInfoAction);
          if (action.content.startsWith("HardwarePlatform:")) {
            var hardwarePlatform = action.content.substring(17);
            callback(hardwarePlatform);
          }
        };

///获取xmodem_pkt_size结果
/// action.content示例,固定格式:xmodem_pkt_size:xxxxxx
final getXmodemPktSizeEffect =
    (OutValueCallback<String> callback) => (ILuciReceiveAction action) {
          assert(action is GetInfoAction);
          if (action.content.startsWith("xmodem_pkt_size:")) {
            var xmodemPktSize = action.content.substring(16);
            callback(xmodemPktSize);
          }
        };

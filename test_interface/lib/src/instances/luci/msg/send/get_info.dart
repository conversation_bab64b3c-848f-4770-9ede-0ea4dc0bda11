
import 'package:test_interface/src/instances/luci/msg/i_send_action.dart';

class GetInfoAction extends ILuciSendAction {
  GetInfoAction(this.infoName);
  final String infoName;

  @override
  int get command => 0xD0; //208

  @override
  String get content => infoName;

}

final readFwDownload=GetInfoAction("READ_fwdownload_xml");

final readModel=GetInfoAction("READ_Model");

final readHardwarePlatform=GetInfoAction("READ_HardwarePlatform");

final readXmodemPktSize= GetInfoAction("READ_xmodem_pkt_size");


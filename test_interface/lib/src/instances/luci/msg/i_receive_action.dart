import 'package:test_interface/src/interface/actions/i_action.dart';

import '../../../mixins/libre_msg_convert/libre_msg_struct.dart';

abstract class ILuciReceiveAction extends IReceiveAction
    with LibreMsgStruct,ReceiveCopyMixin {
  @override
  String content = '';
}

class NoneReceiveAction extends ILuciReceiveAction {
  NoneReceiveAction();
  @override
  int get command => 0;

  @override
  ILuciReceiveAction copyWith({
    String? content,
  }) {
    return NoneReceiveAction().copyWith(
      content: content,
    );
  }

  @override
  String get content => '';
}

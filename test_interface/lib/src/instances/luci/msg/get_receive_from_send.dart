import 'package:test_interface/src/instances/luci/msg/i_receive_action.dart';

import '../../../mixins/receive_actions_mixin.dart';
import '../luci_send_mixin.dart';
import 'i_send_action.dart';



mixin GetReceiveBySendMixin on LuciSend,ReceiveActionsMixin {

  //还需要一个方法关联send与receive
  ILuciReceiveAction? getReceiveAction(ILuciSendAction sendAction) {
    for (var action in allReceiveActions) {
      if (checkEqual(sendAction, action as ILuciReceiveAction)) {
        return action;
      }
    }
    assert(false, 'no receive action found for $sendAction');
    
    return NoneReceiveAction();
  }
}

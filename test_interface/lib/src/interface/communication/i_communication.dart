import 'package:test_interface/src/interface/device/i_device.dart';

import '../../common/typedef.dart';
import '../actions/i_action.dart';

abstract class ICommunication {
  ///启动服务
  /// [device] 设备
  /// 返回值：关闭服务函数
  Future<FutureVoidCallback> startUp(IDevice device);

  ///发送消息
  /// [action] 要发送的消息
  /// 返回值：接收消息的Future
  Future<IReceiveAction?> send(ISendAction action,{Duration timeout=const Duration(seconds: 5)});

  Future<void> dispose()async{}

}

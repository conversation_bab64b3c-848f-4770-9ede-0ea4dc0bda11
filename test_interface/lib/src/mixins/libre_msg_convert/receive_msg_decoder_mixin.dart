import 'dart:convert';

import '../receive_actions_mixin.dart';
import 'libre_msg_struct.dart';

mixin ReceiveMsgDecoder on ReceiveActionsMixin {
  List<LibreMsgStruct> parseMessage(List<int> msg) {
    List<LibreMsgStruct> msgList = [];

    void addMsg(List<int> msg) {
      // 解析Remote ID
      List<int> remoteIdCode = msg.sublist(0, 2);
      int remoteId = (remoteIdCode[0] * 256) + remoteIdCode[1];

      // 解析Command Type
      List<int> commandTypeCode = msg.sublist(2, 3);
      int commandType = commandTypeCode[0];

      // 解析Command
      List<int> commandCode = msg.sublist(3, 5);
      int command = (commandCode[0] * 256) + commandCode[1];

      // 解析Command Status
      List<int> commandStatus = msg.sublist(5, 6);
      int commandStatusValue = commandStatus[0];

      // 解析CRC
      List<int> crcCode = msg.sublist(6, 8);
      int crc = (crcCode[0] * 256) + crcCode[1];

      // 解析Data Length
      List<int> dataLengthCode = msg.sublist(8, 10);
      int dataLength = (dataLengthCode[0] * 256) + dataLengthCode[1];

      // 解析Data
      List<int> dataCode;

      if (dataLength == 0) {
        dataCode = [];
      } else {
        dataCode = msg.sublist(10, 10 + dataLength);
      }

      String data;

      try {
        data = utf8.decode(dataCode);
      } catch (e) {
        data = '';
      }

      if (10 + dataLength != msg.length) {
        List<int> newCode = msg.sublist(10 + dataLength);
        addMsg(newCode);
      }
      final map = {
        'remoteId': '$remoteId',
        'commandType': '$commandType',
        'command': command,
        'commandStatus': '$commandStatusValue',
        'crc': '$crc',
        'dataLength': '$dataLength',
        'messageData': data.replaceAll(RegExp(r'\n'), ''),
      };
      final action = _convert2Action(map);
      msgList.addAll(action);
    }

    addMsg(msg);
    return msgList;
  }

  List<LibreMsgStruct> _convert2Action(Map<String, dynamic> map) {
    final List<LibreMsgStruct> actions = List.empty(growable: true);
    assert(map['command'] != null);
    final code = map['command'];
    for (var action in allReceiveActions) {
      assert(action is LibreMsgStruct);
      final cAction = action as LibreMsgStruct;
      if (cAction.command == code) {
        //提供一个备份消息出去
        assert(action is ReceiveCopyMixin);
        final cAction = action as ReceiveCopyMixin;
        actions.add(cAction.copyWith(content: map['messageData']));
      }
    }
    return actions;
  }
}

// ignore_for_file: constant_identifier_names



mixin LibreMsgStruct {
  int get command;
  String get content;
  CommandType get commandType => CommandType.GET;
  int get commandStatus => 0x00;
}

mixin ReceiveCopyMixin on LibreMsgStruct{
  LibreMsgStruct copyWith({
    String content = '',
  });
}

enum CommandType {
  GET._(0x01),
  SET._(0x02);

  final int value;
  const CommandType._(this.value);
}



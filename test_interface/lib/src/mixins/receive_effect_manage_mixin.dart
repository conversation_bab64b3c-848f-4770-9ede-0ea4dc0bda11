import 'package:test_interface/src/interface/actions/i_action.dart';

import '../common/typedef.dart';

mixin ReceiveEffectManager {
  final Map<Type, List<OutValueCallback<IReceiveAction>>> effects = {};

  //执行
  void executeEffect(List<IReceiveAction> actions) {
    for (var action in actions) {
      for (var effect
          in effects.entries.where((e) => e.key == action.runtimeType)) {
        for (var element in effect.value) {
          element(action);
        }
      }
    }
  }

  void registerEffect(
      Type receiveType, OutValueCallback<IReceiveAction> effect) {
    if (!effects.containsKey(receiveType)) {
      effects.addAll({
        receiveType: [effect]
      });
    } else {
      effects[receiveType]!.add(effect);
    }
  }

  void unregisterEffect(
      Type receiveType, OutValueCallback<IReceiveAction> effect) {
    if (effects.containsKey(receiveType)) {
      effects[receiveType]!.remove(effect);
    }
  }
}

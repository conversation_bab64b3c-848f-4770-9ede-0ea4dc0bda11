import 'dart:async';

import 'package:test_interface/src/interface/communication/i_communication.dart';
import 'package:test_interface/src/interface/communication/i_send.dart';

import '../common/typedef.dart';
import '../interface/actions/i_action.dart';

///实现节流队列,控制发送频率
///
/// 实现方式, 假定tcp为稳定,一发一收,同时设定超时时间
mixin SendQueue on ISend,ICommunication {
  final List<ISendAction> _sendQueue = [];
  ISendAction? _currentAction;

  final StreamController<IReceiveAction> _receiveSubscription =
      StreamController.broadcast();

  ///发送间隔时间 ms
  int sendInterval = 50;

  void sendActions(List<ISendAction> actions) {
    _sendQueue.addAll(actions);
    if (_currentAction == null) {
      _sendNext();
    }
  }

  ///发送下一个
  void _sendNext() {
    if (_sendQueue.isEmpty) {
      _currentAction = null;
      return;
    }
    //取出第一个
    _currentAction = _sendQueue.removeAt(0);

    VoidCallback? cancelReceiveListener;
    VoidCallback? cancelTimeoutListener;
    //监听receive通道
    cancelReceiveListener = _receiveSubscription.stream.listen((event) {
      assert(_currentAction != null);
      //判断发收是否是一对
      if (checkEqual(_currentAction!, event)) {
        cancelTimeoutListener?.call();
        cancelReceiveListener?.call();
        //发送下一个
        _sendNext();
      }
    }).cancel;
    //同时开始超时倒计时
    cancelTimeoutListener = Timer(Duration(milliseconds: sendInterval), () {
      cancelReceiveListener?.call();
      cancelTimeoutListener?.call();
      //发送下一个
      _sendNext();
    }).cancel;
    _send2socket(_currentAction!);
  }

  void _send2socket(ISendAction action) {
    send2Channel(action);
  }

  ///接收到的消息
  void receiveMsg(
    List<IReceiveAction> actions,
  ) {
    _receiveSubscription.addStream(Stream.fromIterable(actions));
  }

  @override
  Future<void> dispose() async {
    super.dispose();
    _receiveSubscription.close();
  }
}

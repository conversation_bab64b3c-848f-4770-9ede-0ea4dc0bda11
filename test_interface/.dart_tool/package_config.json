{"configVersion": 2, "packages": [{"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "bluez", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/bluez-0.8.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/develop/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_blue_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/flutter_blue_plus-1.35.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_blue_plus_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/flutter_blue_plus_android-4.0.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_blue_plus_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/flutter_blue_plus_darwin-4.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_blue_plus_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/flutter_blue_plus_linux-3.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_blue_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/flutter_blue_plus_platform_interface-4.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_blue_plus_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/flutter_blue_plus_web-3.0.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/develop/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/develop/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/develop/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "xml2json", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/http%2558%2547%2547192.168.10.105%25584001/xml2json-6.2.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "test_interface", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.6"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///Users/<USER>/develop/flutter", "flutterVersion": "3.32.2", "pubCache": "file:///Users/<USER>/.pub-cache"}
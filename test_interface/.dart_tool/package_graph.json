{"roots": ["test_interface"], "packages": [{"name": "test_interface", "version": "0.0.1", "dependencies": ["flutter", "flutter_blue_plus", "xml2json"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "xml2json", "version": "6.2.7", "dependencies": ["xml"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "flutter_blue_plus", "version": "1.35.5", "dependencies": ["flutter", "flutter_blue_plus_android", "flutter_blue_plus_darwin", "flutter_blue_plus_linux", "flutter_blue_plus_platform_interface", "flutter_blue_plus_web"]}, {"name": "flutter_blue_plus_platform_interface", "version": "4.0.2", "dependencies": ["flutter"]}, {"name": "flutter_blue_plus_web", "version": "3.0.1", "dependencies": ["flutter", "flutter_blue_plus_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "flutter_blue_plus_darwin", "version": "4.0.1", "dependencies": ["flutter", "flutter_blue_plus_platform_interface"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "flutter_blue_plus_linux", "version": "3.0.2", "dependencies": ["bluez", "flutter", "flutter_blue_plus_platform_interface", "rxdart"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "flutter_blue_plus_android", "version": "4.0.5", "dependencies": ["flutter", "flutter_blue_plus_platform_interface"]}, {"name": "bluez", "version": "0.8.3", "dependencies": ["dbus"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "args", "version": "2.7.0", "dependencies": []}], "configVersion": 1}
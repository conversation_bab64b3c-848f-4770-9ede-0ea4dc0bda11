{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "flutter_blue_plus_darwin", "path": "/Users/<USER>/.pub-cache/hosted/http%58%47%47192.168.10.105%584001/flutter_blue_plus_darwin-4.0.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "flutter_blue_plus_android", "path": "/Users/<USER>/.pub-cache/hosted/http%58%47%47192.168.10.105%584001/flutter_blue_plus_android-4.0.5/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "flutter_blue_plus_darwin", "path": "/Users/<USER>/.pub-cache/hosted/http%58%47%47192.168.10.105%584001/flutter_blue_plus_darwin-4.0.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "flutter_blue_plus_linux", "path": "/Users/<USER>/.pub-cache/hosted/http%58%47%47192.168.10.105%584001/flutter_blue_plus_linux-3.0.2/", "native_build": false, "dependencies": [], "dev_dependency": false}], "windows": [], "web": [{"name": "flutter_blue_plus_web", "path": "/Users/<USER>/.pub-cache/hosted/http%58%47%47192.168.10.105%584001/flutter_blue_plus_web-3.0.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "flutter_blue_plus", "dependencies": ["flutter_blue_plus_android", "flutter_blue_plus_darwin", "flutter_blue_plus_linux", "flutter_blue_plus_web"]}, {"name": "flutter_blue_plus_android", "dependencies": []}, {"name": "flutter_blue_plus_darwin", "dependencies": []}, {"name": "flutter_blue_plus_linux", "dependencies": []}, {"name": "flutter_blue_plus_web", "dependencies": []}], "date_created": "2025-06-09 17:21:30.812118", "version": "3.32.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}
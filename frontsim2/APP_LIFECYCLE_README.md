# 应用生命周期监听功能

## 功能概述

这个功能实现了应用生命周期的智能监听，当应用进入后台时自动断开所有设备连接，当应用恢复前台时自动重新连接设备，有效管理资源和连接状态。

## 主要特性

- ✅ **自动断连**：应用进入后台时断开所有设备连接
- ✅ **智能重连**：应用恢复前台时重新连接之前断开的设备
- ✅ **延迟机制**：支持配置断连和重连的延迟时间，避免频繁切换
- ✅ **重试策略**：智能重连策略，支持多次重试和指数退避
- ✅ **灵活配置**：支持多种预设配置和自定义配置
- ✅ **状态管理**：完整的状态跟踪和日志记录

## 核心文件

1. **AppLifecycleObserver** (`src/ui/business/app_lifecycle_observer.dart`)
   - 核心生命周期观察者类
   - 监听应用前台/后台状态变化
   - 管理设备连接和断连

2. **DeviceReconnectStrategy** (`src/helpers/device_reconnect_strategy.dart`)
   - 智能重连策略
   - 支持重试机制和指数退避
   - 过滤可重连设备

3. **AppLifecycleConfig** (`src/models/app_lifecycle_config.dart`)
   - 配置类，支持多种预设配置
   - 控制断连延迟、重连延迟等行为

4. **AppLifecycleManager** (`src/helpers/app_lifecycle_manager.dart`)
   - 管理器类，提供简单易用的API
   - 封装了常用的配置和操作

## 使用方法

### 基本使用

在 `main.dart` 中已经自动初始化，使用默认配置：

```dart
// 在 main() 函数中
AppLifecycleObserver.instance.init();
```

### 高级配置

如果需要自定义配置，可以使用 `AppLifecycleManager`：

```dart
// 使用保守配置（更长的延迟时间）
AppLifecycleManager.initWithConservativeConfig();

// 使用激进配置（立即断连重连）
AppLifecycleManager.initWithAggressiveConfig();

// 仅断连，不自动重连
AppLifecycleManager.initDisconnectOnly();

// 自定义配置
AppLifecycleManager.initWithCustomConfig(
  AppLifecycleConfig(
    enableAutoDisconnect: true,
    enableAutoReconnect: true,
    disconnectDelaySeconds: 3,  // 进入后台3秒后断连
    reconnectDelaySeconds: 1,   // 恢复前台1秒后重连
  )
);
```

### 运行时操作

```dart
// 获取当前状态
final status = AppLifecycleManager.getCurrentStatus();
print('是否在后台: ${status.isInBackground}');
print('等待重连设备数量: ${status.pendingReconnectCount}');

// 手动断连所有设备
await AppLifecycleManager.forceDisconnectAll();

// 手动重连设备
await AppLifecycleManager.forceReconnectDevices();

// 动态更新配置
AppLifecycleManager.updateConfig(
  AppLifecycleConfig.conservativeConfig
);
```

## 配置选项

### 预设配置

1. **默认配置** (`defaultConfig`)
   - 自动断连：启用
   - 自动重连：启用
   - 断连延迟：2秒
   - 重连延迟：1秒

2. **保守配置** (`conservativeConfig`)
   - 断连延迟：5秒
   - 重连延迟：2秒

3. **激进配置** (`aggressiveConfig`)
   - 断连延迟：0秒（立即）
   - 重连延迟：0秒（立即）

4. **仅断连配置** (`disconnectOnlyConfig`)
   - 自动重连：禁用

### 自定义配置参数

```dart
AppLifecycleConfig(
  enableAutoDisconnect: true,        // 是否启用自动断连
  enableAutoReconnect: true,         // 是否启用自动重连
  disconnectDelaySeconds: 2,         // 断连延迟时间（秒）
  reconnectDelaySeconds: 1,          // 重连延迟时间（秒）
  showDisconnectNotification: false, // 是否显示断连通知
  showReconnectNotification: false,  // 是否显示重连通知
)
```

## 工作原理

1. **进入后台**：
   - 应用检测到 `AppLifecycleState.paused` 状态
   - 启动断连延迟定时器
   - 延迟时间到达后，断开所有设备连接
   - 缓存断开的设备IP列表用于重连

2. **恢复前台**：
   - 应用检测到 `AppLifecycleState.resumed` 状态
   - 取消断连定时器（如果还在等待中）
   - 启动重连延迟定时器
   - 延迟时间到达后，使用智能重连策略重新连接设备

3. **智能重连**：
   - 最多重试3次
   - 使用指数退避策略（2s, 4s, 6s）
   - 过滤可重连的设备
   - 记录重连成功和失败的设备

## 日志输出

系统会输出详细的日志信息，方便调试和监控：

```
AppLifecycleObserver initialized with config: enableAutoDisconnect=true, enableAutoReconnect=true
App paused to background
Scheduling disconnect in 2s
Disconnecting 2 devices
Device ************* disconnected successfully
Device ************* disconnected successfully
All devices disconnected successfully
App resumed from background
Scheduling reconnect in 1s
Attempting to reconnect 2 devices
Reconnect attempt 1/3 for 2 devices
Successfully reconnected devices: [*************, *************]
```

## 注意事项

1. **延迟配置**：建议设置适当的延迟时间，避免短暂切换应用时频繁断连重连
2. **网络环境**：重连成功率依赖于网络环境和设备状态
3. **资源管理**：在应用销毁时会自动清理所有资源和定时器
4. **线程安全**：所有操作都在主线程中执行，确保线程安全

## 故障排除

如果设备重连失败，请检查：
1. 设备是否仍在同一网络中
2. 设备是否正常工作
3. 网络连接是否稳定
4. 查看日志输出获取详细错误信息

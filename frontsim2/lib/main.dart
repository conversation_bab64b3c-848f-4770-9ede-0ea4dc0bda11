import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rk_get/rk_get.dart';
import 'package:rk_kvstore_mmkv/rk_kvstore_mmkv.dart' as mmkv;
import 'package:flutter_blue_plus_platform_interface/flutter_blue_plus_platform_interface.dart';
import 'package:rk_package/rk_package.dart';

import 'src/configs/local.dart';
import 'src/configs/log.dart' as log;
import 'src/configs/theme.dart';
import 'src/global/global_store.dart';
import 'src/routes/route_config.dart';
import 'src/routes/route_config.get_x_router_config.dart';
import 'src/servers/services.dart';
import 'src/services/devices_service.dart';
import 'src/ui/business/app_lifecycle_observer.dart';
import 'src/ui/radio_gage/routes.dart';

void main() {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();
    //todo 需要等第一帧获取到屏幕大小,到时候配合启动页使用
    await ScreenUtil.ensureScreenSize();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    //日志初始化
    log.init();
    // 缓存
    mmkv.init();
    FlutterBluePlus.setLogLevel(LogLevel.none);
    Get.put(ThemeConfig());

    //初始化deviceService
    Get.registerLazySingleton(() {
      final devices = LibreDevicesService();
      devices.init();
      return devices;
    });

    await GlobalStore().init();
    //servers
    serverInit();    // 初始化应用生命周期观察者
    // 可以根据需要选择不同的配置：
    // AppLifecycleManager.initWithDefaultConfig();        // 默认配置
    // AppLifecycleManager.initWithConservativeConfig();   // 保守配置（更长延迟）
    // AppLifecycleManager.initWithAggressiveConfig();     // 激进配置（立即断连重连）
    // AppLifecycleManager.initDisconnectOnly();           // 仅断连，不自动重连
    AppLifecycleObserver.instance.init();

    FlutterError.onError = (FlutterErrorDetails details) {
      log.log.logError(
        'Flutter Global Sync Error',
        details.exception,
        details.stack,
      );
    };
    return runApp(const MyApp());
  }, (error, stackTrace) {
    log.log.logError('Flutter Global Async Error:', error, stackTrace);
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final botToastBuilder = BotToastInit();
    return Obx(() => GetMaterialApp(
          title: 'BRIONVEGA',
          getPages: [...Sim2RouteConfig.pages, ...RadioPageRoutes.radioPages],
          initialRoute: RouteNames.homeView,
          builder: (context, child) {
            ScreenUtil.init(context, designSize: Size(1179, 2758));
            child = botToastBuilder(context, child);
            return child;
          },
          navigatorObservers: [BotToastNavigatorObserver()],
          localizationsDelegates: localizationsDelegates,
          locale: GlobalStore().locale,
          supportedLocales: supportedLocales,
          debugShowCheckedModeBanner: kDebugMode,
          theme: ThemeConfig.to.themeData.value,
          enableLog: true,
          logWriterCallback: (text, {isError=false}) {
            if (isError == true) {
              log.log.logError(text);
            } else {
              log.log.logDebug(text);
            }
          },
        ));
  }
}

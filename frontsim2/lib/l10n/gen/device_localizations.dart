import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'device_localizations_en.dart';
import 'device_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of DeviceLocalizations
/// returned by `DeviceLocalizations.of(context)`.
///
/// Applications need to include `DeviceLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen/device_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: DeviceLocalizations.localizationsDelegates,
///   supportedLocales: DeviceLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the DeviceLocalizations.supportedLocales
/// property.
abstract class DeviceLocalizations {
  DeviceLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static DeviceLocalizations? of(BuildContext context) {
    return Localizations.of<DeviceLocalizations>(context, DeviceLocalizations);
  }

  static const LocalizationsDelegate<DeviceLocalizations> delegate = _DeviceLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @general_settings.
  ///
  /// In zh, this message translates to:
  /// **'常规设置'**
  String get general_settings;

  /// No description provided for @about.
  ///
  /// In zh, this message translates to:
  /// **'关于'**
  String get about;

  /// No description provided for @net_and_conn.
  ///
  /// In zh, this message translates to:
  /// **'网络与连接'**
  String get net_and_conn;

  /// No description provided for @general.
  ///
  /// In zh, this message translates to:
  /// **'常规'**
  String get general;

  /// No description provided for @product_name.
  ///
  /// In zh, this message translates to:
  /// **'设备名称'**
  String get product_name;

  /// No description provided for @software_version.
  ///
  /// In zh, this message translates to:
  /// **'软件版本'**
  String get software_version;

  /// No description provided for @iP_ID.
  ///
  /// In zh, this message translates to:
  /// **'IP ID'**
  String get iP_ID;

  /// No description provided for @mAC_ID.
  ///
  /// In zh, this message translates to:
  /// **'MAC ID'**
  String get mAC_ID;

  /// No description provided for @connectivity.
  ///
  /// In zh, this message translates to:
  /// **'连接方式'**
  String get connectivity;

  /// No description provided for @google_cast.
  ///
  /// In zh, this message translates to:
  /// **'Google Cast'**
  String get google_cast;

  /// No description provided for @report_issue.
  ///
  /// In zh, this message translates to:
  /// **'报告问题'**
  String get report_issue;

  /// No description provided for @factory_reset.
  ///
  /// In zh, this message translates to:
  /// **'恢复出厂设置'**
  String get factory_reset;

  /// No description provided for @ethernet.
  ///
  /// In zh, this message translates to:
  /// **'以太网'**
  String get ethernet;

  /// No description provided for @wifi.
  ///
  /// In zh, this message translates to:
  /// **'WiFi'**
  String get wifi;

  /// No description provided for @gcast1.
  ///
  /// In zh, this message translates to:
  /// **'已启用Cast'**
  String get gcast1;

  /// No description provided for @gcast2.
  ///
  /// In zh, this message translates to:
  /// **'激活Cast'**
  String get gcast2;

  /// No description provided for @gcast3.
  ///
  /// In zh, this message translates to:
  /// **'向Google发送设备使用和崩溃报告'**
  String get gcast3;

  /// No description provided for @gcast4.
  ///
  /// In zh, this message translates to:
  /// **'此选择不会影响您在'**
  String get gcast4;

  /// No description provided for @gcast5.
  ///
  /// In zh, this message translates to:
  /// **'设置语音控制和多房间在'**
  String get gcast5;

  /// No description provided for @gcast6.
  ///
  /// In zh, this message translates to:
  /// **'了解Google Cast'**
  String get gcast6;

  /// No description provided for @gcast7.
  ///
  /// In zh, this message translates to:
  /// **'Google Cast音频营销网站'**
  String get gcast7;

  /// No description provided for @gcast8.
  ///
  /// In zh, this message translates to:
  /// **'Google Cast音频应用网站'**
  String get gcast8;

  /// No description provided for @gcast9.
  ///
  /// In zh, this message translates to:
  /// **'Google Cast音频学习网站'**
  String get gcast9;

  /// No description provided for @gcast10.
  ///
  /// In zh, this message translates to:
  /// **'Google Cast帮助中心'**
  String get gcast10;

  /// No description provided for @gcast_tip1.
  ///
  /// In zh, this message translates to:
  /// **'您已准备好开始使用 Casting。在数百个应用中查找 Cast 按钮'**
  String get gcast_tip1;

  /// No description provided for @gcast_tip2.
  ///
  /// In zh, this message translates to:
  /// **'要通过支持 Google 助理的设备设置语音控制和与支持 Google Cast 的设备组建多房间分组，请下载 Google Home 应用'**
  String get gcast_tip2;

  /// No description provided for @go_home_app.
  ///
  /// In zh, this message translates to:
  /// **'前往Google Home应用'**
  String get go_home_app;

  /// No description provided for @g_cast_agreement1.
  ///
  /// In zh, this message translates to:
  /// **'您可以通过您喜欢的移动应用将音乐投射到您的设备。您对 Google Cast 的使用受以下条款约束：'**
  String get g_cast_agreement1;

  /// No description provided for @g_cast_agreement2.
  ///
  /// In zh, this message translates to:
  /// **'Google 服务条款'**
  String get g_cast_agreement2;

  /// No description provided for @g_cast_agreement3.
  ///
  /// In zh, this message translates to:
  /// **'以及'**
  String get g_cast_agreement3;

  /// No description provided for @g_cast_agreement4.
  ///
  /// In zh, this message translates to:
  /// **'Google 隐私权政策'**
  String get g_cast_agreement4;

  /// No description provided for @g_cast_agreement5.
  ///
  /// In zh, this message translates to:
  /// **'描述了 Google Cast 如何处理您的数据。'**
  String get g_cast_agreement5;

  /// No description provided for @g_cast_agreement6.
  ///
  /// In zh, this message translates to:
  /// **'如需了解 Google 如何保护您的隐私，请访问'**
  String get g_cast_agreement6;

  /// No description provided for @g_cast_agreement7.
  ///
  /// In zh, this message translates to:
  /// **'g.co/cast/privacy。'**
  String get g_cast_agreement7;

  /// No description provided for @g_cast_skip_hit.
  ///
  /// In zh, this message translates to:
  /// **'您需要接受此条款才能使用 Google Cast。如果您现在不确定，仍可稍后在设备设置中接受'**
  String get g_cast_skip_hit;

  /// No description provided for @g_cast_set_fail.
  ///
  /// In zh, this message translates to:
  /// **'Google Cast 设置失败'**
  String get g_cast_set_fail;

  /// No description provided for @time_zone.
  ///
  /// In zh, this message translates to:
  /// **'时区'**
  String get time_zone;

  /// No description provided for @reset_text.
  ///
  /// In zh, this message translates to:
  /// **'你确定要将设备恢复出厂设置吗?'**
  String get reset_text;

  /// No description provided for @action_success.
  ///
  /// In zh, this message translates to:
  /// **'操作成功'**
  String get action_success;

  /// No description provided for @action_fail.
  ///
  /// In zh, this message translates to:
  /// **'操作失败'**
  String get action_fail;

  /// No description provided for @rename_success_tip.
  ///
  /// In zh, this message translates to:
  /// **'设备重命名成功'**
  String get rename_success_tip;

  /// No description provided for @rename_input_hint.
  ///
  /// In zh, this message translates to:
  /// **'请输入设备名称'**
  String get rename_input_hint;

  /// No description provided for @rename_input_empty.
  ///
  /// In zh, this message translates to:
  /// **'设备名称不能为空'**
  String get rename_input_empty;

  /// No description provided for @none_playing.
  ///
  /// In zh, this message translates to:
  /// **'当前暂无歌曲播放'**
  String get none_playing;

  /// No description provided for @upgrade_downloading.
  ///
  /// In zh, this message translates to:
  /// **'正在下载新固件，请耐心等待……'**
  String get upgrade_downloading;

  /// No description provided for @upgrade_installing.
  ///
  /// In zh, this message translates to:
  /// **'下载完成，正在升级当前固件……'**
  String get upgrade_installing;

  /// No description provided for @upgrade_failed.
  ///
  /// In zh, this message translates to:
  /// **'升级失败，可能是网络问题，请稍后重试'**
  String get upgrade_failed;

  /// No description provided for @rebooting_tip1.
  ///
  /// In zh, this message translates to:
  /// **'一切准备就绪，设备将重启以完成更新。此过程大约需要1~2分钟'**
  String get rebooting_tip1;

  /// No description provided for @rebooting_tip2.
  ///
  /// In zh, this message translates to:
  /// **'请勿在此过程中断电。'**
  String get rebooting_tip2;

  /// No description provided for @upgrade_success.
  ///
  /// In zh, this message translates to:
  /// **'设备升级完成'**
  String get upgrade_success;

  /// No description provided for @led_light_switch.
  ///
  /// In zh, this message translates to:
  /// **'LED灯开关'**
  String get led_light_switch;

  /// No description provided for @sound_settings.
  ///
  /// In zh, this message translates to:
  /// **'声音设置'**
  String get sound_settings;

  /// No description provided for @volume_limit.
  ///
  /// In zh, this message translates to:
  /// **'音量限制'**
  String get volume_limit;

  /// No description provided for @treble.
  ///
  /// In zh, this message translates to:
  /// **'高音'**
  String get treble;

  /// No description provided for @bass.
  ///
  /// In zh, this message translates to:
  /// **'低音'**
  String get bass;

  /// No description provided for @mid.
  ///
  /// In zh, this message translates to:
  /// **'中音'**
  String get mid;

  /// No description provided for @balance.
  ///
  /// In zh, this message translates to:
  /// **'平衡'**
  String get balance;

  /// No description provided for @advance_audio_settings.
  ///
  /// In zh, this message translates to:
  /// **'高级音频设置'**
  String get advance_audio_settings;

  /// No description provided for @personalized_EQ.
  ///
  /// In zh, this message translates to:
  /// **'个性化均衡器'**
  String get personalized_EQ;
}

class _DeviceLocalizationsDelegate extends LocalizationsDelegate<DeviceLocalizations> {
  const _DeviceLocalizationsDelegate();

  @override
  Future<DeviceLocalizations> load(Locale locale) {
    return SynchronousFuture<DeviceLocalizations>(lookupDeviceLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_DeviceLocalizationsDelegate old) => false;
}

DeviceLocalizations lookupDeviceLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return DeviceLocalizationsEn();
    case 'zh': return DeviceLocalizationsZh();
  }

  throw FlutterError(
    'DeviceLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

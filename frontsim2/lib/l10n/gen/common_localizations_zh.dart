import 'common_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class CommonLocalizationsZh extends CommonLocalizations {
  CommonLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确认';

  @override
  String get ok => '好的';

  @override
  String get skip => '跳过';

  @override
  String get notice => '注意';

  @override
  String get accept => '接受';

  @override
  String input_too_long(int length, String input) {
    return '\"$input\"不能超过\"$length\"个字符';
  }

  @override
  String input_too_short(int length, String input) {
    return '\"$input\"不能少于\"$length\"个字符';
  }

  @override
  String get device => '设备';

  @override
  String get music => '音乐';

  @override
  String get settings => '设置';

  @override
  String get back => '返回';

  @override
  String get delete => '删除';

  @override
  String get save => '保存';

  @override
  String get none_connected_wifi_network => '没有可用的 Wi-Fi 网络';
}

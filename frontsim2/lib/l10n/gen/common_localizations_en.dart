import 'common_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class CommonLocalizationsEn extends CommonLocalizations {
  CommonLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get ok => 'OK';

  @override
  String get skip => 'Skip';

  @override
  String get notice => 'Notice';

  @override
  String get accept => 'Accept';

  @override
  String input_too_long(int length, String input) {
    return '\"$input\" cannot exceed \"$length\" characters';
  }

  @override
  String input_too_short(int length, String input) {
    return '\"$input\" cannot be less than \"$length\" characters';
  }

  @override
  String get device => 'Device';

  @override
  String get music => 'Music';

  @override
  String get settings => 'Settings';

  @override
  String get back => 'Back';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get none_connected_wifi_network => 'No available Wi-Fi networks';
}

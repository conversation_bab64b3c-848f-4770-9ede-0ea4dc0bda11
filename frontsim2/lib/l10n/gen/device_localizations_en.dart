import 'device_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class DeviceLocalizationsEn extends DeviceLocalizations {
  DeviceLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get general_settings => 'General Settings';

  @override
  String get about => 'About';

  @override
  String get net_and_conn => 'Network & Connection';

  @override
  String get general => 'General';

  @override
  String get product_name => 'Device Name';

  @override
  String get software_version => 'Software Version';

  @override
  String get iP_ID => 'IP ID';

  @override
  String get mAC_ID => 'MAC ID';

  @override
  String get connectivity => 'Connectivity';

  @override
  String get google_cast => 'Google Cast';

  @override
  String get report_issue => 'Report Issue';

  @override
  String get factory_reset => 'Factory Reset';

  @override
  String get ethernet => 'Ethernet';

  @override
  String get wifi => 'WiFi';

  @override
  String get gcast1 => 'Cast Enabled';

  @override
  String get gcast2 => 'Activate Cast';

  @override
  String get gcast3 => 'Send device usage and crash reports to Google';

  @override
  String get gcast4 => 'This selection does not affect you in';

  @override
  String get gcast5 => 'Set up voice control and multi-room in';

  @override
  String get gcast6 => 'Learn about Google Cast';

  @override
  String get gcast7 => 'Google Cast Audio Marketing Site';

  @override
  String get gcast8 => 'Google Cast Audio App Site';

  @override
  String get gcast9 => 'Google Cast Audio Learning Site';

  @override
  String get gcast10 => 'Google Cast Help Center';

  @override
  String get gcast_tip1 => 'You\'re ready to start Casting. Look for the Cast button in hundreds of apps';

  @override
  String get gcast_tip2 => 'To set up voice control with Google Assistant-enabled devices and group with Google Cast devices, download the Google Home app';

  @override
  String get go_home_app => 'Go to Google Home App';

  @override
  String get g_cast_agreement1 => 'You can cast music to your device from your favorite mobile apps. Your use of Google Cast is subject to the following terms:';

  @override
  String get g_cast_agreement2 => 'Google Terms of Service';

  @override
  String get g_cast_agreement3 => 'and';

  @override
  String get g_cast_agreement4 => 'Google Privacy Policy';

  @override
  String get g_cast_agreement5 => 'describes how Google Cast handles your data.';

  @override
  String get g_cast_agreement6 => 'To learn how Google protects your privacy, visit';

  @override
  String get g_cast_agreement7 => 'g.co/cast/privacy.';

  @override
  String get g_cast_skip_hit => 'You need to accept this agreement to use Google Cast. If you are unsure now, you can still accept it later in device settings';

  @override
  String get g_cast_set_fail => 'Google Cast setup failed';

  @override
  String get time_zone => 'Time Zone';

  @override
  String get reset_text => 'Are you sure you want to restore the device to factory settings?';

  @override
  String get action_success => 'Operation successful';

  @override
  String get action_fail => 'Operation failed';

  @override
  String get rename_success_tip => 'Device renamed successfully';

  @override
  String get rename_input_hint => 'Please enter device name';

  @override
  String get rename_input_empty => 'Device name cannot be empty';

  @override
  String get none_playing => 'No Music Selected';

  @override
  String get upgrade_downloading => 'Downloading the new firmware, please wait patiently...';

  @override
  String get upgrade_installing => 'Download complete, upgrading the current firmware...';

  @override
  String get upgrade_failed => 'The upgrade failed, maybe it is a network problem, please try again later';

  @override
  String get rebooting_tip1 => 'All prepared, the device will reboot to finish the updating itself. And it will take about 1 ~ 2 minutes';

  @override
  String get rebooting_tip2 => 'Please do not cut power in this process.';

  @override
  String get upgrade_success => 'Device upgrade completed';

  @override
  String get led_light_switch => 'Led light ON/OFF';

  @override
  String get sound_settings => 'Sound Settings';

  @override
  String get volume_limit => 'Volume adjustement';

  @override
  String get treble => 'Treble';

  @override
  String get bass => 'Bass';

  @override
  String get mid => 'Mid';

  @override
  String get balance => 'Balance';

  @override
  String get advance_audio_settings => 'Advanced Audio Settings';

  @override
  String get personalized_EQ => 'Personalized Equalizer';
}

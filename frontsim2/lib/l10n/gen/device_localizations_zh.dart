import 'device_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class DeviceLocalizationsZh extends DeviceLocalizations {
  DeviceLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get general_settings => '常规设置';

  @override
  String get about => '关于';

  @override
  String get net_and_conn => '网络与连接';

  @override
  String get general => '常规';

  @override
  String get product_name => '设备名称';

  @override
  String get software_version => '软件版本';

  @override
  String get iP_ID => 'IP ID';

  @override
  String get mAC_ID => 'MAC ID';

  @override
  String get connectivity => '连接方式';

  @override
  String get google_cast => 'Google Cast';

  @override
  String get report_issue => '报告问题';

  @override
  String get factory_reset => '恢复出厂设置';

  @override
  String get ethernet => '以太网';

  @override
  String get wifi => 'WiFi';

  @override
  String get gcast1 => '已启用Cast';

  @override
  String get gcast2 => '激活Cast';

  @override
  String get gcast3 => '向Google发送设备使用和崩溃报告';

  @override
  String get gcast4 => '此选择不会影响您在';

  @override
  String get gcast5 => '设置语音控制和多房间在';

  @override
  String get gcast6 => '了解Google Cast';

  @override
  String get gcast7 => 'Google Cast音频营销网站';

  @override
  String get gcast8 => 'Google Cast音频应用网站';

  @override
  String get gcast9 => 'Google Cast音频学习网站';

  @override
  String get gcast10 => 'Google Cast帮助中心';

  @override
  String get gcast_tip1 => '您已准备好开始使用 Casting。在数百个应用中查找 Cast 按钮';

  @override
  String get gcast_tip2 => '要通过支持 Google 助理的设备设置语音控制和与支持 Google Cast 的设备组建多房间分组，请下载 Google Home 应用';

  @override
  String get go_home_app => '前往Google Home应用';

  @override
  String get g_cast_agreement1 => '您可以通过您喜欢的移动应用将音乐投射到您的设备。您对 Google Cast 的使用受以下条款约束：';

  @override
  String get g_cast_agreement2 => 'Google 服务条款';

  @override
  String get g_cast_agreement3 => '以及';

  @override
  String get g_cast_agreement4 => 'Google 隐私权政策';

  @override
  String get g_cast_agreement5 => '描述了 Google Cast 如何处理您的数据。';

  @override
  String get g_cast_agreement6 => '如需了解 Google 如何保护您的隐私，请访问';

  @override
  String get g_cast_agreement7 => 'g.co/cast/privacy。';

  @override
  String get g_cast_skip_hit => '您需要接受此条款才能使用 Google Cast。如果您现在不确定，仍可稍后在设备设置中接受';

  @override
  String get g_cast_set_fail => 'Google Cast 设置失败';

  @override
  String get time_zone => '时区';

  @override
  String get reset_text => '你确定要将设备恢复出厂设置吗?';

  @override
  String get action_success => '操作成功';

  @override
  String get action_fail => '操作失败';

  @override
  String get rename_success_tip => '设备重命名成功';

  @override
  String get rename_input_hint => '请输入设备名称';

  @override
  String get rename_input_empty => '设备名称不能为空';

  @override
  String get none_playing => '当前暂无歌曲播放';

  @override
  String get upgrade_downloading => '正在下载新固件，请耐心等待……';

  @override
  String get upgrade_installing => '下载完成，正在升级当前固件……';

  @override
  String get upgrade_failed => '升级失败，可能是网络问题，请稍后重试';

  @override
  String get rebooting_tip1 => '一切准备就绪，设备将重启以完成更新。此过程大约需要1~2分钟';

  @override
  String get rebooting_tip2 => '请勿在此过程中断电。';

  @override
  String get upgrade_success => '设备升级完成';

  @override
  String get led_light_switch => 'LED灯开关';

  @override
  String get sound_settings => '声音设置';

  @override
  String get volume_limit => '音量限制';

  @override
  String get treble => '高音';

  @override
  String get bass => '低音';

  @override
  String get mid => '中音';

  @override
  String get balance => '平衡';

  @override
  String get advance_audio_settings => '高级音频设置';

  @override
  String get personalized_EQ => '个性化均衡器';
}

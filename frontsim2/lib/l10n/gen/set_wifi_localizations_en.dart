import 'set_wifi_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SetWifiLocalizationsEn extends SetWifiLocalizations {
  SetWifiLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get title => 'WiFi Setup';

  @override
  String get setup_wifi_description => 'The network setup process may take about 30 seconds, please wait patient.';

  @override
  String get password_input_hint => 'Please enter password';

  @override
  String get password_cannot_empty => 'Password cannot be empty';

  @override
  String connecting_wifi(String wifiName) {
    return 'Connecting to \"$wifiName\"';
  }

  @override
  String get check_update_hint => 'Connect to WiFi network to check firmware update. It may take about 20 seconds, please wait...';

  @override
  String get wrong_password => 'Incorrect password';

  @override
  String get return_prev_step => 'Return to previous step';

  @override
  String get connected => 'This device is up to date';

  @override
  String get scanning_devices => 'Scanning devices';

  @override
  String get no_found_device => 'No device found';

  @override
  String get connecting_device => 'Connecting device';

  @override
  String get connect_failed => 'Connection failed';

  @override
  String get scanning_ap => 'Scanning Wi-Fi';

  @override
  String get no_found_ap => 'No Wi-Fi found';

  @override
  String get scanning_ap_failed => 'Wi-Fi scan failed';

  @override
  String get connect_ap_failed => 'Failed to connect to Wi-Fi';

  @override
  String get wait_device_online => 'Wi-Fi connected successfully, waiting for device to come online...';

  @override
  String get device_online_failed => 'No device found. Please check if the device and phone are on the same Wi-Fi network.';

  @override
  String get open_ble_instructions => 'Please enable Bluetooth on your phone';

  @override
  String get open_ble_permission => 'Please enable Bluetooth permission to discover nearby devices';

  @override
  String get open_location_permission => 'Please enable location permission to discover nearby devices';

  @override
  String get open_setting_tip => 'Please enable Bluetooth and location permissions in settings to discover nearby devices';
}

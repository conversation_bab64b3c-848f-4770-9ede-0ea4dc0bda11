import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'radio_page_localizations_en.dart';
import 'radio_page_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of RadioPageLocalizations
/// returned by `RadioPageLocalizations.of(context)`.
///
/// Applications need to include `RadioPageLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen/radio_page_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: RadioPageLocalizations.localizationsDelegates,
///   supportedLocales: RadioPageLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the RadioPageLocalizations.supportedLocales
/// property.
abstract class RadioPageLocalizations {
  RadioPageLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static RadioPageLocalizations? of(BuildContext context) {
    return Localizations.of<RadioPageLocalizations>(context, RadioPageLocalizations);
  }

  static const LocalizationsDelegate<RadioPageLocalizations> delegate = _RadioPageLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @privacy_policy.
  ///
  /// In zh, this message translates to:
  /// **'隐私政策'**
  String get privacy_policy;

  /// No description provided for @service_content.
  ///
  /// In zh, this message translates to:
  /// **'服务内容'**
  String get service_content;

  /// No description provided for @service_content_text.
  ///
  /// In zh, this message translates to:
  /// **'您对服务的使用和您提供的任何内容负责，包括遵守适用的法律、法规和规章。你应该只提供你愿意与他人分享的内容。使用或依赖您通过本服务分享的任何内容或材料，风险由您自行承担。我们不认可、支持、代表或保证通过本服务分享的任何内容的完整性、真实性、准确性或可靠性，也不认可通过本服务表达的任何意见。您理解，通过使用服务，您可能会接触到可能具有攻击性、有害、不准确或其他不适当的内容，或者在某些情况下被错误标记或以其他方式具有欺骗性的内容。所有内容均由原创该等内容的人全权负责。我们可能无法监控或控制通过服务共享的内容，并且我们对此类内容不承担任何责任。我们保留删除包括非法行为、骚扰或任何不适宜传播的内容的权利。'**
  String get service_content_text;

  /// No description provided for @rights.
  ///
  /// In zh, this message translates to:
  /// **'您在内容中的权利和权利授予'**
  String get rights;

  /// No description provided for @rights_text.
  ///
  /// In zh, this message translates to:
  /// **'您保留对通过服务提交、共享的任何内容的权利。通过服务提交、共享内容，您授予我们全球范围内的、非排他性的、免版税的许可（有权再许可），以使用、复制、转载、处理、改编、修改、发布、传输、显示和分发此类内容，无论是在现在已知的还是以后开发的任何和所有媒体或分发方法中（为清楚起见，这些权利包括，例如，策划、转换和翻译）。此许可证授权我们向世界其他地区提供您的内容，并允许其他人也这样做。您同意，本许可证包括Arylic提供、推广和改进服务的权利，以及向其他公司、组织或个人提交或通过服务提供内容的权利，以便在其他媒体和服务上联合、广播、分发、推广或发布此类内容，但须遵守我们对此类内容使用的条款和条件。Arylic或其他公司、组织或个人进行此类额外使用时，不会就您通过服务提交、分享或以其他方式提供的内容向您支付任何补偿，因为您使用服务的行为被视为对本协议内容和权利授予的充分补偿。'**
  String get rights_text;

  /// No description provided for @privacy_notice.
  ///
  /// In zh, this message translates to:
  /// **'隐私通知'**
  String get privacy_notice;

  /// No description provided for @privacy_notice_text.
  ///
  /// In zh, this message translates to:
  /// **'保护您的隐私对我们很重要。我们努力为您的个人信息保密。我们通过物理、电子和管理程序保护您发送给我们的数据的安全性。我们敦促您在上网时采取一切预防措施保护您的个人资料。为了获得最高的安全性，我们建议您使用字母和数字的组合作为密码，并确保您使用安全的浏览器。在注册时，您需要提供您的联系信息（如姓名和电子邮件地址）。此信息将用于保证您的帐户安全和密码恢复。我们永远不会问你的密码。本隐私政策可随时更改。我们鼓励您定期查看隐私政策的任何变化。'**
  String get privacy_notice_text;

  /// No description provided for @rads1.
  ///
  /// In zh, this message translates to:
  /// **'准备好播放音乐了吗？'**
  String get rads1;

  /// No description provided for @rads2.
  ///
  /// In zh, this message translates to:
  /// **'在您的扬声器或电视上收听，'**
  String get rads2;

  /// No description provided for @rads3.
  ///
  /// In zh, this message translates to:
  /// **'使用Spotify应用作为遥控器'**
  String get rads3;

  /// No description provided for @rads4.
  ///
  /// In zh, this message translates to:
  /// **'了解更多'**
  String get rads4;

  /// No description provided for @rads5.
  ///
  /// In zh, this message translates to:
  /// **'打开Spotify应用'**
  String get rads5;

  /// No description provided for @rads6.
  ///
  /// In zh, this message translates to:
  /// **'获取Spotify应用'**
  String get rads6;

  /// No description provided for @rad1.
  ///
  /// In zh, this message translates to:
  /// **'收听TlDAL从未如此简单'**
  String get rad1;

  /// No description provided for @rad2.
  ///
  /// In zh, this message translates to:
  /// **'无缝从云中将您喜爱的音乐流式传输到您的设备'**
  String get rad2;

  /// No description provided for @rad3.
  ///
  /// In zh, this message translates to:
  /// **'了解更多'**
  String get rad3;

  /// No description provided for @rad4.
  ///
  /// In zh, this message translates to:
  /// **'打开TIDAL应用'**
  String get rad4;

  /// No description provided for @rad5.
  ///
  /// In zh, this message translates to:
  /// **'获取TIDAL应用'**
  String get rad5;

  /// No description provided for @local_music.
  ///
  /// In zh, this message translates to:
  /// **'本地音乐'**
  String get local_music;

  /// No description provided for @music_sources.
  ///
  /// In zh, this message translates to:
  /// **'音源'**
  String get music_sources;
}

class _RadioPageLocalizationsDelegate extends LocalizationsDelegate<RadioPageLocalizations> {
  const _RadioPageLocalizationsDelegate();

  @override
  Future<RadioPageLocalizations> load(Locale locale) {
    return SynchronousFuture<RadioPageLocalizations>(lookupRadioPageLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_RadioPageLocalizationsDelegate old) => false;
}

RadioPageLocalizations lookupRadioPageLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return RadioPageLocalizationsEn();
    case 'zh': return RadioPageLocalizationsZh();
  }

  throw FlutterError(
    'RadioPageLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

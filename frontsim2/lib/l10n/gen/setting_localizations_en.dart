import 'setting_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SettingLocalizationsEn extends SettingLocalizations {
  SettingLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get setting => 'Settings';

  @override
  String get setting_language => 'Language';

  @override
  String get setting_language_zh => 'Simplified Chinese';

  @override
  String get setting_language_en => 'English';

  @override
  String get setting_language_zh_hk => 'Traditional Chinese';

  @override
  String get setting_language_ja => 'Japanese';

  @override
  String get setting_language_ko => 'Korean';

  @override
  String get setting_language_de => 'German';

  @override
  String get setting_language_fr => 'French';

  @override
  String get app_version => 'App Version';

  @override
  String get select_language_success => 'Switched successfully';

  @override
  String get following_system => 'Auto';
}

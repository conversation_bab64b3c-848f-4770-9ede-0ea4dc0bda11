import 'setting_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class SettingLocalizationsZh extends SettingLocalizations {
  SettingLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get setting => '设置';

  @override
  String get setting_language => '语言';

  @override
  String get setting_language_zh => '简体中文';

  @override
  String get setting_language_en => 'English';

  @override
  String get setting_language_zh_hk => '繁體中文';

  @override
  String get setting_language_ja => '日本語';

  @override
  String get setting_language_ko => '한국어';

  @override
  String get setting_language_de => 'Deutsch';

  @override
  String get setting_language_fr => 'Français';

  @override
  String get app_version => '应用版本';

  @override
  String get select_language_success => '切换成功';

  @override
  String get following_system => '跟随系统';
}

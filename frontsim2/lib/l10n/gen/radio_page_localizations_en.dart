import 'radio_page_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class RadioPageLocalizationsEn extends RadioPageLocalizations {
  RadioPageLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get privacy_policy => 'Privacy Policy';

  @override
  String get service_content => 'Service Content';

  @override
  String get service_content_text => 'You are responsible for your use of the service and any content you provide, including compliance with applicable laws, rules, and regulations. You should only provide content you are comfortable sharing with others. Use or reliance on any content or materials shared via the service is at your own risk. We do not endorse, support, represent or guarantee the completeness, truthfulness, accuracy, or reliability of any content shared via the service or endorse any opinions expressed via the service. You understand that by using the service, you may be exposed to content that might be offensive, harmful, inaccurate or otherwise inappropriate, or in some cases, content that has been mislabeled or is otherwise deceptive. All content is the sole responsibility of the person who originated such content. We may not monitor or control the content shared via the service and we take no responsibility for such content. We reserve the right to remove content including content that involves illegal conduct, harassment, or is otherwise inappropriate to share.';

  @override
  String get rights => 'Your Rights in Content and Grant of Rights';

  @override
  String get rights_text => 'You retain your rights to any content you submit or share through the service. By submitting or sharing content through the service, you grant us a worldwide, non-exclusive, royalty-free license (with the right to sublicense) to use, copy, reproduce, process, adapt, modify, publish, transmit, display, and distribute such content in any and all media or distribution methods now known or later developed (these rights include, for example, curating, transforming, and translating). This license authorizes us to make your content available to the rest of the world and to let others do the same. You agree that this license includes the right for Arylic to provide, promote, and improve the service and to make content submitted to or through the service available to other companies, organizations, or individuals for the syndication, broadcast, distribution, promotion, or publication of such content on other media and services, subject to our terms and conditions for such content use. No compensation will be paid to you for any content that you submit, share, or otherwise make available through the service, as the use of the service by you is hereby agreed as being sufficient compensation for the rights granted hereunder.';

  @override
  String get privacy_notice => 'Privacy Notice';

  @override
  String get privacy_notice_text => 'Protecting your privacy is important to us. We strive to keep your personal information confidential. We safeguard the security of the data you send us with physical, electronic, and managerial procedures. We urge you to take every precaution to protect your personal data when you are on the Internet. For maximum security, we recommend that you use a combination of letters and numbers as your password and make sure you use a secure browser. During registration, you are required to provide your contact information (such as name and email address). This information will be used to ensure your account security and password recovery. We will never ask for your password. This privacy policy may change at any time. We encourage you to review the privacy policy regularly for any changes.';

  @override
  String get rads1 => 'Ready to play some music?';

  @override
  String get rads2 => 'Listen on your speaker or TV,';

  @override
  String get rads3 => 'Use the Spotify app as a remote';

  @override
  String get rads4 => 'Learn more';

  @override
  String get rads5 => 'Open Spotify app';

  @override
  String get rads6 => 'Get Spotify app';

  @override
  String get rad1 => 'Listening to TIDAL has never been easier';

  @override
  String get rad2 => 'Seamlessly stream your favorite music from the cloud to your device';

  @override
  String get rad3 => 'Learn more';

  @override
  String get rad4 => 'Open TIDAL app';

  @override
  String get rad5 => 'Get TIDAL app';

  @override
  String get local_music => 'Local Music';

  @override
  String get music_sources => 'Music Sources';
}

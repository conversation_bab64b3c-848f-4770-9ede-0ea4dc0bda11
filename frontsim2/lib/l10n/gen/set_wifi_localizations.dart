import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'set_wifi_localizations_en.dart';
import 'set_wifi_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of SetWifiLocalizations
/// returned by `SetWifiLocalizations.of(context)`.
///
/// Applications need to include `SetWifiLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen/set_wifi_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: SetWifiLocalizations.localizationsDelegates,
///   supportedLocales: SetWifiLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the SetWifiLocalizations.supportedLocales
/// property.
abstract class SetWifiLocalizations {
  SetWifiLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static SetWifiLocalizations? of(BuildContext context) {
    return Localizations.of<SetWifiLocalizations>(context, SetWifiLocalizations);
  }

  static const LocalizationsDelegate<SetWifiLocalizations> delegate = _SetWifiLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @title.
  ///
  /// In zh, this message translates to:
  /// **'设置 Wi-Fi'**
  String get title;

  /// No description provided for @setup_wifi_description.
  ///
  /// In zh, this message translates to:
  /// **'网络设置过程可能大约需要30秒，请耐心等待。'**
  String get setup_wifi_description;

  /// No description provided for @password_input_hint.
  ///
  /// In zh, this message translates to:
  /// **'请输入密码'**
  String get password_input_hint;

  /// No description provided for @password_cannot_empty.
  ///
  /// In zh, this message translates to:
  /// **'密码不能为空'**
  String get password_cannot_empty;

  /// Connecting to Wi-Fi
  ///
  /// In zh, this message translates to:
  /// **'正在连接 \"{wifiName}\"'**
  String connecting_wifi(String wifiName);

  /// No description provided for @check_update_hint.
  ///
  /// In zh, this message translates to:
  /// **'连接到WiFi网络，检查固件更新。可能需要大约20秒钟，请等待...'**
  String get check_update_hint;

  /// No description provided for @wrong_password.
  ///
  /// In zh, this message translates to:
  /// **'密码有误'**
  String get wrong_password;

  /// No description provided for @return_prev_step.
  ///
  /// In zh, this message translates to:
  /// **'返回上一步'**
  String get return_prev_step;

  /// No description provided for @connected.
  ///
  /// In zh, this message translates to:
  /// **'此设备当前版本是最新的'**
  String get connected;

  /// No description provided for @scanning_devices.
  ///
  /// In zh, this message translates to:
  /// **'正在扫描设备'**
  String get scanning_devices;

  /// No description provided for @no_found_device.
  ///
  /// In zh, this message translates to:
  /// **'没有找到设备'**
  String get no_found_device;

  /// No description provided for @connecting_device.
  ///
  /// In zh, this message translates to:
  /// **'正在连接设备'**
  String get connecting_device;

  /// No description provided for @connect_failed.
  ///
  /// In zh, this message translates to:
  /// **'连接失败'**
  String get connect_failed;

  /// No description provided for @scanning_ap.
  ///
  /// In zh, this message translates to:
  /// **'正在扫描Wi-Fi'**
  String get scanning_ap;

  /// No description provided for @no_found_ap.
  ///
  /// In zh, this message translates to:
  /// **'没有找到Wi-Fi'**
  String get no_found_ap;

  /// No description provided for @scanning_ap_failed.
  ///
  /// In zh, this message translates to:
  /// **'扫描Wi-Fi失败'**
  String get scanning_ap_failed;

  /// No description provided for @connect_ap_failed.
  ///
  /// In zh, this message translates to:
  /// **'连接Wi-Fi失败'**
  String get connect_ap_failed;

  /// No description provided for @wait_device_online.
  ///
  /// In zh, this message translates to:
  /// **'Wifi连接成功，等待设备上线中...'**
  String get wait_device_online;

  /// No description provided for @device_online_failed.
  ///
  /// In zh, this message translates to:
  /// **'没有找到设备,请检查设备与手机是否处于在同一Wi-Fi网络下'**
  String get device_online_failed;

  /// No description provided for @open_ble_instructions.
  ///
  /// In zh, this message translates to:
  /// **'请打开手机的蓝牙功能'**
  String get open_ble_instructions;

  /// No description provided for @open_ble_permission.
  ///
  /// In zh, this message translates to:
  /// **'请打开蓝牙权限,用于发现附近的设备'**
  String get open_ble_permission;

  /// No description provided for @open_location_permission.
  ///
  /// In zh, this message translates to:
  /// **'请打开位置权限,用于发现附近的设备'**
  String get open_location_permission;

  /// No description provided for @open_setting_tip.
  ///
  /// In zh, this message translates to:
  /// **'请在设置中打开蓝牙和位置权限用于发现附近的设备'**
  String get open_setting_tip;
}

class _SetWifiLocalizationsDelegate extends LocalizationsDelegate<SetWifiLocalizations> {
  const _SetWifiLocalizationsDelegate();

  @override
  Future<SetWifiLocalizations> load(Locale locale) {
    return SynchronousFuture<SetWifiLocalizations>(lookupSetWifiLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_SetWifiLocalizationsDelegate old) => false;
}

SetWifiLocalizations lookupSetWifiLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return SetWifiLocalizationsEn();
    case 'zh': return SetWifiLocalizationsZh();
  }

  throw FlutterError(
    'SetWifiLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

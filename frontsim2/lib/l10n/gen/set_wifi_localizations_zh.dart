import 'set_wifi_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class SetWifiLocalizationsZh extends SetWifiLocalizations {
  SetWifiLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get title => '设置 Wi-Fi';

  @override
  String get setup_wifi_description => '网络设置过程可能大约需要30秒，请耐心等待。';

  @override
  String get password_input_hint => '请输入密码';

  @override
  String get password_cannot_empty => '密码不能为空';

  @override
  String connecting_wifi(String wifiName) {
    return '正在连接 \"$wifiName\"';
  }

  @override
  String get check_update_hint => '连接到WiFi网络，检查固件更新。可能需要大约20秒钟，请等待...';

  @override
  String get wrong_password => '密码有误';

  @override
  String get return_prev_step => '返回上一步';

  @override
  String get connected => '此设备当前版本是最新的';

  @override
  String get scanning_devices => '正在扫描设备';

  @override
  String get no_found_device => '没有找到设备';

  @override
  String get connecting_device => '正在连接设备';

  @override
  String get connect_failed => '连接失败';

  @override
  String get scanning_ap => '正在扫描Wi-Fi';

  @override
  String get no_found_ap => '没有找到Wi-Fi';

  @override
  String get scanning_ap_failed => '扫描Wi-Fi失败';

  @override
  String get connect_ap_failed => '连接Wi-Fi失败';

  @override
  String get wait_device_online => 'Wifi连接成功，等待设备上线中...';

  @override
  String get device_online_failed => '没有找到设备,请检查设备与手机是否处于在同一Wi-Fi网络下';

  @override
  String get open_ble_instructions => '请打开手机的蓝牙功能';

  @override
  String get open_ble_permission => '请打开蓝牙权限,用于发现附近的设备';

  @override
  String get open_location_permission => '请打开位置权限,用于发现附近的设备';

  @override
  String get open_setting_tip => '请在设置中打开蓝牙和位置权限用于发现附近的设备';
}

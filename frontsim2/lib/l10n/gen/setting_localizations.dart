import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'setting_localizations_en.dart';
import 'setting_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of SettingLocalizations
/// returned by `SettingLocalizations.of(context)`.
///
/// Applications need to include `SettingLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen/setting_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: SettingLocalizations.localizationsDelegates,
///   supportedLocales: SettingLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the SettingLocalizations.supportedLocales
/// property.
abstract class SettingLocalizations {
  SettingLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static SettingLocalizations? of(BuildContext context) {
    return Localizations.of<SettingLocalizations>(context, SettingLocalizations);
  }

  static const LocalizationsDelegate<SettingLocalizations> delegate = _SettingLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @setting.
  ///
  /// In zh, this message translates to:
  /// **'设置'**
  String get setting;

  /// No description provided for @setting_language.
  ///
  /// In zh, this message translates to:
  /// **'语言'**
  String get setting_language;

  /// No description provided for @setting_language_zh.
  ///
  /// In zh, this message translates to:
  /// **'简体中文'**
  String get setting_language_zh;

  /// No description provided for @setting_language_en.
  ///
  /// In zh, this message translates to:
  /// **'English'**
  String get setting_language_en;

  /// No description provided for @setting_language_zh_hk.
  ///
  /// In zh, this message translates to:
  /// **'繁體中文'**
  String get setting_language_zh_hk;

  /// No description provided for @setting_language_ja.
  ///
  /// In zh, this message translates to:
  /// **'日本語'**
  String get setting_language_ja;

  /// No description provided for @setting_language_ko.
  ///
  /// In zh, this message translates to:
  /// **'한국어'**
  String get setting_language_ko;

  /// No description provided for @setting_language_de.
  ///
  /// In zh, this message translates to:
  /// **'Deutsch'**
  String get setting_language_de;

  /// No description provided for @setting_language_fr.
  ///
  /// In zh, this message translates to:
  /// **'Français'**
  String get setting_language_fr;

  /// No description provided for @app_version.
  ///
  /// In zh, this message translates to:
  /// **'应用版本'**
  String get app_version;

  /// No description provided for @select_language_success.
  ///
  /// In zh, this message translates to:
  /// **'切换成功'**
  String get select_language_success;

  /// No description provided for @following_system.
  ///
  /// In zh, this message translates to:
  /// **'跟随系统'**
  String get following_system;
}

class _SettingLocalizationsDelegate extends LocalizationsDelegate<SettingLocalizations> {
  const _SettingLocalizationsDelegate();

  @override
  Future<SettingLocalizations> load(Locale locale) {
    return SynchronousFuture<SettingLocalizations>(lookupSettingLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_SettingLocalizationsDelegate old) => false;
}

SettingLocalizations lookupSettingLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return SettingLocalizationsEn();
    case 'zh': return SettingLocalizationsZh();
  }

  throw FlutterError(
    'SettingLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

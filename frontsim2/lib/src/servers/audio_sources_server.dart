import 'dart:async';
import 'dart:io';

import 'package:base_common/base_common.dart';
import 'package:rk_package/rk_package.dart';

import '../configs/log.dart';
import '../helpers/ip_tools.dart';
import '../models/audio_source_model.dart';

class AudioSourcesServer {
  late final HttpServer _server;

  final Lock _serverStartLock = Lock();

  ///默认端口
  static const int _port = 8800;

  bool get serverReady => !_serverStartLock.isUsing();

  Future<void> init() {
    //创建并启动http服务器
    return _serverStartLock.mutex(() => _createServer());
  }

  final List<AudioSourceModel> _audioSourceList = [];

  ///添加到server管理
  void addAudioSource(List<AudioSourceModel> audioSources) {
    _audioSourceList.assignAll(audioSources);
  }

  /// 获取json Path
  Future<String> get getSourceJsonPath async {
    final ip = await IpV4AndIpV6Tool.localIpv4;

    return 'http://$ip:$_port/play_list.json';
  }

  ///创建http服务器
  Future<void> _createServer() async {
    try {
      _server = await HttpServer.bind(InternetAddress.anyIPv4, _port);
      _server.listen((request) async {
        try {
          log.logDebug(
              '收到http请求:${request.method} ${request.uri} header:${request.headers}');

          //get ,如果请求文件,则返回文件,需要校验要取的文件是不是当前的升级文件
          if (request.method == 'GET') {
            if (request.uri.path.endsWith('.json')) {
              String resourceContent = "";
              for (var element in _audioSourceList) {
                resourceContent += '''{
                "track":"demo",
                "url": "${element.url}"
              }''';
                if (element != _audioSourceList.last) {
                  resourceContent += ',';
                }
              }
              String json = '''{
                  "totalitems": ${_audioSourceList.length}, 
                  "currentindex": 0, 
                  "content": {
                      "entries": [
                        $resourceContent
                      ]
                    }
                  }''';
              json = json.replaceAll(' ', '');
              json = json.replaceAll('\r', '');
              json = json.replaceAll('\n', '');
              log.logDebug('返回json文件:$json');
              request.response.statusCode = HttpStatus.ok;
              //写入文件名
              request.response.headers.set(HttpHeaders.contentDisposition,
                  'attachment; filename=play_list.json');
              request.response.headers.contentType = ContentType.json;
              request.response.headers.contentLength = json.length;
              // 读取文件并发送
              request.response.write(json);
              await request.response.done;
              log.logDebug('json文件发送完成');
              request.response.close();
              return;
            }
          }
          //post
          request.response.statusCode = HttpStatus.notFound;
          request.response.close();
          request.response.close();
        } catch (e, stack) {
          log.logError('处理http请求失败:$e', stack);
        }
      });

      log.logInformation('http服务器启动成功,端口:$_port');
    } catch (e, stack) {
      log.logError('创建http服务器失败:$e', stack);
    }
  }

  //销毁
  Future dispose() async {
    if (_serverStartLock.isUsing()) {
      await _serverStartLock.waitDone();
    }
    return _server.close(force: true);
  }
}

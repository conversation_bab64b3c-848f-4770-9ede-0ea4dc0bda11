import 'dart:io';

import 'package:base_common/base_common.dart';
import 'package:rk_package/rk_package.dart';

import '../configs/log.dart';
import '../helpers/ip_tools.dart';
import '../models/audio_source_model.dart';

class LocalAudioSourcesServer {
  late final HttpServer _server;

  final Lock _serverStartLock = Lock();

  ///默认端口
  static const int _port = 8801;

  bool get serverReady => !_serverStartLock.isUsing();

  ///管理资源文件
  final Map<String, LocalAudioFile> _localAudioFiles = {};

  ///添加到server管理,返回可请求的url
  String addAudioFile({
    required String filePath,
    String trackName = '',
  }) {
    final items = _localAudioFiles.entries
        .where((element) => element.value.filePath == filePath);
    if (items.isNotEmpty) {
      return items.first.key;
    }
    final localAudioFile = LocalAudioFile(
      filePath: filePath,
      trackName: trackName,
    );
    final uuid = Uuid().v4();
    _localAudioFiles[uuid] = localAudioFile;
    return uuid;
  }

  //拼接local audio file的url
  Future<String> getAudioFileUrl(String uuid) async {
    final ip = await IpV4AndIpV6Tool.localIpv4;
    return 'http://$ip:$_port/$uuid.file';
  }

  Future<void> init() async {
    //创建并启动http服务器
    return _serverStartLock.mutex(() => _createServer());
  }

  ///创建http服务器
  Future<void> _createServer() async {
    try {
      _server = await HttpServer.bind(InternetAddress.anyIPv4, _port);
      _server.listen((request) async {
        try {
          log.logDebug(
              '收到http请求:${request.method} ${request.uri} header:${request.headers}');

          //get ,如果请求文件,则返回文件
          if (request.method == 'GET') {
            if (request.uri.path.contains('.file')) {
              // http://ip:port/uuid.file::trackName::artistName::fileFormat
              //通过uuid获取文件
              final uuid = request.uri.pathSegments.first.split('.file').first;
              log.logDebug('uuid:$uuid');
              final localAudioFile = _localAudioFiles[uuid];
              if (localAudioFile == null) {
                log.logDebug('没有找到文件:$uuid');
                request.response.statusCode = HttpStatus.notFound;
                request.response.close();
                return;
              }
              if (await File(localAudioFile.filePath).exists() == false) {
                log.logDebug('文件不存在:$uuid');
                request.response.statusCode = HttpStatus.notFound;
                request.response.close();
                return;
              }

              //获取文件长度
              final fileLength = await File(localAudioFile.filePath).length();
              //获取文件名
              final fileName = localAudioFile.trackName.isNotEmpty
                  ? localAudioFile.trackName
                  : localAudioFile.filePath.split('/').last;

              log.logDebug('返回音乐文件');
              //获取header
              final header = request.headers;
              //获取range 请求头
              final range = header['range'];

              if (range?.isNotEmpty == true) {
                //处理range请求头
                final rangeStart =
                    range!.first.split('=').last.split('-').first;
                final start = int.tryParse(rangeStart) ?? 0;
                final end = fileLength - 1;
                //获取文件流
                final fileStream =
                    File(localAudioFile.filePath).openRead(start);
                request.response.headers
                    .set(HttpHeaders.acceptRangesHeader, 'bytes');
                request.response.headers.set(HttpHeaders.contentRangeHeader,
                    'bytes $start-$end/$fileLength');
                request.response.statusCode = HttpStatus.partialContent;
                request.response.headers.contentLength = fileLength - start;
                await request.response.addStream(fileStream);
              } else {
                //获取文件流
                final fileStream = File(localAudioFile.filePath).openRead();
                request.response.statusCode = HttpStatus.ok;
                //写入文件名
                request.response.headers.set(HttpHeaders.contentDisposition,
                    'attachment; filename=${Uri.encodeComponent(fileName)}');
                request.response.headers.contentType = ContentType.binary;
                request.response.headers.contentLength = fileLength;

                // 读取文件并发送
                await request.response.addStream(fileStream);
              }
              log.logDebug('文件发送完成');
              request.response.close();
              return;
            }
          }
          request.response.statusCode = HttpStatus.notFound;
          request.response.close();
          request.response.close();
        } catch (e, stack) {
          log.logError('处理http请求失败:$e', stack);
        }
      });

      log.logInformation('http服务器启动成功,端口:$_port');
    } catch (e, stack) {
      log.logError('创建http服务器失败:$e', stack);
    }
  }

  //销毁
  Future dispose() async {
    if (_serverStartLock.isUsing()) {
      await _serverStartLock.waitDone();
    }
    return _server.close(force: true);
  }
}

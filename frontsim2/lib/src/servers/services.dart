import 'package:flutter/widgets.dart';
import 'package:rk_get/rk_get.dart';

import 'audio_sources_server.dart';
import 'local_audio_file_server.dart';

void serverInit() {
  //等一帧渲染完成后再执行
  WidgetsBinding.instance.addPostFrameCallback((_) {
    Get.registerLazySingleton(() async {
      final localAudioSourcesServer = LocalAudioSourcesServer();
      await localAudioSourcesServer.init();
      return localAudioSourcesServer;
    });
    Get.registerLazySingleton(() async {
      final audioSourcesServer = AudioSourcesServer();
      await audioSourcesServer.init();
      return audioSourcesServer;
    });
  });
}

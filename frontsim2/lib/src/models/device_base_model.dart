import 'dart:math';

import 'package:flutter/widgets.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_package/rk_package.dart';

abstract class DeviceBaseModel {
  late final LibreDeviceActions actions;

  late final String deviceUUID;

  final Rx<UpgradeSteps> upgradeState = Rx(UpgradeSteps.none);

  final Rx<bool> online = Rx(false);

  @protected
  @mustCallSuper
  void init() {
    deviceUUID = _generateUUID();
  }

  String _generateUUID() {
    const alphanumeric = '0123456789ABCDEF';
    final random = Random();
    String part1 = '';
    String part2 = '';
    String part3 = '';
    for (int i = 0; i < 4; i++) {
      part1 += alphanumeric[random.nextInt(alphanumeric.length)];
      part2 += alphanumeric[random.nextInt(alphanumeric.length)];
      part3 += alphanumeric[random.nextInt(alphanumeric.length)];
    }
    return '1770B849-$part1-$part2-$part3-6FD6E68C7321';
  }

  @protected
  @mustCallSuper
  Future<void> dispose() async {}
}

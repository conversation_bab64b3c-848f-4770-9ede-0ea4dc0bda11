/// 应用生命周期观察者配置
class AppLifecycleConfig {
  /// 是否启用自动断连功能
  final bool enableAutoDisconnect;
  
  /// 是否启用自动重连功能
  final bool enableAutoReconnect;
  
  /// 进入后台后延迟断连的时间（秒）
  /// 防止短暂切换应用时频繁断连重连
  final int disconnectDelaySeconds;
  
  /// 恢复前台后延迟重连的时间（秒）
  /// 给应用一些时间完全恢复
  final int reconnectDelaySeconds;
  
  /// 是否在断连时显示通知
  final bool showDisconnectNotification;
  
  /// 是否在重连时显示通知
  final bool showReconnectNotification;

  const AppLifecycleConfig({
    this.enableAutoDisconnect = true,
    this.enableAutoReconnect = true,
    this.disconnectDelaySeconds = 2,
    this.reconnectDelaySeconds = 1,
    this.showDisconnectNotification = false,
    this.showReconnectNotification = false,
  });

  /// 默认配置
  static const AppLifecycleConfig defaultConfig = AppLifecycleConfig();
  
  /// 保守配置（更长的延迟时间）
  static const AppLifecycleConfig conservativeConfig = AppLifecycleConfig(
    disconnectDelaySeconds: 5,
    reconnectDelaySeconds: 2,
  );
  
  /// 激进配置（立即断连重连）
  static const AppLifecycleConfig aggressiveConfig = AppLifecycleConfig(
    disconnectDelaySeconds: 0,
    reconnectDelaySeconds: 0,
  );
  
  /// 仅断连配置（不自动重连）
  static const AppLifecycleConfig disconnectOnlyConfig = AppLifecycleConfig(
    enableAutoReconnect: false,
  );

  /// 复制配置并修改部分参数
  AppLifecycleConfig copyWith({
    bool? enableAutoDisconnect,
    bool? enableAutoReconnect,
    int? disconnectDelaySeconds,
    int? reconnectDelaySeconds,
    bool? showDisconnectNotification,
    bool? showReconnectNotification,
  }) {
    return AppLifecycleConfig(
      enableAutoDisconnect: enableAutoDisconnect ?? this.enableAutoDisconnect,
      enableAutoReconnect: enableAutoReconnect ?? this.enableAutoReconnect,
      disconnectDelaySeconds: disconnectDelaySeconds ?? this.disconnectDelaySeconds,
      reconnectDelaySeconds: reconnectDelaySeconds ?? this.reconnectDelaySeconds,
      showDisconnectNotification: showDisconnectNotification ?? this.showDisconnectNotification,
      showReconnectNotification: showReconnectNotification ?? this.showReconnectNotification,
    );
  }
}

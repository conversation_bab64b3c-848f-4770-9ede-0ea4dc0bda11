
import 'package:freezed_annotation/freezed_annotation.dart';

part 'device_identity.freezed.dart'; 
part 'device_identity.g.dart'; 

@freezed
abstract class DeviceIdentity with _$DeviceIdentity {
  const factory DeviceIdentity({
    required String deviceIp,
    required String deviceName,
   @Default(false)  bool isOnline
  }) = _DeviceIdentity;

  factory DeviceIdentity.fromJson(Map<String, dynamic> json) =>
      _$DeviceIdentityFromJson(json);

  
}
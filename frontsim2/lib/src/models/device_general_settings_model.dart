

import 'package:rk_package/rk_package.dart';

mixin DeviceGeneralSettingsModel {
  bool initialized = false;
  final RxString deviceName = ''.obs;
  final RxString version = ''.obs;
  final RxString ipAddress = ''.obs;
  final RxString macAddress = ''.obs;
  final RxString connectivity = ''.obs;
  final RxBool gCastStatus = false.obs;
  final RxBool crashReportStatus = false.obs;
  final RxString timeZone = ''.obs;

  // 在in AppleHome --true 不能改名
  final RxBool inAppleHome = false.obs;

  final RxBool ledState = false.obs;

}

import 'package:freezed_annotation/freezed_annotation.dart';

part 'audio_source_model.g.dart';
part 'audio_source_model.freezed.dart';

@freezed
abstract class AudioSourceModel with _$AudioSourceModel {
  const factory AudioSourceModel({
    required String track,
    required String url,
  
  }) = _AudioSourceModel;

  factory AudioSourceModel.fromJson(Map<String, dynamic> json) =>
      _$AudioSourceModelFromJson(json);
}

@freezed
abstract class  LocalAudioFile with _$LocalAudioFile {
  const factory LocalAudioFile({
    required String filePath,
    required String trackName ,
  }) = _LocalAudioFile;

  factory LocalAudioFile.fromJson(Map<String, dynamic> json) =>
      _$LocalAudioFileFromJson(json);
}
import 'package:rk_get/rk_get.dart';
import 'package:i_device_action/i_device_action.dart';

mixin DeviceSoundSettingsModel {
  final RxInt maxVolume = 100.obs;
  final RxInt treble = 0.obs;
  final RxInt bass = 0.obs;
  final RxInt mid = 0.obs;
  final RxInt balance = 0.obs;

  final RxInt eqIndex = 0.obs;

  final RxList<ICeqModel> eqList = RxList.empty();

  final Map<int, List<IEqValueModel>> eqMap = <int, List<IEqValueModel>>{};

  final RxList<IEqValueModel> currentEqValues = RxList.empty();
}

import 'package:i_device_action/i_device_action.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_package/rk_package.dart';

mixin DeviceViewModel   {
  final RxList<String> inputSources = <String>[].obs;

  final Rx<String> currentInput = ''.obs;

  final Rx<double> volume = 0.0.obs;

  final Rx<LibrePlayViewModel?> playView = Rx(null);

  final RxInt playProgress = 0.obs;
  
  int get playProgressSafety => playProgress.value.clamp(0, playView.value?.viewInfo.TotalTime ?? 0); 

  final Rx<DevicePlayStatus?> playState = Rx(null);

  final RxBool mute = false.obs;

  final RxString playSource = ''.obs;

  final RxString deviceName = ''.obs;
}

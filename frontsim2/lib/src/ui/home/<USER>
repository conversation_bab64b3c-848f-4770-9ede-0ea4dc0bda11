import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../configs/local.dart';
import '../common/keep_alive_warp.dart';
import '../device/device_list/device_list.dart';
import '../radio_gage/radio_page.dart';
import '../setting/setting.dart';
import 'logic.dart';

@GetXRoute(bindings: [
  DeviceListBinding,
  SettingBinding,
])
class HomeView extends GetView<HomeLogic> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      initialIndex: 1,
      child: Scaffold(
        body: TabBarView(
          children: [
            const RadioPage(),
            const KeepAliveWrap(child: DeviceListView()),
            const SettingView(),
          ]
        ),
        bottomNavigationBar: SafeArea(
          bottom: true,
          child: TabBar(
            dividerColor: Colors.transparent,
            overlayColor: WidgetStateProperty.resolveWith((_) => Colors.transparent),
            labelStyle: TextStyle( fontFamily: STextFamily.regular.value, fontSize: 38.sp),
            labelColor: Theme.of(context).colorScheme.surface,
            indicator: BoxDecoration(),
            tabs: [
              Tab(
                icon: Builder(
                  builder: (context) => Sim2Icon(
                    path: Assets.ASSETS_ICONS_MUSIC_SVG,
                    size: 120.w,
                    color: DefaultTextStyle.of(context).style.color,
                  ),
                ),
                text: Local.common.music,
              ),
              Tab(
                icon: Builder(
                    builder: (context) => Sim2Icon(
                          path: Assets.ASSETS_ICONS_DEVICE_SVG,
                          size: 120.w,
                          color: DefaultTextStyle.of(context).style.color,
                        )),
                text: Local.common.device,
              ),
              Tab(
                icon: Builder(
                    builder: (context) => Sim2Icon(
                          path: Assets.ASSETS_ICONS_SETTING_SVG,
                          size: 120.w,
                          color: DefaultTextStyle.of(context).style.color,
                        )),
                text: Local.common.settings,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

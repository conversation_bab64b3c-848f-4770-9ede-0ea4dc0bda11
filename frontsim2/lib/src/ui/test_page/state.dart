
import 'package:flutter/widgets.dart';
import 'package:rk_get/rk_get.dart';

mixin TestPageState {
  final RxBool switchValue = false.obs;
  final RxBool textInfoState = true.obs;
  final RxString title = 'Product Name'.obs;
  final RxString selectTimeZone = 'Africa'.obs;
  final TextEditingController passwordController = TextEditingController();
  
  final RxDouble volumeValue = 0.0.obs;
  final RxBool volumeMute = false.obs;

  final RxDouble playValue = 0.0.obs;
  final RxDouble totalValue = 215.0.obs;

  final List<String> timeZones = [
    'Africa',
    'America',
    'Antarctica',
    'Arctic',
    'Asia',
    'Atlantic',
    'Australia',
    'Europe',
    'Indian',
    'Pacific',
    'Others',
  ];

  final List<int> wifiRssiList = [
    -33,
    -44,
    -50,
    -55,
    -66,
    -69,
    -77,
    -81,
    -88,
    -97,
    -91,
    -94,
    -140
  ];
}



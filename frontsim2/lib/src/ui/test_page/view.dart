import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/configs/log.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:sim2/src/ui/business/text_class.dart';
import 'package:sim2/src/ui/common/play_progress_slider.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../business/setting_container.dart';
import '../business/sim2_switch.dart';
import '../common/tap_scale.dart';
import '../common/volume_slider.dart';
import 'components/icon_example.dart';
import 'logic.dart';

@GetXRoute()
class TestPageView extends GetView<TestPageLogic> {
  const TestPageView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TestPage1'),
      ),
      backgroundColor: const Color.fromARGB(255, 152, 104, 101),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Sim2IconExample(),
            
            OnTapToScale(
              onTap: () {
                log.logDebug('TapOnScale');
              },
              child: Container(
                width: 400.w,
                height: 140.w,
                margin: EdgeInsets.only(bottom: 20.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(20.w),
                ),
                child: SText(
                  text: '点击',
                  color: Colors.white,
                ),
              ),
            ),
            ElevatedButton(onPressed: (){}, child: Text('data')),
            Text('data'),
            Sim2Switch(
              value: controller.switchValue,
              onChanged: (value) {
                log.logDebug('switch value: $value');
              },
            ),
            SizedBox(height: 30.w),
            SettingBlock(
              title: 'About',
              on: true.obs,
              children: [
                SettingBlockChild.info(
                  title: controller.title.value,
                  iconPath: Assets.ASSETS_ICONS_DEVICE_NAME_ICON_SVG,
                  info: 'Arylic LP10 710002'.obs,
                  onTap: () { },
                ),
                SettingBlockChild.switcher(
                  title: controller.title.value,
                  iconPath: Assets.ASSETS_ICONS_LOCATION_ICON_SVG,
                  switcher: Sim2Switch(
                    value: controller.switchValue,
                  ),
                ),
                SettingBlockChild.navigation(
                  iconPath: Assets.ASSETS_ICONS_DEVICE_NAME_ICON_SVG,
                  title: controller.title.value,
                  navigateTo: () {}
                ),
                SettingBlockChild.none(
                  title: controller.title.value,
                ),
              ],
            ),
            SettingBlock(
              children: [
                for (final item in controller.timeZones) 
                  SettingBlockChild.navigation(
                    title: item,
                    navigateTo: () {},
                  )
              ]
            ),
            SettingBlock(
              children: [
                for (final rssi in controller.wifiRssiList) 
                  SettingBlockChild.wifi(
                    title: 'OFFICE $rssi',
                    rssi: rssi,
                    wifiLock: rssi > -89,
                    onTap: () {
                      log.logDebug('wifi: $rssi');
                    },
                  )
              ]
            ),
            SettingBlock(
              children: [
                for (final item in controller.timeZones) 
                  SettingBlockChild.selected(
                    title: item,
                    selected: () => controller.selectTimeZone.value == item,
                    onTap: () { 
                      controller.selectTimeZone.value = item;
                      log.logDebug('selectTimeZone: ${controller.selectTimeZone.value}');
                    }
                  )
              ]
            ),
            // 页面标题
            STextTo.pageTitle('Device'),
            // 设备列表item方面
            STextTo.deviceItemTitle('Opera 3'),
            STextTo.deviceItemSongName('10cc'),
            STextTo.deviceItemSinger('Exclusive Radio'),
            STextTo.deviceItemInputSource('Music'),
            // 
            Obx(()=> VolumeSlider(
              value: controller.volumeValue.value,
              mute: controller.volumeMute.value,
              setMute: () {
                log.logDebug('setMute');
                controller.volumeMute.value = !controller.volumeMute.value;
              },
              onChanged: (value) {
                controller.volumeValue.value = value;
              },
              onChangeEnd: (value) {
                log.logDebug('volume: $value');
              },
            )),
            Obx(()=> PlayProgressSlider(
              value: controller.playValue.value,
              max: 99999,
              seekAble: !controller.volumeMute.value,
              onChanged: (value) {
                controller.playValue.value = value;
              },
              onChangeEnd: (value) {
                log.logDebug('volume: ${controller.playValue.value}');
              },
            )),
          ],
        ),
      )
    );
  }
}

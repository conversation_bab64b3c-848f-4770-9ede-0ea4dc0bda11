
import 'package:flutter/material.dart';

import '../../common/sim2_icons.dart';

/// 图标示例, 用于测试图标组件是否正常显示
class Sim2IconExample extends StatelessWidget {
  const Sim2IconExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: [
        Sim2Icon(
          path: Assets.ASSETS_ICONS_ADD_DEVICE_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_SETTINGS_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_LOADING_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_BLUETOOTH_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_ADD_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_ARROW_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_CLOSED_EYE_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_DEVICE_NAME_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_DOWN_SELECT_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_EQ_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_FM_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_USB_DAC_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_GOOGLE_CAST_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_LED_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_LINE_IN_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_LOCATION_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_LOCK_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_MUSIC_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_PREV_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_NEXT_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_OPT_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_PAUSE_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_PLAY_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_PHONO_IN_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_REGISTER_PHONE_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_VOLUME_XXX_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_VOLUME_XX_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_VOLUME_X_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_VOLUME_MUTE_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_WIFI_X_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_WIFI_XX_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_WIFI_XXX_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_WIFI_XXXX_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_USB_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_UPC_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_SOUND__EFFECT_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_SOFTWARE_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_RESET_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_REPORT_ICON_SVG,
        ),
        Sim2Icon(
          path: Assets.ASSETS_ICONS_LOCATION2_ICON_SVG,
        ),
      ],
    );
  }
}

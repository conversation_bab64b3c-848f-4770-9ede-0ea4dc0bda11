import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:i_device_action/i_device_action.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/routes/route_config.get_x_router_config.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../../helpers/text_formate.dart';
import '../../business/sim2_slider.dart';
import '../../business/text.dart';
import '../../business/text_class.dart';
import '../../common/common_background.dart';
import '../../common/sim2_icons.dart';
import '../../common/tap_scale.dart';
import '../../common/volume_slider.dart';
import 'logic.dart';

@GetXRoute(routeName: 'play_view')
class DevicePlayViewView extends GetView<DevicePlayViewLogic> {
  const DevicePlayViewView({
    super.key,
    required this.deviceIP,
  });

  final String deviceIP;

  String get deviceName => controller.deviceView.value?.deviceName.value ?? '';

  @override
  Widget build(BuildContext context) {
    final textColor = Theme.of(context).colorScheme.primary;
    return Stack(
      children: [
        Positioned.fill(
          child: Obx(() {
            final defaultWidget = CommonBackground(
                safeAreaTop: false,
                safeAreaBottom: false,
                playViewMask: false,
                padding: EdgeInsets.zero,
                body: Container(
                  color: Colors.black.withAlpha((255 * 0.3).toInt()),
                  child: Container(),
                ));
            final image = controller.backImageProvider.value;
            if (image == null) {
              return defaultWidget;
            }
            return ExtendedImage(
              image: controller.backImageProvider.value!,
              fit: BoxFit.cover,
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
            );
          }),
        ),
        Positioned.fill(
          child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  Assets.ASSETS_ICONS_LOGO_BRIONVEGA_SVG,
                  colorFilter: ColorFilter.mode(
                    textColor,
                    BlendMode.srcIn,
                  ),
                  width: 360.w,
                ),
                SizedBox(height: 25.w),
                Obx(() => SText(
                  text: deviceName,
                  fontSize: STextSize.xlm,
                  fontFamily: STextFamily.bold,
                  color: textColor,
                )),
              ],
            ),
            centerTitle: true,
            leading: OnTapToOpacity(
              onTap: () => Get.back(),
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                child: Sim2Icon(
                  path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
                  color: textColor,
                  size: 68.w,
                ),
              )
            ),
            actions: [
              OnTapToOpacity(
                  onTap: () {
                    Get.toTyped(GeneralSettingsTypedRoute(ip: deviceIP));
                  },
                  child: Container(
                    color: Colors.transparent,
                    width: 200.w,
                    alignment: Alignment.center,
                    child: Sim2Icon(
                      path: Assets.ASSETS_ICONS_SETTINGS_ICON_SVG,
                      color: textColor,
                      size: 65.w,
                    ),
                  )
                )
              ],
              toolbarHeight: 200.w,
              backgroundColor: Colors.transparent,
            ),
          body: Obx(() {
            if (controller.deviceView.value == null) {
              return const Center(child: CircularProgressIndicator());
            }
            return Padding(
              padding: EdgeInsets.only(
                left: 80.w,
                right: 80.w,
                top: 20.w,
                bottom: 20.w,
              ),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight,
                      ),
                      child: IntrinsicHeight(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            coverUrl,
                            musicInfo,
                            playSlider,
                            playControl,
                            volumeControl,
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
            ;
          }),
        )),
      ],
    );
  }

  Widget get coverUrl {
    return Obx(() {
      final defaultWidget = Container(
        width: 1000.w,
        height: 1000.w,
        decoration: BoxDecoration(
          color: Colors.black.withAlpha((255 * 0.3).toInt()),
        ),
        child: Sim2Icon(
          path: Assets.ASSETS_ICONS_MUSIC_ICON_SVG,
        ),
      );
      final imageUrl =
          controller.deviceView.value?.playView.value?.coverArtUrl(deviceIP);
      if (imageUrl?.isNotEmpty == true) {
        return ExtendedImage.network(
          imageUrl!,
          fit: BoxFit.cover,
          width: 1000.w,
          height: 1000.w,
          loadStateChanged: (state) {
            if (state.extendedImageLoadState == LoadState.failed) {
              return defaultWidget;
            }
            return null;
          },
        );
      }

      return defaultWidget;
    });
  }

  Widget get musicInfo {
    final deviceView = controller.deviceView.value!;
    return Obx(() {
      final playView = deviceView.playView.value;
      return Container(
        margin: EdgeInsets.only(top: 30.w),
        height: 230.w,
        child: switch (playView?.viewInfo.TrackName.isNotEmpty == true) {
          true => Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // icon
                switch (playView?.viewInfo.CurrentSource) {
                  4 => Sim2Icon(
                      path: Assets.ASSETS_ICONS_PRIMARY_LOGO_GREEN_PMS_U_SVG,
                      size: 100.w,
                    ),
                  _ => Container()
                },
                // info
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 20.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        STextTo.playPageSongName(
                            playView?.viewInfo.TrackName ?? ''),
                        STextTo.playPageSinger([
                          if (playView?.viewInfo.Artist.isNotEmpty == true)
                            playView?.viewInfo.Artist,
                          if (playView?.viewInfo.Album.isNotEmpty == true)
                            playView?.viewInfo.Album
                        ].join(',')),
                        if (playView?.viewInfo.SampleRate.isNotEmpty == true &&
                            playView?.viewInfo.SampleRate != '0')
                          STextTo.playPageSampleRate(
                              'SampleRate: ${playView?.viewInfo.SampleRate ?? ''}'),
                      ],
                    ),
                  ),
                )
              ],
            ),
          false => Align(
              alignment: Alignment.bottomLeft,
              child: STextTo.playPageSongName(Local.device.none_playing),
            ),
        },
      );
    });
  }

  Widget get playSlider {
    final deviceView = controller.deviceView.value!;
    return Padding(
      padding: EdgeInsets.only(top: 20.w, bottom: 20.w),
      child: Obx(() {
        final playView = deviceView.playView.value;
        if (playView == null) {
          return Row(
            children: [
              STextTo.playPageSliderValue('00:00'),
              Expanded(
                child: Sim2Slider(
                  value: 0,
                  max: 100,
                  onChanged: (_) {},
                  onChangeEnd: (_) {},
                  trackHeight: 20.w,
                  inactiveTrackColor: _inactiveTrackColor,
                  activeTrackColor: _activeTrackColor,
                  thumbColor: _activeTrackColor,
                  thumbShape: SliderComponentShape.noThumb,
                ),
              ),
              STextTo.playPageSliderValue('00:00'),
            ],
          );
        }
        if ((playView?.viewInfo.TotalTime ?? -1) < 1) {
          return Row(
            children: [
              STextTo.playPageSliderValue(
                TextFormate.formateMill2Min(deviceView.playProgress.value),
              ),
              Expanded(
                child: Sim2Slider(
                  value: 0,
                  max: 100,
                  onChanged: (_) {},
                  onChangeEnd: (_) {},
                  trackHeight: 20.w,
                  inactiveTrackColor: _inactiveTrackColor,
                  activeTrackColor: _activeTrackColor,
                  thumbColor: _activeTrackColor,
                  thumbShape: switch(deviceView.playView.value?.viewInfo.Seek) {
                    true => RoundSliderThumbShape(
                        enabledThumbRadius: 22.w,
                        disabledThumbRadius: 22.w,
                      ),
                    _ => SliderComponentShape.noThumb,
                  }
                ),
              ),
              STextTo.playPageSliderValue(
                TextFormate.formateMill2Min(playView!.viewInfo.TotalTime),
              ),
            ],
          );
        }
        return Row(
          children: [
            STextTo.playPageSliderValue(
              TextFormate.formateMill2Min(deviceView.playProgressSafety),
            ),
            Expanded(
              child: Sim2Slider(
                value: deviceView.playProgressSafety * 1.0,
                max: playView!.viewInfo.TotalTime * 1.0,
                onChanged: (value) {
                  deviceView.playProgress.value = value.toInt();
                },
                onChangeEnd: (value) {
                  controller.seek(value);
                },
                trackHeight: 20.w,
                inactiveTrackColor: _inactiveTrackColor,
                activeTrackColor: _activeTrackColor,
                thumbColor: _activeTrackColor,
                thumbShape: switch(deviceView.playView.value?.viewInfo.Seek) {
                  true => RoundSliderThumbShape(
                      enabledThumbRadius: 22.w,
                      disabledThumbRadius: 22.w,
                    ),
                  _ => SliderComponentShape.noThumb,
                }
              ),
            ),
            STextTo.playPageSliderValue(
              TextFormate.formateMill2Min(playView.viewInfo.TotalTime),
            ),
          ],
        );
      }),
    );
  }

  Widget get playControl {
    final deviceView = controller.deviceView.value!;
    return Obx(() {
      final playState = deviceView.playState.value ??
          deviceView.playView.value?.viewInfo.playState.toDevicePlayStatus;
      return SizedBox(
        // padding: EdgeInsets.symmetric(horizontal: 180.w),
        width: 720.w,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            GestureDetector(
              child: Sim2Icon(
                path: Assets.ASSETS_ICONS_PREV_ICON_SVG,
                size: 90.w,
              ),
              onTap: () {
                controller.prev();
              },
            ),
            SizedBox(width: 40.w),
            GestureDetector(
              onTap: () {
                controller.playOrPause();
              },
              child: Sim2Icon(
                path: playState == DevicePlayStatus.playing
                    ? Assets.ASSETS_ICONS_PAUSE_ICON_SVG
                    : Assets.ASSETS_ICONS_PLAY_ICON_SVG,
                size: 210.w,
              ),
            ),
            SizedBox(width: 40.w),
            GestureDetector(
              child: Sim2Icon(
                path: Assets.ASSETS_ICONS_NEXT_ICON_SVG,
                size: 90.w,
              ),
              onTap: () {
                controller.next();
              },
            ),
          ],
        ),
      );
    });
  }

  Widget get volumeControl {
    final deviceView = controller.deviceView.value!;
    return Padding(
      padding: EdgeInsets.only(top: 20.w),
      child: Obx(() {
        final mute = deviceView.mute.value;
        return SafeArea(
          top: false,
          bottom: true,
          child: VolumeSlider(
            value: deviceView.volume.value,
            mute: false,
            onChanged: (v) {
              if (!mute) {
                deviceView.volume.value = v;
              }
            },
            onChangeEnd: (v) {
              if (!mute) {
                controller.setVolume(v);
              }
            },
          ),
        );
      }),
    );
  }
}

const _inactiveTrackColor = Color.fromRGBO(255, 255, 255, 0.2);
const _activeTrackColor = Color.fromRGBO(255, 255, 255, 1);

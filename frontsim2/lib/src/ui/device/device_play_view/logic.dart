import 'package:base_common/base_common.dart';
import 'package:flutter/material.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_package/rk_package.dart';

import '../../../helpers/background_image.dart';
import '../../../services/devices_service.dart';
import 'state.dart';

class DevicePlayViewLogic extends GetxController
    with DevicePlayViewState, GetDeviceExt {
  CancelListener? cancelListener;
  String _coverArtUrl = '';
  CancelListener? inputSourceListener;

  @override
  void onInit() {
    super.onInit();
    deviceIP = Get.arguments['deviceIP'] as String;

    findDeviceService(deviceIP).then((device) {
      deviceView.value = device;
      //进入页面之后刷新一下play_view
      device.refreshPlayView();

      cancelListener = deviceView.value!.playView.listen((playView) {
        playChange(playView);
      }).cancel;
      //如果playView不为空，直接赋值
      if (deviceView.value!.playView.value != null) {
        playChange(deviceView.value!.playView.value);
      }
      //如果收到切换模式的监听切当前有背景,清空背景
      inputSourceListener =
          deviceView.value!.actions.registerInputSourceListener((event) {
        if (event != "NET") {
          if (backImageProvider.value != null) {
            backImageProvider.value = null;
          }
        }
      });
    });
  }

  void playChange(LibrePlayViewModel? playView) {
    if (playView?.viewInfo.CoverArtUrl != null) {
      if (playView?.viewInfo.CoverArtUrl != _coverArtUrl) {
        BackgroundImage.getBackgroundImage((
          ExtendedNetworkImageProvider(
            playView!.coverArtUrl(deviceIP),
            cache: true,
            retries: 0,
            printError: false,
          ),
          Size(500.w, 500.w)
        )).then((value) {
          backImageProvider.value = value;
        });
        _coverArtUrl = playView.viewInfo.CoverArtUrl;
      }
    } else {
      backImageProvider.value = null;
    }
  }

  Future<void> toggleMute() async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.tootleMute();
  }

  Future<void> setVolume(double vol) async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.setVolume(vol);
  }

  Future<void> setInputSource(String input) async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.setInputSource(input);
  }

  Future<void> seek(double progress) async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.seek(progress);
  }

  Future<void> playOrPause() async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.playOrPause();
  }

  Future<void> next() async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.next();
  }

  Future<void> prev() async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.prev();
  }

  @override
  void onClose() {
    super.onClose();
    cancelListener?.call();
    inputSourceListener?.call();
  }
}

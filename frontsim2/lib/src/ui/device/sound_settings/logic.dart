import 'package:base_common/base_common.dart';
import 'package:i_device_action/i_device_action.dart';
import 'package:rk_get/rk_get.dart';

import '../../../services/device_sound_settings_service.dart';
import '../../../services/devices_service.dart';
import 'state.dart';

class SoundSettingsLogic extends GetxController
    with SoundSettingsState, GetDeviceExt, ControllerMountedMixin {
  late final String deviceIP;

  CancelListener? _settingsValueListener;
  @override
  void onInit() {
    super.onInit();
    deviceIP = Get.arguments['ip'] as String;
  }

  @override
  void onReady() {
    super.onReady();
    findDeviceService(deviceIP).then((device) {
      deviceService.value = device as DeviceSoundSettingsService;
      _settingsValueListener =
          deviceService.value!.registerSoundSettingsListener();
      if (!mounted) {
        _settingsValueListener?.call();
        _settingsValueListener = null;
      }
    });
  }

  ICeqModel? get selectedEq {
    if (deviceService.value == null) return null;
    final eqIndex = deviceService.value?.eqIndex.value ?? 0;
    return deviceService.value?.eqList.firstWhereOrNull((e) => e.id == eqIndex);
  }

  String get eqName {
    return selectedEq?.name ?? '';
  }

  void setEQgain(int index, int gain) {
    if (deviceService.value == null) return;
    final eqValues = deviceService.value!.currentEqValues;
    if (index < 0 || index >= eqValues.length) return;
    final value = eqValues[index];

    if (value.gain == gain) return; // No change needed
    final newValue = value.copyWith(gain: gain);
    deviceService.value!.currentEqValues[index] = newValue;
    deviceService.value!.setEQValue(newValue);
  }

  @override
  void onClose() {
    super.onClose();
    _settingsValueListener?.call();
  }
}

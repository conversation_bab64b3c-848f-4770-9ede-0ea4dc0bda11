import 'package:flutter/material.dart';
import 'package:rk_get/rk_get.dart';
import 'package:rk_package/rk_package.dart';

import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../business/setting_box.dart';
import '../../business/text.dart';
import '../../business/the_unfold.dart';
import '../../common/sim2_nav.dart';
import '../../radio_gage/components/ontaps.dart';
import 'components/silder_eq.dart';
import 'components/slider_setting.dart';
import 'logic.dart';

@getXRoute
class SoundSettingsView extends GetView<SoundSettingsLogic> {
  const SoundSettingsView({required this.ip, super.key});

  final String ip;
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: Sim2AppBar(
        title: Local.device.sound_settings,
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 18.0),
        child: Obx(() {
          final service = controller.deviceService.value;
          return switch (service) {
            null => SizedBox.shrink(),
            _ => ListView(
                physics: ClampingScrollPhysics(),
                children: [
                  SettingBox(
                    children: [
                      Obx(() => SliderSetting(
                            name: Local.device.volume_limit,
                            value: service.maxVolume.value * 1.0,
                            min: 30,
                            max: 100,
                            formatValue: (value) => '${value.ceil()}%',
                            onChanged: (value) =>
                                service.maxVolume.value = value.ceil(),
                            onChangeEnd: (value) =>
                                service.setMaxVolume(value.ceil()),
                          )),
                      Obx(() => SliderSetting.withCenter(
                            name: Local.device.treble,
                            value: service.treble.value * 1.0,
                            min: -10,
                            max: 10,
                            center: 0,
                            formatValue: (value) => '${value.ceil()}dB',
                            onChanged: (value) =>
                                service.treble.value = value.ceil(),
                            onChangeEnd: (value) =>
                                service.setTreble(value.ceil()),
                          )),
                      Obx(() => SliderSetting.withCenter(
                            center: 0,
                            name: Local.device.mid,
                            value: service.mid.value * 1.0,
                            min: -10,
                            max: 10,
                            formatValue: (value) => '${value.ceil()}dB',
                            onChanged: (value) =>
                                service.mid.value = value.ceil(),
                            onChangeEnd: (value) =>
                                service.setMid(value.ceil()),
                          )),
                      Obx(() => SliderSetting.withCenter(
                            name: Local.device.bass,
                            value: service.bass.value * 1.0,
                            min: -10,
                            max: 10,
                            center: 0,
                            formatValue: (value) => '${value.ceil()}dB',
                            onChanged: (value) =>
                                service.bass.value = value.ceil(),
                            onChangeEnd: (value) =>
                                service.setBass(value.ceil()),
                          )),
                      Obx(() => SliderSetting.withCenter(
                            center: 0,
                            name: Local.device.balance,
                            value: service.balance.value * 1.0,
                            min: -10,
                            max: 10,
                            formatValue: (value) => '${value.ceil()}dB',
                            onChanged: (value) =>
                                service.balance.value = value.ceil(),
                            onChangeEnd: (value) =>
                                service.setBalance(value.ceil()),
                          )),
                      Padding(padding: EdgeInsets.only(bottom: 10.w)),
                    ],
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: EdgeInsets.only(left: 20.w),
                          child: SText(
                            text: Local.device.advance_audio_settings,
                            fontSize: 50.w,
                            fontFamily: STextFamily.bold,
                          ),
                        ),
                        Unfold(on: controller.on)
                      ],
                    ),
                  ),
                  Obx(() => AnimatedOpacity(
                        opacity: controller.on.value ? 1.0 : 0.0,
                        duration: const Duration(milliseconds: 150),
                        curve: Curves.easeInOut,
                        child: AnimatedContainer(
                            height: controller.on.value ? 1212.w + 20.w : 0.0,
                            margin: EdgeInsets.only(top: 35.w),
                            padding: EdgeInsets.symmetric(
                                horizontal: 44.w, vertical: 10.w),
                            duration: Duration(milliseconds: 150),
                            curve: Curves.easeInOut,
                            decoration: BoxDecoration(
                                color: Color.fromRGBO(15, 15, 15, .7),
                                borderRadius: BorderRadius.circular(52.w)),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: 14.w,
                              ),
                              child: Column(
                                children: [
                                  Padding(
                                    padding:
                                        EdgeInsets.only(top: 40.w, bottom: 6.w),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          alignment: Alignment.centerLeft,
                                          margin: EdgeInsets.only(
                                            left: 4.w,
                                          ),
                                          child: SText(
                                            text: Local
                                                .device.personalized_EQ, // 预设
                                            fontSize: STextSize.md,
                                            color: colorScheme.primary,
                                            fontFamily: STextFamily.bold,
                                          ),
                                        ),
                                        Row(
                                          children: [
                                            OnTapScaleToSmallBox(
                                              onTap: () {},
                                              child: Container(
                                                height: 100.w,
                                                alignment: Alignment.center,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 24.w),
                                                decoration: BoxDecoration(
                                                    color: colorScheme.surface,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10.r)),
                                                child: SText(
                                                  text: Local.common.delete,
                                                  fontSize: STextSize.md,
                                                  color: colorScheme.primary,
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 14.w,
                                            ),
                                            OnTapScaleToSmallBox(
                                              onTap: () {},
                                              child: Container(
                                                height: 100.w,
                                                alignment: Alignment.center,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 24.w),
                                                decoration: BoxDecoration(
                                                    color: colorScheme.surface,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10.r)),
                                                child: SText(
                                                  text: Local.common.save,
                                                  fontSize: STextSize.md,
                                                  color: colorScheme.primary,
                                                ),
                                              ),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                  Padding(
                                      padding: EdgeInsets.only(
                                          top: 20.w, bottom: 10.w),
                                      child: GestureDetector(
                                        onTap: () {},
                                        child: Container(
                                          height: 129.w,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 14.w),
                                          decoration: BoxDecoration(
                                              color: colorScheme.surface,
                                              borderRadius:
                                                  BorderRadius.circular(12.w)),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Obx(() => Padding(
                                                  padding: EdgeInsets.only(
                                                      left: 20.w),
                                                  child: SText(
                                                    text: controller.eqName,
                                                    color: colorScheme.primary,
                                                    fontSize: STextSize.md,
                                                  ))),
                                              //三角
                                              Icon(
                                                Icons.arrow_drop_down,
                                                color: colorScheme.primary,
                                                size: 80.w,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  Expanded(
                                      child: Padding(
                                    padding: EdgeInsets.only(
                                        top: 20.w, bottom: 10.w),
                                    child: Obx(() {
                                      final eqList = controller
                                          .deviceService.value?.currentEqValues;
                                      if (eqList?.isNotEmpty == true) {
                                        return AbsorbPointer(
                                          absorbing:
                                              controller.selectedEq?.isCustom ==
                                                  true,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: List.generate(
                                                eqList!.length, (index) {
                                              return Expanded(
                                                  child: Column(
                                                children: [
                                                  Expanded(
                                                      child: SliderEQ(
                                                          value: eqList[index]
                                                              .gain,
                                                          onChanged: (gain) {
                                                            eqList[
                                                                index] = eqList[
                                                                    index]
                                                                .copyWith(
                                                                    gain: gain);
                                                          },
                                                          onChangeEnd: (gain) {
                                                            controller
                                                                .setEQgain(
                                                                    index,
                                                                    gain);
                                                          })),
                                                  //分贝
                                                  Obx(() => SText(
                                                        text:
                                                            '${eqList[index].gain}dB',
                                                        fontSize: STextSize.sm,
                                                        color:
                                                            colorScheme.primary,
                                                      )),
                                                  //频率
                                                  Obx(() => SText(
                                                        text:
                                                            '${eqList[index].freq}',
                                                        fontSize: STextSize.sm,
                                                        color:
                                                            colorScheme.primary,
                                                      ))
                                                ],
                                              ));
                                            }),
                                          ),
                                        );
                                      } else {
                                        return AbsorbPointer(
                                          absorbing: false, //todo
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: List.generate(9, (index) {
                                              //
                                              return SliderEQ(
                                                  value: 0,
                                                  onChanged: (_) {},
                                                  onChangeEnd: (_) {});
                                            }),
                                          ),
                                        );
                                      }
                                    }),
                                  )),
                                ],
                              ),
                            )),
                      )),
                ],
              )
          };
        }),
      ),
    );
  }
}

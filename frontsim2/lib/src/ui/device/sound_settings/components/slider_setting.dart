import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import '../../../business/sim2_slider_shape.dart';

final _inactiveTrackColor = Color.fromRGBO(255, 255, 255, 0.2);
final _activeTrackColor = Color.fromRGBO(255, 255, 255, 1);

class SliderSetting extends StatefulWidget {
  const SliderSetting(
      {super.key,
      required this.name,
      this.min = 0,
      this.max = 1,
      this.formatValue,
      required this.value,
      this.onChanged,
      this.onChangeEnd})
      : center = null;

  const SliderSetting.withCenter({
    super.key,
    required this.name,
    this.min = 0,
    this.max = 1,
    required double this.center,
    this.formatValue,
    required this.value,
    this.onChanged,
    this.onChangeEnd,
  });

  final String name;

  final double min;

  final double max;

  final double? center;

  final String Function(double value)? formatValue;
  final double value;
  final ValueChanged<double>? onChanged;
  final ValueChanged<double>? onChangeEnd;

  @override
  State<SliderSetting> createState() => _SliderSettingState();
}

class _SliderSettingState extends State<SliderSetting> {
  @override
  Widget build(BuildContext context) {
    final textColor = Theme.of(context).colorScheme.primary;
    SliderThemeData sliderThemeData;
    if (widget.center == null) {
      sliderThemeData = SliderThemeData(
        activeTrackColor: _activeTrackColor,
        inactiveTrackColor: _inactiveTrackColor,
        trackHeight: 18.w,
        trackShape: const TrackHeightSliderTrackShape(),
        thumbColor: _activeTrackColor,
        thumbShape: RoundSliderThumbShape(
          enabledThumbRadius: 18.w,
          disabledThumbRadius: 18.w,
        ),
      );
    } else {
      sliderThemeData = SliderThemeData(
        secondaryActiveTrackColor: _activeTrackColor,
        activeTrackColor: _inactiveTrackColor,
        inactiveTrackColor: _inactiveTrackColor,
        trackHeight: 18.w,
        trackShape: const TrackHeightSliderTrackShape(),
        thumbColor: _activeTrackColor,
        thumbShape: RoundSliderThumbShape(
          enabledThumbRadius: 18.w,
          disabledThumbRadius: 18.w,
        ),
      );
    }
    return Container(
      padding: EdgeInsets.only(left: 50.w, right: 50.w, top: 50.w, bottom: 0.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                widget.name,
                style: TextStyle(
                  fontSize: 46.sp,
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Expanded(child: SizedBox.shrink()),
              Text(
                widget.formatValue?.call(widget.value) ?? '${widget.value}',
                style: TextStyle(
                  fontSize: 46.sp,
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SliderTheme(
            data: sliderThemeData,
            child: Slider(
              min: widget.min,
              max: widget.max,
              secondaryTrackValue: widget.center,
              value: widget.value,
              onChanged: widget.onChanged,
              onChangeEnd: widget.onChangeEnd,
            ),
          )
        ],
      ),
    );
  }
}

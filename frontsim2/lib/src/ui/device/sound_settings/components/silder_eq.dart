import 'package:base_common/base_common.dart';
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import '../../../business/sim2_slider_shape.dart';

final _inactiveTrackColor = Color.fromRGBO(255, 255, 255, 0.2);
final _activeTrackColor = Color.fromRGBO(255, 255, 255, 1);

class SliderEQ extends StatefulWidget {
  const SliderEQ({
    super.key,
    required this.value,
    required this.onChanged,
    required this.onChangeEnd,
  });

  final int value;

  final OutValueCallback<int> onChanged;

  final OutValueCallback<int> onChangeEnd;

  @override
  State<SliderEQ> createState() => _SliderEQState();
}

class _SliderEQState extends State<SliderEQ> {
  @override
  Widget build(BuildContext context) {
    final sliderThemeData = SliderThemeData(
      secondaryActiveTrackColor: _activeTrackColor,
      activeTrackColor: _inactiveTrackColor,
      inactiveTrackColor: _inactiveTrackColor,
      trackHeight: 18.w,
      trackShape: const TrackHeightSliderTrackShape(),
      thumbColor: _activeTrackColor,
      thumbShape: RoundSliderThumbShape(
        enabledThumbRadius: 18.w,
        disabledThumbRadius: 18.w,
      ),
    );
    return SliderTheme(
        data: sliderThemeData,
        child: RotatedBox(
          quarterTurns: -1,
          child: Slider(
            min: -10,
            max: 10,
            secondaryTrackValue: 0,
            value: widget.value * 1.0,
            onChanged: (value) => widget.onChanged(value.ceil()),
            onChangeEnd: (value) => widget.onChangeEnd(
              value.ceil(),
            ),
          ),
        ));
  }
}

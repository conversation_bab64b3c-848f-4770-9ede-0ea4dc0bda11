import 'package:rk_get/rk_get.dart';

import '../../../configs/log.dart';
import '../../../services/devices_service.dart';
import 'state.dart';

class DeviceItemLogic extends GetxController
    with DeviceItemState, GetDeviceExt {
  @override
  void onReady() {
    super.onReady();
    findDeviceService(deviceIP).then((device) {
      deviceView.value = device;
    }).catchError((_) {
      //设备连不上或者啥的异常,不处理
    });
  }

  Future<void> toggleMute() async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.tootleMute();
  }

  Future<void> setVolume(double vol) async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.setVolume(vol);
  }

  Future<void> setInputSource(String input) async {
    assert(deviceView.value != null, 'deviceView is null');
    await deviceView.value!.setInputSource(input);
  }

  @override
  void onClose() {
    super.onClose();
    //销毁设备由刷新的差异化触发,不需要在这里销毁
    log.logDebug('device Item onClose,IP is $deviceIP');
  }
}

import 'package:flutter/material.dart';
import 'package:i_device_action/i_device_action.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../configs/input_source_icon.dart';
import '../../../configs/local.dart';
import '../../../routes/route_config.get_x_router_config.dart';
import '../../../services/devices_service.dart';
import '../../business/text_class.dart';
import '../../common/mmvlizer.dart';
import '../../common/sim2_icons.dart';
import '../../common/volume_slider.dart';
import '../../radio_gage/routes.dart';
import 'logic.dart';

class DeviceItemView extends GetView<DeviceItemLogic> {
  const DeviceItemView({
    required this.tag,
    super.key,
  });

  @override
  // ignore: overridden_fields
  final String tag;

  LibreDevicesService get devicesService => Get.find<LibreDevicesService>();

  @override
  Widget build(BuildContext context) {
    return Obx(() => switch (controller.deviceView.value) {
          null => Container(),
          _ => Obx(() {
              return GestureDetector(
                onTap: () {
                  if (!devicesService.isSelect(tag)) {
                    devicesService.selectDevice(tag);
                  } else {
                    Get.toTyped(
                      PlayViewTypedRoute(
                        deviceIP: tag,
                      ),
                    );
                  }
                },
                child: Container(
                  padding: EdgeInsets.only(left: 45.w, right: 45.w, top: 35.w),
                  margin: EdgeInsets.only(bottom: 45.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: switch (devicesService.isSelect(tag)) {
                          true => [
                              Color.fromRGBO(5, 5, 5, 0.5),
                              Color.fromRGBO(12, 12, 12, 1),
                            ],
                          false => [
                              Theme.of(context).cardColor,
                              Theme.of(context).cardColor,
                            ]
                        }),
                    borderRadius: BorderRadius.circular(60.w),
                  ),
                  child: Column(
                    children: [header, playView, volumeControl, inputSource],
                  ),
                ),
              );
            }),
        });
  }

  String get deviceName {
    return controller.deviceView.value?.deviceName.value ?? '';
  }

  Widget get header {
    return Container(
      margin: EdgeInsets.only(top: 10.w, bottom: 40.w),
      child: Row(
        children: [
          // title
          Obx(() => STextTo.deviceItemTitle(deviceName)),
          // options
          Expanded(
            child: SizedBox.shrink(),
          )
        ],
      ),
    );
  }

  Widget get playView {
    final deviceView = controller.deviceView.value!;
    final defaultCover = Container(
      width: 170.w,
      height: 170.w,
      decoration: BoxDecoration(
        color: Colors.grey,
        borderRadius: BorderRadius.circular(20.w),
      ),
      alignment: Alignment.center,
      child: Sim2Icon(
        path: Assets.ASSETS_ICONS_MUSIC_ICON_SVG,
      ),
    );
    return Obx(() => Row(
          children: [
            switch (
                deviceView.playView.value?.viewInfo.CoverArtUrl.isNotEmpty) {
              // coverUrl
              true => ExtendedImage.network(
                  deviceView.playView.value?.coverArtUrl(tag) ?? '',
                  width: 170.w,
                  height: 170.w,
                  fit: BoxFit.cover,
                  cache: true,
                  loadStateChanged: (state) {
                    if (state.extendedImageLoadState != LoadState.completed) {
                      return defaultCover;
                    }
                    return null;
                  },
                ),
              // error
              _ => defaultCover,
            },
            // space
            SizedBox(width: 40.w),
            // info
            Expanded(
                child: switch (
                    deviceView.playView.value?.viewInfo.TrackName.isNotEmpty ==
                        true) {
              true => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    STextTo.deviceItemSongName(
                        deviceView.playView.value?.title ?? ''),
                    Row(
                      children: [
                        Flexible(
                          child: Container(
                            margin: const EdgeInsets.only(right: 10),
                            child: STextTo.deviceItemSinger(
                                deviceView.playView.value?.viewInfo.Artist ??
                                    ''),
                          ),
                        ),
                        // playState
                        Obx(() {
                          final DevicePlayStatus? playState =
                              deviceView.playState.value ??
                                  deviceView.playView.value?.viewInfo.playState
                                      .toDevicePlayStatus;
                          return switch (
                              playState == DevicePlayStatus.playing) {
                            true => MiniMusicVisualizer(),
                            false => SizedBox.shrink()
                          };
                        })
                      ],
                    )
                  ],
                ),
              _ => Container(
                  padding: EdgeInsets.only(top: 10.w),
                  height: 170.w,
                  alignment: Alignment.topLeft,
                  child: STextTo.deviceItemSongName(
                    deviceView.currentInput.value == "NET"
                        ? Local.device.none_playing
                        : 'Unknown',
                  ),
                )
            }),
          ],
        ));
  }

  Widget get volumeControl {
    final deviceView = controller.deviceView.value!;
    return Obx(() {
      //
      final mute = deviceView.mute.value;
      final volume = deviceView.volume;
      //
      return VolumeSlider(
        value: volume.value,
        mute: mute,
        setMute: () => controller.toggleMute(),
        onChanged: (value) {
          if (!mute) {
            volume.value = value;
          }
        },
        onChangeEnd: (value) {
          if (!mute) {
            controller.setVolume(value);
          }
        },
        customEndIcon: GestureDetector(
          onTap: () {
            Get.toTyped(
              SoundSettingsViewTypedRoute(
                ip: tag,
              ),
            );
          },
          child: Container(
            width: 120.w,
            alignment: Alignment.center,
            child: Sim2Icon(
              path: Assets.ASSETS_ICONS_EQ_ICON_SVG,
              size: 85.w,
            ),
          ),
        ),
      );
    });
  }

  Widget get inputSource {
    final deviceView = controller.deviceView.value!;
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          height: devicesService.isSelect(tag) ? 260.w : 0,
          child: Stack(
            children: [
              ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                scrollDirection: Axis.horizontal,
                itemCount: deviceView.inputSources.length,
                itemBuilder: (context, index) {
                  final source = deviceView.inputSources[index];
                  return OnTapToOpacity(
                    onTap: () {
                      if (deviceView.currentInput.value != source) {
                        controller.setInputSource(source);
                      } else {
                        if (source == 'NET') {
                          Get.toNamed(
                            RadioPageRoutes.radioPage,
                          );
                        }
                      }
                    },
                    child: Container(
                      width: 150.w,
                      margin: const EdgeInsets.only(right: 10),
                      child: SingleChildScrollView(
                        child: SizedBox(
                          height: 260.w,
                          child: Column(
                            children: [
                              SingleChildScrollView(
                                physics: const NeverScrollableScrollPhysics(),
                                child: SizedBox(
                                  width: 160.w,
                                  height: 160.w,
                                  child: Obx(() => Container(
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          color: switch (
                                              deviceView.currentInput.value ==
                                                  source) {
                                            true => Theme.of(context)
                                                .colorScheme
                                                .surface,
                                            _ => Theme.of(context)
                                                .colorScheme
                                                .tertiary,
                                          },
                                          borderRadius:
                                              BorderRadius.circular(30.w),
                                        ),
                                        child: Sim2Icon(
                                          path: getInputSourceIcon(source),
                                          size: getInputSourceIconSize(source),
                                        ),
                                      )),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.symmetric(vertical: 20.w),
                                alignment: Alignment.center,
                                child: STextTo.deviceItemInputSource(source),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              )
            ],
          ),
        ));
  }
}

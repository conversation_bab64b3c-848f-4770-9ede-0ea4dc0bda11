import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import 'logic.dart';
import 'view.dart';

class DeviceWrapper extends StatelessWidget {
  const DeviceWrapper({super.key, required this.tag});

  final String tag;
  @override
  Widget build(BuildContext context) {
    // 如果当前页面不在最顶层,直接不显示.防止find<controller>绑错路由,从而被错误的delete
    //如果盖上透明的弹窗路由会有问题
    if (ModalRoute.of(context)?.isCurrent != true) {
      return SizedBox.shrink();
    }
    return GetBuilder<DeviceItemLogic>(
        tag: tag,
        global: true,
        autoRemove: true,
        init: DeviceItemLogic()..deviceIP = tag,
        builder: (_) => DeviceItemView(
              tag: tag,
            ));
  }
}


import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';

import 'logic.dart';


class HomeNavTopView extends GetView<HomeNavTopLogic> {
  const HomeNavTopView({
    super.key,
    this.addToDevice,
  });

  final VoidCallback? addToDevice;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 220.w,
      margin: EdgeInsets.only(bottom: 60.w),
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // 图标区域
          Container(
            alignment: Alignment.topLeft,
            child: SvgPicture.asset(
              Assets.ASSETS_ICONS_LOGO_BRIONVEGA_SVG,
              width: 500.w,
            ),
          ),
          Sized<PERSON><PERSON>(height: 25.w),
          Container(
            alignment: Alignment.topLeft,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SText(
                    text: 'Opera 3',
                    fontSize: 72.w,
                    fontFamily: STextFamily.bold,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  OnTapToOpacity(
                    onTap: addToDevice,
                    child: Container(
                      width: 120.w,
                      height: 120.w,
                      color: Colors.transparent,
                      alignment: Alignment.center,
                      child: Sim2Icon(
                        path: Assets.ASSETS_ICONS_ADD_DEVICE_ICON_SVG,
                        size: 75.w,
                      ),
                    ),
                  )
                ],
              ),
          )
        ],
      )
    );
  }
}

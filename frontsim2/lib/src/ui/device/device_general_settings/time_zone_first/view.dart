import 'dart:math';

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../../configs/local.dart';
import '../../../../configs/time_zone_data.dart';
import '../../../../routes/route_config.get_x_router_config.dart';
import '../../../business/setting_box.dart';
import '../time_zone_second/view.dart';
import 'logic.dart';

@getXRoute
class TimeZoneFirstView extends GetView<TimeZoneFirstLogic> {
  const TimeZoneFirstView({
    super.key,
    required this.selectedTimeZone,
    required this.onTimeZoneSelected,
  });

  final String selectedTimeZone;

  final FutureSetTimeZoneCallback onTimeZoneSelected;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Sim2AppBar(
        title: Local.device.time_zone,
      ),
      body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.w),
          child: SettingBox(
            children: [
              for (var i = 0; i < TimeZone.timeZoneList.length; i++) ...[
                SetBox2(
                  title: TimeZone.timeZoneList[i],
                  info: '',
                  iconInfo: Transform.rotate(
                    angle: pi / 1,
                    child: Sim2Icon(
                      path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
                      size: 60.w,
                      // color: themeBase.primaryColor.value,
                    ),
                  ),
                  onTap: () {
                    Get.toTyped(
                      TimeZoneSecondViewTypedRoute(
                        index: i,
                        selectedTimeZone: selectedTimeZone,
                        onTimeZoneSelected: onTimeZoneSelected,
                      ),
                    );
                  },
                ),
                if (i != TimeZone.timeZoneList.length - 1)
                  Padding(
                    padding: EdgeInsets.only(left: 36.w, right: 36.w),
                    child: Divider(
                      height: 1,
                      color: Color(0xFFDDDDDD),
                    ),
                  ),
              ]
            ],
          )),
    );
  }
}

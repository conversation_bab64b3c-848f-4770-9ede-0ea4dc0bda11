import 'package:base_common/base_common.dart';
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:sim2/src/routes/route_config.get_x_router_config.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:sim2/src/ui/common/common_background.dart';
import 'package:sim2/src/ui/common/sim2_button.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../../services/devices_service.dart';
import '../../business/setting_container.dart';
import '../../business/sim2_switch.dart';
import '../../common/sim2_icons.dart';
import 'logic.dart';

@GetXRoute(
  routeName: 'general_settings',
)
class DeviceGeneralSettingsView extends GetView<DeviceGeneralSettingsLogic> {
  const DeviceGeneralSettingsView({
    super.key,
    required this.ip,
  });

  final String ip;

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
        appBar: Sim2AppBar(
          title: Local.device.general_settings,
        ),
        body: Obx(() => ListView(
              children: [
                Obx(() {
                  final inAppleHome =
                      controller.deviceSetting.value?.inAppleHome.value == true;
                  final rxDeviceName =
                      controller.deviceSetting.value?.deviceName;
                  return SettingBlock(
                    title: Local.device.about,
                    on: controller.aboutExpanded,
                    children: [
                      SettingBlockChild.info(
                        title: Local.device.product_name,
                        iconPath: Assets.ASSETS_ICONS_DEVICE_NAME_ICON_SVG,
                        info: rxDeviceName,
                        onTap: inAppleHome
                            ? null
                            : () {
                                //rename dialog
                                Get.dialog(
                                  DeviceRenameDialog(
                                    deviceName: rxDeviceName?.value ?? '',
                                    onRename: (name) async {
                                      final result = await loadingCallback(
                                          () => controller.renameDevice(name));
                                      if (result) {
                                        ToastUtils.showToast(
                                            msg: Local
                                                .device.rename_success_tip);
                                        rxDeviceName?.value = name;

                                        Get.back();
                                      } else {
                                        ToastUtils.showToast(
                                            msg: Local.device.action_fail);
                                      }
                                    },
                                    onCancel: () {
                                      Get.back();
                                    },
                                  ),
                                );
                              },
                      ),
                      SettingBlockChild.info(
                        title: Local.device.software_version,
                        iconPath: Assets.ASSETS_ICONS_SOFTWARE_ICON_SVG,
                        info: controller.deviceSetting.value?.version,
                        onTap: () {
                          controller.checkUpdate();
                        },
                      )
                    ],
                  );
                }),
                SettingBlock(
                  title: Local.device.net_and_conn,
                  on: controller.networkExpanded,
                  children: [
                    SettingBlockChild.info(
                      title: Local.device.iP_ID,
                      iconPath: Assets.ASSETS_ICONS_LOCATION2_ICON_SVG,
                      info: controller.ip,
                    ),
                    SettingBlockChild.info(
                      title: Local.device.mAC_ID,
                      iconPath: Assets.ASSETS_ICONS_LOCATION_ICON_SVG,
                      info: controller.deviceSetting.value?.macAddress,
                    ),
                    SettingBlockChild.info(
                      title: Local.device.connectivity,
                      iconPath: Assets.ASSETS_ICONS_LOCATION_ICON_SVG,
                      info: controller.deviceSetting.value?.connectivity,
                    )
                  ],
                ),
                SettingBlock(
                  title: Local.device.general,
                  on: controller.generalExpanded,
                  children: [
                    SettingBlockChild.switcher(
                        title: Local.device.led_light_switch,
                        iconPath:
                            Assets.ASSETS_ICONS_LED_ICON_SVG,
                        switcher: Sim2Switch(
                          value: controller.deviceSetting.value?.ledState ??
                              RxBool(false),
                          onChanged: (value) async {
                            loadingCallback(
                              () => controller.deviceSetting.value!
                                  .toggleLedState(value),
                            );
                          },
                        )),
                    SettingBlockChild.info(
                      title: Local.device.time_zone,
                      iconPath: Assets.ASSETS_ICONS_LOCATION_ICON_SVG,
                      info: controller.deviceSetting.value?.timeZone,
                      onTap: () {
                        if (controller.deviceSetting.value?.timeZone.value ==
                            null) {
                          return;
                        }
                        Get.toTyped(
                          TimeZoneFirstViewTypedRoute(
                            selectedTimeZone: controller
                                    .deviceSetting.value?.timeZone.value ??
                                '',
                            onTimeZoneSelected: (value) async {
                              await loadingCallback(
                                  () => controller.setTimeZone(value));

                              Get.until(
                                (route) =>
                                    route.settings.name ==
                                    RouteNames.generalSettings,
                              );
                            },
                          ),
                        );
                      },
                    ),
                    SettingBlockChild.navigation(
                      title: Local.device.google_cast,
                      iconPath: Assets.ASSETS_ICONS_GOOGLE_CAST_ICON_SVG,
                      navigateTo: () {
                        Get.toTyped(GcastReadyTypedRoute(deviceIP: ip));
                      },
                    ),
                    SettingBlockChild.navigation(
                      title: Local.device.report_issue,
                      iconPath: Assets.ASSETS_ICONS_REPORT_ICON_SVG,
                      navigateTo: () {},
                    ),
                    SettingBlockChild.navigation(
                      title: Local.device.factory_reset,
                      iconPath: Assets.ASSETS_ICONS_RESET_ICON_SVG,
                      navigateTo: () {
                        ToastUtils.showConfirmTip(
                            tips: Local.device.reset_text,
                            onConfirm: () async {
                              final result = await loadingCallback<bool>(
                                  () => controller.restoreFactorySettings());
                              if (result) {
                                ToastUtils.showToast(
                                    msg: Local.device.action_success);
                                //删除当前设备
                                Get.find<LibreDevicesService>()
                                    .removeDevice(ip);
                                Get.until((route) => route.isFirst);
                              }
                            },
                            onCancel: () {});
                      },
                    )
                  ],
                )
              ],
            )));
  }
}

class DeviceRenameDialog extends StatefulWidget {
  const DeviceRenameDialog({
    super.key,
    required this.deviceName,
    required this.onRename,
    required this.onCancel,
  });
  final String deviceName;
  final VoidCallback onCancel;
  final OutValueCallback<String> onRename;

  @override
  State<DeviceRenameDialog> createState() => _DeviceRenameDialogState();
}

class _DeviceRenameDialogState extends State<DeviceRenameDialog> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _controller.text = widget.deviceName;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  static const int renameMaxLength = 17;
  static const int renameMinLength = 2;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 28),
          decoration: BoxDecoration(
            color: Color.fromRGBO(15, 15, 15, 0.6),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SText(
                text: Local.device.rename_input_hint,
                fontSize: 18,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 18),
              TextField(
                controller: _controller,
                focusNode: _focusNode,
                maxLength: renameMaxLength,
                cursorColor: Colors.black,
                cursorErrorColor: Colors.black,
                decoration: InputDecoration(
                  hintText: Local.device.rename_input_hint,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.primary,
                  counterText: '',
                ),
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 28),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: Sim2Button(
                      onTap: () => widget.onCancel(),
                      text: Local.common.cancel,
                    ),
                  ),
                  const SizedBox(width: 18),
                  Expanded(
                    child: Sim2Button(
                      onTap: () {
                        if (_controller.text.isEmpty) {
                          ToastUtils.showToast(
                              msg: Local.device.rename_input_empty);
                          return;
                        }
                        if (_controller.text == widget.deviceName) {
                          return;
                        }
                        if (_controller.text.length > renameMaxLength) {
                          ToastUtils.showToast(
                              msg: Local.common.input_too_long(
                                  renameMaxLength, Local.device.product_name));
                          return;
                        }
                        if (_controller.text.length < renameMinLength) {
                          ToastUtils.showToast(
                              msg: Local.common.input_too_short(
                                  renameMinLength, Local.device.product_name));
                          return;
                        }

                        widget.onRename(_controller.text);
                      },
                      text: Local.common.confirm,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

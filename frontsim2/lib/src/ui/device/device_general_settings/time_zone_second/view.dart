import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../../configs/time_zone_data.dart';
import '../../../business/setting_box.dart';
import '../../../common/sim2_icons.dart';
import 'logic.dart';

typedef FutureSetTimeZoneCallback = Future<void> Function(String value);

@getXRoute
class TimeZoneSecondView extends GetView<TimeZoneSecondLogic> {
  const TimeZoneSecondView({
    super.key,
    required this.index,
    required this.selectedTimeZone,
    required this.onTimeZoneSelected,
  });
  final int index;
  final String selectedTimeZone;

  final FutureSetTimeZoneCallback onTimeZoneSelected;

  get pi => null;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Sim2AppBar(
        title: TimeZone.timeZoneList[index],
      ),
      body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.w),
          child: Builder(builder: (_) {
            final data = timeZoneList;
            return SingleChildScrollView(
              child: SettingBox(
                children: [
                  for (var i = 0; i < data.length; i++) ...[
                    SetBox2(
                      title: data[i],
                      info: '',
                      iconInfo: selectedTimeZone == data[i]
                          ? Sim2Icon(
                              path: Assets.ASSETS_ICONS_ARROW_ICON_SVG,
                              size: 50.sp,
                              // color: themeBase.primaryColor.value,
                            )
                          : Container(),
                      onTap: () {
                        onTimeZoneSelected(data[i]);
                      },
                    ),
                    if (i != data.length - 1)
                      Padding(
                        padding: EdgeInsets.only(left: 36.w, right: 36.w),
                        child: Divider(
                          height: 1.w,
                          color: const Color(0xFFDDDDDD),
                        ),
                      )
                  ]
                ],
              ),
            );
          })),
    );
  }

  List<String> get timeZoneList {
    List<String> zones = [];
    switch (index) {
      case 0:
        zones = TimeZone.africa;
        break;
      case 1:
        zones = TimeZone.america;
        break;
      case 2:
        zones = TimeZone.antarctica;
        break;
      case 3:
        zones = TimeZone.arctic;
        break;
      case 4:
        zones = TimeZone.asia;
        break;
      case 5:
        zones = TimeZone.atlantic;
        break;
      case 6:
        zones = TimeZone.australia;
        break;
      case 7:
        zones = TimeZone.europe;
        break;
      case 8:
        zones = TimeZone.indian;
        break;
      case 9:
        zones = TimeZone.pacific;
        break;
      case 10:
        zones = TimeZone.others;
        break;
    }
    return zones;
  }
}

import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_get/rk_get.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:sim2/src/routes/route_config.get_x_router_config.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../configs/local.dart';
import '../../../services/device_general_settings_service.dart';
import '../../../services/device_upgrade_service.dart';
import 'state.dart';
import '../../../services/devices_service.dart';

class DeviceGeneralSettingsLogic extends GetxController
    with DeviceGeneralSettingsState, GetDeviceExt {
  @override
  void onInit() {
    super.onInit();
    ip.value = Get.arguments['ip'] as String;
  }

  @override
  void onReady() {
    super.onReady();
    findDeviceService(ip.value).then((device) {
      deviceSetting.value = device as DeviceGeneralSettingsServices;
      device.intoSettingPage();
    });
  }

  Future<void> setTimeZone(String timeZone) async {
    assert(deviceSetting.value != null,
        'Device setting service is not initialized');
    await deviceSetting.value!.setTimeZone(timeZone);
  }

  Future<bool> restoreFactorySettings() async {
    assert(deviceSetting.value != null,
        'Device setting service is not initialized');
    return deviceSetting.value!.restoreFactorySettings();
  }

  Future<bool> renameDevice(String name) async {
    assert(deviceSetting.value != null,
        'Device setting service is not initialized');
    return deviceSetting.value!.rename(name);
  }

  Future<void> checkUpdate() async {
    assert(deviceSetting.value != null,
        'Device setting service is not initialized');
    //先检查全局状态是否是已经升级了
    switch (deviceSetting.value!.upgradeState.value) {
      case UpgradeSteps.noUpdate:
        //没有更新
        ToastUtils.showToast(msg: Local.setWifi.connected);
        break;
      case UpgradeSteps.downloadProgressEnum:
      case UpgradeSteps.imageAvailable:
      case UpgradeSteps.installProgressEnum:
      case UpgradeSteps.installComplete:
        //正在升级,直接进界面
        Get.toTyped(DeviceUpgradingViewTypedRoute(
          deviceIP: ip.value,
        ));
        break;
      default:
        // 可以升级,尝试发送升级命令,如果有升级进入升级界面
        final result = await loadingCallback(
            () => (deviceSetting.value! as DeviceUpgradeService).queryUpdate());
        if (result) {
          //有更新,进入升级界面
          Get.toTyped(DeviceUpgradingViewTypedRoute(
            deviceIP: ip.value,
          ));
        } else {
          //没有更新
          ToastUtils.showToast(msg: Local.setWifi.connected);
        }
        break;
    }
  }
}

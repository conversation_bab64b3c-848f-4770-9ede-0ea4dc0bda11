import 'package:base_common/base_common.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_get/rk_get.dart';

import '../../../configs/log.dart';
import '../../../services/devices_service.dart';
import 'state.dart';

class DeviceUpgradingLogic extends GetxController
    with ControllerMountedMixin, DeviceUpgradingState, GetDeviceExt {
  String? deviceIP;
  CancelListener? _cancelListener;
  CancelListener? _deviceOnlineListener;

  @override
  void onInit() {
    super.onInit();
    deviceIP ??= Get.arguments['deviceIP'] as String;

    findDeviceService(deviceIP!).then((device) {
      deviceView.value = device;
      if (!mounted) {
        return;
      }
      _cancelListener = deviceView.value!.actions
          .registerDeviceInUpgradeStateListener((state) {
        progress.value = state.progress * 1.0;
        if (state == UpgradeSteps.installComplete) {
          _deviceOnlineListener ??= device.online.listen((online) {
            if (online) {
              upgradeSucceed.value = true;
              log.logDebug('设备升级成功');
              _deviceOnlineListener?.call();
            }
          }).cancel;
        }
      });
    });
  }

  @override
  void onClose() {
    _cancelListener?.call();
    _deviceOnlineListener?.call();
    super.onClose();
  }
}

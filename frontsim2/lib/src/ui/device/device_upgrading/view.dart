import 'package:flutter/material.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';

import 'package:loading_animation_widget/loading_animation_widget.dart';

import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/common_background.dart';
import 'package:sim2/src/ui/common/sim2_button.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:simple_circular_progress_bar/simple_circular_progress_bar.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../business/text.dart';
import '../../set_wifi/components/loading.dart';
import 'logic.dart';

@getXRoute
class DeviceUpgradingView extends GetView<DeviceUpgradingLogic> {
  const DeviceUpgradingView({super.key, required this.deviceIP});

  final String deviceIP;

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: Sim2AppBar(
        title: Local.setWifi.title,
        showPop: false,
      ),
      body: UpgradeStepsView(
        onOk: () {
          Get.back();
        },
      ),
    );
  }
}

class UpgradeStepsView extends GetView<DeviceUpgradingLogic> {
  const UpgradeStepsView({super.key, required this.onOk});

  final VoidCallback onOk;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.deviceView.value == null) {
        return Center(
          child: LoadingAnimationWidget.hexagonDots(
            color: Theme.of(context).colorScheme.primary,
            size: 150.w,
          ),
        );
      }
      if (controller.upgradeSucceed.value) {
        return _installSucceed;
      }
      final step = controller.deviceView.value!.upgradeState.value;
      return switch (step) {
        UpgradeSteps.downloadProgressEnum => _downloadProgress,
        UpgradeSteps.installProgressEnum => _installProgress,
        UpgradeSteps.crcCheckError => _upgradeFailed,
        UpgradeSteps.downloadFail => _upgradeFailed,
        UpgradeSteps.imageAvailable => _installProgress,
        UpgradeSteps.none => Container(),
        UpgradeSteps.noUpdate => Container(
            alignment: Alignment.center,
            child: SetWiFiLoadingContainer(
              title: Local.setWifi.connected,
              type: SetWiFiLoadingType.ok,
            ),
          ),
        UpgradeSteps.installComplete => _waitDeviceOnline,
        _ => Container()
      };
    });
  }

  Widget get _getLoading {
    return Builder(
        builder: (context) => SimpleCircularProgressBar(
              size: 500.w,
              valueNotifier: controller.progress,
              progressStrokeWidth: 100.w,
              backStrokeWidth: 100.w,
              mergeMode: true,
              animationDuration: 0,
              onGetText: (value) {
                return Text(
                  '${value.toInt()}%',
                  style: TextStyle(
                    fontSize: 50.sp,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                );
              },
              progressColors: const [Colors.greenAccent],
              backColor:
                  Theme.of(context).colorScheme.primary.withAlpha(90), //0.4
            ));
  }

  Widget get _downloadProgress {
    return SizedBox(
        width: Get.width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              flex: 1,
              child: Container(
                alignment: Alignment.bottomCenter,
                color: Colors.transparent,
                child: _getLoading,
              ),
            ),
            SizedBox(height: 100.w),
            Expanded(
              flex: 1,
              child: Builder(
                  builder: (context) => Column(
                        children: [
                          LoadingAnimationWidget.hexagonDots(
                            color: Theme.of(context).colorScheme.secondary,
                            size: 150.w,
                          ),
                          SizedBox(height: 11.sp),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 16.sp),
                            child: SText.multiLine(
                              text: Local.device.upgrade_downloading,
                              color: Theme.of(context).colorScheme.secondary,
                              textAlign: TextAlign.center,
                              fontSize: STextSize.md,
                              softWrap: true,
                            ),
                          ),
                        ],
                      )),
            ),
          ],
        ));
  }

  Widget get _installProgress {
    return SizedBox(
        width: Get.width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
                flex: 1,
                child: Container(
                  alignment: Alignment.bottomCenter,
                  color: Colors.transparent,
                  child: _getLoading,
                )),
            SizedBox(height: 100.w),
            Expanded(
              flex: 1,
              child: Builder(
                  builder: (context) => Column(
                        children: [
                          LoadingAnimationWidget.hexagonDots(
                            color: Theme.of(context).colorScheme.secondary,
                            size: 150.w,
                          ),
                          SizedBox(height: 11.w),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 16.sp),
                            child: SText.multiLine(
                              text: Local.device.upgrade_installing,
                              color: Theme.of(context).colorScheme.secondary,
                              textAlign: TextAlign.center,
                              fontFamily: STextFamily.regular,
                              fontSize: STextSize.md,
                              softWrap: true,
                            ),
                          ),
                        ],
                      )),
            ),
          ],
        ));
  }

  Widget get _upgradeFailed {
    return Builder(
        builder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.sp, vertical: 26.sp),
                  child: SText.multiLine(
                    text: Local.device.upgrade_failed,
                    color: Theme.of(context).colorScheme.secondary,
                    textAlign: TextAlign.center,
                    fontSize: STextSize.mdlz,
                    fontFamily: STextFamily.regular,
                    softWrap: true,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 17.sp),
                  child: Sim2Button(
                    onTap: () {
                      //直接返回首页
                      Get.until((route) => route.isFirst);
                    },
                    text: Local.common.back,
                  ),
                )
              ],
            ));
  }

  Widget get _installSucceed {
    return Builder(
      builder: (context) => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
              width: 200.w,
              height: 200.w,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(44.w),
                  color: Colors.greenAccent),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 200.w,
                    height: 200.w,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(33.w),
                        color: Colors.white),
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 200.w,
                    ),
                  ),
                ],
              )),
          SizedBox(height: 200.w),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 26.w),
            child: SText.multiLine(
              text: Local.setWifi.connected,
              color: Theme.of(context).colorScheme.secondary,
              textAlign: TextAlign.center,
              fontSize: STextSize.md,
              fontFamily: STextFamily.regular,
              softWrap: true,
            ),
          ),
             SizedBox(height: 100.w),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 17.w),
            child: Sim2Button(
              onTap: () {
                onOk();
              },
              text: Local.common.ok,
            ),
          )
        ],
      ),
    );
  }

  Widget get _waitDeviceOnline {
    return Builder(
        builder: (context) => SizedBox(
            width: Get.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LoadingAnimationWidget.hexagonDots(
                  color: Theme.of(context).colorScheme.secondary,
                  size: 150.w,
                ),
                SizedBox(height: 40.w),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: SText.multiLine(
                    text:
                        Local.device.rebooting_tip1,
                    color: Theme.of(context).colorScheme.secondary,
                    textAlign: TextAlign.center,
                    fontFamily: STextFamily.regular,
                    fontSize: STextSize.mdlz,
                    softWrap: true,
                  ),
                ),
                Container(
                    width: Get.width,
                    padding: EdgeInsets.symmetric(horizontal: 6.w),
                    margin: EdgeInsets.only(top: 50.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Pointer,
                        Icon(Icons.warning_amber_outlined,
                            color: Colors.amber, size: 50.sp),
                        SizedBox(width: 8.sp),
                        Flexible(
                          child: SText.multiLine(
                            text: Local.device.rebooting_tip2,
                            color: Theme.of(context).colorScheme.secondary,
                            textAlign: TextAlign.center,
                            fontFamily: STextFamily.regular,
                            fontSize: STextSize.mdlz,
                            softWrap: true,
                          ),
                        )
                      ],
                    ))
              ],
            )));
  }
}

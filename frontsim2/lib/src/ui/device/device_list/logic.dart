import 'package:base_common/base_common.dart';
import 'package:rk_get/rk_get.dart';

import '../../../configs/log.dart';
import '../../../helpers/volume_key_intercept.dart';
import '../../../services/device_services.dart';
import '../../../services/devices_service.dart';
import 'state.dart';

class DeviceListLogic extends GetxController
    with DeviceListState, GetDevicesExt, GetDeviceExt {
  CancelListener? volumeKeyListener;
  CancelListener? deviceListListener;

  @override
  void onInit() {
    super.onInit();
    //注册列表变化监听
    deviceListListener = deviceService.devices.listen((value) {
      if (value.isEmpty) {
        volumeKeyListener?.call();
        return;
      } else {
        if (volumeKeyListener == null) {
          registerVolumeListener();
        }
      }
    }).cancel;
  }

  void registerVolumeListener() {
    //注册音量键监听
    volumeKeyListener = VolumeKeyIntercept.registerVolumeKeyListener((key) {
      if (key == DownKeyAction.volumeDown) {
        selectDevice.then((e) => e?.volumeDown(10));
      } else if (key == DownKeyAction.volumeUp) {
        selectDevice.then((e) => e?.volumeUp(10));
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    deviceService.scan();
  }

  Future<DeviceServices?> get selectDevice async {
    final deviceIP = deviceService.selectDeviceIP;
    if (deviceIP == null) {
      return null;
    }
    //通过ip查找设备
    final device = await findDeviceService(deviceIP);
    return device;
  }

  Future<void> onRefresh() async {
    //这里的刷新需要做一个延时disconnect设备的操作,不需要每次在刷新都去断连重连设备
    //首先备份设备
    // ignore: invalid_use_of_protected_member
    final devices = [...deviceService.devices.value];
    //清空
    deviceService.devices.value = [];
    //查找
    await deviceService.scan();
    //比较前后设备列表,差异化掉线的设备
    final diff =
        devices.where((device) => !deviceService.devices.contains(device));
    //这些是断连的设备,需要销毁
    for (var device in diff) {
      log.logDebug('设备${device.deviceName}已断连');
      //销毁
      await deviceService.destroyDevice(device.deviceIp);
    }
  }

  @override
  void onClose() {
    super.onClose();
    volumeKeyListener?.call();
    volumeKeyListener = null;
    deviceListListener?.call();
    deviceListListener = null;
  }
}

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:rk_get/rk_get.dart';
import 'package:sim2/src/configs/log.dart';
import 'package:sim2/src/ui/common/common_background.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';


import '../../../routes/route_config.get_x_router_config.dart';
import '../../../services/devices_service.dart';
import '../device_item/device_item.dart';
import '../home_nav_top/view.dart';
import 'logic.dart';


class DeviceListView extends GetView<DeviceListLogic> with GetDevicesExt {
  const DeviceListView({super.key});

  @override
  Widget build(BuildContext context) {
    controller;
    return CommonBackground(
      body: Column(
        children: [
          HomeNavTopView(
            addToDevice: () {
              log.logDebug('点击添加设备');
              Get.toTyped(
                BleDiscoveryViewTypedRoute(),
              );
            },
          ),
          ScrollConfiguration(
            behavior: const MaterialScrollBehavior().copyWith(
              dragDevices: {
                PointerDeviceKind.touch,
                PointerDeviceKind.mouse,
              },
            ),
            child: Flexible(
              child: RefreshIndicator(
                child: Obx(() => ListView.builder(
                  itemBuilder: (context, index) => DeviceWrapper(
                    tag: deviceService.devices[index].deviceIp,
                  ),
                  itemCount: deviceService.devices.length,
                )),
                onRefresh: () => controller.onRefresh(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OnTapBoxOnly<PERSON>lick extends StatelessWidget {
  const OnTapBoxOnlyClick({
    super.key,
    required this.child,
    required this.onTap,
    required this.tag,
    this.disable
  });

  final Widget child;
  final Function onTap;
  final RxBool tag;
  final bool? disable;

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: disable ?? false,
      child: GestureDetector(
        onTap: (){
          onTap();
          if(tag.value) tag.value = false;
          Future.delayed(const Duration(milliseconds: 1),()=> tag.value = true);
          Future.delayed(const Duration(milliseconds: 80),()=> tag.value = false);
        },
        onTapDown: (v){
          if(tag.value) tag.value = false;
          Future.delayed(const Duration(milliseconds: 1),()=> tag.value = true);
        },
        onTapUp: (v)=> tag.value = false,
        onTapCancel: ()=> tag.value = false,
        child: child,
      ),
    );
  }
}

// 点击缩小再
class OnTapScaleToSmallBox extends StatelessWidget {
  const OnTapScaleToSmallBox({
    super.key,
    required this.child,
    required this.onTap,
    this.disable,
    this.scale = 0.98
  });

  final Widget child;
  final Function onTap;
  final bool? disable;
  final double scale;

  @override
  Widget build(BuildContext context) {
    RxBool tag = false.obs;
    
    return AbsorbPointer(
      absorbing: disable??false,
      child: GestureDetector(
        onTap: (){
          onTap();
          if(tag.value) tag.value = false;
          Future.delayed(const Duration(milliseconds: 1),()=> tag.value = true);
          Future.delayed(const Duration(milliseconds: 80),()=> tag.value = false);
        },
        onTapDown: (v){
          if(tag.value) tag.value = false;
          Future.delayed(const Duration(milliseconds: 1),()=> tag.value = true);
        },
        onTapUp: (v)=> tag.value = false,
        onTapCancel: ()=> tag.value = false,
        child: Obx(()=> AnimatedScale(
          scale: tag.value? scale : 1,
          duration: const Duration(milliseconds: 150),
          curve: Curves.easeInOut,
          child: child
        )),
      ),
    );
  }
}
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';

import '../../business/text.dart';
import 'ontaps.dart';

class RadioBoxList extends StatelessWidget {
  const RadioBoxList({super.key, required this.children});

  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.w),
            // color: themeBase.primaryColor.value
          ),
          child: <PERSON>umn(
            children: children,
          ),
        ),
        SizedBox(
          height: 16.w,
        )
      ],
    );
  }
}

class RadioBox extends StatelessWidget {
  const RadioBox(
      {super.key,
      required this.name,
      this.imgUrl = '',
      required this.onTap,
      this.song = false,
      this.assetImage});

  final String name;
  final String imgUrl;
  final Function onTap;
  final bool song;
  final Widget? assetImage;

  @override
  Widget build(BuildContext context) {
    final RxBool playtion = false.obs;
    return OnTapScaleToSmallBox(
      onTap: () {
        onTap();
        playtion.value = true;
        final random = Random();
        int randomNumber = 400 + random.nextInt(401);
        Future.delayed(
            Duration(milliseconds: randomNumber), () => playtion.value = false);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.w),
        decoration: BoxDecoration(
            // color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8.r)),
        child: Row(
          children: [
            Container(
                height: 116.w,
                width: 116.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8.r),
                      bottomLeft: Radius.circular(8.r),
                    ),
                    color: Colors.black12),
                child: _getImage(context)),
            SizedBox(width: 12.w),
            Flexible(
              child: Container(
                  alignment: Alignment.centerLeft,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                          child: Container(
                        margin: EdgeInsets.only(right: 24.w),
                        child: SText(
                          text: name,
                          fontSize: STextSize.md,
                          color: Theme.of(context).colorScheme.secondary,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )),
                      song
                          ? Obx(() => Visibility(
                                visible: playtion.value && song,
                                child: Container(
                                    width: 58.w,
                                    height: 58.w,
                                    padding: EdgeInsets.all(18.w),
                                    child: CircularProgressIndicator(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      strokeWidth: 3.w,
                                    )),
                              ))
                          : Container(
                              margin: EdgeInsets.only(right: 16.w),
                              child: Transform.rotate(
                                angle: pi / 1,
                                child: Sim2Icon(
                                  path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
                                  size: 58.w,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .secondary
                                      .withAlpha((255 * 0.5).toInt()),
                                ),
                              ),
                            ),
                    ],
                  )),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getImage(BuildContext context) {
    final Widget body = Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(10.w),
        child: Sim2Icon(
          path: Assets.ASSETS_ICONS_MUSIC_ICON_SVG,
          size: 56.w,
          color: Theme.of(context).colorScheme.secondary,
        ));
    if (assetImage != null) {
      return ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.r),
            bottomLeft: Radius.circular(8.r),
          ),
          child: assetImage!);
    } else {
      if (imgUrl == '') {
        return body;
      } else {
        return SizedBox(
          height: 116.w,
          width: 116.w,
          child: ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.r),
                bottomLeft: Radius.circular(8.r),
              ),
              child: ExtendedImage.network(
                imgUrl,
                fit: BoxFit.cover,
                width: 116.w,
                handleLoadingProgress: true,
                loadStateChanged: (state) {
                  if (state.extendedImageLoadState == LoadState.failed) {
                    return body;
                  }
                  return null;
                },
                cache: true,
              )),
        );
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import '../../../../../configs/log.dart';
import '../../../../common/common_background.dart';
import '../../../../common/sim2_nav.dart';
import '../../../components/radio_box.dart';
import '../../../controller/aroha_controller.dart';

class ArohaGenresPage extends StatelessWidget {
  const ArohaGenresPage({super.key});

  @override
  Widget build(BuildContext context) {
    String title = 'Radio';
    if (Get.parameters['name'] != null) title = Get.parameters['name']!;

    return CommonBackground(
      appBar: Sim2AppBar(
        title: title,
      ),
      body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
          // alignment: Alignment.center,
          child: Obx(() => _getBox())),
    );
  }

  Widget _getBox() {
    if (arohaController.genres.isEmpty) {
      return SizedBox(
        width: Get.width,
        height: Get.height,
        child: CircularProgressIndicator(
          strokeWidth: 2.sp,
        ),
      );
    } else {
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp, bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arohaController.genres[index];
          log.logTrace(item);
          return RadioBox(
            name: item['name'],
            imgUrl: '${arohaController.imgBaseGenre}${item['logo']}',
            onTap: () async {
              arohaController.getStations('${item['id']}');
              Get.toNamed('/arohaRadio/arohaStations',
                  parameters: {'name': item['name']});
            },
          );
        },
        itemCount: arohaController.genres.length,
      );
    }
  }
}

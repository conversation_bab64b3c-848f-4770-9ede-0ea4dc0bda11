import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../../../configs/log.dart';
import '../../../../../routes/route_config.get_x_router_config.dart';
import '../../../../../services/device_play_service.dart';
import '../../../../common/common_background.dart';
import '../../../../common/sim2_nav.dart';
import '../../../components/radio_box.dart';
import '../../../controller/aroha_controller.dart';

class ArohaStationsPage extends StatelessWidget {
  const ArohaStationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    String title = 'Radio';
    if (Get.parameters['name'] != null) title = Get.parameters['name']!;

    return CommonBackground(
      appBar: Sim2AppBar(
        title: title,
      ),
      body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 0.sp),
          child: Obx(() => _getBox())),
    );
  }

  Widget _getBox() {
    if (arohaController.stations.isEmpty) {
      return SizedBox(
        width: Get.width,
        height: Get.height,
        child: CircularProgressIndicator(),
      );
    } else {
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp, bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arohaController.stations[index];
          return RadioBox(
            name: item['name'],
            imgUrl: '${arohaController.imgBaseStation}${item['logo']}',
            song: true,
            onTap: () async {
              log.logDebug(item);

              final String info =
                  await arohaController.getStationInfo(item['id']);
              if (info != '') {
                Map infoMap = json.decode(info);
                final url =
                    '${infoMap['stream_url']}::::${item['name']}::::Radio::::Music::::${arohaController.imgBaseStation}${item['logo']}';
                DevicePlayService.getSelectInstance().then((service) {
                  service?.playRadio(url);
                });
                await Future.delayed(const Duration(milliseconds: 200), () {});
                Get.toTyped(
                  PlayViewTypedRoute(
                    deviceIP: DevicePlayService.getSelectDeviceIP,
                  ),
                );
              }
            },
          );
        },
        itemCount: arohaController.stations.length,
      );
    }
  }
}

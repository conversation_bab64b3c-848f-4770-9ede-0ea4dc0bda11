import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import '../../../../configs/log.dart';
import '../../../common/common_background.dart';
import '../../../common/sim2_nav.dart';
import '../../components/radio_box.dart';
import '../../controller/aroha_controller.dart';

class ArohaRadioPage extends StatelessWidget {
  const ArohaRadioPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: Sim2AppBar(
        title: 'YouRadio',
      ),
      body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
          child: Obx(() => _getBox())),
    );
  }

  Widget _getBox() {
    if (arohaController.classList.isEmpty) {
      return SizedBox(
        width: Get.width,
        height: Get.height,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    } else {
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp, bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arohaController.classList[index];
          return RadioBox(
            name: item['name'],
            imgUrl: '${arohaController.imgBase}${item['logo']}',
            onTap: () async {
              log.logDebug(item);
              arohaController.getGenres('${item['id']}');
              Get.toNamed('/arohaRadio/arohaGenres',
                  parameters: {'name': item['name']});
            },
          );
        },
        itemCount: arohaController.classList.length,
      );
    }
  }
}

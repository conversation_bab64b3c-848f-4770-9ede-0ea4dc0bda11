import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';

import '../../../../configs/local.dart';
import '../../../business/text.dart';
import '../../../common/common_background.dart';
import '../../../common/sim2_nav.dart';
import '../../components/ontaps.dart';

// GMT utc/ETC
class SpotifyGetPage extends StatelessWidget {
  const SpotifyGetPage({super.key});

  @override
  Widget build(BuildContext context) {
    final spotifyGetCon = Get.put(SpotifyGetCon());

    return CommonBackground(
      appBar: Sim2AppBar(
        title: '',
      ),
      body: Container(
        alignment: Alignment.center,
        child: SingleChildScrollView(
          physics: NeverScrollableScrollPhysics(),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                  bottom: 200.w,
                ),
                width: 650.w,
                child: Sim2Icon(
                  path: Assets.ASSETS_ICONS_FULL_LOGO_GREEN_PMS_U_SVG,
                  size: 200.w,
                ),
              ),
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                      bottom: 20.w
                    ),
                    child: SText(
                      text: Local.radioPage.rads1,
                      fontFamily: STextFamily.bold,
                      color: const Color.fromRGBO(67, 85, 105, 1),
                      fontSize: STextSize.xl,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(vertical: 20.w),
                    child: Column(
                      children: [
                        SizedBox(
                          child: SText(
                            text: Local.radioPage.rads2,
                            color: const Color.fromRGBO(67, 85, 105, .7),
                            fontSize:STextSize.mds,
                          ),
                        ),
                        SizedBox(
                          child: SText(
                            text: Local.radioPage.rads3,
                            fontFamily: STextFamily.bold,
                            color: const Color.fromRGBO(67, 85, 105, .5),
                            fontSize: STextSize.mds,
                          ),
                        )
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      if (await canLaunchUrl(
                          Uri.parse('https://connect.spotify.com/'))) {
                        await launchUrl(
                            Uri.parse('https://connect.spotify.com/'));
                      }
                    },
                    child: Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                        top: 20.w
                      ),
                      child: SText(
                        text: Local.radioPage.rads4,
                        color: const Color.fromRGBO(21, 201, 127, 1),
                        fontSize: STextSize.md,
                        fontFamily: STextFamily.bold,
                      ),
                    ),
                  ),
                ],
              ),
              OnTapScaleToSmallBox(
                onTap: () async {
                  if (spotifyGetCon.iHave.value) {
                    await launchUrl(Uri.parse('spotify://'));
                  } else {
                    if (GetPlatform.isIOS) {
                      await launchUrl(Uri.parse(
                          'https://itunes.apple.com/cn/app/id324684580'));
                    } else {
                      await launchUrl(Uri.parse(
                          'https://play.google.com/store/apps/details?id=com.spotify.music'));
                    }
                  }
                },
                child: Container(
                  margin: EdgeInsets.only(top: 150.w, bottom: 80.w),
                  width: Get.width * 0.75,
                  height: 120.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(21, 201, 127, 1),
                    borderRadius: BorderRadius.circular(120.w)
                  ),
                  child: Obx(() => SText(
                    text: spotifyGetCon.iHave.value ? Local.radioPage.rads5 : Local.radioPage.rads6,
                    color:  Colors.white,
                    fontFamily: STextFamily.bold,
                    fontSize:STextSize.md,
                  )),
                ),
              ),
            ],
          ),
        ),
        // child: Padding(
        //   padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
        //   child: SingleChildScrollView(
        //       physics: NeverScrollableScrollPhysics(),
        //       child: Scrollbar(
        //         thumbVisibility: false,
        //         child: Container(
        //           // height: Get.height,
        //           color: Colors.red,
        //           child: ,
        //         ),
        //       )),
        // ),
      )
    );
  }
}

class SpotifyGetCon extends GetxController {
  final RxBool iHave = false.obs;
  final RxBool tidalIHave = false.obs;
}

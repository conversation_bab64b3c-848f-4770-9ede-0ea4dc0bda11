import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';



import '../../../common/common_background.dart';
import '../../components/radio_box.dart';
import '../../controller/arylic_controller.dart';


class ArylicRadioPage extends StatelessWidget {
  const ArylicRadioPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: AppBar(
        title: Text('Arylic Radio'),
        centerTitle: true,
      ),
      body: Container(
          height: Get.height ,
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: Column(
            children: [
              RadioBox(
                imgUrl: '',
                name: 'Pubilc Radio',
                onTap: (){
                  arylicRadioMusic.getSummaryCode();
                  Get.toNamed('/arylicRadio/public');
                },
              ),
              RadioBox(
                imgUrl: '',
                name: 'My Station',
                onTap: (){},
              ),
            ],
          ),
        ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import '../../../../common/common_background.dart';
import '../../../../common/sim2_nav.dart';
import '../../../components/radio_box.dart';
import '../../../controller/arylic_controller.dart';

class ArylicPublicRadio extends StatelessWidget {
  const ArylicPublicRadio({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
        appBar: Sim2AppBar(
          title: 'Public Radio',
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
          child: <PERSON>umn(
            children: [
              RadioBox(
                  name: 'All Station',
                  imgUrl: '',
                  onTap: () async {
                    arylicRadioMusic.getAllStation();
                    Get.toNamed('/arylicRadio/public/allStation');
                  }),
              RadioBox(
                  name: '<PERSON><PERSON>',
                  imgUrl: '',
                  onTap: () {
                    Get.toNamed('/arylicRadio/public/genre');
                  }),
              RadioBox(
                  name: 'Language',
                  imgUrl: '',
                  onTap: () {
                    Get.toNamed('/arylicRadio/public/language');
                  }),
              RadioBox(
                  name: 'Country',
                  imgUrl: '',
                  onTap: () {
                    Get.toNamed('/arylicRadio/public/country');
                  }),
            ],
          ),
        ));
  }
}

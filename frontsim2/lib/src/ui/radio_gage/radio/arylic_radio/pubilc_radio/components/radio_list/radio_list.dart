import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../../../../../routes/route_config.get_x_router_config.dart';
import '../../../../../../../services/device_play_service.dart';
import '../../../../../../common/common_background.dart';
import '../../../../../../common/sim2_nav.dart';
import '../../../../../components/radio_box.dart';
import '../../../../../controller/arylic_controller.dart';

class ArylicRadioFlexList extends StatelessWidget {
  const ArylicRadioFlexList({super.key});

  @override
  Widget build(BuildContext context) {
    String title = 'Radio';
    if (Get.parameters['name'] != null) title = Get.parameters['name']!;

    return CommonBackground(
      appBar: Sim2AppBar(
        title: title,
      ),
      body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
          child: Obx(() => _getBox())),
    );
  }

  // 判断是否等于空
  Widget _getBox() {
    if (arylicRadioMusic.flexList.isEmpty) {
      return SizedBox(
        width: Get.width,
        height: Get.height,
        child: Center(
          child: CircularProgressIndicator(
            strokeWidth: 2.sp,
          ),
        ),
      );
    } else {
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp, bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arylicRadioMusic.flexList[index];
          return RadioBox(
            name: item['title'],
            imgUrl: item['image_url'].replaceAll(RegExp(r'\s+'), ''),
            song: true,
            onTap: () async {
              final url =
                  '${item['play_url']}::::${item['title']}::::Radio::::Music${item['id']}::::${item['image_url']}';
              DevicePlayService.getSelectInstance().then((service) {
                service?.playRadio(url);
              });
              await Future.delayed(const Duration(milliseconds: 200), () {});
              Get.toTyped(
                PlayViewTypedRoute(
                  deviceIP: DevicePlayService.getSelectDeviceIP,
                ),
              );
            },
          );
        },
        itemCount: arylicRadioMusic.flexList.length,
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';


import '../../../../../../common/common_background.dart';
import '../../../../../../common/sim2_nav.dart';
import '../../../../../components/radio_box.dart';
import '../../../../../controller/arylic_controller.dart';

class ArylicRadioCountry extends StatelessWidget {
  const ArylicRadioCountry({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: Sim2AppBar(title: 'Country'),
      body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
          child: Obx(() => _getBox())),
    );
  }

  Widget _getBox() {
    if (arylicRadioMusic.genresList.isEmpty) {
      return SizedBox(
        width: Get.width,
        height: Get.height,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    } else {
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp, bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arylicRadioMusic.countryList[index];
          return RadioBox(
            name: item['name'],
            imgUrl: '',
            onTap: () async {
              arylicRadioMusic.flexList.clear();
              arylicRadioMusic.getQuery(country: item['id']).then((relist) {
                arylicRadioMusic.flexList.value = relist;
              });
              Get.toNamed('/flexList', parameters: {'name': item['name']});
            },
          );
        },
        itemCount: arylicRadioMusic.countryList.length,
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:rk_toast/rk_toast.dart';

import '../../components/radio_box.dart';
import '../../controller/upnp_radio_controller.dart';

class UpnpRadioPage extends StatelessWidget {
  const UpnpRadioPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('UPNP Radio'),
          centerTitle: true,
        ),
        body: Container(
            height: Get.height,
            padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
            child: Obx(() => ListView.builder(
                  padding: EdgeInsets.all(0.sp),
                  itemBuilder: ((context, index) {
                    NASservice item = nasRadio.services[index];
                    return RadioBox(
                      imgUrl: '',
                      name: item.name,
                      onTap: () async {
                        nasRadio.mllsA.value = await item.getContent();
                        nasRadio.selectService = item;
                        if (nasRadio.mllsA.isEmpty) {
                          ToastUtils.showToast(msg: 'The directory is empty!');
                          return;
                        }
                        Get.toNamed('/upnpRadio/upnpRadioA',
                            parameters: {'name': item.name});
                      },
                    );
                  }),
                  itemCount: nasRadio.services.length,
                ))));
  }
}

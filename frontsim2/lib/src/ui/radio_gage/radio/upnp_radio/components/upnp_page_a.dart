import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../../../configs/log.dart';
import '../../../../../models/audio_source_model.dart';
import '../../../../../routes/route_config.get_x_router_config.dart';
import '../../../../../services/device_play_service.dart';
import '../../../components/radio_box.dart';
import '../../../controller/upnp_radio_controller.dart';

class UpnpPageA extends StatelessWidget {
  const UpnpPageA({super.key});

  @override
  Widget build(BuildContext context) {
    final title = Get.parameters['title'] ?? 'Radio';

    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) {
            nasRadio.handleBack();
          }
        },
        child: Scaffold(
          appBar: AppBar(title: Text(title), actions: [
            if (nasRadio.showSort.value)
              GestureDetector(
                onTap: () {
                  nasRadio.handleSort();
                },
                child: Container(
                  // height: themeBase.topBarHeight,
                  // width: themeBase.topBarHeight,
                  color: Colors.transparent,
                  child: Sim2Icon(
                    path: Assets.ASSETS_CERT_CLIENT_KEY,
                    // color: themeBase.primaryColor.value,
                    size: 25.sp,
                  ),
                ),
              )
          ]),
          body: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.sp),
              alignment: Alignment.center,
              child: Obx(() => _getBox())),
        ));
  }

  Widget _getBox() {
    if (nasRadio.mllsA.isEmpty) {
      return SizedBox(
        width: Get.width,
        height: Get.height,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.only(top: 12.sp),
        child: ListView.builder(
          padding: EdgeInsets.only(top: 0.sp, bottom: 12.sp),
          itemBuilder: (context, index) {
            final SubContent item = nasRadio.mllsA[index];
            return RadioBox(
              name: item.title,
              song: item.type == 'musicTrack',
              imgUrl: item.imageUrl,
              onTap: () async {
                if (item.type == 'musicTrack') {
                  final List<SubContent> tracks = nasRadio.mllsA.sublist(index);
                  DevicePlayService.getSelectInstance().then((service) {
                    service?.playList(tracks
                        .map((e) => AudioSourceModel(
                              track: e.title,
                              url: e.playUrl,
                            ))
                        .toList());
                  });
                  await Future.delayed(
                      const Duration(milliseconds: 200), () {});
                  Get.toTyped(
                    PlayViewTypedRoute(
                      deviceIP: DevicePlayService.getSelectDeviceIP,
                    ),
                  );
                  return;
                }

                assert(nasRadio.selectService != null);
                final List<SubContent> list =
                    await nasRadio.selectService!.getContent(objectId: item.id);

                for (var el in list) {
                  log.logDebug(el.type);
                  if (el.type == 'musicTrack') {
                    nasRadio.showSort.value = true;
                    break; // 发现是musicTrack，直接终止循环
                  } else {
                    nasRadio.showSort.value = false;
                  }
                }

                if (list.isEmpty) {
                  nasRadio.mllsAisEmpty.value = true;
                  ToastUtils.showToast(msg: 'The directory is empty!');
                  return;
                } else {
                  nasRadio.mllsAisEmpty.value = false;
                }

                log.logDebug('msg === ${nasRadio.mllsAisEmpty.value}');
                nasRadio.navigateToDirectory(item);
              },
            );
          },
          itemCount: nasRadio.mllsA.length,
        ),
      );
    }
  }
}

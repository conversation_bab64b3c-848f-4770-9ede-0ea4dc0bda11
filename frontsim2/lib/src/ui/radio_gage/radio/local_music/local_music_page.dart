import 'package:flutter/material.dart';

import 'package:on_audio_query/on_audio_query.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/common_background.dart';

import '../../../../configs/local.dart';
import '../../../common/sim2_nav.dart';
import '../../components/radio_box.dart';
import '../../controller/local_music_con.dart';

class LocalMusicPage extends StatelessWidget {
  const LocalMusicPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
        appBar: Sim2AppBar(
          title: Local.radioPage.local_music,
        ),
        body: Container(
            height: Get.height,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
            child: Obx(() => ListView.builder(
                  padding: EdgeInsets.all(0.w),
                  itemBuilder: ((context, index) {
                    SongModel item = localMusicCon.audios[index];
                    return RadioBox(
                      song: true,
                      imgUrl: '',
                      name: '${index + 1}. ${item.title}',
                      onTap: () {
                        localMusicCon.playRadio(item);
                      },
                    );
                  }),
                  itemCount: localMusicCon.audios.length,
                ))));
  }
}

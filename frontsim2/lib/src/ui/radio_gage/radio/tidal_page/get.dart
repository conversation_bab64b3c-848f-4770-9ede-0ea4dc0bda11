import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/common_background.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';

import '../../../../configs/local.dart';
import '../../../business/text.dart';
import '../../../common/sim2_nav.dart';
import '../../components/ontaps.dart';
import '../spotify_page/get.dart';

class TidalGetPage extends StatelessWidget {
  const TidalGetPage({super.key});

  @override
  Widget build(BuildContext context) {
    final spotifyGetCon = Get.put(SpotifyGetCon());

    return CommonBackground(
        appBar: Sim2AppBar(
          title: '',
        ),
        padding: EdgeInsets.zero,
        body: Container(
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  height: 176.w,
                ),
                Sim2Icon(
                  path: Assets.ASSETS_ICONS_CONNECT_VERTICAL_DARK_SVG,
                  size: 200.w,
                ),
                SizedBox(
                  height: 132.w,
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: SText(
                    text: Local.radioPage.rad1,
                    fontFamily: STextFamily.bold,
                    color: const Color.fromRGBO(67, 85, 105, 1),
                    fontSize: STextSize.md,
                  ),
                ),
                SizedBox(
                  height: 16.w,
                ),
                Container(
                  margin: EdgeInsets.symmetric(vertical: 16.w),
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: SText(
                    text: Local.radioPage.rad2,
                    softWrap: true,
                    color: const Color.fromRGBO(67, 85, 105, .7),
                    fontSize: STextSize.mds,
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: 64.w,
                ),
                OnTapScaleToSmallBox(
                    onTap: () async {
                      await launchUrl(Uri.parse('https://tidal.com/connect'));
                    },
                    child: Container(
                      margin: EdgeInsets.symmetric(vertical: 12.w),
                      child: SText(
                        text: Local.radioPage.rad3,
                        color: const Color.fromARGB(255, 5, 48, 30),
                        fontSize: STextSize.mds,
                        fontFamily: STextFamily.bold,
                      ),
                    )),
                SizedBox(
                  height: 64.w,
                ),
                OnTapScaleToSmallBox(
                  onTap: () async {
                    if (spotifyGetCon.tidalIHave.value) {
                      await launchUrl(Uri.parse('tidal://'));
                    } else {
                      if (GetPlatform.isAndroid) {
                        if (await canLaunchUrl(Uri.parse(
                            'https://play.google.com/store/apps/details?id=com.aspiro.tidal'))) {
                          await launchUrl(Uri.parse(
                              'https://play.google.com/store/apps/details?id=com.aspiro.tidal'));
                        }
                        return;
                      }
                      if (GetPlatform.isIOS) {
                        if (await canLaunchUrl(Uri.parse(
                            'https://apps.apple.com/us/app/tidal-music-hifi-sound/id913943275'))) {
                          await launchUrl(Uri.parse(
                              'https://apps.apple.com/us/app/tidal-music-hifi-sound/id913943275'));
                        }
                        return;
                      }
        
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 24.w),
                    width: Get.width * 0.75,
                    height: 120.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: const Color.fromRGBO(67, 85, 105, 1),
                        borderRadius: BorderRadius.circular(48.w)),
                    child: Obx(() => SText(
                          text: spotifyGetCon.tidalIHave.value
                              ? Local.radioPage.rad4
                              : Local.radioPage.rad5,
                          color: Colors.white,
                        )),
                  ),
                ),
              ],
            )));
  }
}

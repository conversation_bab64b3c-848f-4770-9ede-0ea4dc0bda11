import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import '../../configs/kv_store.dart';
import '../../configs/local.dart';
import '../business/text.dart';
import 'components/ontaps.dart';
import 'controller/arylic_controller.dart';
import 'routes.dart';

// 隐私政策
class PrivacyPolicy extends StatelessWidget {
  const PrivacyPolicy({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: SText(
          text: Local.radioPage.privacy_policy,
          fontSize: STextSize.xl,
          // color: themeBase.textColor1.value,
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // 内容
          Expanded(
            flex: 4,
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: 18.w,
                // vertical: 12.sp,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SText(
                    text: Local.radioPage.service_content,
                    fontSize: STextSize.mdlz,
                    // color: themeBase.primaryColor.value,
                    softWrap: true,
                    fontFamily: STextFamily.bold,
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  SText(
                    text: Local.radioPage.service_content_text,
                    fontSize: STextSize.md,
                    // color: themeBase.primaryColor.value.withAlpha((255 * 0.8).round()),
                    letterSpacing: 0.4,
                    softWrap: true,
                  ),
                  SizedBox(
                    height: 30.w,
                  ),
                  SText(
                    text: Local.radioPage.rights,
                    fontSize: STextSize.xl,
                    // color: themeBase.primaryColor.value,
                    softWrap: true,
                    fontFamily: STextFamily.bold,
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  SText(
                    text: Local.radioPage.rights_text,
                    fontSize: STextSize.md,
                    // color: themeBase.primaryColor.value.withAlpha((255 * 0.8).round()),
                    softWrap: true,
                    letterSpacing: 0.4,
                  ),
                  SizedBox(
                    height: 30.w,
                  ),
                  SText(
                    text: Local.radioPage.privacy_notice,
                    fontSize: STextSize.xl,
                    // color: themeBase.primaryColor.value,
                    softWrap: true,
                    fontFamily: STextFamily.bold,
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  SText(
                    text: Local.radioPage.privacy_notice_text,
                    fontSize: STextSize.md,
                    // color: themeBase.primaryColor.value.withAlpha((255 * 0.8).round()),
                    softWrap: true,
                    letterSpacing: 0.4,
                  ),
                  SizedBox(height: 100.w)
                ],
              ),
            ),
          ),
          // 按钮
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 22.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: OnTapScaleToSmallBox(
                    onTap: () {
                      // 取消的操作
                      Get.back();
                    },
                    child: Container(
                      height: 100.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          // color: themeBase.buttonColor2.value,
                          borderRadius: BorderRadius.circular(10.r)),
                      child: SText(
                        text: Local.common.cancel,
                        fontSize: STextSize.md,
                        // color: themeBase.textColor1.value,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 14.w,
                ),
                Flexible(
                  child: OnTapScaleToSmallBox(
                    onTap: () {
                      // 确认的操作
                      kvStore.setBool('ArylicRadioPrivacy', true);
                      arylicRadioMusic.getSummaryCode();
                      Get.offNamedUntil('/arylicRadio/public', ModalRoute.withName(RadioPageRoutes.radioPage));
                    },
                    child: Container(
                      height: 100.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          // color: themeBase.buttonColor1.value,
                          borderRadius: BorderRadius.circular(10.r)),
                      child: SText(
                        text: Local.common.confirm,
                        fontSize: STextSize.md,
                        // color: themeBase.textColor1.value,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

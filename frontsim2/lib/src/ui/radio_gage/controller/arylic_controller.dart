import 'dart:convert';
import 'package:get/get.dart';

import 'package:http/http.dart' as http;

import '../../../configs/log.dart';

ArylicRadioMusic arylicRadioMusic = Get.put(ArylicRadioMusic());

// aylic radio 方法
class ArylicRadioMusic extends GetxController {
  String url = 'https://radio.arylic.com/';

  final RxList genresCode = RxList([]);
  final RxList<Map> genresList = RxList([]);

  final RxList languageCode = RxList([]);
  final RxList<Map> languageList = RxList([]);

  final RxList countryCode = RxList([]);
  final RxList<Map> countryList = RxList([]);

  final RxList<Map> allStation = RxList<Map>([]);
  final RxList<Map> flexList = RxList<Map>([]);

  // post方法
  Future<String> postRequst(String methods, {Map? dat}) async {
    try {
      final response = await http.post(
        Uri.parse('$url$methods'),
        headers: {
          'Content-type': 'application/json',
        },
        body: dat != null ? json.encode(dat) : null,
      );
      if (response.statusCode == 200) {
        return response.body;
      } else {
        return Future.error('${response.statusCode}');
      }
    } catch (e) {
      return Future.error(e.toString());
    }
  }

  // get方法
  Future<String> getRequst(String methods) async {
    try {
      final response = await http.get(Uri.parse('$url$methods'));
      if (response.statusCode == 200) {
        return response.body;
      } else {
        return Future.error('${response.statusCode}');
      }
    } catch (e) {
      return Future.error(e);
    }
  }

  // 获取Radio
  Future<List<Map>> getQuery(
      {int count = 9999,
      String genre = '',
      String language = '',
      String country = ''}) async {
    Map dat = {
      'count': count,
      'genre': genre,
      'language': language,
      'country': country
    };
    List<Map> lt = [];
    await postRequst('station/query', dat: dat).then((val) {
      Map res = json.decode(val);
      if (res['status'] == 0) {
        lt = List<Map>.from(res['stations']);
      } else {
        return Future.error('错误请求');
      }
      // return lt = value;
    }).catchError((e) {
      return e;
    });
    // return lt;
    if (lt.isNotEmpty) {
      return lt;
    } else {
      // 此处请求失败
      return Future.error('无');
    }
  }

  // 获取genres，language，country
  void getSummaryCode() async {
    List<Future<String>> futures = [
      getRequst('genre'),
      getRequst('language'),
      getRequst('country'),
    ];

    // 等待所有并发请求完成
    try {
      List<String> responses = await Future.wait(futures);
      //
      for (String item in responses) {
        Map map = json.decode(item);
        if (map.containsKey('genres')) {
          genresCode.value = map['genres'];
          // Log.d(genresCode);
        }
        if (map.containsKey('language')) {
          languageCode.value = map['language'];
          log.logInformation(languageCode);
        }
        if (map.containsKey('country')) {
          countryCode.value = map['country'];
        }
      }
      getSummary();
    } catch (e) {
      log.logError(e);
    }
  }

  // 获取Summary
  void getSummary() {
    postRequst('station/summary').then((value) {
      try {
        Map res = json.decode(value);
        log.logDebug(res);
        if (res.containsKey('genres')) {
          for (String id in res['genres']) {
            Map<String, dynamic>? item = genresCode.firstWhere(
                (element) => '${element["id"]}' == id,
                orElse: () => {});
            if (item != null) {
              genresList.add({'name': item["name"], 'id': id});
            }
          }
          log.logInformation(genresList);
        }
        if (res.containsKey('languages')) {
          for (String code in res['languages']) {
            Map<String, dynamic>? item = languageCode.firstWhere(
                (element) => '${element["code"]}' == code,
                orElse: () => {});
            if (item != null) {
              languageList.add({'name': item["name"], 'id': code});
            }
          }
          log.logInformation(languageList);
        }
        if (res.containsKey('countries')) {
          for (String code in res['countries']) {
            Map<String, dynamic>? item = countryCode.firstWhere(
                (element) => '${element["code"]}' == code,
                orElse: () => {});
            if (item != null) {
              countryList.add({'name': item["name"], 'id': code});
            }
          }
          log.logInformation(countryList);
        }
      } catch (e) {
        log.logError('获取消息失败 $e');
      }
    }).catchError((e) {
      log.logError('e');
    });
  }

  // 获取全部歌曲
  void getAllStation() async {
    await arylicRadioMusic.getQuery().then((value) {
      allStation.value = value;
    }).catchError((e) {
      log.logError('$e');
    });
  }

  // 获取当前歌曲信息
  Future<String> getinfo(String id) async {
    try {
      final String item = await postRequst('station/info', dat: {'sid': id});
      return item;
    } catch (e) {
      return '';
    }
  }

  void playRadio(Map data) {
    // try{
    //   device.requestPost(
    //     url: CreateQueue.url(device.ip),
    //     hsa: CreateQueue.hsa,
    //     dat: CreateQueue.dat(
    //       CreateQueue.queueContext(data,'Arylic Radio')
    //     )
    //   ).then((value) async {
    //     Log.d(value);
    //     device.playQueue(CreateQueue.queueName);
    //     await Future.delayed(const Duration(milliseconds: 100),(){});
    //     Get.toNamed('/player_page');
    //   });
    // }catch(e){
    //   Log.e('失败了');
    // }
  }
}

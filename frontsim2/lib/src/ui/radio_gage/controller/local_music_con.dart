import 'dart:io';

import 'package:get_settings/getsettings.dart';
import 'package:on_audio_query/on_audio_query.dart';
import 'package:rk_get/rk_get.dart';
import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../configs/log.dart';
import '../../../routes/route_config.get_x_router_config.dart';
import '../../../servers/local_audio_file_server.dart';
import '../../../services/device_play_service.dart';

final LocalMusicCon localMusicCon = () {
  if (Get.isRegistered<LocalMusicCon>()) {
    return Get.find<LocalMusicCon>();
  } else {
    return Get.put(LocalMusicCon());
  }
}();

class LocalMusicCon extends GetxController {
  final OnAudioQuery audioQuery = OnAudioQuery();

  final audios = RxList<SongModel>([]);

  void init() async {
    try {
      //获取当前安卓版本
      if (Platform.isAndroid) {
        final info = await DeviceInfoPlugin().androidInfo;
        if (info.version.sdkInt >= 33) {
          // Android 13 以上申请radio,video, external storage权限
          {final result = await Permission.audio.request();
          if (result.isDenied) {
            ToastUtils.showToast(msg: 'File access permission is required!');
            return;
          }
          }{
          final result = await Permission.videos.request();
          if (result.isDenied) {
            ToastUtils.showToast(msg: 'File access permission is required!');
            return;
          }
          }

        }else{
          final result= await Permission.storage.request();
          if (result.isDenied) {
            ToastUtils.showToast(msg: 'File access permission is required!');
            return;
          }
        }
      }
      if (Platform.isIOS || Platform.isMacOS) {
        final result = await Permission.mediaLibrary.request();
        if (result.isDenied) {
          ToastUtils.showToast(msg: 'File access permission is required!');
          return;
        }    
      }


      audios.value = await audioQuery.querySongs();
    } catch (e, stack) {
      log.logError('LocalMusicCon init failed', e, stack);
    }
  }

  void playRadio(SongModel data) {
    log.logError('播放 ${data.data}');
    //先跳路由
    Get.toTyped(
      PlayViewTypedRoute(
        deviceIP: DevicePlayService.getSelectDeviceIP,
      ),
    );
    Future.sync(() async {
      String filePath = data.data;

      if (Platform.isIOS || Platform.isMacOS) {
        final setting = GetSettings();
        filePath = await setting.ipodToPath(data.data);
      }
      final localFile = await Get.findAsync<LocalAudioSourcesServer>();
      final id = localFile.addAudioFile(filePath: filePath);

      final component =
          '::::${data.title}::::${(data.artist == '' || data.artist == '<unknown>' || data.artist == null ? 'Artist' : data.artist)}::::Music';

      DevicePlayService.getSelectInstance().then((value) {
        if (value == null) {
          log.logError('DevicePlayService is null');
          return;
        }
        value.playRadio('${localFile.getAudioFileUrl(id)}${Uri.encodeComponent(component)}');
      });
    });
  }
}

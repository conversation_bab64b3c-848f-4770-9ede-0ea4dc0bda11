import 'dart:convert';
import 'package:get/get.dart';

import 'package:http/http.dart' as http;
import 'package:html/parser.dart' show parseFragment;

import '../../../configs/log.dart';

final ArohaController arohaController = Get.put(ArohaController());

class ArohaController extends GetxController {

  static String url = 'https://manager.uber.radio/api/public';
  String imgBase = 'https://manager.uber.radio/static/uploads/brand/';
  String imgBaseGenre = 'https://manager.uber.radio/static/uploads/genre/';
  String imgBaseStation = 'https://manager.uber.radio/static/uploads/station/';

  final RxList classList = RxList([]);
  final RxList genres = RxList([]);
  final RxList stations = RxList([]);

  // post方法
  Future<String> postRequst(String methods,{Map? dat}) async {
    try{
      final response = await http.post(
        Uri.parse('$url$methods'),
        headers: {'Content-type': 'application/json'},
        body: dat != null? json.encode(dat) : null,
      );
      if (response.statusCode == 200) {
        return response.body;
      }else{
        return Future.error('${response.statusCode}');
      }
    }catch(e){
      return Future.error(e.toString());
    }
  }

  // get方法
  Future<String> getRequst(String methods) async {
    try{
      final response = await http.get(Uri.parse('$url$methods'));
      if (response.statusCode == 200) {
        String str = response.body;
        str = str.replaceAll('\\r', '');
        str = str.replaceAll('\\n', '');
        str = str.replaceAll(RegExp(r'<[^>]+>'), '');
        str = str.replaceAll('&quot;', "'");
        str = decodeHtmlEntities(str);
        return str;
      }else{
        return Future.error('${response.statusCode}');
      }
    }catch(e){
      return Future.error(e);
    }
  }

  // 解析html标签
  String decodeHtmlEntities(String text) {
    String? decodedText = parseFragment(text).text;
    return decodedText??'';
  }
    
  // 获取所有的歌曲
  void getAllBrand() async {
    getRequst('/brand').then((data){
      classList.value = json.decode(data);
    }).catchError((e){
      log.logDebug(e);
    });
  }

  Future<String> getStationInfo(int id) async {
    try {
      final response = await getRequst('/station/$id');
      return response;
    } catch(e) {
      return '';
    }
  }

  void getGenres(String id){
    genres.clear();
    try{
      getRequst('/brand/genres/$id').then((data){
        genres.value = json.decode(data);
        // Log.d(genres);
      }).catchError((e){
         log.logDebug(e);
      });
    }catch(e){
       log.logError(e);
    }
  }

   void getStations(String id){
    stations.clear();
    try{
      getRequst('/station/genre/$id').then((data) {
        stations.value = json.decode(data)[0]['stations'];
        // Log.d(stations);
      }).catchError((e){
        log.logDebug(e);
      });
    }catch(e){
      log.logError(e);
    }
  }

  void playRadio(Map data) {
    // Map playData = {
    //   'title': data['name'],
    //   'image_url': '$imgBaseStation${data['logo']}',
    //   'user_name': data['brand']['name'],
    //   'play_url': data['stream_url']
    // };
    // try{
    //   device.requestPost(
    //     url: CreateQueue.url(device.ip), 
    //     hsa: CreateQueue.hsa,
    //     dat: CreateQueue.dat(
    //       CreateQueue.queueContext(playData,'Aroha Radio')
    //     )
    //   ).then((value) async {
    //     Log.d(value);
    //     device.playQueue(CreateQueue.queueName);
    //     await Future.delayed(const Duration(milliseconds: 100),(){});
    //     Get.toNamed('/player_page');
    //   });
    // }catch(e){
    //   Log.e('失败了');  
    // }
  }

}




import 'package:flutter/widgets.dart';
import 'package:rk_package/rk_package.dart';

import 'privacy_policy.dart';
import 'radio/aroha_radio/aroha.dart';
import 'radio/aroha_radio/genres/genres.dart';
import 'radio/aroha_radio/stations/stations.dart';
import 'radio/arylic_radio/arylic_radio.dart';
import 'radio/arylic_radio/pubilc_radio/arylic_public_radio.dart';
import 'radio/arylic_radio/pubilc_radio/components/all_station.dart';
import 'radio/arylic_radio/pubilc_radio/components/country/country.dart';
import 'radio/arylic_radio/pubilc_radio/components/genre/genre.dart';
import 'radio/arylic_radio/pubilc_radio/components/languaged/language.dart';
import 'radio/local_music/local_music_page.dart';
import 'radio/spotify_page/get.dart';
import 'radio/tidal_page/get.dart';
import 'radio/upnp_radio/components/upnp_page_a.dart';
import 'radio/upnp_radio/upnp_radio.dart';
import 'radio_page.dart';

class RadioPageRoutes {
  static const String arohaRadio = '/arohaRadio';
  static const String arohaGenres = '/arohaGenres';
  static const String arohaStations = '/arohaStations';
  static const String upnpRadio = '/upnpRadio';
  static const String upnpRadioA = '/upnpRadioA';
  static const String upnpRadioB = '/upnpRadioB';
  static const String audioPage = '/audioPage';
  static const String spotifyGet = '/spotifyGet';
  static const String tidalGet = '/tidalGet';
  static const String privacyPolicy = '/privacyPolicy';
  static const String radioPage = '/radioPage';
    static const String localMusicPage = '/localMusicPage';

  static final radioPages = [
      pageRoute(
      routeName: localMusicPage,
      page: const LocalMusicPage(),
    ),
     pageRoute(
      routeName: radioPage,
      page: const RadioPage(),
    ),
     pageRoute(
      routeName: privacyPolicy,
      page: const PrivacyPolicy(),
    ),
     pageRoute(
      routeName: spotifyGet,
      page: const SpotifyGetPage(),
    ),
    pageRoute(
      routeName: tidalGet,
      page: const TidalGetPage(),
    ),
    pageRoute(
        routeName: arohaRadio,
        page: const ArohaRadioPage(),
        childRoutes: [
          pageRoute(
            routeName: arohaGenres,
            page: const ArohaGenresPage(),
          ),
          pageRoute(
            routeName: arohaStations,
            page: const ArohaStationsPage(),
          ),
        ]),
    pageRoute(routeName: upnpRadio, page: const UpnpRadioPage(), childRoutes: [
      pageRoute(
        routeName: upnpRadioA,
        page: const UpnpPageA(),
      ),
    ]),
    ...ArylicRoutes.pages,
  ];

  static GetPage pageRoute({
    required String routeName,
    required Widget page,
    List<GetPage>? childRoutes,
  }) {
    return GetPage(
      name: routeName,
      page: () => page,
      children: childRoutes ?? [],
    );
  }
}

class ArylicRoutes {
  static const String arylicRadio = '/arylicRadio';
  static const String public = '/public';
  static const String allStation = '/allStation';
  static const String myStation = '/myStation';
  static const String genre = '/genre';
  static const String language = '/language';
  static const String country = '/country';

  static final List<GetPage> pages = [
    GetPage(
        name: arylicRadio,
        page: () => const ArylicRadioPage(),
        transition: Transition.rightToLeft,
        transitionDuration: const Duration(milliseconds: 170),
        curve: Curves.easeInOut,
        children: [
          GetPage(
              name: public,
              page: () => const ArylicPublicRadio(),
              transition: Transition.rightToLeft,
              transitionDuration: const Duration(milliseconds: 170),
              curve: Curves.easeInOut,
              children: [
                GetPage(
                  name: allStation,
                  page: () => const ArylicPublicAllStation(),
                  transition: Transition.rightToLeft,
                  transitionDuration: const Duration(milliseconds: 170),
                  curve: Curves.easeInOut,
                ),
                GetPage(
                  name: genre,
                  page: () => const ArylicRadioGenre(),
                  transition: Transition.rightToLeft,
                  transitionDuration: const Duration(milliseconds: 170),
                  curve: Curves.easeInOut,
                ),
                GetPage(
                  name: language,
                  page: () => const ArylicRadioLanguage(),
                  transition: Transition.rightToLeft,
                  transitionDuration: const Duration(milliseconds: 170),
                  curve: Curves.easeInOut,
                ),
                GetPage(
                  name: country,
                  page: () => const ArylicRadioCountry(),
                  transition: Transition.rightToLeft,
                  transitionDuration: const Duration(milliseconds: 170),
                  curve: Curves.easeInOut,
                ),
              ]),
        ]),
  ];
}

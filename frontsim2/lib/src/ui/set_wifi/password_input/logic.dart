import 'package:base_common/base_common.dart';
import 'package:flutter/material.dart';
import 'package:rk_get/rk_get.dart';
import 'package:rk_toast/rk_toast.dart';

import '../../../configs/local.dart';
import 'state.dart';

class PasswordInputLogic extends GetxController with PasswordInputState {
  @override
  void onInit() {
    super.onInit();
    passwordCallback = Get.arguments['passwordCallback'] as OutValueCallback<String>;
  }

  @override
  void onClose() {
    passwordController.dispose();
    super.onClose();
  }

  void onSubmit() {
    if (passwordController.text.isNotEmpty) {
      Get.back();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        passwordCallback(passwordController.text);
      });
    } else {
      ToastUtils.showToast(msg: Local.setWifi.password_cannot_empty);
    }
  }
}

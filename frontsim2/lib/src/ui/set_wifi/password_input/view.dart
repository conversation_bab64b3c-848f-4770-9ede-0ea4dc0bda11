import 'package:base_common/base_common.dart';
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:sim2/src/ui/business/text_class.dart';
import 'package:sim2/src/ui/common/sim2_button.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../common/sim2_input.dart';
import 'logic.dart';

@GetXRoute(
    opaque: false, transition: TlTransition.fadeIn, fullscreenDialog: true)
class PasswordInputView extends GetView<PasswordInputLogic> {
  const PasswordInputView({super.key, required this.passwordCallback});

  final OutValueCallback<String> passwordCallback;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Material(
      color: Theme.of(context).colorScheme.secondary.withAlpha((255 * 0.1).toInt()),
      child: SizedBox(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              alignment: Alignment.center,
              height: MediaQuery.of(context).size.height * 0.4,
              decoration: BoxDecoration(
                color: Color.fromRGBO(15, 15, 15, 0.6),
                borderRadius: BorderRadius.circular(16),
              ),
              padding: EdgeInsets.symmetric(
                vertical: 45.w,
                horizontal: 120.w
              ),
              margin: EdgeInsets.symmetric(horizontal: 45.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Sim2Input(
                    controller: controller.passwordController, 
                    isPassword: true,
                  ).marginOnly(bottom: 30.w),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 20.w
                    ),
                    child: SText(
                      text: 'The network setup process might take approximately 30 seconds, please wait patiently.',
                      softWrap: true,
                      overflow: TextOverflow.clip,
                      color: Theme.of(context).colorScheme.primary,
                      fontFamily: STextFamily.thin,
                      fontSize: STextSize.sm,
                    ),
                  ).marginOnly(bottom: 100.w),
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Sim2Button(
                          onTap: () {
                            Get.back();
                          },
                          text: Local.common.cancel,
                          color: Theme.of(context).colorScheme.tertiary,
                        ),
                      ),
                      SizedBox(width: 80.w),
                      Expanded(
                        flex: 1,
                        child: Sim2Button(
                          onTap: () {
                            controller.onSubmit();
                          },
                          text: Local.common.confirm,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        )
      )
    ));
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:i_device_action/i_device_action.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_get/rk_get.dart';
import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../configs/local.dart';
import '../../../configs/log.dart';
import '../../../helpers/g_cast_helper.dart';
import '../../../helpers/ip_tools.dart';
import '../../../helpers/lssdp_discovery.dart';
import '../../../routes/route_config.get_x_router_config.dart';
import '../../../services/device_services.dart';
import '../../../services/devices_service.dart';
import '../../business/text.dart';
import '../../common/sim2_button.dart';
import '../wifi_discovery/logic.dart';
import 'state.dart';

class ConnectingDeviceStatusLogic extends GetxController
    with ConnectingDeviceStatusState, ControllerMountedMixin, GetDeviceExt {
  LibreDevicesService get _deviceService {
    assert(Get.isRegistered<LibreDevicesService>(),
        'DevicesService is not registered');
    return Get.find<LibreDevicesService>();
  }

  @override
  void onReady() {
    super.onReady();
    // 连接wifi
    connect2Wifi();
  }

  Future<void> connect2Wifi() async {
    try {
      // 连接wifi
      final result = await _pveLogic.device.connect2Wifi(
        LibreConnect2WifiParams(
          password: _password,
          wifi: _selectedWifi,
          friendlyName: _pveLogic.device.friendlyName,
        ),
      );
      if (result == ConnectWifiResult.wrongPassword) {
        connectingDeviceType.value = ConnectingDeviceType.wrongPassword;
      } else if (result == ConnectWifiResult.success) {
        log.logInformation('连接wifi成功');
        //连接成功后，开始扫描设备
        waitDeviceOnline();
        connectingDeviceType.value = ConnectingDeviceType.waitDeviceOnline;
      } else {
        log.logError('连接wifi失败');
        connectingDeviceType.value = ConnectingDeviceType.connectWifiFailed;
      }
    } catch (e) {
      log.logError('connect2Wifi error: $e');
      connectingDeviceType.value = ConnectingDeviceType.connectWifiFailed;
    }
  }

  Future<void> waitDeviceOnline() async {
    final completer = Completer<String>();
    final cancel = LSSDPSocketClient().addDeviceListener((discovery) {
      log.logDebug('发现设备: ${discovery.name}');
      if (discovery.name == _pveLogic.device.friendlyName) {
        completer.complete(discovery.ip);
      }
    });
    int scanCount = 0;
    final timerCancel = Timer.periodic(const Duration(seconds: 10), (_) {
      scanCount++;
      if (scanCount > 5) {
        cancel();
        completer.completeError(TimeoutException('扫描设备超时'));
        return;
      }
      log.logDebug('开始扫描设备,第$scanCount次');
      LSSDPSocketClient().scan();
    }).cancel;
    try {
      final ip = await completer.future.whenComplete(() {
        cancel();
        timerCancel();
      });
      onlineDeviceIP.value = ip;
      final device = await _deviceService.getDeviceService(ip);
      log.logInformation('设备上线成功');
      //直接尝试获取设备
      findDeviceService(onlineDeviceIP.value);

      connectingDeviceType.value = ConnectingDeviceType.waitQueryUpdate;
      //获取gCast状态
      assert(device != null, 'device is null');
      waitDeviceUpdateSignal(device!);
      await device.getGCastStatus();
      gCastStatus.value = device.gCastStatus.value;
      crashReportStatus.value = device.crashReportStatus.value;
    } catch (e) {
      log.logError('waitDeviceOnline error: $e');
      connectingDeviceType.value = ConnectingDeviceType.deviceOffline;
    }
  }

  //等待设备更新信号
  Future<void> waitDeviceUpdateSignal(DeviceServices device) async {
    //直接监听设备更新信号
    final Completer<ConnectingDeviceType> completer =
        Completer<ConnectingDeviceType>();

    final cancel = device.actions.registerDeviceInUpgradeStateListener((state) {
      if (completer.isCompleted) {
        return;
      }
      if (state != UpgradeSteps.noUpdate) {
        //设备正在升级
        completer.complete(ConnectingDeviceType.upgrading);
      } else {
        //设备升级完成
        completer.complete(ConnectingDeviceType.noUpdate);
      }
    });
    //超时监听
    final timerCancel = Timer.periodic(const Duration(seconds: 10), (_) {
      if (completer.isCompleted) {
        return;
      }
      completer.complete(ConnectingDeviceType.noUpdate);
    }).cancel;
    try {
      final result = await completer.future.whenComplete(() {
        cancel();
        timerCancel();
      });
      connectingDeviceType.value = result;
    } catch (e) {
      log.logError('waitDeviceUpdateSignal error: $e');
      connectingDeviceType.value = ConnectingDeviceType.noUpdate;
    }
  }

  // 这里的获取bleDevice以及selectWifi都从上一个页面的logic中获取
  WifiDiscoveryLogic get _pveLogic {
    assert(Get.isRegistered<WifiDiscoveryLogic>(),
        'WifiDiscoveryLogic is not registered');
    return Get.find<WifiDiscoveryLogic>();
  }

  LibreWifiScanModel get _selectedWifi {
    assert(_pveLogic.selectedWifi != null, 'selectedWifi is null');
    return _pveLogic.selectedWifi!;
  }

  String get ssid {
    return _selectedWifi.ssid;
  }

  String get _password {
    assert(_pveLogic.wifiPassword != null, 'wifiPassword is null');
    return _pveLogic.wifiPassword!;
  }

  //返回上一步
  void back() {
    //清空选中的wifi与密码
    _pveLogic.selectedWifi = null;
    _pveLogic.wifiPassword = null;
    Get.back();
  }

  // ok
  Future<void> ok() async {
    // 先检查gCast状态
    if (gCastStatus.value) {
      // 如果gCast状态为true,回到首页,因为在scan时设备已经连上,不需要重新刷新
      Get.until((route) => route.isFirst);
      return;
    } else {
      Get.toTyped(
        SetGCastViewTypedRoute(
          onSkip: () {
            // ToastUtils.showConfirmTip(
            //     tips: Local.device.g_cast_skip_hit,
            //     onConfirm: () {
            // 跳转到首页
            //       Get.until((route) => route.isFirst);
            //     },
            //     onCancel: () {});
            // 弹窗确认
            showDialog(
              context: Get.context!,
              builder: (context) {
                return AlertDialog(
                  backgroundColor: const Color(0xBA0F0F0F),
                  title: Text(
                    Local.device.g_cast_skip_hit,
                    style: TextStyle(
                      fontSize: STextSize.md,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  actions: [
                    Row(
                      children: [
                        Expanded(
                          child: Sim2Button(
                            onTap: () {
                              Get.back();
                            },
                            text: Local.common.cancel,
                          ),
                        ),
                        SizedBox(width: 50.w),
                        Expanded(
                            child: Sim2Button(
                          onTap: () {
                            Get.back();
                            //跳转到首页
                            Get.until((route) => route.isFirst);
                          },
                          text: Local.common.ok,
                        )),
                      ],
                    )
                  ],
                );
              },
            );
          },
          onAccept: () async {
            await loadingCallback(() async {
              final ipv4 = await IpV4AndIpV6Tool.localIpv4;
              final device =
                  await _deviceService.getDeviceService(onlineDeviceIP.value);
              await device!.acceptGCast(ipv4);
            });
            Get.toTyped(GCastInfoViewTypedRoute(onSkip: () {
              // 跳转到首页
              Get.until((route) => route.isFirst);
            }, onGotoHomeApp: () {
              // 先去app,然后再去首页
              GCastHelper.tapGoogleHomeApp();
              Get.until((route) => route.isFirst);
            }));
          },
        ),
      );
    }
  }
}

class WifiConnectError implements Exception {
  const WifiConnectError(this.message);
  final String message;
}

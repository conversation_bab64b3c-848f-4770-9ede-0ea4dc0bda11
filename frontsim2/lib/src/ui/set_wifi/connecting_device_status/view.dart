import 'package:flutter/material.dart';
import 'package:rk_get/rk_get.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:sim2/src/ui/set_wifi/connecting_device_status/state.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../common/common_background.dart';
import '../../device/device_upgrading/device_upgrading.dart';
import '../components/loading.dart';
import 'logic.dart';

@GetXRoute()
class ConnectingDeviceStatusView extends GetView<ConnectingDeviceStatusLogic> {
  const ConnectingDeviceStatusView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => CommonBackground(
          appBar: Sim2AppBar(
            title: Local.setWifi.title,
            showPop: switch (controller.connectingDeviceType.value) {
              ConnectingDeviceType.wrongPassword => true,
              ConnectingDeviceType.connectWifiFailed => true,
              ConnectingDeviceType.deviceOffline => true,
              ConnectingDeviceType.noUpdate => true,
              _ => false,
            },
          ),
          body: switch (controller.connectingDeviceType.value) {
            ConnectingDeviceType.connecting => Container(
                alignment: Alignment.center,
                child: SetWiFiLoadingContainer(
                  title: Local.setWifi.connecting_wifi(controller.ssid),
                  type: SetWiFiLoadingType.loading,
                ),
              ),
            ConnectingDeviceType.wrongPassword => Container(
                alignment: Alignment.center,
                child: SetWiFiLoadingContainer(
                  title: Local.setWifi.wrong_password,
                  type: SetWiFiLoadingType.fail,
                ),
              ),
            ConnectingDeviceType.connectWifiFailed => Container(
                alignment: Alignment.center,
                child: SetWiFiLoadingContainer(
                  title: Local.setWifi.connect_ap_failed,
                  type: SetWiFiLoadingType.fail,
                ),
              ),
            ConnectingDeviceType.waitDeviceOnline => Container(
                alignment: Alignment.center,
                child: SetWiFiLoadingContainer(
                  title: Local.setWifi.wait_device_online,
                  type: SetWiFiLoadingType.loading,
                ),
              ),
            ConnectingDeviceType.waitQueryUpdate => Container(
                alignment: Alignment.center,
                child: SetWiFiLoadingContainer(
                  title: Local.setWifi.check_update_hint,
                  type: SetWiFiLoadingType.loading,
                ),
              ),
            ConnectingDeviceType.deviceOffline => Container(
                alignment: Alignment.center,
                child: SetWiFiLoadingContainer(
                  title: Local.setWifi.device_online_failed,
                  type: SetWiFiLoadingType.empty,
                ),
              ),
            ConnectingDeviceType.noUpdate => Container(
                alignment: Alignment.center,
                child: SetWiFiLoadingContainer(
                  title: Local.setWifi.connected,
                  type: SetWiFiLoadingType.ok,
                ),
              ),
            ConnectingDeviceType.upgrading => Center(
                child: GetBuilder<DeviceUpgradingLogic>(
                  autoRemove: true,
                  init: DeviceUpgradingLogic()
                    ..deviceIP = controller.onlineDeviceIP.value,
                  builder: (_) => UpgradeStepsView(
                    onOk: () {
                      controller.ok();
                    },
                  ),
                ),
              ),
          },
        ));
  }
}


// FutureLoadingWrap(
//   future: controller.connect2WifiFuture.value,
//   valueBuilder: (_, __) => FutureLoadingWrap(
//       future: controller.deviceOnlineFuture.value,
//       valueBuilder: (_, __) {
//         return Center(
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               const Icon(Icons.check_circle, color: Colors.green),
//               const SizedBox(height: 16),
//               Text(Local.setWifi.connected),
//               const SizedBox(height: 16),
//               ElevatedButton(
//                 onPressed: () {
//                   controller.ok();
//                 },
//                 child: Text(Local.common.ok),
//               ),
//             ],
//           ),
//         );
//       },
//       loadingWidget: Center(
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             const CircularProgressIndicator(),
//             const SizedBox(height: 16),
//             Text(Local.setWifi.check_update_hint),
//           ],
//         ),
//       )),
//   loadingWidget: Center(
//     child: Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         const CircularProgressIndicator(),
//         const SizedBox(height: 16),
//         Text(Local.setWifi.connecting_wifi(controller.ssid)),
//       ],
//     ),
//   ),
//   errorBuilder: (error, context) {
//     if (error is WifiConnectError) {
//       return Center(
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             const Icon(Icons.error, color: Colors.red),
//             const SizedBox(height: 16),
//             Text(Local.setWifi.wrong_password),
//             const SizedBox(height: 16),
//             ElevatedButton(
//               onPressed: () {
//                 controller.back();
//               },
//               child: Text(Local.setWifi.return_prev_step),
//             ),
//           ],
//         ),
//       );
//     }
//     return Center(
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           const Icon(Icons.error, color: Colors.red),
//           const SizedBox(height: 16),
//           Text(error.toString()),
//           const SizedBox(height: 16),
//           ElevatedButton(
//             onPressed: () {
//               controller.back();
//             },
//             child: Text(Local.setWifi.return_prev_step),
//           ),
//         ],
//       ),
//     );
//   },
// )
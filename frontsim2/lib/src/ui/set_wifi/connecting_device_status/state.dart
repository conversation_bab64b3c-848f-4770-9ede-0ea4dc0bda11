import 'package:rk_get/rk_get.dart';

mixin ConnectingDeviceStatusState {

  final Rx<ConnectingDeviceType> connectingDeviceType =
      ConnectingDeviceType.connecting.obs;

  final RxString deviceUUID = ''.obs;

  final RxBool gCastStatus = false.obs;
  final RxBool crashReportStatus = false.obs;

  final RxString onlineDeviceIP = ''.obs;
}

enum ConnectingDeviceType {
  connecting,
  wrongPassword,
  connectWifiFailed,
  waitDeviceOnline,
  waitQueryUpdate,
  deviceOffline,
  noUpdate,
  upgrading,
}

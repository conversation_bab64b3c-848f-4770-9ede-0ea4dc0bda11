import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_button.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';

import '../../../configs/local.dart';
import '../../business/text.dart';

class SetWiFiLoadingContainer extends StatelessWidget {
  const SetWiFiLoadingContainer(
      {super.key, required this.title, required this.type});

  final String title;
  final SetWiFiLoadingType type;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: switch (type) {
              SetWiFiLoadingType.loading => LoadingAnimationWidget.hexagonDots(
                  color: Theme.of(context).primaryColor,
                  size: 150.w,
                ),
              SetWiFiLoadingType.empty => Icon(
                  Icons.hourglass_empty,
                  color: Theme.of(context).primaryColor,
                  size: 148.w,
                ),
              SetWiFiLoadingType.ok => Icon(
                  Icons.hourglass_empty,
                  color: Theme.of(context).primaryColor,
                  size: 148.w,
                ),
              SetWiFiLoadingType.fail => Sim2Icon(
                  path: Assets.ASSETS_ICONS_ERROR_ICON_SVG,
                  size: 150.w,
                ),
            },
          ),
          Container(
            margin: EdgeInsets.only(
              top: 80.w,
              left: 80.w,
              right: 80.w,
            ),
            child: SText(
              text: title,
              softWrap: true,
              overflow: TextOverflow.clip,
              textAlign: TextAlign.center,
            ),
          ),
          if (type == SetWiFiLoadingType.fail)
            Container(
              margin: EdgeInsets.only(top: 60.w),   
              width: 420.w, 
              child: Sim2Button(
                text: Local.setWifi.return_prev_step,
                onTap: () => Get.back(),
              ),
            ),
          if (type == SetWiFiLoadingType.ok)
            Container(
              margin: EdgeInsets.only(top: 60.w),
              width: 300.w,
              child: Sim2Button(
                text: Local.common.ok,
                onTap: () {
                  //直接回到首页
                  Get.until((route) => route.isFirst);
                },
              ),
            ),
        ],
      ),
    );
  }
}

enum SetWiFiLoadingType { loading, empty, fail, ok }

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/setting_container.dart';
import 'package:sim2/src/ui/common/common_background.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:sim2/src/ui/set_wifi/components/loading.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../../routes/route_config.get_x_router_config.dart';
import 'logic.dart';

@GetXRoute()
class WifiDiscoveryView extends GetView<WifiDiscoveryLogic> {
  const WifiDiscoveryView({required this.device, super.key});

  final ScanResult device;

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: Sim2AppBar(
        title: Local.setWifi.title,
        reset: () {
          controller.onRefresh();
        },
      ),
      body: Obx(()=>Container(
        alignment: controller.wifiDiscoveryType.value == WifiDiscoveryType.wifiDiscoveryEnd? Alignment.topCenter : Alignment.center,
        child: Obx(()=> switch (controller.wifiDiscoveryType.value) {
          WifiDiscoveryType.deviceConnecting => SetWiFiLoadingContainer(
            title: Local.setWifi.connecting_device,
            type: SetWiFiLoadingType.loading,
          ),
          WifiDiscoveryType.connectingFail => SetWiFiLoadingContainer(
            title:  Local.setWifi.connect_failed,
            type: SetWiFiLoadingType.fail,
          ),
          WifiDiscoveryType.wifiDiscovery => SetWiFiLoadingContainer(
            title: '${Local.setWifi.scanning_ap} ...',
            type: SetWiFiLoadingType.loading,
          ),
          WifiDiscoveryType.wifiDiscoveryEnd =>  RefreshIndicator(
            onRefresh: () => controller.onRefresh(),
            child: SingleChildScrollView(
              child: SettingBlock(
                margin: EdgeInsets.zero,
                children: [
                  for (final wifiItem in controller.wifiList) 
                    SettingBlockChild.wifi(
                      onTap: () {
                        if (wifiItem.security != 'WPA-PSK') {
                          controller.gotoConnectWifi(
                            wifiItem,
                            '',
                          );
                        } else {
                          Get.toTyped(
                            PasswordInputViewTypedRoute(
                              passwordCallback: (password) {
                                controller.gotoConnectWifi(
                                  wifiItem,
                                  password,
                                );
                              },
                            ),
                          );
                        }
                      },
                      rssi: wifiItem.rssi,
                      title: wifiItem.ssid,
                      wifiLock: (wifiItem.security == 'WPA-PSK')
                    )
                ]
              )
            ).marginOnly(bottom: 44.w)
          ),
          WifiDiscoveryType.wifiDiscoveryEmpty => SetWiFiLoadingContainer(
            title:Local.setWifi.no_found_ap,
            type: SetWiFiLoadingType.empty,
          ),
          WifiDiscoveryType.wifiDiscoveryError => SetWiFiLoadingContainer(
            title: Local.setWifi.scanning_ap_failed,
            type: SetWiFiLoadingType.fail,
          ),
        })
      )),
    );
  }
}

import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_get/rk_get.dart';

mixin WifiDiscoveryState {
  late final LibreDeviceActions device;
  final RxList<LibreWifiScanModel> wifiList = <LibreWifiScanModel>[].obs;

  final wifiDiscoveryType = WifiDiscoveryType.deviceConnecting.obs;

  LibreWifiScanModel? selectedWifi;
  String? wifiPassword;
}


enum WifiDiscoveryType {
  deviceConnecting,
  connectingFail,
  wifiDiscovery,
  wifiDiscoveryEnd,
  wifiDiscoveryEmpty,
  wifiDiscoveryError
}
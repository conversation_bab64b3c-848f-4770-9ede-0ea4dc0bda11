import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/routes/route_config.get_x_router_config.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../configs/log.dart';
import 'state.dart';
export 'state.dart';

class WifiDiscoveryLogic extends GetxController with WifiDiscoveryState {

  @override
  void onInit() {
    super.onInit();
    //初始化获取路由参数
    final bleDevice = Get.arguments['device'] as ScanResult?;
    assert(bleDevice != null, 'device is null');
    device = LibreDeviceActions.fromBt(bleDevice!.device);
  }

  @override
  void onReady() {
    super.onReady();
    deviceInit();
  }

  @override
  void onClose() {
    log.logInformation('${device.btDevice?.platformName} 注销连接');
    device.dispose();
    super.onClose();
  }

  Future<void> deviceInit() async {
    try {
      // 连接设备
      log.logInformation('${device.btDevice?.platformName} 创建连接');
      wifiDiscoveryType.value = WifiDiscoveryType.deviceConnecting;
      await device.connectDevice();
      await device.init();
    } catch (e) {
      log.logError('deviceInit error: $e');
      wifiDiscoveryType.value = WifiDiscoveryType.connectingFail;
      rethrow;
    }
    log.logInformation('deviceInit success');
    wifiDiscoveryType.value = WifiDiscoveryType.wifiDiscovery;
    wifiDiscovery();
  }

  Future<void> wifiDiscovery() async {
    try {
      // 获取wifi列表
      final result = await device.getWIfiList();
      wifiList.assignAll(result.cast<LibreWifiScanModel>());
      if (wifiList.isEmpty) {
        wifiDiscoveryType.value = WifiDiscoveryType.wifiDiscoveryEmpty;
      } else {
        wifiDiscoveryType.value = WifiDiscoveryType.wifiDiscoveryEnd;
      }
    } catch (e) {
      log.logError('wifiDiscovery error: $e');
      wifiDiscoveryType.value = WifiDiscoveryType.wifiDiscoveryError;
      rethrow;
    }
  }

  Future<void> onRefresh() async {
    wifiDiscoveryType.value = WifiDiscoveryType.wifiDiscovery;
    wifiList.clear();
    await wifiDiscovery();
  }

  void gotoConnectWifi(LibreWifiScanModel wifi, String password) {
    selectedWifi = wifi;
    wifiPassword = password;
    //跳转到连接wifi页面
    Get.toTyped(ConnectingDeviceStatusViewTypedRoute());
  }
}

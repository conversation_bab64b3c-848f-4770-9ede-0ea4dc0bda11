import 'dart:async';
import 'dart:io';

import 'package:base_common/base_common.dart';
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:sim2/src/configs/log.dart';

import '../../../configs/local.dart';
import '../../../helpers/permission.dart';
import '../../common/android_permission_tip.dart';
import '../../common/confirm_dialog.dart';
import 'state.dart';
export 'state.dart';

class BleDiscoveryLogic extends GetxController with BleDiscoveryState {
  CancelListener? _bleListener;
  CancelListener? cancel;

  @override
  void onReady() {
    super.onReady();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // 获取蓝牙权限
      if (!Platform.isMacOS) {
        final persmissAgreed = await PermissionHelper.isAllGranted([
          Permission.bluetooth,
          if (Platform.isAndroid) ...[
            Permission.bluetoothAdvertise,
            Permission.bluetoothScan,
            Permission.bluetoothConnect,
          ],
          Permission.location,
        ]);
        if (!persmissAgreed) {
          final bool ask = await askBTPermission();
          if (!ask) {
            ToastUtils.showCustomWidgetModal(customBuilder: (_, cancel) {
              return ConfirmDialog(
                content: Local.setWifi.open_setting_tip,
                onConfirm: () {
                  cancel.call();
                },
              );
            });
            return;
          }
        }
      }
      //判断蓝牙是否打开
      if (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.unknown) {
        // 如果蓝牙状态未知，等待状态变更
        await FlutterBluePlus.adapterState.first;
      }
      final bool isOpen =
          (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.on);
      if (!isOpen) {
        if (!Platform.isAndroid) {
          await FlutterBluePlus.turnOn();
        } else {
          ToastUtils.showCustomWidgetModal(
              customBuilder: (_, cancel) => ConfirmDialog(
                    content: Local.setWifi.open_ble_instructions,
                    onConfirm: () {
                      cancel.call();
                      initData();
                    },
                  ));
          return;
        }
      }
      initData();
    });
  }

  void initData() {
    // 直接创建蓝牙监听
    _bleListener = FlutterBluePlus.onScanResults.listen((results) {
      final info = _parseBLEDeviceInfo(results);
      // ignore: invalid_use_of_protected_member
      if (ListEquality().equals(info, scanDevices.value)) {
        return;
      }
      //
      if (info.isNotEmpty) {
        bleDiscoveryType.value = BleDiscoveryType.scannedHaveResult;
        scanDevices.assignAll(info);
      }
    }).cancel;

    // 直接开始扫描
    onRefresh();
  }

  Future<bool> askBTPermission() async {
    {
      final result = await Permission.bluetooth.request();
      if (result.isDenied) {
        return false;
      }
      if (result.isPermanentlyDenied) {
        return false;
      }
    }
    if (Platform.isAndroid) {
      VoidCallback? cancel;
      final result = await PermissionHelper.isAllGranted([
        Permission.bluetoothAdvertise,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
      ]);
      if (!result) {
        ToastUtils.showCustomWidgetModal(customBuilder: (_, cancelFunc) {
          cancel = cancelFunc;
          return AndroidPermissionTip(
            message: Local.setWifi.open_ble_permission,
          );
        });
      }

      try {
        {
          final result = await Permission.bluetoothAdvertise.request();
          if (result.isDenied) {
            return false;
          }
          if (result.isPermanentlyDenied) {
            return false;
          }
        }
        {
          final result = await Permission.bluetoothScan.request();
          if (result.isDenied) {
            return false;
          }
          if (result.isPermanentlyDenied) {
            return false;
          }
        }
        {
          final result = await Permission.bluetoothConnect.request();
          if (result.isDenied) {
            return false;
          }
          if (result.isPermanentlyDenied) {
            return false;
          }
        }
      } finally {
        cancel?.call();
      }
    }
    {
      VoidCallback? cancel;
      if (Platform.isAndroid) {
        final result = await PermissionHelper.isAllGranted([
          Permission.location,
        ]);
        if (!result) {
          // android 权限背景说明
          ToastUtils.showCustomWidgetModal(customBuilder: (_, cancelFunc) {
            cancel = cancelFunc;
            return AndroidPermissionTip(
              message: Local.setWifi.open_location_permission,
            );
          });
        }
      }
      final result = await Permission.location.request();
      cancel?.call();
      if (result.isDenied) {
        return false;
      }
      if (result.isPermanentlyDenied) {
        return false;
      }
    }
    return true;
  }

  List<LibreBLEDiscoverInfo> _parseBLEDeviceInfo(List<ScanResult> results) {
    // 处理设备信息
    final List<LibreBLEDiscoverInfo> devices = [];

    for (final item in results) {
      if (item.rssi < -88) {
        log.logDebug('信号太差过滤 --- ${item.device.platformName}');
        continue;
      }
      // 过滤掉没有名称的设备
      if (item.device.platformName.isEmpty) {
        log.logDebug('设备名称为空过滤 --- ${item.device.remoteId}');
        continue;
      }

      Guid libreGuid = Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1');
      final serviceData = item.advertisementData.serviceData.keys;

      if (serviceData.isNotEmpty && serviceData.contains(libreGuid)) {
        String platformName =
            item.device.platformName.replaceAll(RegExp(r'-BLE$'), '');
        devices.add(LibreBLEDiscoverInfo(
          scanResult: item,
          name: platformName,
          ssid: item.rssi,
        ));
      }
    }

    return devices;
  }

  Future<void> onRefresh() async {
    // 如果正在扫描，则不重复扫描
    if (FlutterBluePlus.isScanningNow) {
      cancel?.call();
      await FlutterBluePlus.stopScan();
    }
    if (_bleListener == null) {
      return onInit();
    }

    bleDiscoveryType.value = BleDiscoveryType.scanning;
    // 设置扫描状态
    log.logDebug('开始扫描, 当前状态${bleDiscoveryType.value}');
    // 清空设备列表
    scanDevices.clear();
    // 重新开始扫描
    await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 5),
        withServiceData: [
          ServiceDataFilter(Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1'),
              data: [0, 0, 0]),
          ...List.generate(3, (index) {
            return ServiceDataFilter(
                Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1'),
                data: [0, 17, (index + 1)]);
          }),
          ServiceDataFilter(Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1'),
              data: [0, 18, 4])
        ]);
    // 等待6秒后，设置结果, 增加一秒容错率，有的设备可能回调的慢一点
    cancel = Timer(const Duration(seconds: 6), () {
      if (scanDevices.isEmpty) {
        log.logDebug('扫描完成，没有设备');
        bleDiscoveryType.value = BleDiscoveryType.scannedAndEmpty;
      }
    }).cancel;
  }

  @override
  void onClose() {
    super.onClose();
    _bleListener?.call();
    cancel?.call();
  }
}

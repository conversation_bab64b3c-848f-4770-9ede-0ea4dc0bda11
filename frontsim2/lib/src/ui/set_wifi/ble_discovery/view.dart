import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../../routes/route_config.get_x_router_config.dart';
import '../../business/setting_container.dart';
import '../../common/sim2_nav.dart';
import '../../common/common_background.dart';
import 'logic.dart';

@GetXRoute()
class BleDiscoveryView extends GetView<BleDiscoveryLogic> {
  const BleDiscoveryView({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: Sim2AppBar(
        title: Local.setWifi.title,
        reset: () {
          controller.onRefresh();
        },
      ),
      body: Column(
        children: [
          Flexible(
            child: Obx(() => RefreshIndicator(
              onRefresh: () => controller.onRefresh(),
              child: switch (controller.bleDiscoveryType.value) {
                // 加载过程
                BleDiscoveryType.scanning => Container(
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Center(
                        child: LoadingAnimationWidget.hexagonDots(
                          color: Theme.of(context).primaryColor,
                          size: 150.w, 
                        ),
                      ),
                      SText(
                        text: Local.setWifi.scanning_devices,
                      ).marginOnly(top: 80.w)
                    ],
                  ),
                ),
                // 扫描出设备
                BleDiscoveryType.scannedHaveResult => SettingBlock(
                  margin: EdgeInsets.zero,
                  children: [
                    for (final item in controller.scanDevices) 
                      SettingBlockChild.navigation(
                        title: item.name,
                        navigateTo: () {
                          Get.toTyped(
                            WifiDiscoveryViewTypedRoute(
                              device: item.scanResult,
                            ),
                          );
                        },
                      )
                  ]
                ),
                // 扫描未发现任何设备
                BleDiscoveryType.scannedAndEmpty => Container(
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.hourglass_empty,
                        color: Theme.of(context).primaryColor,
                        size: 148.w,
                      ),
                      SText(
                        text: Local.setWifi.no_found_device,
                        fontSize: STextSize.mdx,
                      ).marginOnly(top: 80.w),
                    ],
                  )
                )
              },
            )),
          )
        ],
      ),
    );
  }
}

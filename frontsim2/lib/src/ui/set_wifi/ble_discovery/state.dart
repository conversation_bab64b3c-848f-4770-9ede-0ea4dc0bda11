
// import 'package:rk_get/rk_get.dart';
import 'package:rk_package/rk_package.dart';

mixin BleDiscoveryState {
  /// 扫描到的设备
  final RxList<LibreBLEDiscoverInfo> scanDevices = <LibreBLEDiscoverInfo>[].obs;
  /// 蓝牙扫描状态
  final Rx<BleDiscoveryType> bleDiscoveryType = BleDiscoveryType.scanning.obs;
}

enum BleDiscoveryType {
  scanning, // 正在扫描
  scannedHaveResult, // 扫描有结果
  scannedAndEmpty, // 扫描没有结果
}


/// 表示蓝牙扫描到的设备信息的类
class LibreBLEDiscoverInfo {
  /// 构造函数，用于创建一个 [LibreBLEDiscoverInfo] 实例
  /// [name] 设备名称，默认为空字符串
  /// [ssid] 设备的 SSID，默认为 0
  /// [scanResult] 扫描结果，必须提供
  const LibreBLEDiscoverInfo({
    /// 设备名称
    this.name = '',
    /// 设备的 SSID
    this.ssid = 0,
    /// 扫描结果
    required this.scanResult,
  });
  /// 扫描结果
  final ScanResult scanResult;
  /// 设备名称
  final String name;
  /// 设备的 SSID
  final int ssid;

  @override
  // ignore: hash_and_equals
  bool operator == (Object other) {
    if (other is! LibreBLEDiscoverInfo) {
      return false;
    }
    return other.name == name && other.ssid == ssid;
  }
}

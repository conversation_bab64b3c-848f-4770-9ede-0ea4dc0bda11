import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../business/text.dart';
import '../../common/common_background.dart';
import '../../common/g_cast_background.dart';
import '../../common/sim2_button.dart';
import '../../common/sim2_icons.dart';
import '../../common/sim2_nav.dart';
import 'logic.dart';

@GetXRoute()
class GCastInfoView extends GetView<GCastInfoLogic> {
  const GCastInfoView({
    super.key,
    required this.onSkip,
    required this.onGotoHomeApp,
  });

  final VoidCallback onSkip;

  final VoidCallback onGotoHomeApp;

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    return CommonBackground(
      appBar: Sim2AppBar(
        title: 'Setup',
      ),
      body: GCastBackground(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(children: [
              Transform.scale(
                scale: .88,
                child: ExtendedImage.asset(
                  Assets.ASSETS_IMAGES_GOOGLE_LOGO_PNG,
                  height: 270.w,
                ),
              ),
              Container(
                  padding: EdgeInsets.symmetric(horizontal: 22.w),
                  margin: EdgeInsets.symmetric(vertical: 12.w),
                  child: SText(
                    text: Local.device.gcast_tip1,
                    fontSize: STextSize.md,
                    color: primaryColor,
                    softWrap: true,
                  )),
              Transform.scale(
                scale: .7,
                child: ExtendedImage.asset(
                  Assets.ASSETS_IMAGES_GOOGLE_WORKS_PNG,
                  fit: BoxFit.cover,
                  height: 270.w,
                ),
              ),
              Container(
                  padding: EdgeInsets.symmetric(horizontal: 22.w),
                  margin: EdgeInsets.symmetric(vertical: 12.w),
                  child: SText(
                    text: Local.device.gcast_tip2,
                    fontSize: STextSize.md,
                    color: primaryColor,
                    softWrap: true,
                  )),
            ]),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                      child: Sim2Button(
                    onTap: () {
                      onSkip();
                    },
                    text: Local.common.skip,
                  )),
                  SizedBox(width: 12.w),
                  Flexible(
                      child: Sim2Button(
                    onTap: () async {
                      onGotoHomeApp();
                    },
                    text: Local.device.go_home_app,
                  )),
                ],
              ),
            )
          ],
        ),
      )),
    );
  }
}

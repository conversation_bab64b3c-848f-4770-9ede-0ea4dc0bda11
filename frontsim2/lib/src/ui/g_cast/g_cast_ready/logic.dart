import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../../configs/local.dart';
import '../../../helpers/g_cast_helper.dart';
import '../../../helpers/ip_tools.dart';
import '../../../routes/route_config.get_x_router_config.dart';
import '../../../services/device_general_settings_service.dart';
import '../../../services/devices_service.dart';
import 'state.dart';

class GCastReadyLogic extends GetxController
    with GCastReadyState, GetDeviceExt {
  @override
  void onInit() {
    super.onInit();
    deviceIP.value = Get.arguments['deviceIP'] as String;
    init();
  }

  void init() {
    findDeviceService(deviceIP.value).then((device) {
      deviceSetting.value = device as DeviceGeneralSettingsServices;
    });
  }

  Future<void> gotoAcceptGCast() async {
    assert(deviceSetting.value != null, 'deviceSetting is null');
    Get.toTyped(
      SetGCastViewTypedRoute(
        onSkip: () {
          Get.back();
        },
        onAccept: () async {
          final value = await loadingCallback<bool>(() async {
            final ipv4 = await IpV4AndIpV6Tool.localIpv4;
            final value = await deviceSetting.value!.acceptGCast(ipv4);
            if (value) {
              deviceSetting.value!.gCastStatus.value = true;
            }
            return value;
          });
          if (!value) {
            ToastUtils.showToast(msg: Local.device.g_cast_set_fail);
          } else {
            Get.replaceTyped(GCastInfoViewTypedRoute(onSkip: () {
              Get.until(
                  (route) => route.settings.name == RouteNames.gcastReady);
            }, onGotoHomeApp: () {
              GCastHelper.tapGoogleHomeApp();
              Get.until(
                  (route) => route.settings.name == RouteNames.gcastReady);
            }));
          }
        },
      ),
    );
  }
}

import 'dart:math';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/common_background.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../../helpers/g_cast_helper.dart';
import '../../business/sim2_switch.dart';
import '../../business/text.dart';
import '../../common/g_cast_background.dart';
import '../../common/sim2_icons.dart';
import 'logic.dart';

@GetXRoute(
  routeName: '/gcast_ready',
)
class GCastReadyView extends GetView<GCastReadyLogic> {
  const GCastReadyView({super.key, required this.deviceIP});

  final String deviceIP;

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    return CommonBackground(
      appBar: Sim2AppBar(
        title: 'Setup',
      ),
      body: Obx(() {
        final setting = controller.deviceSetting.value;
        if (setting == null) {
          return const SizedBox();
        }
        return GCastBackground(
            child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: 16.w),
          child: Column(
            children: [
              Transform.scale(
                scale: .9,
                child: ExtendedImage.asset(
                  Assets.ASSETS_IMAGES_GOOGLE_LOGO_PNG,
                  height: 270.w,
                ),
              ),
              SizedBox(height: 5.sp),
              GestureDetector(
                onTap: () {
                  if (!setting.gCastStatus.value) {
                    controller.gotoAcceptGCast();
                  }
                },
                child: Container(
                    height: 86.w,
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    margin: EdgeInsets.symmetric(vertical: 9.w),
                    decoration: BoxDecoration(
                        color: Color(0x66CFCFCF),
                        borderRadius: BorderRadius.circular(52.w / 6),
                        border: Border.all(
                            width: 1.sp, color: Colors.grey.withAlpha(40))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() => SText(
                              text: setting.gCastStatus.value
                                  ? Local.device.gcast1
                                  : Local.device.gcast2,
                              fontSize: STextSize.md,
                              color: primaryColor,
                            )),
                        Obx(() => Visibility(
                              visible: !setting.gCastStatus.value,
                              child: Transform.rotate(
                                angle: pi / 1,
                                child: Sim2Icon(
                                  path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
                                  size: STextSize.mds,
                                  color: primaryColor,
                                ),
                              ),
                            ))
                      ],
                    )),
              ),
              SizedBox(height: 12.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.w),
                margin: EdgeInsets.symmetric(vertical: 24.w),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Flexible(
                      child: SText(
                        text: Local.device.gcast3,
                        fontSize: STextSize.md,
                        softWrap: true,
                        color: primaryColor,
                      ),
                    ),
                    SizedBox(width: 20.w),
                    Container(
                      margin: EdgeInsets.only(top: 4.w),
                      child: Sim2Switch(
                        value: setting.crashReportStatus,
                        onChanged: (status) {
                          setting.toggleCrashReport(status);
                        },
                      ),
                    )
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 0.w),
                alignment: Alignment.centerLeft,
                child: SText(
                  text: '${Local.device.gcast4} myaccount.google.com',
                  fontSize: STextSize.md,
                  softWrap: true,
                  color: primaryColor,
                ),
              ),
              Container(
                  margin: EdgeInsets.symmetric(vertical: 24.w),
                  alignment: Alignment.centerLeft,
                  child: Text.rich(
                      softWrap: true,
                      TextSpan(
                          text: Local.device.gcast5,
                          style: TextStyle(
                            fontSize: STextSize.md,
                            fontFamily: 'Medium',
                            color: primaryColor,
                          ),
                          children: [
                            TextSpan(
                              text: 'Google Home App',
                              style: TextStyle(
                                decoration: TextDecoration.underline,
                                decorationStyle: TextDecorationStyle.solid,
                                decorationColor: primaryColor,
                              ),
                              mouseCursor: SystemMouseCursors.precise,
                              recognizer: TapGestureRecognizer()
                                ..onTap = () => GCastHelper.tapGoogleHomeApp(),
                            ),
                          ]))),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 24.w),
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment,
                  children: [
                    Container(
                      alignment: Alignment.centerLeft,
                      child: SText(
                        text: Local.device.gcast6,
                        fontSize: STextSize.md,
                        fontFamily: STextFamily.bold,
                        softWrap: true,
                        color: primaryColor,
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: GestureDetector(
                        onTap: () => GCastHelper.goTou(),
                        child: Text(
                          Local.device.gcast7,
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            decorationStyle: TextDecorationStyle.solid,
                            decorationColor: primaryColor,
                            fontSize: STextSize.mds,
                            color: primaryColor,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: GestureDetector(
                        onTap: () => GCastHelper.goTou(),
                        child: Text(
                          Local.device.gcast8,
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            decorationStyle: TextDecorationStyle.solid,
                            decorationColor: primaryColor,
                            fontSize: STextSize.mds,
                            color: primaryColor,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: GestureDetector(
                        onTap: () => GCastHelper.goTou(),
                        child: Text(
                          Local.device.gcast9,
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            decorationStyle: TextDecorationStyle.solid,
                            decorationColor: primaryColor,
                            fontSize: STextSize.mds,
                            color: primaryColor,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: GestureDetector(
                        onTap: () => GCastHelper.goTou(),
                        child: Text(
                          Local.device.gcast10,
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            decorationColor: primaryColor,
                            decorationStyle: TextDecorationStyle.solid,
                            fontSize: STextSize.mds,
                            color: primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
      }),
    );
  }
}

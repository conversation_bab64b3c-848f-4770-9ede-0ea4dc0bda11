import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../../helpers/g_cast_helper.dart';
import '../../business/text.dart';
import '../../common/common_background.dart';
import '../../common/g_cast_background.dart';
import '../../common/sim2_button.dart';
import '../../common/sim2_icons.dart';
import '../../common/sim2_nav.dart';
import 'logic.dart';

@GetXRoute()
class SetGCastView extends GetView<SetGCastLogic> {
  const SetGCastView({
    super.key, 
    required this.onSkip, 
    required this.onAccept
  });

  final VoidCallback onSkip;
  final VoidCallback onAccept;

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: Sim2AppBar(
        title: 'Setup',
      ),
      body: GCastBackground(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(children: [
              Transform.scale(
                scale: .88,
                child: ExtendedImage.asset(
                  Assets.ASSETS_IMAGES_GOOGLE_LOGO_PNG,
                  height: 270.w,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                child: Text.rich(
                  softWrap: true,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: STextSize.xl,
                    fontFamily: STextFamily.regular.value,
                  ),
                  TextSpan(
                    text: Local.device.g_cast_agreement1,
                    children: [
                      TextSpan(
                        text: Local.device.g_cast_agreement2,
                        style: TextStyle(
                          decoration: TextDecoration.underline,
                          decorationStyle: TextDecorationStyle.solid,
                          decorationColor:
                              Theme.of(context).colorScheme.primary,
                        ),
                        mouseCursor: SystemMouseCursors.precise,
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            GCastHelper.goGoogleTerms();
                          },
                      ),
                      TextSpan(
                        text: '.',
                      ),
                      TextSpan(
                        text: Local.device.g_cast_agreement3,
                      ),
                      TextSpan(
                        text: Local.device.g_cast_agreement4,
                        style: TextStyle(
                          decoration: TextDecoration.underline,
                          decorationStyle: TextDecorationStyle.solid,
                          decorationColor:
                              Theme.of(context).colorScheme.primary,
                        ),
                        mouseCursor: SystemMouseCursors.precise,
                        recognizer: TapGestureRecognizer()
                          ..onTap = () async {
                            GCastHelper.goGooglePrivacy();
                          },
                      ),
                      TextSpan(
                        text: Local.device.g_cast_agreement5,
                      ),
                    ],
                  ),
                ),
              )
            ]),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                      child: Center(
                    child: Sim2Button(
                        onTap: () {
                          onSkip();
                        },
                        text: Local.common.skip),
                  )),
                  SizedBox(width: 12.w),
                  Flexible(
                      child: Center(
                    child: Sim2Button(
                      onTap: () async {
                        onAccept();
                      },
                      text: Local.common.accept,
                    ),
                  )),
                ],
              ),
            )
          ],
        ),
      )),
    );
  }
}

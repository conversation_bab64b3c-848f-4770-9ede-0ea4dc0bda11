import 'package:flutter/material.dart';

import '../../configs/local.dart';

class ConfirmDialog extends StatefulWidget {
  const ConfirmDialog({
    super.key,
    this.title,
    required this.content,
    this.confirmText,
    required this.onConfirm,
  });

  final String? title;
  final String content;
  final String? confirmText;
  final VoidCallback onConfirm;

  @override
  State<ConfirmDialog> createState() => _ConfirmDialogState();
}

class _ConfirmDialogState extends State<ConfirmDialog> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      color: Colors.black54.withValues(alpha: 0.5),
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: GestureDetector(
        onTap: () {},
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color:  Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.title != null)
                  Text(widget.title!,
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold,color:colorScheme.primary )),
                const SizedBox(height: 10),
                Text(widget.content, style: TextStyle(fontSize: 16,color: colorScheme.primary)),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    widget.onConfirm();
                  },
                  child: Text(widget.confirmText ?? Local.common.ok),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

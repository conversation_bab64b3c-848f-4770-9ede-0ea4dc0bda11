import 'package:flutter/material.dart';

class KeepAliveWrap extends StatefulWidget {
  const KeepAliveWrap({
    super.key,
    required this.child,
  });
  final Widget child;

  @override
  State<KeepAliveWrap> createState() => _KeepAliveWrapState();
}

class _KeepAliveWrapState extends State<KeepAliveWrap>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}

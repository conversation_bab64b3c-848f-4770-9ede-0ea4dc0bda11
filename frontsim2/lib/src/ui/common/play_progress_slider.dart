
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/text_class.dart';

import '../../helpers/text_formate.dart';
import '../business/sim2_slider.dart';

final Color _inactiveTrackColor = Color.fromRGBO(255, 255, 255, 0.2);
final Color _activeTrackColor = Color.fromRGBO(255, 255, 255, 1);

class PlayProgressSlider extends StatelessWidget {
  const PlayProgressSlider({
    super.key,
    required this.value,
    required this.max,
    required this.onChanged,
    this.onChangeEnd,
    this.seekAble = false
  });

  final double value;
  final double max;
  final ValueChanged<double> onChanged;
  final ValueChanged<double>? onChangeEnd;
  final bool seekAble;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: AbsorbPointer(
        absorbing: !seekAble,
        child: Row(
          children: [
            SizedBox(
              width: 100.w,
              child: STextTo.playPageSliderValue(
                TextFormate.formateMill2Min(
                  value.round()
                )
              ),
            ),
            Flexible(
              child: Sim2Slider(
                value: value,
                max: max,
                onChanged: onChanged,
                onChangeEnd: onChangeEnd,
                trackHeight: 20.w,
                inactiveTrackColor: _inactiveTrackColor,
                activeTrackColor: _activeTrackColor,
                thumbColor: _activeTrackColor,
                thumbShape: switch (seekAble) {
                  true => RoundSliderThumbShape(
                    enabledThumbRadius: 20.w,
                    disabledThumbRadius: 20.w,
                  ),
                  false => SliderComponentShape.noThumb
                }
              ),
            ),
            SizedBox(
              width: 100.w,
              child: STextTo.playPageSliderValue(
                TextFormate.formateMill2Min(
                  max.round()
                )
              ),
            ),
          ],
        ),
      ),
    );
  }
}
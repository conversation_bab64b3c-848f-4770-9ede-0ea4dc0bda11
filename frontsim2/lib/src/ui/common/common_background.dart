import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:rk_package/rk_package.dart';

import 'sim2_icons.dart';

class CommonBackground extends StatelessWidget {
  const CommonBackground(
      {this.appBar,
      required this.body,
      this.safeAreaTop = true,
      this.safeAreaBottom = true,
      this.playViewMask = false,
      this.padding,
      super.key});

  final PreferredSizeWidget? appBar;
  final Widget body;
  final bool safeAreaTop;
  final bool safeAreaBottom;
  final bool playViewMask;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
              ),
              alignment: Alignment.center,
              child: SvgPicture.asset(
                  width: 840.w, Assets.ASSETS_ICONS_BAGROUND_ICON_SVG)),
        ),
        if (playViewMask)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withAlpha((255 * 0.2).toInt()),
              ),
            ),
          ),
        Scaffold(
          appBar: appBar,
          body: SafeArea(
            top: safeAreaTop,
            bottom: safeAreaBottom,
            child: Padding(
              padding: padding ?? EdgeInsets.symmetric(horizontal: 44.w),
              child: body,
            ),
          ),
          backgroundColor: Colors.transparent,
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';

import '../business/text.dart';

class AndroidPermissionTip extends StatefulWidget {
  const AndroidPermissionTip({super.key, required this.message});

  final String message;

  @override
  State<AndroidPermissionTip> createState() => _AndroidPermissionTipState();
}

class _AndroidPermissionTipState extends State<AndroidPermissionTip> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    //在屏幕大概中偏上30%弹出内容
    return Stack(
      children: [
        Positioned.fill(
            child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(8.0),
          ),
        )),
        Positioned(
          top: MediaQuery.of(context).size.height * 0.3,
          left: 0,
          right: 0,
          child: Align(
              alignment: Alignment.topCenter,
              child: Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  border:  Border.all(
                    color: colorScheme.primary,
                    width: 2.0,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: SText.multiLine(
                  text: widget.message,
                  fontSize: STextSize.xlm,
                  color: colorScheme.primary,
                ),
              )),
        )
      ],
    );
  }
}

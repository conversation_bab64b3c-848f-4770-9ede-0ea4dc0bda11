import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

class GCastBackground extends StatelessWidget {
  const GCastBackground({super.key, required this.child});

  final Widget child;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration:  BoxDecoration(
       color:  const Color(0xBA0F0F0F),
        borderRadius: BorderRadius.all(Radius.circular(50.w)),
      ),
      margin:  EdgeInsets.only(top: 100.w,bottom: 150.w,left: 20.w,right: 20.w),
      child: child,
    );
  }
}

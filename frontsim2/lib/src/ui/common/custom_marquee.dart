import 'dart:async';

import 'package:flutter/material.dart';

class CustomMarquee extends StatefulWidget {
  final String text;
  final TextStyle style;
  final Axis scrollAxis;
  final double? velocity;

  const CustomMarquee({
    super.key,
    required this.text,
    this.style = const TextStyle(fontSize: 14.0),
    this.scrollAxis = Axis.horizontal,
    this.velocity = 50.0,
  });

  @override
  State<CustomMarquee> createState() => _CustomMarqueeState();
}

class _CustomMarqueeState extends State<CustomMarquee> {
  final ScrollController _scrollController = ScrollController();

  String text = '';

  @override
  void initState() {
    super.initState();
    _startMarquee();
  }

  @override
  void didUpdateWidget(covariant CustomMarquee oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text || oldWidget.style != widget.style) {
      // 如果文本或样式发生变化，重新开始动画
      _scrollController.jumpTo(0); // 重置滚动位置

      _startMarquee();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _startMarquee() async {
    setState(() {
      text = widget.text;
    });
    if (widget.text.isEmpty) {
      return;
    }
    //获取当前文本实际可滚动的距离
    final itemDistance = await _getScrollDistance();
    if (itemDistance == 0 || !mounted) {
      return;
    }
    setState(() {
      text = [widget.text, widget.text, widget.text].join(''); // 复制文本三次,保证滑动效果
    });

    //开始滚动
    _animate(itemDistance);
  }

  //获取当前文本实际可滚动的距离
  Future<double> _getScrollDistance() async {
    final Completer<double?> completer = Completer<double?>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) {
        completer.complete(null);
        return;
      }
      final position = _scrollController.position;
      if (position.viewportDimension == 0) {
        completer.complete(null);
      } else {
        completer.complete(position.maxScrollExtent);
      }
    });
    final result = await completer.future;
    if (!mounted) {
      return 0;
    }
    if (result == null) {
      setState(() {});
      return _getScrollDistance();
    } else {
      return result;
    }
  }

  Future<void> _animate(double itemDistance) async {
    //获取已经滚动的距离
    final scrollDistance = _scrollController.position.pixels;
    //需要滚动的距离
    double distance;
    //获取滚动时长
    double duration;
    if (scrollDistance == 0) {
      //如果是第一次滚动,只需要滚iemDistance,也就是文本溢出的距离
      distance = itemDistance;
      duration = itemDistance / (widget.velocity ?? 50);
    } else {
      //滚动到index 1 end
      distance =
          _scrollController.position.viewportDimension + (itemDistance) * 2;
      duration = distance / (widget.velocity ?? 50);
    }

    //滚动到下一项
    _scrollController
        .animateTo(
      distance,
      duration: Duration(milliseconds: (duration * 1000).toInt()),
      curve: Curves.linear,
    )
        .then((_) {
      if (mounted) {
        if (scrollDistance + itemDistance > itemDistance) {
          _scrollController.jumpTo(itemDistance);
        }
        _animate(itemDistance);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.zero,
      controller: _scrollController,
      scrollDirection: widget.scrollAxis,
      physics: const NeverScrollableScrollPhysics(),
      child: Text(
        text,
        style: widget.style,
      ),
    );
  }
}

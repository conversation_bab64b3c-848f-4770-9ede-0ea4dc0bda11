import 'package:base_common/base_common.dart';
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';

import '../business/sim2_slider.dart';
import '../business/text.dart';

final _inactiveTrackColor = Color.fromRGBO(255, 255, 255, 0.2);
final _activeTrackColor = Color.fromRGBO(255, 255, 255, 1);

class VolumeSlider extends StatelessWidget {
  const VolumeSlider({
    required this.value,
    required this.onChanged,
    required this.mute,
    this.setMute,
    this.onChangeEnd,
    super.key,
    this.customEndIcon,
  });

  final double value;
  final bool mute;
  final VoidCallback? setMute;

  final OutValueCallback<double> onChanged;
  final OutValueCallback<double>? onChangeEnd;
  final Widget? customEndIcon;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        OnTapToOpacity(
          onTap: setMute,
          child: switch (mute) {
            true => Sim2Icon(
                path: Assets.ASSETS_ICONS_VOLUME_MUTE_ICON_SVG,
                size: 85.w,
              ),
            false => switch (value) {
                <= 30 => Sim2Icon(
                    path: Assets.ASSETS_ICONS_VOLUME_X_ICON_SVG,
                    size: 85.w,
                  ),
                > 30 && < 60 => Sim2Icon(
                    path: Assets.ASSETS_ICONS_VOLUME_XX_ICON_SVG,
                    size: 85.w,
                  ),
                > 60 => Sim2Icon(
                    path: Assets.ASSETS_ICONS_VOLUME_XXX_ICON_SVG,
                    size: 85.w,
                  ),
                _ => Sim2Icon(
                    path: Assets.ASSETS_ICONS_VOLUME_XXX_ICON_SVG,
                    size: 85.w,
                  )
              }
          },
        ),
        Flexible(
          child: Sim2Slider(
            value: value,
            max: 100,
            onChanged: onChanged,
            onChangeEnd: onChangeEnd,
            trackHeight: 14.w,
            inactiveTrackColor: _inactiveTrackColor,
            activeTrackColor: _activeTrackColor,
            thumbColor: _activeTrackColor,
            thumbShape: RoundSliderThumbShape(
              enabledThumbRadius: 18.w,
              disabledThumbRadius: 18.w,
            ),
          ),
        ),
        customEndIcon ??
            Container(
                width: 120.w,
                alignment: Alignment.center,
                child: Transform.translate(
                  offset: Offset(0, -3.w),
                  child: SText(
                    text: '${value.toInt()}%',
                    color: Colors.white,
                    fontSize: 45.sp,
                    fontFamily: STextFamily.regular,
                  ),
                ))
      ],
    );
  }
}

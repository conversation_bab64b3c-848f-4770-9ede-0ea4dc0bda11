import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';

import '../business/text.dart';

// class Sim2InputContainer extends StatelessWidget {
//   const Sim2InputContainer({
//     super.key,
//     required this.controller,
//     this.autofocus = false,
//     this.inputType = TextInputType.text,
//     this.placeholder = '',
//     this.isPassword = false
//   });

//   final TextEditingController controller;
//   final String placeholder;
//   final bool autofocus;
//   final TextInputType inputType;
//   final bool isPassword;

//   @override
//   Widget build(BuildContext context) {
//     return ;
//   }
// }

class Sim2Input extends StatefulWidget {
  const Sim2Input({
    super.key,
    required this.controller,
    this.isPassword = false,
    this.autofocus = true,
    this.inputType,
    this.placeholder = ''
  });

  final TextEditingController controller;
  final bool isPassword;
  final bool autofocus;
  final TextInputType? inputType;
  final String placeholder;

  @override
  State<Sim2Input> createState() => _Sim2InputState();
}

class _Sim2InputState extends State<Sim2Input> {

  final FocusNode focusNode = FocusNode();
  bool showPsw = false;

  @override
  Widget build(BuildContext context) {
    return Container(
          height: 135.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.w),
            color: Theme.of(context).colorScheme.tertiary,
          ),
          padding: EdgeInsets.only(
            left: 10.w
          ),
          child: Row(
      children: [
        Flexible(
          child: TextField(
            cursorColor: Theme.of(context).colorScheme.primary,
            controller: widget.controller,
            keyboardType: widget.inputType,
            obscureText: switch(widget.isPassword) {
              true => !showPsw,
              false => false,
            },
            focusNode: focusNode,
            autofocus: widget.autofocus,
            decoration: InputDecoration(
              fillColor: Theme.of(context).colorScheme.primary,
              focusColor: Theme.of(context).colorScheme.primary,
              hintText: widget.placeholder,
              hintStyle: TextStyle(
                color: Theme.of(context).colorScheme.primary.withAlpha((255 * 0.5).toInt()),
                fontSize: STextSize.md,
                fontFamily: STextFamily.regular.value,
              ),
              border: InputBorder.none,
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide.none
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide.none
              ),
              isDense: true,
            ),
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: STextSize.mds,
              fontFamily: STextFamily.regular.value,
            ),
          ),
        ),
        OnTapToScale(
            onTap: () {
              setState(() {
                showPsw = !showPsw;
              });
            },
            child: Container(
              width: 60.w,
              height: 60.w,
              alignment: Alignment.center,
              child: Sim2Icon(
                path: Assets.ASSETS_ICONS_CLOSED_EYE_ICON_SVG,
                color: Theme.of(context).colorScheme.primary.withAlpha((255 * (
                  showPsw? 1 : 0.5
                )).toInt()),
                size: 60.w,
              ),
            )
        ).marginOnly(right: 40.w)
      ],
    )
          
    );
  }
}
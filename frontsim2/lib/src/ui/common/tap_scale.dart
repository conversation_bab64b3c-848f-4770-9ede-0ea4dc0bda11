
import 'package:flutter/material.dart';

class OnTapToScale extends StatefulWidget {
  const OnTapToScale({
    super.key,
    required this.child,
    this.onTap,
    this.scale = 0.96,
    this.duration = const Duration(milliseconds: 120),
    this.curve = Curves.easeInOut,
  });

  final Widget child;

  final VoidCallback? onTap;

  final double scale;

  final Duration duration;

  final Curve curve;

  @override
  State<OnTapToScale> createState() => _OnTapToScaleState();
}


class _OnTapToScaleState extends State<OnTapToScale> {
  bool iTap = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTap?.call();
        if (widget.onTap == null) return;
        setState(() {
          iTap = false;
          iTap = true;
        });
        Future.delayed(const Duration(milliseconds: 80)).then((_) {
          setState(() {
            iTap = false;
          });
        });
      },
      onTapDown: (_) {
        if (widget.onTap == null) return;
        setState(() {
          iTap = true;
        });
      },
      onTapCancel: () {
        if (widget.onTap == null) return;
        setState(() {
          iTap = false;
        });
      },
      onTapUp: (_) {
        if (widget.onTap == null) return;
        setState(() {
          iTap = false;
        });
      },
      child: TweenAnimationBuilder<double>(
        tween: Tween(
          begin: 1.0,
          end: iTap ? widget.scale : 1.0,
        ),
        duration: widget.duration,
        curve: widget.curve,
        builder: (_, value, child) => Transform.scale(
          scale: value,
          child: child,
        ),
        child: widget.child,
      ),
    );
  }
}


class OnTapToOpacity extends StatefulWidget {
  const OnTapToOpacity({
    super.key,
    required this.child,
    this.onTap,
    this.opacity = 0.3,
    this.duration = const Duration(milliseconds: 100),
    this.curve = Curves.easeInOut,
  });

  final Widget child;

  final VoidCallback? onTap;

  final double opacity;

  final Duration duration;

  final Curve curve;

  @override
  State<OnTapToOpacity> createState() => _OnTapToOpacityState();
}

class _OnTapToOpacityState extends State<OnTapToOpacity> {
  bool iTap = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTap?.call();
        if (widget.onTap == null) return;
        setState(() {
          iTap = false;
          iTap = true;
        });
        Future.delayed(const Duration(milliseconds: 60)).then((_) {
          setState(() {
            iTap = false;
          });
        });
      },
      onTapDown: (_) {
        if (widget.onTap == null) return;
        setState(() {
          iTap = true;
        });
      },
      onTapCancel: () {
        if (widget.onTap == null) return;
        setState(() {
          iTap = false;
        });
      },
      onTapUp: (_) {
        if (widget.onTap == null) return;
        setState(() {
          iTap = false;
        });
      },
      child: TweenAnimationBuilder<double>(
        tween: Tween(
          begin: 1.0,
          end: iTap ? widget.opacity : 1.0,
        ),
        duration: widget.duration,
        curve: widget.curve,
        builder: (_, value, child) => Opacity(
          opacity: value,
          child: child,
        ),
        child: widget.child,
      ),
    );
  }
}
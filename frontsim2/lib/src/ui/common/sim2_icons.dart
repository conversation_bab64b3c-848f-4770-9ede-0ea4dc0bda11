import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';

export '../../configs/assets_gen.dart';

class Sim2Icon extends StatelessWidget {
  const Sim2Icon({
    super.key,
    required this.path,
    this.size = 30,
    this.color,
    this.colorMapper,
    this.matchTextDirection = false,
    this.fit = BoxFit.contain,
    this.alignment = Alignment.center,
    this.allowDrawingOutsideViewBox = false,
    this.clipBehavior = Clip.hardEdge,
    this.svgTheme,
  });

  // 图片路径
  final String path;
  // 图片大小
  final double size;
  /// 主要修改颜色的手段
  final Color? color;
  /// 颜色映射器
  final ColorMapper? colorMapper;
  /// 如果为 true，则会在 [TextDirection.rtl] 上下文环境中水平翻转图片。
  final bool matchTextDirection;
  /// 如何将图片嵌入到布局过程中分配的空间中。默认值为 [BoxFit.contain]。
  final BoxFit fit;
  /// 图片的显示区域。默认值为 [Alignment.center]。
  final Alignment alignment;
  /// 是否允许在绘制时超出边界。默认值为 false。
  final bool allowDrawingOutsideViewBox;
  /// 裁剪图片，默认值为 [Clip.hardEdge]。
  final Clip clipBehavior;
  /// SVG 主题
  final SvgTheme? svgTheme;

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      path,
      width: size,
      height: size,
      matchTextDirection: matchTextDirection,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      clipBehavior: clipBehavior,
      errorBuilder: (context, error, stackTrace) => Text('error image url'),
      theme: svgTheme,
      colorMapper: colorMapper,
      colorFilter: switch(color) {
        Color() => ColorFilter.mode(color!, BlendMode.srcIn),
        _ => null
      },
    );
  }
}
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/text_class.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';


class Sim2AppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;

  const Sim2AppBar({
    super.key, required this.title, this.reset, this.goToSetting, this.showPop = true,
  });

  final VoidCallback? reset;
  final VoidCallback? goToSetting;
  final bool showPop;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: STextTo.pageTitle(title),
      centerTitle: true,
      leading:showPop? NavBackWidget():Container(),
      actions: [
        if (reset != null)
          OnTapToOpacity(
            onTap: reset,
            child: Container(
              width: 200.w,
              alignment: Alignment.center,
              child: Sim2Icon(
                path: Assets.ASSETS_ICONS_RESET_ICON_SVG,
                color: Theme.of(context).primaryColor,
                size: 90.w,
              ),
            ),
          ),
        if (goToSetting != null)
          OnTapToOpacity(
              onTap: goToSetting,
              child: Container(
                width: 200.w,
                alignment: Alignment.center,
                child: Sim2Icon(
                  path: Assets.ASSETS_ICONS_SETTINGS_ICON_SVG,
                  color: Theme.of(context).primaryColor,
                  size: 65.w,
                ),
              ),
            )
      ],
      toolbarHeight: 200.w,
      backgroundColor: Theme.of(context).colorScheme.primary,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(200.w);
}


class NavBackWidget extends StatefulWidget {
  const NavBackWidget({super.key});

  @override
  State<NavBackWidget> createState() => NavBackWidgetState();
}

class NavBackWidgetState extends State<NavBackWidget> with WidgetsBindingObserver {
  bool _canPop = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _updateRouteState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _updateRouteState() {
    final currentContext = context;
    if (mounted) {
      final newValue = Navigator.of(currentContext).canPop();
      if (newValue != _canPop) {
        setState(() {
          _canPop = newValue;
        });
      }
    }
  }

  @override
  Future<bool> didPopRoute() async {
    _updateRouteState();
    return super.didPopRoute();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _canPop ? OnTapToOpacity(
          onTap: () => Get.back(),
          child: Container(
            color: Colors.transparent,
            alignment: Alignment.center,
            child: Sim2Icon(
              path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
              color: Theme.of(context).primaryColor,
              size: 68.w,
            ),
          )
        )
      : null,
    );
  }
}
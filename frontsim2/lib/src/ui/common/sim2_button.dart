
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';

class Sim2Button extends StatelessWidget {
  const Sim2Button({
    super.key,
    required this.text,
    this.onTap,
    this.color
  });

  final String text;
  final VoidCallback? onTap;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return OnTapToScale(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(
          vertical: 25.w,
          horizontal: 90.w
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.w),
          color: color ?? Theme.of(context).colorScheme.surface
        ),
        child: SText(
          text: text,
          fontSize: STextSize.md,
          color: Theme.of(context).colorScheme.primary
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';

class FutureLoadingWrap<T> extends StatelessWidget {
  const FutureLoadingWrap(
      {required this.future,
      required this.valueBuilder,
      this.loadingWidget = const CommonLoading(),
      this.errorBuilder,
      super.key});

  final Future<T>? future;

  final FutureValueBuilder<T?> valueBuilder;

  final Widget loadingWidget;

  final FutureErrorBuilder? errorBuilder;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<T>(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return loadingWidget;
        }
        if (snapshot.hasError) {
          return errorBuilder != null ? 
            errorBuilder!(snapshot.error!, context)
            : Center(
            child: Text(
              'Error: ${snapshot.error}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }
        return valueBuilder(
          snapshot.data,
          context,
        );
      },
    );
  }
}

class CommonLoading extends StatelessWidget {
  const CommonLoading({super.key});

  @override
  Widget build(BuildContext context) {
    //todo 自定义loading
    return Center(
      child: CircularProgressIndicator(
        color: Theme.of(context).primaryColor,
      ),
    );
  }
}

typedef FutureValueBuilder<T> = Widget Function(T value, BuildContext context);
typedef FutureErrorBuilder = Widget Function(
    Object error, BuildContext context);

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/configs/theme.dart';


class Sim2Switch extends StatelessWidget {
  const Sim2Switch({
    super.key,
    required this.value,
    this.size,
    this.onChanged,
  });

  /// 组件大小，相对于高度比例计算
  final double? size;

  /// 默认大小
  double get sizeValue => size ?? 73.w;

  /// 值
  final RxBool value;
  
  /// 改变回调
  final Function(bool value)? onChanged;

  /// 宽高比例 设计稿 (147 * 73) ≈ (2.013)
  final double _aspectRatio = 2.013;

  /// 按钮比例 (56/73 ≈ 0.767)
  final double _knobRatio = 0.767;

  /// 间隔比例 (0.233)
  final double _kspace = 0.233;

  @override
  Widget build(BuildContext context) {
    return Obx(()=> GestureDetector(
      onTap: () {
        value.value = !value.value;
        if (onChanged != null) {
          onChanged!(value.value);
        }
      },
      child: AnimatedContainer(
        width: sizeValue * _aspectRatio,
        height: sizeValue,
        padding: EdgeInsets.symmetric(
          horizontal: sizeValue * (_kspace / 2),
        ),
        alignment: value.value? Alignment.centerRight : Alignment.centerLeft,
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(sizeValue),
          color: value.value? Sim2SwitchThemeExt.of(context)!.activeBgColor : Sim2SwitchThemeExt.of(context)!.unactiveBgColor,
        ),
        child: Container(
          width: sizeValue * _knobRatio,
          height: sizeValue * _knobRatio,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(sizeValue * _knobRatio),
            color: Sim2SwitchThemeExt.of(context)!.circleColor,
          ),
        )
      ),
    ));
  }
  
}

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart' as rk_package;

import 'sim2_slider_shape.dart';

class Sim2Slider extends StatelessWidget {
  const Sim2Slider({
    super.key,
    required this.value,
    this.max,
    this.onChanged,
    this.onChangeEnd,
    this.margin,
    this.trackHeight,
    this.activeTrackColor,
    this.inactiveTrackColor,
    this.thumbColor,
    this.thumbShape,
    this.space
  });

  final double value;
  final double? max;
  final ValueChanged<double>? onChanged;
  final ValueChanged<double>? onChangeEnd;

  final EdgeInsets? margin;
  final double? trackHeight;

  final Color? activeTrackColor;
  final Color? inactiveTrackColor;

  final Color? thumbColor;
  final SliderComponentShape? thumbShape;

  final double? space;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.symmetric(
        horizontal: 35.w
      ),
      child: SliderTheme(
        data: SliderThemeData(
          activeTrackColor: activeTrackColor,
          inactiveTrackColor: inactiveTrackColor,
          trackHeight: trackHeight ?? 20.w,
          trackShape: const TrackHeightSliderTrackShape(),
          thumbColor: thumbColor,
          thumbShape: thumbShape,
          overlayShape: RoundSliderOverlayShape(
            overlayRadius: space ?? 70.w
          ),
          overlayColor: Colors.transparent,
        ),
        child: Slider(
          value: value,
          min: 0,
          max: max ?? 100,
          onChanged: onChanged,
          onChangeEnd: onChangeEnd,
        )
      ),
    );
  }
}
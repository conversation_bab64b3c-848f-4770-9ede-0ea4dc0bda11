
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';

class Unfold extends StatelessWidget {
  const Unfold({
    super.key,
    required this.on,
    this.onTap,
  });

  final RxBool on;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return OnTapToScale(
      onTap: () {
        on.value = !on.value;
        onTap?.call();
      },
      child: Obx(()=> Container(
        width: 126.w,
        height: 65.w,
        decoration: BoxDecoration(
          color: Color.fromRGBO(213, 116, 64, 1),
          borderRadius: BorderRadius.circular(65.w),
        ),
        child: Center(
          child: AnimatedRotation(
            turns: on.value ? 0.25 : 0.75,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            child: Sim2Icon(
              path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
              size: 48.w,
            ),
          ),
        ),
      )),
    );
  }
}
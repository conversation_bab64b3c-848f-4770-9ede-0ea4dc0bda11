import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import '../common/custom_marquee.dart';
import 'text.dart';

/// 格式是 组件名 + 名字
class STextTo {
  static Widget pageTitle(String text) {
    return Builder(
      builder: (context) {
        return SText(
          text: text,
          fontSize: STextSize.xlm,
          fontFamily: STextFamily.bold,
          color: Theme.of(context).primaryColor,
        );
      },
    );
  }

  /// 设备名字
  static Widget deviceItemTitle(String text) {
    return Builder(
      builder: (context) {
        return SText(
          text: text,
          fontSize: STextSize.xl,
          fontFamily: STextFamily.bold,
          color: Theme.of(context).colorScheme.primary,
        );
      },
    );
  }

  /// 设备当前播放的歌曲名
  static Widget deviceItemSongName(String text) {
    return Builder(
      builder: (context) {
        return SText(
          text: text,
          fontSize: STextSize.mdlz,
          fontFamily: STextFamily.regular,
          color: Theme.of(context).colorScheme.primary,
        );
      },
    );
  }

  /// 设备当前播放的歌手和专辑名
  static Widget deviceItemSinger(String text) {
    return Builder(
      builder: (context) {
        return SText(
          text: text,
          fontSize: STextSize.md,
          fontFamily: STextFamily.thin,
          color: Theme.of(context).colorScheme.primary,
        );
      },
    );
  }

  /// 设备输入源信息
  static Widget deviceItemInputSource(String text) {
    return Builder(
      builder: (context) {
        return SText(
          text: text,
          fontSize: STextSize.xls,
          fontFamily: STextFamily.thin,
          color: Theme.of(context).colorScheme.primary,
        );
      },
    );
  }

  /// 播放页面的歌名
  static Widget playPageSongName(String text) {
    return Builder(
      builder: (context) {
        return CustomMarquee(
          text: text,
          style: TextStyle(
            fontSize: STextSize.xlm,
            fontFamily: STextFamily.bold.value,
            color: Theme.of(context).colorScheme.primary,
          ),
        );
      },
    );
  }

  /// 播放页面的歌手名
  static Widget playPageSinger(String text) {
    return Builder(
      builder: (context) {
        return CustomMarquee(
          text: text,
          style: TextStyle(
            fontSize: STextSize.md,
            fontFamily: STextFamily.regular.value,
            color: Theme.of(context).colorScheme.primary,
          ),
        );
      },
    );
  }

  /// 播放页面的采样率
  static Widget playPageSampleRate(String text) {
    return Builder(
      builder: (context) {
        return SText(
          text: text,
          fontSize: STextSize.sm,
          fontFamily: STextFamily.thin,
          color: Theme.of(context).colorScheme.primary,
        );
      },
    );
  }

  /// 播放页面的进度条值
  static Widget playPageSliderValue(String text) {
    return Builder(
      builder: (context) {
        return SText(
          text: text,
          color: Colors.white,
          fontSize: STextSize.mds,
          fontFamily: STextFamily.regular,
        );
      },
    );
  }

  /// 设备设置块的标题
  static Widget deviceSettingBlockTitle(String text) {
    return SText(
      text: text,
      fontSize: STextSize.md,
      fontFamily: STextFamily.bold,
    );
  }

  /// 设备设置项的标题
  static Widget deviceSettingItemTitle(String text) {
    return SText(
      text: text,
      fontSize: STextSize.md,
      fontFamily: STextFamily.regular,
    );
  }

  /// 设备设置项的描述信息
  static Widget deviceSettingItemInfo(RxString text) {
    return SText(
      text: text.value,
      fontSize: STextSize.md,
      fontFamily: STextFamily.thin,
    );
  }

  /// 设备时区设置项
  static Widget timeZoneItem(RxString text) {
    return SText(
      text: text.value,
      fontSize: STextSize.md,
      fontFamily: STextFamily.thin,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:rk_get/rk_get.dart';
import 'package:sim2/src/configs/log.dart';
import 'package:sim2/src/services/devices_service.dart';
import 'package:sim2/src/helpers/device_reconnect_strategy.dart';
import 'package:sim2/src/models/app_lifecycle_config.dart';
import 'dart:async';

/// 应用生命周期观察者
/// 负责监听应用的前台/后台状态变化
/// 当应用进入后台时断开所有设备连接
/// 当应用恢复前台时重新连接设备
class AppLifecycleObserver extends WidgetsBindingObserver {
  static AppLifecycleObserver? _instance;
  late final LibreDevicesService _devicesService;
  
  /// 配置对象
  AppLifecycleConfig _config = AppLifecycleConfig.defaultConfig;
  
  /// 设备连接状态缓存，用于恢复连接
  List<String> _disconnectedDeviceIPs = [];
  
  /// 是否处于后台状态
  bool _isInBackground = false;
  
  /// 断连定时器
  Timer? _disconnectTimer;
  
  /// 重连定时器
  Timer? _reconnectTimer;

  AppLifecycleObserver._internal() {
    _devicesService = Get.find<LibreDevicesService>();
  }

  /// 获取单例实例
  static AppLifecycleObserver get instance {
    _instance ??= AppLifecycleObserver._internal();
    return _instance!;
  }
  /// 初始化生命周期观察者
  void init([AppLifecycleConfig? config]) {
    if (config != null) {
      _config = config;
    }
    WidgetsBinding.instance.addObserver(this);
    log.logInformation('AppLifecycleObserver initialized with config: enableAutoDisconnect=${_config.enableAutoDisconnect}, enableAutoReconnect=${_config.enableAutoReconnect}');
  }

  /// 销毁生命周期观察者
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _disconnectTimer?.cancel();
    _reconnectTimer?.cancel();
    _instance = null;
    log.logInformation('AppLifecycleObserver disposed');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.inactive:
        // 应用处于非活动状态（如接听电话时），暂不处理
        log.logDebug('App state changed to inactive');
        break;
      case AppLifecycleState.detached:
        // 应用即将销毁，确保清理资源
        _onAppDetached();
        break;
      case AppLifecycleState.hidden:
        // iOS特有状态，应用被隐藏但未进入后台
        log.logDebug('App state changed to hidden');
        break;
    }
  }
  /// 应用恢复前台
  void _onAppResumed() {
    log.logInformation('App resumed from background');
    
    // 取消断连定时器
    _disconnectTimer?.cancel();
    _disconnectTimer = null;
    
    if (_isInBackground && _config.enableAutoReconnect) {
      _isInBackground = false;
      
      // 延迟重连
      if (_config.reconnectDelaySeconds > 0) {
        log.logDebug('Scheduling reconnect in ${_config.reconnectDelaySeconds}s');
        _reconnectTimer = Timer(Duration(seconds: _config.reconnectDelaySeconds), () {
          _reconnectDevices();
        });
      } else {
        _reconnectDevices();
      }
    } else {
      _isInBackground = false;
    }
  }

  /// 应用进入后台
  void _onAppPaused() {
    log.logInformation('App paused to background');
    
    // 取消重连定时器
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
    
    if (!_isInBackground && _config.enableAutoDisconnect) {
      _isInBackground = true;
      
      // 延迟断连
      if (_config.disconnectDelaySeconds > 0) {
        log.logDebug('Scheduling disconnect in ${_config.disconnectDelaySeconds}s');
        _disconnectTimer = Timer(Duration(seconds: _config.disconnectDelaySeconds), () {
          _disconnectAllDevices();
        });
      } else {
        _disconnectAllDevices();
      }
    } else {
      _isInBackground = true;
    }
  }
  /// 应用即将销毁
  void _onAppDetached() {
    log.logInformation('App is being detached');
    _disconnectTimer?.cancel();
    _reconnectTimer?.cancel();
    if (_config.enableAutoDisconnect) {
      _disconnectAllDevices();
    }
  }

  /// 断开所有设备连接
  Future<void> _disconnectAllDevices() async {
    try {
      // 清空之前的缓存
      _disconnectedDeviceIPs.clear();
      
      // 获取当前所有连接的设备IP
      final connectedDevices = List<String>.from(
        _devicesService.devices.map((device) => device.deviceIp)
      );
      
      if (connectedDevices.isEmpty) {
        log.logDebug('No devices to disconnect');
        return;
      }

      log.logInformation('Disconnecting ${connectedDevices.length} devices');
      
      // 缓存要断开的设备IP，用于后续重连
      _disconnectedDeviceIPs.addAll(connectedDevices);
      
      // 逐个断开设备连接
      for (final deviceIP in connectedDevices) {
        try {
          await _devicesService.destroyDevice(deviceIP);
          log.logDebug('Device $deviceIP disconnected successfully');
        } catch (e) {
          log.logError('Failed to disconnect device $deviceIP: $e');
        }
      }
      
      // 清空设备列表
      _devicesService.devices.clear();
      
      log.logInformation('All devices disconnected successfully');
    } catch (e) {
      log.logError('Error disconnecting devices: $e');
    }
  }
  /// 重新连接设备
  Future<void> _reconnectDevices() async {
    try {
      if (_disconnectedDeviceIPs.isEmpty) {
        log.logDebug('No devices to reconnect');
        return;
      }

      // 过滤可重连的设备
      final reconnectableDevices = DeviceReconnectStrategy.filterReconnectableDevices(_disconnectedDeviceIPs);
      
      if (reconnectableDevices.isEmpty) {
        log.logDebug('No reconnectable devices found');
        _disconnectedDeviceIPs.clear();
        return;
      }

      log.logInformation('Attempting to reconnect ${reconnectableDevices.length} devices');
      
      // 使用智能重连策略
      final reconnectedDevices = await DeviceReconnectStrategy.executeReconnect(
        deviceIPs: reconnectableDevices,
        scanCallback: () => _devicesService.scan(),
        getConnectedDevices: () => _devicesService.devices
            .map((device) => device.deviceIp)
            .toList(),
      );
      
      if (reconnectedDevices.isNotEmpty) {
        log.logInformation('Successfully reconnected ${reconnectedDevices.length} devices: $reconnectedDevices');
      }
      
      // 从待重连列表中移除已成功重连的设备
      _disconnectedDeviceIPs.removeWhere((ip) => reconnectedDevices.contains(ip));
      
      // 如果还有设备未重连成功，记录警告
      if (_disconnectedDeviceIPs.isNotEmpty) {
        log.logWarning('Some devices failed to reconnect: $_disconnectedDeviceIPs');
      }
      
    } catch (e) {
      log.logError('Error reconnecting devices: $e');
    }
  }

  /// 手动触发断开所有设备（用于测试或特殊场景）
  Future<void> manualDisconnectAll() async {
    log.logInformation('Manual disconnect all devices triggered');
    await _disconnectAllDevices();
  }

  /// 手动触发重连设备（用于测试或特殊场景）
  Future<void> manualReconnectDevices() async {
    log.logInformation('Manual reconnect devices triggered');
    await _reconnectDevices();
  }  /// 获取当前后台状态
  bool get isInBackground => _isInBackground;

  /// 获取等待重连的设备数量
  int get pendingReconnectDevicesCount => _disconnectedDeviceIPs.length;
  
  /// 获取当前配置
  AppLifecycleConfig get config => _config;
  
  /// 更新配置
  void updateConfig(AppLifecycleConfig config) {
    _config = config;
    log.logInformation('AppLifecycleObserver config updated');
  }
}


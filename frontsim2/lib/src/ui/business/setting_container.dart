import 'dart:math';

import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/ui/business/sim2_switch.dart';
import 'package:sim2/src/ui/business/text.dart';
import 'package:sim2/src/ui/business/the_unfold.dart';
import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/tap_scale.dart';

class SettingBlock extends StatelessWidget {
  const SettingBlock({
    super.key,
    required this.children,
    this.on,
    this.title = '',
    this.showTitle = true,
    this.margin,
  });

  final RxBool? on;
  final String title;
  final List<SettingBlockChild> children;
  final bool showTitle;
  final EdgeInsets? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      margin: margin ??
          EdgeInsets.symmetric(
            vertical: 24.w,
            // horizontal: 64.w,
          ),
      child: Column(
        children: [
          // --- title ---
          switch (showTitle && on != null) {
            true => Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: EdgeInsets.only(left: 20.w),
                      child: SText(
                        text: title,
                        fontSize: 50.sp,
                        fontFamily: STextFamily.bold,
                      ),
                    ),
                    Unfold(on: on ?? false.obs)
                  ],
                ),
              ),
            false => SizedBox.shrink(),
          },
          // --- content ---
          switch (on != null) {
            /// 显示内容
            true => Obx(() => AnimatedOpacity(
                  opacity: on!.value ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 150),
                  curve: Curves.easeInOut,
                  child: AnimatedContainer(
                      height:
                          on!.value ? (140.w * children.length) + 20.w : 0.0,
                      margin: EdgeInsets.only(top: 35.w),
                      padding: EdgeInsets.symmetric(
                          horizontal: 44.w, vertical: 10.w),
                      duration: Duration(milliseconds: 150),
                      curve: Curves.easeInOut,
                      decoration: BoxDecoration(
                          color: Color.fromRGBO(15, 15, 15, .7),
                          borderRadius: BorderRadius.circular(52.w)),
                      child: _buildChild(children)),
                )),

            /// 不需要折叠
            false => Container(
                margin: EdgeInsets.only(top: 35.w),
                height: (140.w * children.length) + 20.w,
                padding: EdgeInsets.symmetric(horizontal: 44.w, vertical: 10.w),
                decoration: BoxDecoration(
                    color: Color.fromRGBO(15, 15, 15, .7),
                    borderRadius: BorderRadius.circular(52.w)),
                child: _buildChild(children)),
          }
        ],
      ),
    );
  }

  Widget _buildChild(List<SettingBlockChild> children) {
    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final SettingBlockChild item = children[index];
        final bool ilast = (index == children.length - 1);

        return Container(
          height: 140.w,
          alignment: Alignment.center,
          child: Row(
            children: [
              switch (item.rssi) {
                null => switch (item.customIcon) {
                    null => switch (item.iconPath.isEmpty) {
                        true => Container(
                            margin: EdgeInsets.only(left: 12.w),
                          ),
                        false => Container(
                            margin: EdgeInsets.only(right: 40.w),
                            child: Transform.translate(
                              offset: Offset(0, 5.w),
                              child: Sim2Icon(
                                path: item.iconPath,
                                size: 60.w,
                              ),
                            ),
                          ),
                      },
                    _ => item.customIcon!
                  },
                int _ => Container(
                    margin: EdgeInsets.only(right: 40.w),
                    child: switch (item.rssi!) {
                      >= -60 => Sim2Icon(
                          path: Assets.ASSETS_ICONS_WIFI_XXXX_ICON_SVG,
                          size: 70.w,
                        ),
                      >= -70 => Sim2Icon(
                          path: Assets.ASSETS_ICONS_WIFI_XXX_ICON_SVG,
                          size: 70.w,
                        ),
                      >= -85 => Sim2Icon(
                          path: Assets.ASSETS_ICONS_WIFI_XX_ICON_SVG,
                          size: 70.w,
                        ),
                      >= -140 => Sim2Icon(
                          path: Assets.ASSETS_ICONS_WIFI_X_ICON_SVG,
                          size: 70.w,
                        ),
                      int() => SizedBox.shrink(),
                    },
                  ),
              },
              // --- title ---
              Flexible(
                child: Container(
                  height: double.maxFinite,
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                          color: ilast ? Colors.transparent : Colors.white,
                          width: 0.5.w),
                    ),
                  ),
                  child: OnTapToOpacity(
                      onTap: item.onTap ?? item.navigateTo,
                      child: Container(
                        color: Colors.transparent,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              margin: EdgeInsets.only(right: 20.w),
                              child: SText(
                                text: item.title,
                                color: Colors.white,
                                fontFamily: STextFamily.regular,
                                fontSize: 45.sp,
                              ),
                            ),
                            Flexible(
                                child: switch (item.type) {
                              SettingBlockType.info => Obx(() => SText(
                                    text: item.info?.value ?? '',
                                    fontFamily: STextFamily.thin,
                                    color: Colors.white,
                                    fontSize: 40.w,
                                  )),
                              SettingBlockType.switcher =>
                                item.switcher ?? SizedBox.shrink(),
                              SettingBlockType.navgation => SizedBox(
                                  child: Transform.rotate(
                                    angle: pi / 1,
                                    child: Sim2Icon(
                                      path: Assets.ASSETS_ICONS_BACK_ICON_SVG,
                                      size: 48.w,
                                    ),
                                  ),
                                ),
                              SettingBlockType.selected => Obx(() => Visibility(
                                    visible: item.selected?.call() ?? false,
                                    child: Container(
                                      margin: EdgeInsets.only(right: 20.w),
                                      child: Sim2Icon(
                                        path:
                                            Assets.ASSETS_ICONS_ARROW_ICON_SVG,
                                        size: 48.w,
                                      ),
                                    ),
                                  )),
                              SettingBlockType.wifi => Visibility(
                                  visible: item.wifiLock ?? false,
                                  child: Sim2Icon(
                                    path: Assets.ASSETS_ICONS_LOCK_ICON_SVG,
                                    size: 65.w,
                                  ),
                                ),
                              SettingBlockType.none => SizedBox.shrink(),
                            })
                          ],
                        ),
                      )),
                ),
              ),
            ],
          ),
        );
      },
      itemCount: children.length,
    );
  }
}

/// 表示设置块中的子项，根据不同的类型有不同的表现形式。
class SettingBlockChild {
  /// 创建一个用于显示信息的设置块子项。
  ///
  /// [title] 是该子项显示的标题，必须提供。
  /// [iconPath] 是该子项显示的图标路径，默认为空字符串。
  /// [info] 是该子项显示的信息，类型为 `RxString`，可选参数。
  /// [onTap] 是该子项被点击时触发的回调函数，可选参数。
  SettingBlockChild.info({
    required this.title,
    this.iconPath = '',
    this.info,
    this.onTap,
  });

  /// 创建一个包含开关组件的设置块子项。
  ///
  /// [title] 是该子项显示的标题，必须提供。
  /// [switcher] 是该子项包含的开关组件，类型为 `Sim2Switch`，必须提供。
  /// [iconPath] 是该子项显示的图标路径，默认为空字符串。
  SettingBlockChild.switcher({
    required this.title,
    required this.switcher,
    this.iconPath = '',
  });

  /// 创建一个用于导航的设置块子项。
  ///
  /// [title] 是该子项显示的标题，必须提供。
  /// [navigateTo] 是该子项被点击时触发的导航回调函数，必须提供。
  /// [iconPath] 是该子项显示的图标路径，默认为空字符串。
  SettingBlockChild.navigation({
    required this.title,
    required this.navigateTo,
    this.iconPath = '',
  });

  /// 创建一个用于表示选中状态的设置块子项。
  ///
  /// [title] 是该子项显示的标题，必须提供。
  /// [onTap] 是该子项被点击时触发的回调函数，必须提供。
  /// [selected] 用于判断该子项是否被选中的回调函数，必须提供。
  /// [iconPath] 是该子项显示的图标路径，默认为空字符串。
  SettingBlockChild.selected({
    required this.title,
    required this.onTap,
    required this.selected,
    this.iconPath = '',
  });

  SettingBlockChild.wifi({
    required this.title,
    required this.onTap,
    required this.rssi,
    required this.wifiLock,
    this.iconPath = '',
  });

  /// 创建一个无特殊功能的设置块子项。
  ///
  /// [title] 是该子项显示的标题，必须提供。
  /// [iconPath] 是该子项显示的图标路径，默认为空字符串。
  /// [onTap] 是该子项被点击时触发的回调函数，可选参数。
  SettingBlockChild.none({
    required this.title,
    this.iconPath = '',
    this.selected,
    this.onTap,
  });

  /// 创建一个自定义图标的设置块子项。
  SettingBlockChild.customIcon({
    required this.title,
    required this.customIcon,
    this.navigateTo,
  }) : iconPath = '';

  /// 该子项显示的标题。
  final String title;

  /// 该子项显示的图标路径。
  String iconPath;

  /// 根据子项的属性判断其类型。
  ///
  /// 如果 `navigateTo` 不为空，则返回 `SettingBlockType.navgation`；
  /// 如果 `switcher` 不为空，则返回 `SettingBlockType.switcher`；
  /// 如果 `info` 不为空，则返回 `SettingBlockType.info`；
  /// 否则返回 `SettingBlockType.none`。
  SettingBlockType get type {
    if (navigateTo != null) {
      return SettingBlockType.navgation;
    } else if (switcher != null) {
      return SettingBlockType.switcher;
    } else if (info != null) {
      return SettingBlockType.info;
    } else if (selected != null) {
      return SettingBlockType.selected;
    } else if (rssi != null) {
      return SettingBlockType.wifi;
    } else {
      return SettingBlockType.none;
    }
  }

  /// 该子项显示的信息，类型为 `RxString`。
  RxString? info;

  /// 该子项包含的开关组件，类型为 `Sim2Switch`。
  Sim2Switch? switcher;

  /// 该子项被点击时触发的导航回调函数。
  VoidCallback? navigateTo;

  /// 该子项被点击时触发的回调函数。
  VoidCallback? onTap;

  /// 该子项是否被选中，类型为 `RxBool`。
  bool Function()? selected;

  ///
  int? rssi;

  ///
  bool? wifiLock;

  /// 自定义图标,这个为空才会使用iconPath
  Widget? customIcon;
}

/// 表示设置块子项的类型。
enum SettingBlockType {
  /// 显示信息的类型。
  info,

  /// 包含开关组件的类型。
  switcher,

  /// 用于导航的类型。
  navgation,

  /// 选中的类型
  selected,

  /// wifi的类型
  wifi,

  /// 无特殊功能的类型。
  none,
}

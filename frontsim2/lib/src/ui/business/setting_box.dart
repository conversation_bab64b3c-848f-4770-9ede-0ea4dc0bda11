import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import 'text.dart';

class SettingBox extends StatelessWidget {
  const SettingBox({super.key, this.title, required this.children});

  final String? title;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Visibility(
          visible: title != null,
          child: Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(bottom: 9.w, left: 17.w),
            child: SText(
              text: title ?? '',
              fontFamily: STextFamily.regular,
              // color: themeBase.primaryColor.value.withOpacity(0.7),
              fontSize: STextSize.md,
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14.w),
            color: const Color(0xAB0f0f0f)
          ),
          child: Column(
            children: children,
          ),
        ),
        SizedBox(
          height: 16.w,
        )
      ],
    );
  }
}

class SetBox2 extends StatelessWidget {
  const SetBox2(
      {super.key,
      this.icon,
      required this.title,
      required this.info,
      this.showSide = true,
      this.last = false,
      this.onTap,
      this.iconInfo,
      this.active = false
      // this.disable
      });

  final Widget? icon;
  final String title;
  final String info;
  final bool showSide;
  final bool last;
  final Function? onTap;
  final Widget? iconInfo;
  final bool active;
  // final RxBool? disable;

  @override
  Widget build(BuildContext context) {
    final RxBool tap = false.obs;

    return GestureDetector(
      onTap: () {
        // if(disable != null && disable!.value == true) return;
        if (onTap != null) {
          onTap!();
          tap.value = true;
          Future.delayed(
              const Duration(milliseconds: 50), () => tap.value = false);
        }
      },
      onTapDown: (details) {
        // if(disable != null && disable!.value == true) return;
        tap.value = true;
      },
      onTapUp: (details) {
        // if(disable != null && disable!.value == true) return;
        tap.value = false;
      },
      onTapCancel: () {
        // if(disable != null && disable!.value == true) return;
        tap.value = false;
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(14.w),
        child: Obx(() => Container(
              height: 120.w,
              color: active
                  ? Colors.black.withAlpha(10)
                  : onTap != null && tap.value
                      ? Colors.black.withAlpha(10)
                      : Colors.transparent,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Visibility(
                      visible: icon != null,
                      child: SizedBox(
                          width: 45.w,
                          height: 45.w,
                          child: Transform.translate(
                            offset: Offset(0, !last ? 2.w : 0),
                            child: icon,
                          ))),
                  Flexible(
                      child: Container(
                    margin: EdgeInsets.only(top: !last ? 1.w : 0),
                    decoration: BoxDecoration(
                        border: !last
                            ? Border(
                                bottom: BorderSide(
                                    width: 0.1.w,
                                    color: Colors.black.withAlpha(70)))
                            : null),
                    child: Row(
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          margin:
                              EdgeInsets.only(left: icon == null ? 12.w : 0),
                          child: SText(
                            text: title,
                            fontFamily: STextFamily.regular,
                            // color: themeBase.primaryColor.value,
                            fontSize: STextSize.md,
                          ),
                        ),
                        Flexible(
                            // flex: 10,
                            child: Container(
                          margin: EdgeInsets.only(right: 12.w),
                          alignment: Alignment.centerRight,
                          child: iconInfo ??
                              SText(
                                text: info,
                                fontFamily: STextFamily.regular,
                                // color: themeBase.primaryColor.value.withOpacity(.7),
                                fontSize: STextSize.md,
                              ),
                        ))
                      ],
                    ),
                  ))
                ],
              ),
            )),
      ),
    );
  }
}

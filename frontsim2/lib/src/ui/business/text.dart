import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

enum STextFamily {
  thin('25Th'), // 细体
  regular('45Lt'), // 常规
  bold('65Md'); // 粗体

  final String value;
  const STextFamily(this.value);
}

class STextSize {
  /// 小小小号
  static double get xs => 29.sp;
  /// 小小号
  static double get xls => 32.sp;
  /// 小号
  static double get sm => 35.sp;
  /// 标准号 稍小
  static double get mds => 42.sp;
  /// 标准号 常用
  static double get md => 45.sp;
  /// 标准号 稍大
  static double get mdx => 48.sp;
  /// 标准号 2号稍大
  static double get mdlz => 50.sp;
  /// 大号 1
  static double get xl => 54.sp;
  /// 大号 2
  static double get xlm => 60.sp;
  /// 大号 3
  static double get xxl => 68.sp;
}


class SText extends StatelessWidget {
  const SText({
    super.key,
    required this.text,
    this.fontSize,
    this.color,
    this.letterSpacing,
    this.softWrap = false,
    this.fontFamily = STextFamily.regular,
    this.overflow = TextOverflow.ellipsis,
    this.textAlign,
  });

  const SText.multiLine({
    super.key,
    required this.text,
    this.fontSize,
    this.color,
    this.letterSpacing,
    this.softWrap = false,
    this.fontFamily = STextFamily.regular,
    this.textAlign,
  }):overflow=null;

  final String text;

  final double? fontSize;

  final STextFamily fontFamily;

  final Color? color;

  final TextOverflow? overflow;

  final bool softWrap;

  final double? letterSpacing;

  final TextAlign? textAlign;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      softWrap: softWrap,
      overflow: overflow,
      textAlign: textAlign ?? TextAlign.start,
      style: TextStyle(
        fontSize: fontSize ?? STextSize.md,
        fontFamily: fontFamily.value,
        letterSpacing: letterSpacing ?? 0.5,
        color: color,
      ),
    );
  }
}


import 'package:flutter/material.dart';
class TrackHeightSliderTrackShape extends SliderTrackShape with BaseSliderTrackShape {
  const TrackHeightSliderTrackShape();

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final trackHeight = sliderTheme.trackHeight ?? 0;
    return Rect.fromLTWH(
      offset.dx,
      offset.dy + (parentBox.size.height - trackHeight) / 2,
      parentBox.size.width,
      trackHeight,
    );
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isEnabled = false,
    bool isDiscrete = false,
    required TextDirection textDirection,
  }) {
    final trackHeight = sliderTheme.trackHeight ?? 0;
    final activePaint = Paint()..color = sliderTheme.activeTrackColor!;
    final inactivePaint = Paint()..color = sliderTheme.inactiveTrackColor!;
    final secondaryActivePaint = Paint()..color = sliderTheme.secondaryActiveTrackColor!;
    
    // 获取轨道矩形区域
    final trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    // 先整体绘制非活动轨道
    context.canvas.drawRRect(
      RRect.fromRectAndRadius(trackRect, Radius.circular(trackHeight / 2)),
      inactivePaint,
    );

    // 绘制活动区域覆盖层
    final activeRect = Rect.fromLTRB(
      trackRect.left,
      trackRect.top,
      thumbCenter.dx,
      trackRect.bottom,
    );

    // 绘制active -> secondaryActive区域
    if(secondaryOffset != null) {
      final secondaryActiveRect = Rect.fromLTRB(
        thumbCenter.dx,
        trackRect.top,
        secondaryOffset.dx,
        trackRect.bottom,
      );
      context.canvas.drawRRect(
        RRect.fromRectAndRadius(secondaryActiveRect, Radius.circular(trackHeight / 2)),
        secondaryActivePaint,
      );
    }

    context.canvas.drawRRect(
      RRect.fromRectAndRadius(activeRect, Radius.circular(trackHeight / 2)),
      activePaint,
    );
  }
}
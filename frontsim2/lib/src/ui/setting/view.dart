import 'package:flutter/material.dart';
import 'package:rk_get/rk_get.dart';
import 'package:sim2/src/ui/common/common_background.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../../configs/local.dart';
import '../../routes/route_config.get_x_router_config.dart';
import '../business/setting_container.dart';
import 'logic.dart';

class SettingView extends GetView<SettingLogic> {
  const SettingView({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
        appBar: Sim2AppBar(
          title: Local.setting.setting,
        ),
        body: Column(
          children: [
            SettingBlock(
              title: Local.setting.setting,
              on: true.obs,
              showTitle: false,
              children: [
                SettingBlockChild.info(
                  title: Local.setting.app_version,
                  iconPath: '',
                  info: controller.appVersion,
                  onTap: () {
                    //todo check for update
                  },
                ),
                SettingBlockChild.info(
                  title: Local.setting.setting_language,
                  iconPath: '',
                  info: localStr(Localizations.localeOf(context).languageCode)
                      .obs,
                  onTap: () {
                    Get.toTyped(
                      SetLanguageViewTypedRoute(),
                    );
                  },
                )
              ],
            ),
          ],
        ));
  }
}

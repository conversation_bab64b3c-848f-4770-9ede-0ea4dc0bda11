import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/global/global_store.dart';

import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../../configs/local.dart';
import '../../business/setting_container.dart';
import '../../common/common_background.dart';
import '../../common/sim2_nav.dart';
import 'logic.dart';

@getXRoute
class SetLanguageView extends GetView<SetLanguageLogic> {
  const SetLanguageView({super.key});

  @override
  Widget build(BuildContext context) {
    final locals = supportedLocales.toList();
    return CommonBackground(
      appBar: Sim2AppBar(
        title: Local.setting.setting_language,
      ),
      body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.w),
          child: SettingBlock(
                children: [
                  SettingBlockChild.selected(
                    title: Local.setting.following_system,
                    selected: () => GlobalStore().localeRx.value == null,
                    onTap: () {
                      controller.setLanguage(null);
                    },
                  ),
                  for (var i = 0; i < locals.length; i++) ...[
                    SettingBlockChild.selected(
                      title: localStr(locals[i].languageCode),
                      selected: () =>
                          GlobalStore().localeRx.value != null &&
                          (Localizations.localeOf(context).languageCode ==
                                  locals[i].languageCode)
                              .obs
                              .value,
                      onTap: () {
                        if (GlobalStore().localeRx.value == null||Localizations.localeOf(context).languageCode !=
                            locals[i].languageCode) {
                          controller.setLanguage(locals[i]);
                        }
                      },
                    ),
                  ]
                ],
              )),
    );
  }
}

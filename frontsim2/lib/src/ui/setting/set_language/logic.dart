import 'package:flutter/material.dart';
import 'package:rk_get/rk_get.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:sim2/src/global/global_store.dart';

import '../../../configs/local.dart';
import 'state.dart';

class SetLanguageLogic extends GetxController with SetLanguageState {
  void setLanguage(Locale? locale) {
    GlobalStore().setLocale(locale);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ToastUtils.showToast(
        msg: Local.setting.select_language_success,
        textStyle: TextStyle(
          color: Get.theme.colorScheme.onPrimary,
        )
      );
    });
  }
}
  
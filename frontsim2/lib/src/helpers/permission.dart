import 'package:rk_package/rk_package.dart';

class PermissionHelper {
  //判断一堆权限是否全部都是已授权
  static Future<bool> isAllGranted(List<Permission> permissions) async {
    if (permissions.isEmpty) {
      return true; // 如果没有权限需要检查，视为全部已授权
    }
    for (final permission in permissions) {
      if (!await permission.isGranted) {
        return false; // 只要有一个权限未授权，就返回false
      }
    }
    return true;
  }
}

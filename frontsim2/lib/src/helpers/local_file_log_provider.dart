import 'dart:io';

import 'package:rk_package/rk_package.dart';
import 'package:rk_log/rk_log.dart';

class WriteLog2FileProvider implements ILogProvider {
  @override
  void output(
      DateTime time, String modelName, LogLevel logLevel, Object message,
      {Object? exception, StackTrace? stackTrace}) {
    //获取日志文件缓存位置
    getCachePath.then((value) async {
      //获取当前时间,一个小时写一个文件
      final fileTime = DateFormat('yyyy_MM_dd_HH').format(DateTime.now());
      final fileName = 'log_$fileTime.txt';
      final file = File('${value}logs$getDelimiter$fileName');
      if (await file.exists() == false) {
        await file.create(recursive: true);
      }
      final log = exception == null
          ? '${getLevelName(logLevel)}: ${time.toUtc().toString()} $modelName $message '
          : '${getLevelName(logLevel)}: ${time.toUtc().toString()} $modelName $message $exception ';
      file.writeAsString('$log\r\n', mode: FileMode.writeOnlyAppend);
    });
  }
}

Future<String> get getCachePath async {
  final directory = await getApplicationCacheDirectory();
  // if (directory == null) {
  //   throw Exception('Unable to get cache directory');
  // }
  return '${directory.path}$getDelimiter';
}

String get getDelimiter {
  if (Platform.isWindows) {
    return '\\';
  } else {
    return '/';
  }
}

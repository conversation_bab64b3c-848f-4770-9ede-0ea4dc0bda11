
class TextFormate {
  /// 毫秒转分钟Str 
  /// @param value 毫秒
  /// @return 分钟Str
  /// example: 123000 => 02:03
  static String formateMill2Min(int value) {
    final int minutes = (value / 1000 / 60).floor();
    final int seconds = ((value / 1000) % 60).floor();
    String minStr = minutes.toString().padLeft(2, '0');
    String secStr = seconds.toString().padLeft(2, '0');
    return '$minStr:$secStr';
  }
}
import 'dart:async';

import 'package:rk_package/rk_package.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

class BackgroundImage {
  //处理成高斯模糊图片
  static Future<ImageProvider> getBackgroundImage(
      (ExtendedNetworkImageProvider image, Size size) params) async {
    final ExtendedNetworkImageProvider image = params.$1;
    final Size size = params.$2;
    // 1. 获取图片的 ImageStream
    final ImageStream stream = image.resolve(ImageConfiguration(size: size));
    final Completer<ui.Image> completer = Completer<ui.Image>();
    late ImageStreamListener listener;
    listener = ImageStreamListener((ImageInfo info, bool _) {
      completer.complete(info.image);
      stream.removeListener(listener);
    }, onError: (dynamic error, StackTrace? stackTrace) {
      completer.completeError(error, stackTrace);
      stream.removeListener(listener);
    });
    stream.addListener(listener);

    final ui.Image uiImage = await completer.future;

    // 2. 创建画布
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    //画笔加上高斯模糊
    final paint = Paint()
      ..isAntiAlias = true
      ..filterQuality = FilterQuality.low
      // ..colorFilter = const ColorFilter.mode(
      //   Color.fromRGBO(10, 10, 10, 0.5),
      //   BlendMode.darken,
      // )
      ..imageFilter = ui.ImageFilter.blur(
        sigmaX: 100,
        sigmaY: 100,
      );

    canvas.drawImageRect(
      uiImage,
      Rect.fromLTWH(0, 0, uiImage.width.toDouble(), uiImage.height.toDouble()),
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );

    // 4. 导出图片数据
    final picture = recorder.endRecording();
    final img = await picture.toImage(size.width.toInt(), size.height.toInt());
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List uint8list = byteData!.buffer.asUint8List();
    return MemoryImage(uint8list);
  }

 
}

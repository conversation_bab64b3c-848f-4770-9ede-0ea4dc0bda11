import 'dart:io';

import 'package:android_intent_plus/android_intent.dart';
import 'package:android_intent_plus/flag.dart';
import 'package:rk_package/rk_package.dart';

import '../configs/log.dart';

class GCastHelper {
  static Future<void> _launchGoogleHomeApp() async {
    const packageName = 'com.google.android.apps.chromecast.app';

    final AndroidIntent intent = AndroidIntent(
      action: 'android.intent.action.VIEW',
      package: packageName,
      flags: [Flag.FLAG_ACTIVITY_NEW_TASK],
    );

    try {
      await intent.launch();
    } catch (e) {
      Uri uri = Uri.parse(
          'https://play.google.com/store/search?q=Google%20Home&c=apps');
      if (await canLaunchUrl(uri)) await launchUrl(uri);
    }
  }

  static Future<void> tapGoogleHomeApp() async {
    const url = 'chromecast://setup/device';
    if (Platform.isAndroid) {
      _launchGoogleHomeApp();
    } else {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
        return;
      } else {
        Uri uri =
            Uri.parse('https://apps.apple.com/cn/app/google-home/id680819774');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        }
      }
    }
  }

  static Future<void> goTou() async {
    Uri uri = Uri.parse('https://www.android.com/better-together/#cast');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      log.logError('Could not launch $uri');
    }
  }

  static Future<void> goGoogleTerms() async {
    final uri =
        Uri.parse('https://policies.google.com/terms?color_scheme=dark');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      log.logError('Could not launch $uri');
    }
  }

  static Future<void> goGooglePrivacy() async {
    final uri =
        Uri.parse('https://policies.google.com/privacy?color_scheme=dark');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      log.logError('Could not launch $uri');
    }
  }
}

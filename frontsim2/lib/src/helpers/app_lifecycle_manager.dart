import 'package:sim2/src/ui/business/app_lifecycle_observer.dart';
import 'package:sim2/src/models/app_lifecycle_config.dart';

/// 应用生命周期管理器示例
/// 展示如何使用和配置AppLifecycleObserver
class AppLifecycleManager {
  static AppLifecycleObserver get _observer => AppLifecycleObserver.instance;

  /// 初始化应用生命周期管理
  /// 
  /// 使用示例：
  /// ```dart
  /// // 使用默认配置
  /// AppLifecycleManager.initWithDefaultConfig();
  /// 
  /// // 使用自定义配置
  /// AppLifecycleManager.initWithCustomConfig(
  ///   AppLifecycleConfig(
  ///     disconnectDelaySeconds: 5,
  ///     enableAutoReconnect: true,
  ///   )
  /// );
  /// ```
  static void initWithDefaultConfig() {
    _observer.init(AppLifecycleConfig.defaultConfig);
  }

  /// 使用保守配置初始化（更长的延迟时间）
  static void initWithConservativeConfig() {
    _observer.init(AppLifecycleConfig.conservativeConfig);
  }

  /// 使用激进配置初始化（立即断连重连）
  static void initWithAggressiveConfig() {
    _observer.init(AppLifecycleConfig.aggressiveConfig);
  }

  /// 使用自定义配置初始化
  static void initWithCustomConfig(AppLifecycleConfig config) {
    _observer.init(config);
  }

  /// 只启用断连功能（不自动重连）
  static void initDisconnectOnly() {
    _observer.init(AppLifecycleConfig.disconnectOnlyConfig);
  }

  /// 运行时更新配置
  static void updateConfig(AppLifecycleConfig config) {
    _observer.updateConfig(config);
  }

  /// 获取当前状态信息
  static AppLifecycleStatus getCurrentStatus() {
    return AppLifecycleStatus(
      isInBackground: _observer.isInBackground,
      pendingReconnectCount: _observer.pendingReconnectDevicesCount,
      config: _observer.config,
    );
  }

  /// 手动触发断连（用于特殊场景）
  static Future<void> forceDisconnectAll() async {
    await _observer.manualDisconnectAll();
  }

  /// 手动触发重连（用于特殊场景）
  static Future<void> forceReconnectDevices() async {
    await _observer.manualReconnectDevices();
  }

  /// 销毁生命周期管理
  static void dispose() {
    _observer.dispose();
  }
}

/// 应用生命周期状态信息
class AppLifecycleStatus {
  final bool isInBackground;
  final int pendingReconnectCount;
  final AppLifecycleConfig config;

  const AppLifecycleStatus({
    required this.isInBackground,
    required this.pendingReconnectCount,
    required this.config,
  });

  @override
  String toString() {
    return 'AppLifecycleStatus('
        'isInBackground: $isInBackground, '
        'pendingReconnectCount: $pendingReconnectCount, '
        'config: ${config.enableAutoDisconnect ? "AutoDisconnect" : ""}${config.enableAutoReconnect ? "+AutoReconnect" : ""}'
        ')';
  }
}

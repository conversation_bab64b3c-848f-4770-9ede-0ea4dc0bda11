import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:base_common/base_common.dart';
import 'package:rk_package/rk_package.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:rk_toast/rk_toast.dart';

import '../configs/local.dart';
import '../configs/log.dart';
import 'ip_tools.dart';

class LSSDPSocketClient {
  static final LSSDPSocketClient _instance =
      LSSDPSocketClient._privateConstructor();

  factory LSSDPSocketClient() {
    return _instance;
  }

  LSSDPSocketClient._privateConstructor() {
    _lock = Lock();
    _initNetworkListener();
    _waitSocket();
  }

  /// 初始化网络状态监听
  void _initNetworkListener() {
    // 获取初始网络状态
    Connectivity().checkConnectivity().then((result) {
      _currentConnectivity = result;
      log.logDebug('Initial network connectivity: $result');
    }).catchError((error) {
      log.logError('Failed to get initial connectivity: $error');
    });

    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> result) {
        log.logDebug('Network connectivity changed: $result');
        _onConnectivityChanged(result);
      },
      onError: (error) {
        log.logError('Connectivity listener error: $error');
      },
    );
  }

  /// 处理网络状态变化
  void _onConnectivityChanged(List<ConnectivityResult> connectivity) async {
    // 检查网络连接是否真的发生了变化
    if (_currentConnectivity.length == connectivity.length &&
        _currentConnectivity
            .every((element) => connectivity.contains(element))) {
      return; // 网络状态没有实际变化
    }

    _currentConnectivity = connectivity;

    // 取消之前的重连定时器
    _reconnectTimer?.cancel();

    // 判断是否有wifi连接
    bool isWifiConnected = connectivity.contains(ConnectivityResult.wifi);
    log.logDebug('Is WiFi connected: $isWifiConnected');
    // 如果没有wifi连接，直接销毁socket
    if (!isWifiConnected) {
      log.logDebug('No WiFi connection, disposing socket');
      await _disposeSocket();
      return;
    }

    // 检查是否有网络连接
    bool hasConnection =
        connectivity.any((result) => result != ConnectivityResult.none);

    if (!hasConnection) {
      log.logDebug('No network connection, disposing socket');
      await _disposeSocket();

      return;
    }

    log.logDebug('Network changed, will recreate LSSDP connection in 1 second');
    IpV4AndIpV6Tool.refreshingIp();
    // 使用防抖机制，延迟1秒后重连，避免频繁网络切换
    _reconnectTimer = Timer(const Duration(seconds: 1), () {
      _recreateConnection();
    });
  }

  /// 销毁当前socket连接
  Future<void> _disposeSocket() async {
    if (_lock.isUsing()) {
      await _lock.waitDone();
    }

    await _lock.mutex(() async {
      try {
        if (_socketSubscription != null) {
          await _socketSubscription!.cancel();
          _socketSubscription = null;
        }
        if (_rawSocket != null) {
          _rawSocket!.close();
          _rawSocket = null;
        }
        log.logDebug('Socket disposed');
      } catch (e) {
        log.logError('Error disposing socket: $e');
      }
    });
  }

  /// 重新创建连接
  Future<void> _recreateConnection() async {
    try {
      // 先销毁现有连接
      await _disposeSocket();

      // 等待一小段时间确保网络稳定
      await Future.delayed(const Duration(milliseconds: 500));

      // 重新创建连接
      await _waitSocket();

      log.logDebug('LSSDP connection recreated successfully');
    } catch (e) {
      log.logError('Failed to recreate LSSDP connection: $e');
    }
  }

  //lssdp固定端口
  static const int _lssdpPort = 1800;
  //历史消息去重
  List<List<String>> msglt = [];

  late final Lock _lock;
  RawDatagramSocket? _rawSocket;
  StreamSubscription? _socketSubscription;

  // 网络状态监听相关
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  List<ConnectivityResult> _currentConnectivity = [];
  Timer? _reconnectTimer; // 防抖定时器

  final List<OutValueCallback<LibreDiscoverInfo>> deviceCallbacks = [];

  final Lock _scanLock = Lock();

  CancelListener addDeviceListener(
      OutValueCallback<LibreDiscoverInfo> callback) {
    if (deviceCallbacks.contains(callback)) {
      return () {
        deviceCallbacks.remove(callback);
      };
    } else {
      deviceCallbacks.add(callback);
    }

    return () {
      deviceCallbacks.remove(callback);
    };
  }

  void _parseDeviceInfo(
    dynamic event,
    RawDatagramSocket socket,
  ) {
    if (event == RawSocketEvent.read) {
      final datagram = socket.receive();
      if (datagram == null) return;
      String msg = utf8.decode(datagram.data).replaceAll('\r\n', ',');
      String ip = datagram.address.address;
      List<String> lt = msg.replaceAll('\r\n', ',').split(',')..removeAt(0);
      List<String> mlt = ['ip:$ip', ...lt];
      log.logDebug('get device info: $mlt');
      if (msglt.contains(mlt)) return; //相同结果去重
      msglt.add(mlt);
      //通知注册的回调
      final converted = _convertDeviceInfo(mlt);
      if (converted == null) return;
      for (var callback in deviceCallbacks) {
        try {
          callback(converted);
        } catch (e) {
          log.logError('callback error: $e');
        }
      }
    }
  }

  Future<void> _waitSocket() async {
    if (_rawSocket != null) return;
    if (_lock.isUsing()) {
      await _lock.waitDone();
      return;
    }

    await _lock.mutex(() async {
      try {
        if (_socketSubscription != null) {
          _socketSubscription!.cancel();
          _socketSubscription = null;
        }
        final localIp = await IpV4AndIpV6Tool.localIpv4;

        log.logDebug('localIp: $localIp');
        //生成随机端口
        _rawSocket = await RawDatagramSocket.bind(localIp, _lssdpPort);
        _rawSocket!.broadcastEnabled = true;
        _rawSocket!.joinMulticast(InternetAddress('***************'));

        log.logDebug('socket binded ip: $localIp,port: $_lssdpPort');

        _socketSubscription = _rawSocket!.listen((event) {
          _parseDeviceInfo(event, _rawSocket!);
        }, onDone: () {
          log.logError('rawsocket done');
        }, onError: (e) {
          log.logError('rawsocket error: $e');
        });
        log.logDebug('device listener added');
      } catch (e) {
        throw Exception('Failed to bind socket: $e');
      }
    });
  }

  /// 手动重新创建网络连接
  /// 用于在网络异常时手动刷新连接
  Future<void> refreshNetworkConnection() async {
    log.logDebug('Manually refreshing LSSDP network connection');
    await _recreateConnection();
  }

  Future<void> scan() async {
    if (_scanLock.isUsing()) {
      return _scanLock.waitDone();
    }
    return _scanLock.mutex(() async {
      log.logDebug('start scan');

      msglt.clear();

      //判断当前连接状态
      if (_currentConnectivity.isEmpty) {
        final connectionStatus = await Connectivity().checkConnectivity();
        _currentConnectivity = connectionStatus;
      }
      if (!_currentConnectivity.contains(ConnectivityResult.wifi)) {
        log.logDebug('No WiFi connection, cannot scan');
        ToastUtils.showToast(
          msg: Local.common.none_connected_wifi_network,
          duration: const Duration(seconds: 2),
        );
        return;
      }

      final localIp = await IpV4AndIpV6Tool.localIpv4;

      final socket = await RawDatagramSocket.bind(localIp, 0);
      List<int> data = utf8.encode(
          'M-SEARCH * HTTP/1.1\r\nHOST: ***************:1800\r\n\r\nPROTOCOL: Version 1.0');
      InternetAddress multicastAddress = InternetAddress('***************');
      await _waitSocket();
      socket.send(data, multicastAddress, _lssdpPort);
      socket.listen((event) {
        _parseDeviceInfo(event, socket);
      }, onDone: () {
        log.logDebug('scan done');
      });
      await Future.delayed(const Duration(seconds: 3)); //设定扫描时间
      socket.close();
    });
  }

  void dispose() {
    Future.sync(() async {
      // 取消防抖定时器
      _reconnectTimer?.cancel();

      // 取消网络状态监听
      if (_connectivitySubscription != null) {
        await _connectivitySubscription!.cancel();
        _connectivitySubscription = null;
      }

      if (_lock.isUsing()) {
        await _lock.waitDone();
      }
      if (_rawSocket == null || _socketSubscription == null) return;
      await _lock.mutex(() async {
        await _socketSubscription!.cancel();
        _rawSocket!.close();
        _rawSocket = null;
        _socketSubscription = null;
      });
    });
  }

  LibreDiscoverInfo? _convertDeviceInfo(List<String> device) {
    // 处理设备信息
    log.logDebug('Device found: $device');
    if (!device.any((e) => e.contains('LSSDP'))) return null;

    final ip =
        device.firstWhereOrNull((e) => e.contains('ip:'))?.split(':').last ??
            '';
    final name = device
        .firstWhereOrNull((e) => e.contains('DeviceName:'))
        ?.split(':')
        .last;
    final project = device
        .firstWhereOrNull((e) => e.contains('CAST_MODEL:'))
        ?.split(':')
        .last;
    final module = device
        .firstWhereOrNull((e) => e.contains('SOURCE_LIST:'))
        ?.split(':')[1];

    final port = int.tryParse(device
                .firstWhereOrNull((e) => e.contains('PORT:'))
                ?.split(':')
                .last ??
            '') ??
        7777;
    final tcpPort = int.tryParse(device
                .firstWhereOrNull((e) => e.contains('TCP_PORT:'))
                ?.split(':')
                .last ??
            '') ??
        7777;
    final version = device
            .firstWhereOrNull((e) => e.contains('FWVERSION:'))
            ?.split(':')
            .last ??
        '';
    return LibreDiscoverInfo(
        name: name ?? '',
        ip: ip,
        project: project ?? '',
        module: module ?? '',
        port: port.toString(),
        tcpPort: tcpPort.toString(),
        version: version);
  }
}

class LibreDiscoverInfo {
  const LibreDiscoverInfo({
    this.name = '',
    this.ip = '',
    this.port = '',
    this.tcpPort = '',
    this.project = '',
    this.module = '',
    this.version = '',
  });
  final String name;
  final String ip;
  final String port;
  final String tcpPort;
  final String project;
  final String module;
  final String version;
}

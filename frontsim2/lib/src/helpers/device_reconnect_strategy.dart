import 'dart:async';
import 'package:sim2/src/configs/log.dart';

/// 设备重连策略
/// 提供智能的重连机制，包括重试逻辑和延迟策略
class DeviceReconnectStrategy {
  /// 最大重试次数
  static const int maxRetryAttempts = 3;
  
  /// 基础重试延迟时间（秒）
  static const int baseRetryDelaySeconds = 2;
  
  /// 设备扫描超时时间（秒）
  static const int scanTimeoutSeconds = 10;

  /// 执行重连策略
  /// [deviceIPs] 需要重连的设备IP列表
  /// [scanCallback] 扫描设备的回调函数
  /// [getConnectedDevices] 获取当前已连接设备的回调函数
  /// 返回成功重连的设备IP列表
  static Future<List<String>> executeReconnect({
    required List<String> deviceIPs,
    required Future<void> Function() scanCallback,
    required List<String> Function() getConnectedDevices,
  }) async {
    if (deviceIPs.isEmpty) {
      log.logDebug('No devices to reconnect');
      return [];
    }

    final reconnectedDevices = <String>[];
    
    for (int attempt = 1; attempt <= maxRetryAttempts; attempt++) {
      log.logInformation('Reconnect attempt $attempt/$maxRetryAttempts for ${deviceIPs.length} devices');
      
      try {
        // 执行设备扫描
        await scanCallback();
        
        // 等待扫描完成
        await Future.delayed(Duration(seconds: scanTimeoutSeconds ~/ attempt));
        
        // 检查哪些设备已经重新连接
        final currentConnectedDevices = getConnectedDevices();
        final newlyConnected = deviceIPs
            .where((ip) => currentConnectedDevices.contains(ip))
            .where((ip) => !reconnectedDevices.contains(ip))
            .toList();
        
        reconnectedDevices.addAll(newlyConnected);
        
        if (newlyConnected.isNotEmpty) {
          log.logInformation('Attempt $attempt: Successfully reconnected devices: $newlyConnected');
        }
        
        // 如果所有设备都重连成功，提前退出
        if (reconnectedDevices.length == deviceIPs.length) {
          log.logInformation('All devices reconnected successfully');
          break;
        }
        
        // 如果不是最后一次尝试，等待一段时间再重试
        if (attempt < maxRetryAttempts) {
          final delaySeconds = baseRetryDelaySeconds * attempt;
          log.logDebug('Waiting ${delaySeconds}s before next retry attempt');
          await Future.delayed(Duration(seconds: delaySeconds));
        }
        
      } catch (e) {
        log.logError('Reconnect attempt $attempt failed: $e');
        
        // 如果是最后一次尝试，记录失败
        if (attempt == maxRetryAttempts) {
          final failedDevices = deviceIPs
              .where((ip) => !reconnectedDevices.contains(ip))
              .toList();
          if (failedDevices.isNotEmpty) {
            log.logError('Failed to reconnect devices after $maxRetryAttempts attempts: $failedDevices');
          }
        }
      }
    }
    
    return reconnectedDevices;
  }

  /// 计算重连延迟时间（指数退避）
  static Duration calculateRetryDelay(int attempt) {
    final delaySeconds = baseRetryDelaySeconds * attempt;
    return Duration(seconds: delaySeconds);
  }

  /// 检查设备是否可以重连
  /// 可以根据设备类型、历史连接状态等进行判断
  static bool canReconnect(String deviceIP) {
    // 这里可以添加更复杂的判断逻辑
    // 例如：检查设备类型、最后连接时间、错误次数等
    return deviceIP.isNotEmpty;
  }

  /// 过滤可重连的设备
  static List<String> filterReconnectableDevices(List<String> deviceIPs) {
    return deviceIPs.where(canReconnect).toList();
  }
}

import 'package:base_common/base_common.dart';
import 'package:flutter/services.dart';
import 'package:rk_package/rk_package.dart';

import '../configs/log.dart';

/// 音量键监听与弹窗拦截工具
class VolumeKeyIntercept {
  static const EventChannel _channel =
      EventChannel('com.example.sim2/volume_key_intercept');

  /// 注册音量键监听
  /// [onVolumeKeyPressed] 音量键回调
  /// 返回值为取消监听的方法
  static CancelListener registerVolumeKeyListener(
      Function(DownKeyAction keyCode) onVolumeKeyPressed) {
    // 监听原生事件，原生需实现拦截系统音量弹窗
    return _channel
        .receiveBroadcastStream()
        .listen((event) {
          if (event is int) {
            final DownKeyAction? key = DownKeyAction.fromValue(event);
            if (key != null) {
              onVolumeKeyPressed(key);
            }
          } else {
            log.logError('Volume key event is not an int: $event');
          }
        }).cancel;
  }
}

/// 音量键类型
enum DownKeyAction {
  volumeDown._(25),
  volumeUp._(24),
  ;

  final int value;
  const DownKeyAction._(this.value);

  static DownKeyAction? fromValue(int value) {
    return DownKeyAction.values.firstWhereOrNull((e) => e.value == value);
  }
}

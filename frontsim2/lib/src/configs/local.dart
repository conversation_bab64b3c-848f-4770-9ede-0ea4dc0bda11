import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:rk_get/rk_get.dart';

import '../../l10n/gen/common_localizations.dart';
import '../../l10n/gen/device_localizations.dart';
import '../../l10n/gen/radio_page_localizations.dart';
import '../../l10n/gen/set_wifi_localizations.dart';
import '../../l10n/gen/setting_localizations.dart';

abstract class Local {
  static CommonLocalizations get common =>
      CommonLocalizations.of(Get.context!)!;
  static SetWifiLocalizations get setWifi =>
      SetWifiLocalizations.of(Get.context!)!;
  static DeviceLocalizations get device =>
      DeviceLocalizations.of(Get.context!)!;
  static SettingLocalizations get setting =>
      SettingLocalizations.of(Get.context!)!;
  static RadioPageLocalizations get radioPage =>
      RadioPageLocalizations.of(Get.context!)!;
}

List<LocalizationsDelegate> get localizationsDelegates => [
      GlobalMaterialLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
      CommonLocalizations.delegate,
      SetWifiLocalizations.delegate,
      DeviceLocalizations.delegate,
      SettingLocalizations.delegate,
      RadioPageLocalizations.delegate,
    ];

Iterable<Locale> get supportedLocales =>
    [...CommonLocalizations.supportedLocales];

String localStr(String localCode) {
  switch (localCode) {
    case 'en':
      return Local.setting.setting_language_en;
    case 'zh':
      return Local.setting.setting_language_zh;
    case 'zh_HK':
      return Local.setting.setting_language_zh_hk;
    case 'ja':
      return Local.setting.setting_language_ja;
    case 'ko':
      return Local.setting.setting_language_ko;
    case 'de':
      return Local.setting.setting_language_de;
    case 'fr':
      return Local.setting.setting_language_fr;
    default:
      return localCode;
  }
}

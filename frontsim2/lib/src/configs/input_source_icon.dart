import 'package:rk_package/rk_package.dart';

import 'assets_gen.dart';


final inputSourceIconsConfig = {
  'NET': InputSourceIconData(
    path: Assets.ASSETS_ICONS_MUSIC_ICON_SVG,
    name: 'Network',
    iconSize: 80.w,
  ),
  'BT': InputSourceIconData(
    path: Assets.ASSETS_ICONS_BLUETOOTH_ICON_SVG,
    name: 'BT',
    iconSize: 85.w,
  ),
  'FM': InputSourceIconData(
    path: Assets.ASSETS_ICONS_FM_ICON_SVG,
    name: 'FM',
    iconSize: 80.w,
  ),
  'LINE-IN': InputSourceIconData(
    path: Assets.ASSETS_ICONS_LINE_IN_ICON_SVG,
    name: 'AUX',
    iconSize: 85.w,
  ),
  'LINE-IN2': InputSourceIconData(
    path: Assets.ASSETS_ICONS_LINE_IN_ICON_SVG,
    name: 'AUX2',
    iconSize: 85.w,
  ),
  'AURA': null, //todo
  'PHONO': InputSourceIconData(
    path: Assets.ASSETS_ICONS_PHONO_IN_ICON_SVG,
    name: 'PHONO',
    iconSize: 90.w,
  ),
  'OPT': InputSourceIconData(
    path: Assets.ASSETS_ICONS_OPT_ICON_SVG,
    name: 'OPT',
    iconSize: 90.w,
  ),
  'HDMI': null,
  'USB': InputSourceIconData(
    path: Assets.ASSETS_ICONS_USB_ICON_SVG,
    name: 'USB',
    iconSize: 90.w
  ),
  'USBPLAY': InputSourceIconData(
    path: Assets.ASSETS_ICONS_USB_ICON_SVG,
    name: 'USB',
    iconSize: 90.w
  ),
  'USBDAC': InputSourceIconData(
    iconSize: 80.w,
    path: Assets.ASSETS_ICONS_USB_DAC_ICON_SVG,
    name: 'DAC',
  ),
  // 'COAX': null, //todo
  // 'DAB': null,
};

class InputSourceIconData {
  const InputSourceIconData({
    required this.path,
    required this.name,
    required this.iconSize
  });

  final String path;
  final String name;
  final double iconSize;
}

String getInputSourceIcon(String inputSource) {
  if (inputSourceIconsConfig[inputSource] != null) {
    return inputSourceIconsConfig[inputSource]!.path;
  } else {
    return Assets.ASSETS_ICONS_SOUND__EFFECT_ICON_SVG;
  }
}

double getInputSourceIconSize(String inputSource) {
  if (inputSourceIconsConfig[inputSource] != null) {
    return inputSourceIconsConfig[inputSource]!.iconSize;
  } else {
    return 80.w;
  }
}
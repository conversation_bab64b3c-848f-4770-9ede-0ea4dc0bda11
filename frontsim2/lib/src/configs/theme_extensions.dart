
import 'package:flutter/material.dart';

class Sim2SwitchThemeExt extends ThemeExtension<Sim2SwitchThemeExt> {
  final Color activeBgColor; // 外层激活背景色
  final Color unactiveBgColor; // 外层未激活背景色
  final Color circleColor; // 小圆的颜色

  const Sim2SwitchThemeExt({
    required this.activeBgColor,
    required this.unactiveBgColor,
    required this.circleColor,
  });

  static Sim2SwitchThemeExt? of(BuildContext context) {
    return Theme.of(context).extension<Sim2SwitchThemeExt>();
  }

  @override
  ThemeExtension<Sim2SwitchThemeExt> copyWith() {
    return Sim2SwitchThemeExt(
      activeBgColor: activeBgColor,
      unactiveBgColor: unactiveBgColor,
      circleColor: circleColor,
    );
  }

  @override
  ThemeExtension<Sim2SwitchThemeExt> lerp(covariant ThemeExtension<Sim2SwitchThemeExt>? other, double t) {
    if (other is! Sim2SwitchThemeExt) {
      return this;
    }
    return Sim2SwitchThemeExt(
      activeBgColor: Color.lerp(activeBgColor, other.activeBgColor, t)!,
      unactiveBgColor: Color.lerp(unactiveBgColor, other.unactiveBgColor, t)!,
      circleColor: Color.lerp(circleColor, other.circleColor, t)!,
    );
  } 
}
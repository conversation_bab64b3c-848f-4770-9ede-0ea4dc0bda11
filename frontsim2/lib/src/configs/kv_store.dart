import 'package:flutter/widgets.dart';
import 'package:rk_kvstore/rk_kvstore.dart';

import 'local.dart';

IKvStore get kvStore => KvStoreHelper.createOrGetInstance('sim2', id: '_');

enum KvKey {
  locale,
}

//用拓展的方式封装存取方法
extension KvStoreExt on IKvStore {
  Future<Locale?> getLocale() async {
    final String? localeStr = await getString(KvKey.locale.name);
    if (localeStr == null || localeStr.isEmpty) {
      return null;
    }
    for (final locale in supportedLocales) {
      if (locale.languageCode == localeStr) {
        return locale;
      }
    }
    return null;
  }

  Future<void> setLocale(Locale? locale) async {
    if (locale == null) {
      await remove(KvKey.locale.name);
    } else {
      final localeStr = locale.languageCode;
      await setString(KvKey.locale.name, localeStr);
    }
  }
}


import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';

import 'theme_extensions.dart';

export 'theme_extensions.dart';

class ThemeConfig extends GetxService {
  static ThemeConfig get to => Get.find<ThemeConfig>();

  static const Color _sim2Color1 = Color.fromRGBO(255, 255, 255, 1);
  static const Color _sim2Color2 = Color.fromRGBO(15, 15, 15, .7);
  static const Color _sim2Color3 = Color.fromRGBO(213, 116, 64, 1);
  static const Color _sim2Color4 = Color.fromRGBO(207, 207, 207, 0.4);
  static const Color _sim2Color5 = Color.fromRGBO(15, 15, 15, 1);
  static const Color _sim2Color6 = Color.fromRGBO(5, 5, 5, 0.7);

  /// 主题数据
  final Rx<ThemeData> themeData = ThemeData(
    scaffoldBackgroundColor: _sim2Color1, // 页面背景色
    colorScheme: ColorScheme.light(
      primary: _sim2Color1, // 主色调 - 白色
      secondary: _sim2Color2,  // 二级色 - 黑色
      onSecondary: _sim2Color6,
      tertiary: _sim2Color4, // 三级色 - 灰色
      surface: _sim2Color3, // 背景色 - 橘色
    ),
    cardColor: _sim2Color2,
    primaryColor: _sim2Color5,
    extensions: [
      Sim2SwitchThemeExt(
        activeBgColor: _sim2Color3, // 外层激活背景色
        unactiveBgColor: _sim2Color4, // 外层未激活背景色
        circleColor: _sim2Color1,
      )
    ]
  ).obs;

}

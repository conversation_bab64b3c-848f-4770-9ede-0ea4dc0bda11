import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/configs/kv_store.dart';

class GlobalStore {
  static final GlobalStore _instance = GlobalStore._internal();

  factory GlobalStore() {
    return _instance;
  }

  GlobalStore._internal();

  final Rx<Locale?> _locale = Rx<Locale?>(null);
  Rx<Locale?> get localeRx => _locale;
  Locale get locale => _locale.value ?? PlatformDispatcher.instance.locale;

  void setLocale(Locale? newLocale) {
    if (_locale.value != newLocale) {
      _locale.value = newLocale;
      kvStore.setLocale(newLocale);
    }
    Get.updateLocale(locale);
  }

  Future <void> init() async {
    final storedLocale = await kvStore.getLocale();
    if (storedLocale != null) {
      _locale.value = storedLocale;
      Get.updateLocale(storedLocale);
    } else {
      _locale.value = null;
    }
  }
}

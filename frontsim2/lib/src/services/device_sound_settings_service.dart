
import 'package:base_common/base_common.dart';
import 'package:i_device_action/i_device_action.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:libre_tcp_actions/libre_tcp_actions.dart' as tcp_action;
import 'package:rk_package/rk_package.dart';

import '../models/device_base_model.dart';
import '../models/device_sound_settings_model.dart';

mixin DeviceSoundSettingsService on DeviceBaseModel
    implements DeviceSoundSettingsModel {
  final Lock _initializedLock = Lock();

  //音频设置监听 (防止同时多人操作同一台设备数据保持同步,进入页面时注册监听,退出页面时取消监听)
  CancelListener registerSoundSettingsListener() {
    //最大音量
    final maxVolEffect = tcp_action.maxVolEffect((int value) {
      maxVolume.value = value;
    });
    actions.socketServiceInstance
        .registerEffect(tcp_action.MaxVolReceiveAction, maxVolEffect);
    //高音
    final trebleEffect = tcp_action.trebleEffect((int value) {
      treble.value = value;
    });
    actions.socketServiceInstance
        .registerEffect(tcp_action.TrebleReceiveAction, trebleEffect);
    //中音
    final midEffect = tcp_action.midChangeEffect((int value) {
      mid.value = value;
    });
    actions.socketServiceInstance
        .registerEffect(tcp_action.MidReceiveAction, midEffect);
    //低音
    final bassEffect = tcp_action.bassEffect((int value) {
      bass.value = value;
    });
    actions.socketServiceInstance
        .registerEffect(tcp_action.BassReceiveAction, bassEffect);
    //声道平衡
    final balanceEffect = tcp_action.balanceEffect((int value) {
      balance.value = value;
    });
    actions.socketServiceInstance
        .registerEffect(tcp_action.BalanceReceiveAction, balanceEffect);
    //eq Index
    final eqIndexEffect = (actions as LibreSoundSettings)
        .registerEQIndexChangeListener((int value) {
      eqIndex.value = value;
      //触发获取eqValues
      if (eqMap.containsKey(value)) {
        currentEqValues.assignAll(eqMap[value] ?? []);
      } else {
        currentEqValues.clear();
        actions.getCurrentEqValues().then((values) {
          eqMap[value] = values;
          currentEqValues.assignAll(values);
        });
      }
    });
    // eqValue监听
    final eqValueListener = (actions as LibreSoundSettings)
        .registerGainChangeListener((IEqValueModel values) {
      if (currentEqValues.isEmpty && currentEqValues.length > values.index) {
        currentEqValues[values.index] = values;
        eqMap[eqIndex.value]?[values.index] = values;
      }
    });

    if (!_initializedLock.isUsing()) {
      _initializedLock.mutex(() async {
        final maxVolResponse = await actions.socketServiceInstance
            .sendAction(tcp_action.MaxVolumeSendAction.get());
        if (maxVolResponse is tcp_action.MaxVolReceiveAction) {
          maxVolume.value = maxVolResponse.maxVol;
        }
        final trebleResponse = await actions.socketServiceInstance
            .sendAction(tcp_action.TrebleSendAction.get());
        if (trebleResponse is tcp_action.TrebleReceiveAction) {
          treble.value = trebleResponse.treble;
        }
        final midResponse = await actions.socketServiceInstance
            .sendAction(tcp_action.MidSendAction.get());
        if (midResponse is tcp_action.MidReceiveAction) {
          mid.value = midResponse.mid;
        }
        final bassResponse = await actions.socketServiceInstance
            .sendAction(tcp_action.BassSendAction.get());
        if (bassResponse is tcp_action.BassReceiveAction) {
          bass.value = bassResponse.bass;
        }
        final balanceResponse = await actions.socketServiceInstance
            .sendAction(tcp_action.BalanceSendAction.get());
        if (balanceResponse is tcp_action.BalanceReceiveAction) {
          balance.value = balanceResponse.balance;
        }
        //获取EQ index
        eqIndex.value = await actions.getEQS();
        //获取预设EQ列表
        eqList.addAll(
          await actions.getPrevEQList(),
        );
        //获取自定义EQ列表
        eqList.addAll(
          await actions.getCustomEQList(),
        );
      });
    }

    //返回取消监听
    return () {
      actions.socketServiceInstance
          .unregisterEffect(tcp_action.MaxVolReceiveAction, maxVolEffect);
      actions.socketServiceInstance
          .unregisterEffect(tcp_action.TrebleReceiveAction, trebleEffect);
      actions.socketServiceInstance
          .unregisterEffect(tcp_action.MidReceiveAction, midEffect);
      actions.socketServiceInstance
          .unregisterEffect(tcp_action.BassReceiveAction, bassEffect);
      actions.socketServiceInstance
          .unregisterEffect(tcp_action.BalanceReceiveAction, balanceEffect);
      eqIndexEffect.call();
      eqValueListener.call();
    };
  }

  /// 设置最大音量
  Future<void> setMaxVolume(int value) async {
    final receive = await actions.socketServiceInstance
        .sendAction(tcp_action.MaxVolumeSendAction.set(value));
    if (receive is tcp_action.MaxVolReceiveAction) {
      maxVolume.value = receive.maxVol;
    }
  }

  /// 设置高音
  Future<void> setTreble(int value) async {
    final receive = await actions.socketServiceInstance
        .sendAction(tcp_action.TrebleSendAction.set(value));
    if (receive is tcp_action.TrebleReceiveAction) {
      treble.value = receive.treble;
    }
  }

  /// 设置中音
  Future<void> setMid(int value) async {
    final receive = await actions.socketServiceInstance
        .sendAction(tcp_action.MidSendAction.set(value));
    if (receive is tcp_action.MidReceiveAction) {
      mid.value = receive.mid;
    }
  }

  /// 设置低音
  Future<void> setBass(int value) async {
    final receive = await actions.socketServiceInstance
        .sendAction(tcp_action.BassSendAction.set(value));
    if (receive is tcp_action.BassReceiveAction) {
      bass.value = receive.bass;
    }
  }

  /// 设置声道平衡
  Future<void> setBalance(int value) async {
    final receive = await actions.socketServiceInstance
        .sendAction(tcp_action.BalanceSendAction.set(value));
    if (receive is tcp_action.BalanceReceiveAction) {
      balance.value = receive.balance;
    }
  }

  /// 设置EQ Index
  Future<void> setEQIndex(int index) async {
    eqIndex.value = index;
    await actions.setEQS(index);
  }

  /// 设置EQValue
  Future<void> setEQValue(IEqValueModel value) async {
    if (currentEqValues.isEmpty || currentEqValues.length <= value.index) {
      throw Exception('Current EQ values are not initialized or index is out of range.');
    }
    currentEqValues[value.index] = value;
    eqMap[eqIndex.value]?[value.index] = value;
    await actions.setCurrentEQValue(value);
  } 
}

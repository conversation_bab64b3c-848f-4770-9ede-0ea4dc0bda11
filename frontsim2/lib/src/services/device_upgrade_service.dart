import 'dart:async';

import 'package:base_common/base_common.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:libre_luci_actions/libre_luci_actions.dart';

import '../configs/log.dart';
import '../models/device_base_model.dart';

mixin DeviceUpgradeService on DeviceBaseModel {
  CancelListener registerNoUpdateListener(VoidCallback listener) {
    final effect = noUpdateEffect(() {
      listener.call();
    });
    actions.luciServiceInstance.registerEffect(UpdateStatus, effect);
    return () {
      actions.luciServiceInstance.unregisterEffect(UpdateStatus, effect);
    };
  }

  /// 查询升级
  ///
  /// [返回值] true表示有更新，false表示没有更新
  Future<bool> queryUpdate() async {
    //监听更新状态,如果没有更新则返回false
    final Completer<bool> completer = Completer();
    final cancel = actions.registerDeviceInUpgradeStateListener((state) {
      if (completer.isCompleted) {
        return;
      }
      if (state == UpgradeSteps.noUpdate) {
        completer.complete(false);
      } else {
        completer.complete(true);
      }
    });
    final timerCancel = Timer.periodic(const Duration(seconds: 10), (_) {
      if (completer.isCompleted) {
        return;
      }
      log.logInformation('queryUpdate get upgradeStep timeout');
      completer.complete(false);
    }).cancel;
    actions.queryUpdate().then((result) {
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    return completer.future.whenComplete(() {
      cancel();
      timerCancel();
    });
  }
}

import 'dart:async';

import 'package:base_common/base_common.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_package/rk_package.dart';
import 'package:libre_tcp_actions/libre_tcp_actions.dart' as tcp_action;

import '../configs/local.dart';
import '../configs/log.dart';
import '../helpers/lssdp_discovery.dart';
import '../models/device_base_model.dart';
import '../models/device_general_settings_model.dart';
import 'devices_service.dart';

mixin DeviceGeneralSettingsServices on DeviceBaseModel
    implements DeviceGeneralSettingsModel {
  CancelListener? _rebootListener;
  @override
  void init() {
    super.init();
    //注册重启监听
    _rebootListener = actions.registerRebootListener(() {
      online.value = false;
      waitDeviceOnline().then((result) {
        if (result) {
          online.value = true;
          log.logDebug('设备上线成功');
          actions.reconnect();
        } else {
          online.value = false;
          log.logError('设备上线失败');
          //删除上线
          Get.find<LibreDevicesService>()
              .removeDevice(actions.lssdpDiscoverInfo!.ip);
        }
      }).catchError((e) {
        log.logError('设备上线失败:$e');
      });
    });
    Future.sync(() async {
      inAppleHome.value = await actions.inAppleHome;
    });
  }

  Future<bool> waitDeviceOnline() async {
    final Completer<String> completer = Completer();
    final cancel = LSSDPSocketClient().addDeviceListener((discovery) {
      log.logDebug('发现设备: ${discovery.name}');
      if (discovery.name == actions.lssdpDiscoverInfo?.deviceName) {
        completer.complete(discovery.ip);
      }
    });
    int scanCount = 0;
    final timerCancel = Timer.periodic(const Duration(seconds: 10), (_) {
      scanCount++;
      if (scanCount > 10) {
        cancel();
        completer.completeError(TimeoutException('扫描设备超时'));
        return;
      }
      log.logDebug('开始扫描设备,第$scanCount次');
      LSSDPSocketClient().scan();
    }).cancel;
    try {
      await completer.future.whenComplete(() {
        cancel();
        timerCancel();
      });
      return true;
    } catch (e) {
      log.logError('waitDeviceOnline error: $e');
      return false;
    }
  }

  void intoSettingPage() {
    if (initialized) {
      return;
    }
    initialized = true;
    //获取信息
    Future.sync(() async {
      deviceName.value = await actions.deviceName;
      version.value = await actions.version;
      timeZone.value = await actions.timeZone;
      final ipInfo = await actions.ipInfo;
      final connectedIP =
          ipInfo.firstWhereOrNull((e) => e.ipAddress != '0.0.0.0');
      ipAddress.value = connectedIP?.ipAddress ?? '';
      final macInfo = await actions.macInfo;
      if (connectedIP?.netType == LibreNetInfoType.wlan) {
        connectivity.value = 'WiFi';
        macAddress.value = macInfo
                .firstWhereOrNull(
                    (element) => element.netType == LibreNetInfoType.wlan)
                ?.macAddress ??
            '';
      }
      if (connectedIP?.netType == LibreNetInfoType.ethernet) {
        connectivity.value = Local.device.ethernet;
        macAddress.value = macInfo
                .firstWhereOrNull(
                    (element) => element.netType == LibreNetInfoType.ethernet)
                ?.macAddress ??
            '';
      }

      final status = await actions.getGCastStatus(deviceUUID);
      if (status != null) {
        gCastStatus.value = status.gCastAble;
        crashReportStatus.value = status.crashReportAble;
      }

      //查询led状态
      final result = await actions.socketServiceInstance
          .sendAction(tcp_action.LEDSendAction.get());
      if (result is tcp_action.LEDReceiveAction) {
        ledState.value = result.isOpen;
      }
    });
  }

  Future<void> getGCastStatus() async {
    final status = await actions.getGCastStatus(deviceUUID);
    if (status != null) {
      gCastStatus.value = status.gCastAble;
      crashReportStatus.value = status.crashReportAble;
    }
  }

  Future<bool> acceptGCast(String ip) async {
    final status = await actions.setGCastStatus(deviceUUID, ip);
    if (status) {
      gCastStatus.value = true;
    }
    return status;
  }

  Future<bool> toggleCrashReport(bool status) async {
    final result =
        await actions.setCrashReportStatus(deviceUUID, status, ipAddress.value);
    if (result) {
      crashReportStatus.value = !crashReportStatus.value;
    }
    return result;
  }

  Future<void> setTimeZone(String timeZone) async {
    await actions.setTimeZone(timeZone);

    this.timeZone.value = timeZone;
  }

  Future<bool> restoreFactorySettings() async {
    return actions.factoryReset();
  }

  Future<bool> rename(String name) async {
    return actions.renameDevice(name);
  }

  Future<bool> toggleLedState(bool state) async {
    final result = await actions.socketServiceInstance
        .sendAction(tcp_action.LEDSendAction.toggle(state));
    if (result is tcp_action.LEDReceiveAction) {
      ledState.value = result.isOpen;
      return true;
    }
    return false;
  }

  @override
  Future<void> dispose() async {
    super.dispose();
    _rebootListener?.call();
  }
}

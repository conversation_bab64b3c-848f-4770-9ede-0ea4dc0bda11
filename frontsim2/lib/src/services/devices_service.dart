import 'package:base_common/base_common.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:rk_get/rk_get.dart';

import '../configs/log.dart';
import '../helpers/lssdp_discovery.dart';
import '../models/device_identity.dart';
import 'device_services.dart';

class LibreDevicesService extends GetxService {
  CancelListener? _deviceListener;
  final RxList<DeviceIdentity> devices = <DeviceIdentity>[].obs;
  final RxInt _selectIndex = 0.obs;

  int get selectIndex => _selectIndex.value.clamp(0, devices.length - 1);

  //通过tag判断当前设备是否选中
  bool isSelect(String deviceIP) {
    return selectIndex ==
        devices
            .where((e) => e.isOnline)
            .toList()
            .indexWhere((device) => device.deviceIp == deviceIP);
  }

  //选中设备
  void selectDevice(String deviceIP) {
    final index = devices
        .where((e) => e.isOnline)
        .toList()
        .indexWhere((device) => device.deviceIp == deviceIP);
    if (index != -1) {
      _selectIndex.value = index;
    }
  }

  String? get selectDeviceIP {
    if (devices.isEmpty) {
      return null;
    }
    return devices[selectIndex].deviceIp;
  }

  ///移除设备
  /// [deviceIP] 设备ip
  void removeDevice(String deviceIP) {
    final index = devices.indexWhere((device) => device.deviceIp == deviceIP);
    if (index != -1) {
      devices.removeAt(index);
      if (selectIndex >= devices.length) {
        _selectIndex.value = devices.length - 1;
      }
    }
  }

  void init() {
    //创建设备上线监听
    _deviceListener = LSSDPSocketClient().addDeviceListener((discovery) {
      log.logDebug('发现设备: ${discovery.name}');
      //去重
      if (devices.any((device) => device.deviceIp == discovery.ip)) {
        return;
      }
      _registerDevice(
          LibreDeviceActions.fromNet(
            LssdpDiscoverInfo(
              ip: discovery.ip,
              luciPort: int.tryParse(discovery.port) ?? 0,
              deviceName: discovery.name,
              socketPort: int.tryParse(discovery.tcpPort) ?? 0,
              castModel: discovery.project,
            ),
            'assets/cert/client.pem',
            'assets/cert/client.key',
          ),
          discovery.ip);
      devices.add(DeviceIdentity(
        deviceIp: discovery.ip,
        deviceName: discovery.name,
      ));
    });
  }

  Future<void> scan() {
    return LSSDPSocketClient().scan();
  }

  void _registerDevice(LibreDeviceActions device, String deviceIP) {
    Get.registerSingletonAsync<DeviceServices>(() async {
      try {
        log.logDebug('注册设备: $deviceIP');
        await device.connectDevice();
        log.logInformation('$deviceIP connect success');
        final deviceView = DeviceServices(device: device);
        deviceView.init();
        log.logDebug('设备注册成功: $deviceIP');
        for (int index = 0; index < devices.length; index++) {
          if (devices[index].deviceIp == deviceIP) {
            // ignore: invalid_use_of_protected_member
            devices.value[index] =
                // ignore: invalid_use_of_protected_member
                devices.value[index].copyWith(isOnline: true);
            //为什么用value[index]而不是devices[index]?,因为需要防止触发list不必要的重绘
            break;
          }
        }
        return deviceView;
      } catch (e) {
        log.logWarning('Failed to connect to $deviceIP: $e');
        rethrow;
      }
    }, tag: deviceIP);
  }

  Future<DeviceServices?> getDeviceService(String deviceIP) async {
    if (!Get.isRegistered<DeviceServices>(tag: deviceIP) &&
        !Get.isRegistered<Future<DeviceServices>>(tag: deviceIP)) {
      return null;
    }
    return (await Get.findAsync<DeviceServices>(tag: deviceIP));
  }

  Future<void> destroyDevice(String deviceIP) async {
    if (Get.isRegistered<DeviceServices>(tag: deviceIP)) {
      final deviceView = Get.find<DeviceServices>(tag: deviceIP);
      await deviceView.dispose();
      log.logInformation('$deviceIP disconnect success');
      Get.delete<DeviceServices>(tag: deviceIP, force: true);
    } else {
      Get.removeSingleton<DeviceServices>(tag: deviceIP);
    }
  }

  void dispose() {
    _deviceListener?.call();
    _deviceListener = null;
  }
}

mixin GetDevicesExt {
  LibreDevicesService get deviceService {
    assert(Get.isRegistered<LibreDevicesService>(),
        'DevicesService is not registered');
    return Get.find<LibreDevicesService>();
  }
}

mixin GetDeviceExt {
  Future<DeviceServices> findDeviceService(String deviceIP) async {
    return Get.findAsync<DeviceServices>(tag: deviceIP);
  }
}

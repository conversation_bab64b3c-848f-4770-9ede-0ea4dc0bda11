import 'package:rk_get/rk_get.dart';

import '../models/audio_source_model.dart';
import '../models/device_base_model.dart';
import '../servers/audio_sources_server.dart';
import 'devices_service.dart';

mixin DevicePlayService on DeviceBaseModel {
  static Future<DevicePlayService?> getSelectInstance() async {
    final devicesService = Get.find<LibreDevicesService>();
    final tag = devicesService.selectDeviceIP;
    if (tag == null) {
      return null;
    }
    return (await devicesService.getDeviceService(tag)) as DevicePlayService;
  }

  static String get getSelectDeviceIP {
    final devicesService = Get.find<LibreDevicesService>();
    return devicesService.selectDeviceIP ?? '';
  }

  //播放radio
  Future<void> playRadio(String radioUrl) async {
    await actions.playRadio(radioUrl);
  }

  //播放列表
  Future<void> playList(List<AudioSourceModel> sources) async {
    //先设置json
    final server = await Get.findAsync<AudioSourcesServer>();
    server.addAudioSource(sources);
    await actions.playList(await server.getSourceJsonPath);
  }
}

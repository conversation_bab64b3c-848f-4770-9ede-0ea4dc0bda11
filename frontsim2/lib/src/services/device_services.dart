import 'dart:async';

import 'package:base_common/base_common.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:sim2/src/services/device_sound_settings_service.dart';

import '../models/device_base_model.dart';
import '../models/device_general_settings_model.dart';
import '../models/device_sound_settings_model.dart';
import '../models/device_view_model.dart';
import 'device_general_settings_service.dart';
import 'device_play_service.dart';
import 'device_upgrade_service.dart';
import 'device_view_service.dart';

class DeviceServices extends DeviceBaseModel
    with
        DeviceViewModel,
        DeviceViewService,
        DeviceGeneralSettingsModel,
        DeviceGeneralSettingsServices,
        DevicePlayService,
        DeviceUpgradeService,
        DeviceSoundSettingsModel,
        DeviceSoundSettingsService {
  DeviceServices({required LibreDeviceActions device}) {
    actions = device;
  }

  CancelListener? _upgradeStateListener;

  @override
  // ignore: overridden_fields
  late final LibreDeviceActions actions;

  @override
  Future<void> init() async {
    super.init();
    _upgradeStateListener =
        actions.registerDeviceInUpgradeStateListener((state) {
      upgradeState.value = state;
    });
    online.value = true;
  }

  @override
  Future<void> dispose() async {
    _upgradeStateListener?.call();
    super.dispose();
    await actions.dispose();
  }
}

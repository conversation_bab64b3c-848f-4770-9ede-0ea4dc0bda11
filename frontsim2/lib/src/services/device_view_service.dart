import 'package:base_common/base_common.dart';
import 'package:i_device_action/i_device_action.dart';
import 'package:libre_device_action_warp/libre_device_action_warp.dart';
import 'package:libre_luci_actions/libre_luci_actions.dart' as luci_config;
import 'package:sim2/src/models/device_base_model.dart';

import '../configs/log.dart';
import '../models/device_view_model.dart';

mixin DeviceViewService on DeviceBaseModel implements DeviceViewModel {
  CancelListener? _inputSourceListener;
  CancelListener? _volumeListener;
  CancelListener? _playViewListener;
  CancelListener? _playProgressListener;
  CancelListener? _playStateListener;
  CancelListener? _muteListener;
  CancelListener? _playSourceListener;

  @override
  void init() {
    super.init();
    _inputSourceListener = actions.registerInputSourceListener((input) {
      currentInput.value = input;
    });
    _volumeListener = actions.registerVolumeListener((vol) {
      volume.value = vol;
    });
    _playViewListener = actions.registerPlayViewListener((view) {
      playView.value = view as LibrePlayViewModel;
    });
    _playProgressListener = actions.registerPlayProgressListener((progress) {
      playProgress.value = progress;
    });
    _playStateListener = actions.registerPlayStateListener((state) {
      playState.value = state;
    });
    _muteListener = actions.registerMuteStateListener((mute) {
      this.mute.value = mute;
    });
    _playSourceListener = actions.registerCurrentPlaySourceListener((source) {
      playSource.value = source;
    });

    Future.sync(() async {
      deviceName.value = await actions.deviceName;
      inputSources.value = await actions.inputSources;
      currentInput.value = await actions.currentInputSource;
      volume.value = await actions.getVolume();
      playView.value = (await actions.getPlayView()) as LibrePlayViewModel;
      //初始化更改source
      final currentSource = luci_config
          .currentSource[(playView.value?.viewInfo.CurrentSource ?? -1)];
      if (currentSource != null) {
        playSource.value = currentSource;
      }
      try {
        mute.value = await actions.getMuteState();
      } catch (e) {
        log.logError('init error:$e');
      }
    });
  }

  Future<void> setInputSource(String input) async {
    await actions.setInputSource(input);
  }

  Future<void> setVolume(double vol) async {
    await actions.setVolume(vol);
  }

  Future<void> tootleMute() async {
    await actions.toggleMute(mute.value);
  }

  Future<void> seek(double progress) async {
    await actions.seekTo(progress);
  }

  Future<void> playOrPause() async {
    if (playState.value == DevicePlayStatus.playing) {
      await actions.pause();
    } else {
      await actions.play();
    }
  }

  Future<void> next() async {
    await actions.next();
  }

  Future<void> prev() async {
    await actions.prev();
  }

  void refreshPlayView() {
    actions.getPlayView();
  }

  Future<void> volumeUp(int vct) async {
    await actions.setVolume((volume.value + vct).clamp(0, 100));
  }

  Future<void> volumeDown(int vct) async {
    await actions.setVolume((volume.value - vct).clamp(0, 100));
  }

  @override
  Future<void> dispose() async {
    super.dispose();
    _inputSourceListener?.call();
    _volumeListener?.call();
    _playViewListener?.call();
    _playProgressListener?.call();
    _playStateListener?.call();
    _muteListener?.call();
    _playSourceListener?.call();
  }
}

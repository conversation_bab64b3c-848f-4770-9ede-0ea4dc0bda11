
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a4_usblist.dart';

abstract class AbstractBLE implements DeviceBase,A4Usblist {
  
  /// BLE设备的实例
  BluetoothDevice device = BluetoothDevice(remoteId: const DeviceIdentifier('')); 
  /// BLE设备的写入特征值
  BluetoothCharacteristic? write;
  /// BLE设备的通知特征值
  BluetoothCharacteristic? notify;
  /// BLE的连接状态
  RxBool get isBLEConnected;
  /// 重连状态
  RxBool get reconnecting;
  /// 连接BLE
  Future<void> connectBLE();
  /// 断开BLE
  Future<void> disconnectBLE();
  /// 重连BLE
  Future<void> reconnectBLE();
  /// 监听BLE状态变化。
  void listenBLEStateChange();
  /// 开启服务
  Future<void> onServices();
  /// 初始化BLE设备数据
  Future<void> initBLEInfo();  /// 初始化数据
  /// 给BLE发送消息
  void sendMsgToBLE({String? msg,List<int>? byteMsg});

}

import 'dart:async';
import 'dart:convert';

import 'package:get/get.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/common/net_api.dart';
import 'package:gocontrol/common/take_bgcolor.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

mixin SetDevicePlayInfo on AbstractNET {
  
  // 当前播放信息--------------[start]---------------

  @override // 歌曲名字
  RxString songName = ''.obs;

  @override // 歌曲艺人信息
  RxString songArtist = ''.obs;

  @override // 歌曲专辑名字
  RxString songAlbum = ''.obs;
  
  @override // 歌曲图片Url
  RxString songImage = ''.obs;

  @override // 播放进度条总长度
  RxDouble totTalVal = 0.1.obs;

  @override // 播放进度条现在长度
  RxDouble playLengths = 0.0.obs;

  @override
  RxBool playLoading = false.obs;

  // 播放进度条Timer
  Timer? timerLoops;

  @override // 播放进度条循环增加
  Timer? startLoopTask() {
    const Duration interval = Duration(seconds: 1);
    return Timer.periodic(interval, (Timer timer) async {
      // 在这里执行循环任务的逻辑
      if (playLengths.value < totTalVal.value) {
        playLengths.value++;
      } else {
        timer.cancel();
        await Future.delayed(const Duration(milliseconds: 500),()=> getPlayInfo());
        await Future.delayed(const Duration(milliseconds: 500),()=> getPlayInfo());
        // location function thchange mutaitonlociant
      }
    });
  }

  @override // 获取播放信息
  Future<void> getPlayInfo() async {
    try{
      await requestPost(
        url: WGetinfo.url(ip),
        hsa: WGetinfo.hsa,
        dat: WGetinfo.dat 
      ).then((body){
        Map res;
        if(body['u:GetInfoExResponse'] is String){
          res = json.decode(body['u:GetInfoExResponse']);
        }else{
          res = body['u:GetInfoExResponse'];
        }

        // 基础部分
        Log.d(res);

        // 播放暂停部分
        String playStatus = res['CurrentTransportState'];
        if (playStatus == 'PAUSED_PLAYBACK' || playStatus == 'NO_MEDIA_PRESENT') {
          isPlaying.value = false;
          if (timerLoops != null) {
            timerLoops!.cancel();
            timerLoops = null;
          }
        } else if (playStatus == 'PLAYING') {
          isPlaying.value = true;
          timerLoops ??= startLoopTask();
        }

        // PlayMedium 播放媒体
        if (res.containsKey('PlayMedium')) {
          Log.d('$name 播放模式: ${res['PlayMedium']}');
          playMedium.value = res['PlayMedium'];
        }

        // TrackDuration 播放条总进度
        if (res.containsKey('TrackDuration')) {
          String trackDuration = res['TrackDuration'];
          double tot = Comm.timeStringToSeconds(trackDuration);
          if (tot > 0) {
            totTalVal.value = tot;
          }
        }

        // RelTime 播放条已播放的进度
        // if (res.containsKey('RelTime')) {
        //   double relTime = Comm.timeStringToSeconds(res['RelTime']);
        //   if (isPlaying.value) {

        //   }
        //   playLengths.value = Comm.timeStringToSeconds(res['RelTime']);
        //   Log.d('已播放的进度 ${playLengths.value}');
        // }

        // RelTime 播放条已播放的进度
        if (res.containsKey('RelTime')) {
          double relTime = Comm.timeStringToSeconds(res['RelTime']);
          if (isPlaying.value && (playLengths.value - relTime) > 3) {
            playLengths.value = relTime;
            Log.d('已播放的进度 ${playLengths.value}');
          } else {
            playLengths.value = relTime;
          }
        }
        
        // TrackMetaData 部分 
        if (!res.containsKey('TrackMetaData') || res['TrackMetaData'] == null) {
          // 清空设备信息
          setSongName('');
          setSongArtist('');
          setSongAlbum('');
          setSongImage('');
        } else {
          var trackData = res['TrackMetaData'];
          String name;
          String artist;
          String album;
          String imgUrl;
          
          if (trackData is String && trackData.contains('dc:title')) {
            Map info;
            if (trackData.contains('<?xml')) {
              info = Comm.translateInfo(res['TrackMetaData'])['DIDL-Lite']['item'];
            } else {
              if (playMedium.value == 'THIRD-DLNA') {
                info = Comm.translateInfo('<?xml version="1.0" encoding="UTF-8"?>${res['TrackMetaData']}')['DIDL-Lite']['item'];
              } else {
                String header = '<?xml version="1.0" encoding="UTF-8"?><DIDL-Lite xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/" xmlns:song="www.wiimu.com/song/" xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/">';
                String footer = '</DIDL-Lite>';
                info = Comm.translateInfo('$header${res['TrackMetaData']}$footer')['DIDL-Lite']['item'];
              }
            }
            Log.y(info);
            name = info['dc:title']??'';
            artist = info['upnp:artist']??'';
            album = info['upnp:album']??'';
            imgUrl = info['upnp:albumArtURI']??'';
          } else {
            Log.d('TrackMetaData 是json格式');
            Map info = json.decode(res['TrackMetaData']);
            name = info['title']??'';
            artist = info['artist']??'';
            album = info['album']??'';
            imgUrl = info['albumArtURI']??'';
            if (artist == '' && info['creator'] != '') artist = info['creator']??'';
          }
          setSongName(name);
          setSongArtist(artist);
          setSongAlbum(album);
          if (playMedium.value == 'AIRPLAY') {
            if (imgUrl == 'unknown' || imgUrl == 'un_known' || imgUrl == '') return;
            if (!imgUrl.contains('http')) imgUrl = '$httpStr://$ip$imgUrl';
          }
          setSongImage(imgUrl);
        }
      });
    }catch(e){
      Log.e('GetPlayInfo错误: 错误设备: $name 错误信息：$e');
    }
  }

  @override
  Future<void> getMedialInfo() async {
    requestPost(
      url: WgetMediaInfo.url(ip),
      hsa: WgetMediaInfo.hsa,
      dat: WgetMediaInfo.dat
    ).then((body) async {
      if(body['u:GetMediaInfoResponse'] != null && body['u:GetMediaInfoResponse'].containsKey('PlayMedium')){
        playMedium.value = body['u:GetMediaInfoResponse']['PlayMedium'];
      }
      if(body['u:GetMediaInfoResponse']['MediaDuration'] != null){
        String totStr = body['u:GetMediaInfoResponse']['MediaDuration'];
        double totVal = Comm.timeStringToSeconds(totStr);
        if(totVal < 1){
          totTalVal.value = 0.1;
          playLengths.value = 0.0;
        }else{
          totTalVal.value = totVal;
        }
      }
      // getPlayInfo();
    });
  }

  // 设置歌曲名字
  void setSongName(String name) async {
    if(name == '') {
      isPlaying.value = false;
      totTalVal.value = 0.1;
      playLengths.value = 0;
    }
    if(songName.value != name) songName.value = name;
  }

  // 设置歌曲艺人
  void setSongArtist(String artist){
    if(songArtist.value != artist) songArtist.value = artist;
  }

  // 设置歌曲艺人
  void setSongAlbum(String album){
    if(songAlbum.value != album) songAlbum.value = album;
  }
  
  // 设置歌曲封面
  void setSongImage(String url) async {
    if (url == '' || url == 'unknown' || url == 'un_known') {
      songImage.value = '';
      return;
    }
    if (songImage.value == url) return;
    songImage.value = url;
    if (homCon.selectDevice == this) TakeBgColor.getImageDominantColor(songImage.value);
    Log.d('$name 当前歌曲封面为 == $url');
  }

  // 当前播放信息--------------[end]---------------
}
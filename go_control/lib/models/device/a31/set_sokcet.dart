import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:gbk_codec/gbk_codec.dart';
import 'package:get/get.dart';
import 'package:gocontrol/api/api.dart';
import 'package:gocontrol/common/net_msg.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/a31/a31.dart';

mixin SetDeviceSocket on AbstractNET {
  // Socket和连接--------------[start]---------------
  @override // Socket
  Socket? socket;
  @override // sokcet的连接状态
  RxBool socketState = true.obs;
  @override // 上一次发送的消息
  RxString lastSendMsg = ''.obs;
  // 
  StreamSubscription? socketStream;

  // 连接socket
  @override 
  Future<void> connectSocket({bool reconnect = false}) async {
    try {
      Socket.connect(
        ip, 
        serverPort,
      ).then((socket) async {
        this.socket = socket;
        socketState.value = true;
        socketStream = socket.listen((data) {
          final Map dataMap = ApiManage.netWorkApi.parseData(data);
          List<int> payload = dataMap['payload'];
          String msg;
          try {
            msg = utf8.decode(payload).trim();
          } catch(e) {
            msg = String.fromCharCodes(payload);
          }
          if (project.contains('A100') && msg.contains('MCU+PAS+DSK:LST:') 
            || sourceInput.value == 'USBPLAY' && msg.contains('MCU+PAS+RAKOIT:DSK:LST')) {
            msg = gbk_bytes.decode(payload);
          }
          NetMsg.getMsg(msg, this as A31);
        },
        onDone: () {
          Log.e('$name 的socket断开连接');
          socketState.value = false;
        },
        onError: (error, stackTrace) {
          socketState.value = false;
          return Future.error('连接失败');
        });
        // 
        if (!reconnect) {
          sendMsgToSocket('MCU+INF+GET');
        } else {
          initNetInfo(reconnect: true);
        }
      }).timeout(const Duration(seconds: 30),onTimeout: (){});
    } catch(e) {
      Log.e('$ip 连接失败');
    }
  }

  // socketListen = socket!.listen((unitsData) async {
  //   Map<String, dynamic> data = ApiManage.netWorkApi.parseData(unitsData);
  //   List<int> payload = [];
  //   for (var i in data['payload']) {
  //     if(i != 10) payload.add(i);
  //   }
  //   // 只处理socket接收的回调信息
  //   String msg;
  //   try {
  //     msg = utf8.decode(payload);
  //   } catch (e) {
  //     msg = String.fromCharCodes(payload);
  //   }
  //   if (project.contains('A100') && msg.contains('MCU+PAS+DSK:LST:')) {
  //     msg = gbk_bytes.decode(payload);
  //   }
  //   final bool updateMsg = msg.contains('AXX+MCU+RUN');
  //   if(!updateMsg) NetMsg.getMsg(msg,this);
  // },
  // onDone:() {
  //   Log.e('$name 的socket断开连接');
  // },
  // onError: (error, stackTrace) {
  //   Log.e('$name sokcet连接失败:$error ,stackTrace = $stackTrace');
  //   // 
  // });
  // if (name.value == '') {
  //   getDeviceName();
  // } else {
  //   initNetInfo();
  // }
  

  @override // 重连socket
  Future<void> reconnectSocket() async {
    if (socketStream != null) {
      await socketStream!.cancel();
      socketStream = null;
    }
    await Future.delayed(const Duration(milliseconds: 500));
    await connectSocket();
  }

  @override
  Future<void> closeSocket() async {
    Log.d('关闭socket');
    socketState.value = false;
    await socketStream!.cancel();
    socketStream = null;
    socket!.close();
    socket = null;
  }

  @override // 给Socket发送消息
  void sendMsgToSocket(String msg) {
    try {
      Log.w('${name.value} 发送: $msg ${DateTime.now()}');
      socket!.add(ApiManage.netWorkApi.toPackage(msg));
    } catch(e) {
      Log.e('发送失败');
    }
  }
  // Socket和连接-------------- [end] ---------------
}
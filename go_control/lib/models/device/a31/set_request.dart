import 'dart:convert';
import 'dart:io';

import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:http/http.dart' as http;

mixin SetDeviceRequest on AbstractNET {
    // 网络请求--------------[start]---------------
  @override // 发送请求
  Future<String> request(String url) async {
    String uri = '$httpStr://$ip/$url';
    if (httpStr == 'https') {
      // 创建一个 HttpClient 客户端并设置信任自签名证书
      HttpClient httpClient = HttpClient()..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      try {
        // 发起 HTTPS GET 请求
        HttpClientRequest request = await httpClient.getUrl(Uri.parse(uri));
        HttpClientResponse response = await request.close();
        String responseBody = await response.transform(utf8.decoder).join();
        // 打印状态码和响应内容
        if(response.statusCode == 200){
          return responseBody;
        }else{
          Log.e('请求失败 ${response.statusCode}');
          return Future.error(response.statusCode);
        }
      } catch (e) {
        return Future.error('Error fetching slave list: $e');
      }
    } else {
      try{ 
        final response = await http.get(Uri.parse(uri));
        if(response.statusCode == 200){
          return response.body;    
        }else {
          return Future.error('${response.statusCode}');
        }
      } catch(e) {
        return Future.error(e);
      }
    }
    
  }
  
  @override // 发送POST请求
  Future<Map> requestPost({required String url,required String hsa,required String dat}) async {
    if (httpStr == 'https') {
      HttpClient httpClient = HttpClient()..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      try {
        HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
        Map headers = {
          'SOAPACTION': hsa,
          'Content-Type': 'text/xml;charset="utf-8"',
        };
        // 添加请求头
        headers.forEach((key, value) {
          request.headers.add(key, value);
        });
        // 添加请求体数据
        request.write(dat);
        HttpClientResponse response = await request.close();
        String responseBody = await response.transform(utf8.decoder).join();
        if (response.statusCode == 200) {
          // Log.p(responseBody);
          // 解码XML实体
          String xml = responseBody.replaceAll("\r", "").replaceAll("\n", "");
          Map data = Comm.translateInfo(xml);
          if(data.containsKey('s:Envelope')){
            if(data['s:Envelope'].containsKey('s:Body')){
              return data['s:Envelope']['s:Body'];
            }
          }
          return data;
        } else {
          // Log.e('请求失败 ${response.statusCode}');
          return Future.error('请求失败 ${response.statusCode}');
        }
      } catch (e) {
        return Future.error('Error fetching slave list: $e');
      }
    } else {
      try {
        final response = await http.post(
          Uri.parse(url),
          headers: {
            'SOAPACTION': hsa,
            'Content-Type': 'text/xml;charset="utf-8"',
          },
          body: dat,
        );
        // Log.w(dat);
        if (response.statusCode == 200) {
          // 解码XML实体
          String xml = response.body.replaceAll("\r", "").replaceAll("\n", "");
          Map data = Comm.translateInfo(xml);
          if(data.containsKey('s:Envelope')){
            if(data['s:Envelope'].containsKey('s:Body')){
              return data['s:Envelope']['s:Body'];
            }
          }
          return data;
        } else {
          Log.e(response.body);
          // 如果请求失败，抛出异常或返回空字符串，具体处理取决于你的需求
          return Future.error('请求失败 ${response.statusCode}');
        }
      } catch(e) {
        return Future.error(e);
      }
    }
  }
  // 播放状态控制--------------[end]---------------
}
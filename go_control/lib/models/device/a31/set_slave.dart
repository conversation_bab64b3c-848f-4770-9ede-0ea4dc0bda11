import 'dart:convert';
import 'package:get/get.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

mixin SetDeviceSlave on AbstractNET {
  // 多房间--------------[start]---------------
  @override // 是否被其他设备组成多房间
  RxBool isRoomChild = false.obs;
  @override // Master时多房间的子设备列表
  RxList<String> roomChildren = RxList<String>([]);
  @override // Master是否显示多房间的列表音量
  RxBool showAllVolume = false.obs;
  @override // Master多房间的总音量
  RxDouble allVolume = 0.0.obs;
  @override // Master上一次的总音量
  RxDouble lastAllVolume = 0.0.obs;

  @override // 计算多房间总音量
  void getMatchingVolumes() {
      try{
      List<double> volumeList = [];
      // 先添加自己
      volumeList.add(volume.value.ceil().toDouble());
      for (DeviceBase dev in homCon.adevices) {
        if (dev is AbstractNET && roomChildren.contains(dev.ip)) {
          volumeList.add(dev.volume.value.ceil().toDouble());
        }
      }
      if (volumeList.isEmpty) return;
      double totalVolume = volumeList.reduce((sum, volume) => sum + volume);
      if(totalVolume / volumeList.length != -1) allVolume.value = totalVolume / volumeList.length;
    }catch(_){
      Log.e('getMatchingVolumes 出错了');
    }
  }
  
  // 设置多房间总音量
  @override 
  void setAllVolume(double ov) {
    List<AbstractNET> svlt = [this];
    List<double> subVolumes = [volume.value];
    for(DeviceBase ab in homCon.adevices){
      if(ab is AbstractNET && roomChildren.contains(ab.ip)){
        subVolumes.add(ab.volume.value);
        svlt.add(ab);
      }
    }
    List<double> cv = synchronizeVolumes(allVolume.value,subVolumes);
    for(int i = 0;i< cv.length;i++){
      svlt[i].setVolume(cv[i]);
    }
  }

  List<double> synchronizeVolumes(double allVolume, List<double> subVolumes) {
    // 如果总音量和子音量列表都为 0，则直接返回一个空的子音量列表
    if (allVolume == 0 && subVolumes.every((volume) => volume == 0)) {
      return List<double>.filled(subVolumes.length, 0.0);
    }
    // 计算子音量的平均值
    double average = subVolumes.reduce((value, element) => value + element) / subVolumes.length;
    // 计算总音量与平均子音量之间的变化
    double change = allVolume - average;
    // 根据总音量的变化来调整子音量列表中的每个元素
    List<double> newSubVolumes = subVolumes.map((subVolume) => subVolume + change).toList();
    // 将新的子音量限制在 0 到 100 之间
    newSubVolumes = newSubVolumes.map((volume) => volume.clamp(0, 100).toDouble()).toList();
    return newSubVolumes;
  }
  
  @override // 获取设备SlaveList多房间信息
  Future<void> getSlaveList() async {
    String url = 'httpapi.asp?command=multiroom:getSlaveList';
    await request(url).then((body) {
      try{
        Map item = json.decode(body);
        if (item.containsKey('slave_list')) {
          List lt = item['slave_list'];
          if(lt.isNotEmpty) showAllVolume.value = true;
          List ltIP = lt.map((el) => el['ip']).toList();
          roomChildren.removeWhere((ip) => !ltIP.contains(ip));
          for (String ip in ltIP) {
            if (!roomChildren.contains(ip)) roomChildren.add(ip);
          }
          Future.delayed(const Duration(milliseconds: 500),()=> getMatchingVolumes());
        } else {
          if(roomChildren.isNotEmpty) roomChildren.clear();
        }
      }catch(e){
        Log.e(e);
        if(roomChildren.isNotEmpty) roomChildren.clear();
      }
    });
  }

  @override // 作为child添加到多房间
  Future<void> joinGroupMaster(String addip) async {
    String url = 'httpapi.asp?command=ConnectMasterAp:JoinGroupMaster:eth$addip:wifi0.0.0.0';
    request(url);
  }

  @override // Master将某个child踢出多房间列表
  Future<void> slaveKickout(String outip) async {
    String url = 'httpapi.asp?command=multiroom:SlaveKickout:$outip';
    request(url);
  }
  // 多房间--------------[end]---------------
}
import 'dart:async';

import '../../model_class/a1_base.dart';

/// 按下NEXT/PRVE键之后控制后续事件触发的状态机
class NextPrveStateMachine {
  late final Timer _stateCreateTimer;
  NextPrveStateMachine({Duration stateTimeout = const Duration(milliseconds: 800)}) {
    _stateCreateTimer = Timer(stateTimeout, () {
      //超时即状态不成立
      for (var action in _stateInvalidActions) {
        action.call();
      }
    });
  }

  //状态不成立触发的回调
  final List<VoidCall> _stateInvalidActions = [];

  void step(VoidCall invalidAction) {
    // assert(_stateCreateTimer.isActive, '状态机已经失效');
    if (_stateCreateTimer.isActive) {
      _stateInvalidActions.add(invalidAction);
    }
  }

  void stateValid() {
    if (_stateCreateTimer.isActive) {
      _stateCreateTimer.cancel();
      _stateInvalidActions.clear();
    }
  }

  void _dispose() {
    _stateCreateTimer.cancel();
    _stateInvalidActions.clear();
  }
}

typedef VoidCall = void Function();

mixin NextPrveStateMachineMixin on DeviceBase {
  NextPrveStateMachine? _nextPrveStateMachine;

  //不重复创建状态机,下一个会覆盖上一个
  void createStateMachine() {
    if (_nextPrveStateMachine != null) {
      _nextPrveStateMachine!._dispose();
    }
    _nextPrveStateMachine = NextPrveStateMachine();
  }

  void stateStep(VoidCall invalidAction) {
    if (_nextPrveStateMachine == null) {
      //如果没有状态机,直接执行操作
      invalidAction.call();
    } else {
      _nextPrveStateMachine!.step(invalidAction);
    }
  }

  void stateValid() {
    if (_nextPrveStateMachine != null) {
      _nextPrveStateMachine!.stateValid();
      _nextPrveStateMachine = null;
    }
  }
}

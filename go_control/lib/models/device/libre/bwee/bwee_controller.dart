import 'dart:async';
import 'dart:convert';

import 'package:bwee_interface/bwee_interface.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:base_common/base_common.dart';
import 'package:get/get.dart';

import '../../../../log.dart';
import '../i_tcp_socket.dart';

mixin BweeController on ITcpSocket implements IBweeController {
  // 日志用
  String deviceName = '';

  final Rx<bool> showBweeControl = false.obs;

  final Rx<bool> supportBweeRx = false.obs;

  @override
  Future<bool> get supportBwee async {
    Log.d('bwee $deviceName 检查Bwee支持');
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:ENA');
      if (result == null) {
        supportBweeRx.value = false;
      } else {
        supportBweeRx.value = result.split(':').last != '-1';
      }
      Log.d('bwee $deviceName 支持Bwee: $result');
      return supportBweeRx.value;
    } catch (e) {
      Log.e('$deviceName 检查Bwee支持失败: $e');
      return false;
    }
  }

  final StreamController<String> _msgStream =
      StreamController<String>.broadcast();
  void onMsgReceived(String msg) {
    if (!msg.startsWith('BWE:')) {
      return;
    }
    Log.d('bwee $deviceName 接收到数据：$msg');
    _msgStream.add(msg);
  }

  @override
  Future<List<ScanResult>> getBleDevices(bool forceRefresh) async {
    if (_bleDevices.isNotEmpty && !forceRefresh) {
      return Future.value(_bleDevices);
    }
    final Completer<List<ScanResult>> completer = Completer<List<ScanResult>>();
    List<ScanResult> devices = [];
    final cancel = FlutterBluePlus.scanResults.listen((items) {
      devices.addAll(items.where((item) =>
          item.device.platformName.isNotEmpty &&
          item.advertisementData.serviceUuids
              .any((uuid) => uuid.toString() == bweeDeviceServiceId)));
      devices = devices
          .uniqueBy((a, b) => a.device.remoteId.str == b.device.remoteId.str);
    }).cancel;
    try {
      // await FlutterBluePlus.stopScan();
      FlutterBluePlus.startScan();
      //扫5s
      await Future.delayed(const Duration(seconds: 5)).then((_) {
        if (!completer.isCompleted) {
          completer.complete(devices);
        }
      });
    } catch (e) {
      if (!completer.isCompleted) {
        completer.completeError(e);
      }
    } finally {
      FlutterBluePlus.stopScan();
      cancel();
    }
    _bleDevices.assignAll(await completer.future);
    Log.d('bwee $deviceName 扫描到 ${devices.length} 个设备');
    return _bleDevices;
  }

  final List<ScanResult> _bleDevices = [];

  @override
  Future<bool> get power async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:ENA');
      return result?.split(':').last == '1';
    } catch (e) {
      Log.e('bwee $deviceName 获取电源状态失败：$e');
      return false;
    }
  }

  @override
  Future<void> setPower(bool powerOn) async {
    await _sendMsgToTcpSocket('BWE:ENA:${powerOn ? 1 : 0}');
  }

  Future<void> _sendMsgToTcpSocket(String msg) async {
    if (tcpSocket == null) {
      //等待socket连接
      final result = await _waitForSocketConnection();
      if (!result) {
        throw Exception('bwee $deviceName TCP套接字为空，无法发送消息：$msg');
      }
    }
    try {
      Log.d('bwee $deviceName 发送数据：$msg');
      tcpSocket?.add(utf8.encode(msg));
    } catch (e) {
      Log.e('bwee $deviceName 发送数据失败：$e');
    }
  }

  Future<bool> _waitForSocketConnection() async {
    if (tcpSocket != null) {
      return true;
    }
    Log.d('bwee $deviceName 等待TCP连接');
    final completer = Completer<bool>();
    //简单粗暴定时查看
    final loopCancel = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (completer.isCompleted) {
        timer.cancel();
        return;
      }
      if (tcpSocket != null) {
        Log.d('bwee $deviceName TCP连接成功');
        completer.complete(true);
        timer.cancel();
      }
    });
    final timerCancel = Timer(const Duration(seconds: 20), () {
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });
    try {
      return await completer.future;
    } finally {
      loopCancel.cancel();
      timerCancel.cancel();
    }
  }

  Future<String?> _sendMsgToTcpSocketWithResponse(String msg) async {
    //先发送消息
    //创建消息监听等待响应
    final completer = Completer<String>();
    final cancel = _msgStream.stream.listen((response) {
      Log.d('bwee $deviceName 接收到响应：$response');
      if (completer.isCompleted) return;
      final msgs = response.split(';');
      for (final res in msgs) {
        if (res.startsWith(msg)) {
          completer.complete(res);
          return;
        }
      }
    }).cancel;
    final timerCancel = Timer(const Duration(seconds: 10), () {
      if (!completer.isCompleted) {
        completer.completeError('Timeout waiting for response');
      }
    });
    try {
      await _sendMsgToTcpSocket(msg);
    } catch (e) {
      Log.e('bwee $deviceName 发送消息失败：$e');
      if (!completer.isCompleted) {
        completer.completeError(e);
      }
    }
    return completer.future.whenComplete(() {
      cancel();
      timerCancel.cancel();
    });
  }

  @override
  Future<int> get brightness async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:MXV');
      return int.parse(result?.split(':').last ?? '0');
    } catch (e) {
      Log.e('bwee $deviceName 获取亮度失败：$e');
      return 0;
    }
  }

  @override
  Future<List<int>> get colorHex async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:COD');
      final hexString = result?.split(':').last ?? '000000';
      return [
        int.parse(hexString.substring(0, 2), radix: 16),
        int.parse(hexString.substring(2, 4), radix: 16),
        int.parse(hexString.substring(4, 6), radix: 16)
      ];
    } catch (e) {
      Log.e('bwee $deviceName 获取颜色失败：$e');
      return [0, 0, 0];
    }
  }

  @override
  Future<bool> get coeDevice async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:COE');
      return result?.split(':').last == '1';
    } catch (e) {
      Log.e('bwee $deviceName 获取设备状态失败：$e');
      return false;
    }
  }

  @override
  Future<void> setBrightness(int brightness) async {
    if (brightness < 0 || brightness > 254) {
      throw ArgumentError('Brightness must be between 0 and 254');
    }
    await _sendMsgToTcpSocket('BWE:MXV:$brightness');
    return Future.value();
  }

  @override
  Future<void> setColorHex(List<int> colorHex) async {
    if (colorHex.length != 3 || colorHex.any((c) => c < 0 || c > 255)) {
      throw ArgumentError(
          'Color must be a list of three integers between 0 and 255');
    }
    final hexString =
        colorHex.map((c) => c.toRadixString(16).padLeft(2, '0')).join('');
    await _sendMsgToTcpSocket('BWE:COD:$hexString');
    return Future.value();
  }

  @override
  Future<void> setDevice(bool deviceOn) async {
    await _sendMsgToTcpSocket('BWE:COE:${deviceOn ? 1 : 0}');
    return Future.value();
  }

  @override
  Future<void> setStrength(SyncStrength strength) async {
    await _sendMsgToTcpSocket('BWE:VOL:${strength.index}');
    return Future.value();
  }

  @override
  Future<void> setSync(bool syncOn) async {
    await _sendMsgToTcpSocket('BWE:SEN:${syncOn ? 1 : 0}');
    return Future.value();
  }

  @override
  Future<void> setSyncBoxMacLast4(String macLast4) {
    return _sendMsgToTcpSocket('BWE:BRD:$macLast4');
  }

  @override
  Future<void> setSyncMode(BweeSyncMode mode) async {
    if (mode == BweeSyncMode.monochrome) {
      await _sendMsgToTcpSocket('BWE:SEN:0');
    } else {
      await _sendMsgToTcpSocket('BWE:SEL:${mode.index - 1}');
      await _sendMsgToTcpSocket('BWE:SEN:1');
    }
    return Future.value();
  }

  @override
  Future<SyncStrength> get strength async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:VOL');
      return SyncStrength.values[int.parse(result?.split(':').last ?? '0')];
    } catch (e) {
      Log.e('bwee $deviceName 获取强度失败：$e');
      return SyncStrength.weak;
    }
  }

  @override
  Future<bool> get sync async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:SEN');
      return result?.split(':').last == '1';
    } catch (e) {
      Log.e('bwee $deviceName 获取同步状态失败：$e');
      return false;
    }
  }

  @override
  Future<String?> get syncBoxMacLast4 async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:BRD');
      // result格式为status,XXXX status为1表示有,其他表示无  XXXX为MAC后4位
      final parts = result?.split(':').last.split(',') ?? [];
      if (parts.length != 2 || parts[0] != '1') {
        return null;
      }
      return parts[1];
    } catch (e) {
      Log.e('bwee $deviceName 获取同步盒MAC后4位失败：$e');
      return null;
    }
  }

  @override
  Future<BweeSyncMode> get syncMode async {
    try {
      final result = await _sendMsgToTcpSocketWithResponse('BWE:SEL');
      final modeIndex = int.parse(result?.split(':').last ?? '0');
      return BweeSyncMode.values[modeIndex + 1];
    } catch (e) {
      Log.e('bwee $deviceName 获取同步模式失败：$e');
      return BweeSyncMode.monochrome;
    }
  }
}

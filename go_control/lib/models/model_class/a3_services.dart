import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_ble.dart';

mixin BLEandOnServices on AbstractBLE {
  @override
  Future<void> onServices() async {
    try{
      List<BluetoothService> services = await device.discoverServices();
      for (var service in services) {
        if(service.uuid.toString() != '01000100-4c69-6e6b-506c-************'){
          List<BluetoothCharacteristic> characteristics = service.characteristics;
          for (BluetoothCharacteristic c in characteristics) {
            if (c.properties.read) await c.read();
            if (c.properties.write || c.properties.writeWithoutResponse) write = c;
            if (c.properties.notify) {
              notify = c;
              await notify!.setNotifyValue(true);
            }
          }
        }
      }
    } catch(e) {
      Log.e('${device.platformName}使用了onServices: 但是错误是$e');
      disconnectBLE();
    }
  }
}
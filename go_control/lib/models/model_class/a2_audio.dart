
import 'package:get/get.dart';

mixin BaseAudio {

  void initAudioData();

  RxDouble get treble; // 高音
  void getTreble(); // 获取高音 
  void setTreble(double val); // 设置高音

  RxDouble get mid; // 中音
  void getMid(); // 获取中音 
  void setMid(double val); // 设置中音

  RxDouble get bass; // 低音
  void getBass(); // 获取低音
  void setBass(double val); // 设置低音

  RxDouble get balance; // 左右声道
  void getBalance(); // 获取左右声道
  void setBalance(double val); // 设置左右声道

  RxDouble get maxVolume; // 最大音量
  void getMaxVolume(); // 获取最大音量
  void setMaxVolume(double val); // 设置最大音量

  RxBool get crossover; // Crossover
  void getCrossover(); // 获取
  void setCrossover(bool val); // 设置

  RxDouble get frequency;
  void getFrequency();
  void setFrequency(double val);
  
  RxBool get deepBass;
  void getDeepBass();
  void setDeepBass(bool val);

  RxDouble get intensity;
  void getIntensity();
  void setIntensity(double val);

  RxInt get eqIndex;
  RxList<Map> get eqList;
  void getEqIndex();
  void setEqIndex(int index);
  void getEqList();
  void setEqList(String str,{bool?custom});

  // 自定义eq
  RxInt get customEQindex;
  RxList<Map> get customEQList;
  List<Map> get customEQValueList;
  // 获取自定义EQ
  void getCustomEQList();
  //
  void customEQListValReset();
  // 
  void getCEQandFLT(String val);
  // 初始化
  void initCustomEQval();
  // 设置FLT
  void setFLT(String val);
  // 保存EQ
  void saveCustomEQ();
  // 删除EQ
  void delectCustomEQ();
  // 清除数据
  void clearCustomEQval();
}
import 'package:get/get.dart';

// 设备类原型
abstract class DeviceBase {
  // 设备发现的类型
  Origins get origin;
  RxString get name; // 设备名字
  RxString get version; // 设备版本
  RxBool get led; // 灯光效果
  RxInt get vst;
  RxBool get updateVer;
  RxString get updateStr;
  void updateConfirm();
  void vstAdd();
  void vstSub();
  void getDeviceName(); // 获取当前设备的名字
  void setDeviceName(String name); // 设置当前设备名字  
  void getVersion(); // 获取当前版本信息
  void resetDevice(); // 重置设备版本信息
  void getUpdateVer(); // 

  
  // 音量部分
  RxDouble get volume;
  void getVolume();
  void setVolume(double volume);
    
  RxString get sourceInput; // 当前选择输入源
  RxList<Map> get sourceInputList; // 输入源列表
  RxString get newSourceInput; // 选择切换的输入源
  RxBool get swingSRC;

  void getSourceList(); // 获取输入源列表
  void getSourceInput(); // 获取当前输入源
  void setSourceInput(String src); // 设置当前输入源
  
  
  void prevPlay(); // 上一曲
  void nextPlay(); // 下一曲
  void playSong(); // 播放
  void pauseSong(); // 暂停
  void playAndPauseSong(); // 播放和暂停
}

/// 发现设备的方式  [BT为蓝牙 NET为网络]
enum Origins {
  ble,
  net
}

// 纯蓝牙系列，没有wifi，只有ble，B50等设备
// 网络系列，需要通过蓝牙配网，主要通过网络来通讯，无网络时也许可以通过蓝牙通讯，A31，A97等设备
// liber,需要通过蓝牙配网，使用liber协议，主要也是通过网络来通讯
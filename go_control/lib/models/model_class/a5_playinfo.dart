import 'dart:async';

import 'package:get/get.dart';

mixin A5Playinfo {
  RxString get songName; // 歌曲名字
  RxString get songArtist; // 歌曲艺人信息
  RxString get songAlbum; // 歌曲专辑名字
  RxString get songImage; // 歌曲图片Url
  RxDouble get totTalVal; // 播放进度条总长度
  RxDouble get playLengths; // 播放进度条现在长度
  Timer? startLoopTask(); // 播放进度条循环增加
  Future<void> getPlayInfo(); // 获取播放信息
  Future<void> getMedialInfo(); // 获取MediaInfo
}
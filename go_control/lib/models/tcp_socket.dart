import 'dart:async';
import 'dart:convert';
import 'dart:io';

// TcpSockst 状态枚举
enum TcpSocketState {
  connecting, // 连接中
  connected,  // 已连接
  disconnected, // 已断开连接
}

/// TCP 客户端套接字封装类
class TcpSocket {
  /// TcpSocket实例
  TcpSocket(this.ip, this.port);

  // 连接状态
  TcpSocketState _state = TcpSocketState.disconnected;
  TcpSocketState get state => _state;

  final String ip;     // 目标服务器IP
  final int port;      // 目标服务器端口
  Socket? socket;      // TCP连接实例（可为空）
  // 监听锁
  // 用于确保在同一时间只有一个连接或监听操作在执行，避免并发问题。
  // 当一个连接或监听操作开始时，锁会被设置为一个Completer，
  // 然后在操作完成后，锁会被释放，允许其他操作继续执行。

  StreamSubscription? subscription;  // 数据流监听订阅
  Completer<void>? _listenLock;  // 连接锁;
  
  /// 建立TCP连接
  /// [timeout] 连接超时时间（单位：秒）
  Future<void> connect({int timeout = 10}) async {
    // 关闭旧连接
    if (socket != null) {
      socket!.close();
      socket = null;
    }

    // 检查连接状态
    if (_state == TcpSocketState.connected) return;
    _state = TcpSocketState.connecting;

    const maxRetries = 3;  // 最大重试次数
    // 这里是因为Libre设备开机以后, 最先起来的是LUCI服务, 2018的端口上线要大概5秒左右
    const retryDelay = Duration(milliseconds: 2500); // 重试间隔2.5秒一次

    // 返回的错误数据
    String? error;

    // 尝试连接
    for (var attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        print('尝试连接第$attempt次');
        // 发起TCP连接并设置超时
        socket = await Socket.connect(ip, port).timeout(
          Duration(seconds: timeout),
          onTimeout: () => Future.error('连接超时'),
        );
        // 开启订阅+
        _listen();
        // 成功后返回
        return;
      } catch (e) {
        // 达到最大重试次数，退出循环
        if (attempt == maxRetries) {
          error = e.toString();
          break;
        }
        // 等待重试间隔
        await Future.delayed(retryDelay); 
      }
    }
    // 返回连接失败的错误，和错误信息
    return Future.error('连接失败，已重试$maxRetries次, 错误信息: $error');
  }

  Future<void> _listen() async {
    // 等待阶段：非阻塞等待（通过await释放CPU）
    while (_listenLock != null) {
      await _listenLock!.future; // 这里会主动让出CPU控制权
    }
    
    // 锁定阶段：立即设置新锁
    _listenLock = Completer();

    try {
      if (subscription != null) return;

      // TODO: 对接业务部分
      // 订阅数据接收事件
      subscription = socket!.listen(
        (data) {
          // 处理接收到的数据
          print(utf8.decode(data));
        },
        onDone: () {
          // 连接关闭时的处理
          print('连接已关闭');
          _close();
        },
        onError: (error) {
          // 处理错误
          print('连接错误: $error');
          _close();
        },
      );
      // 连接成功后，设置连接状态为已连接
      await Future.delayed(const Duration(milliseconds: 50));
      _state = TcpSocketState.connected;
    } finally {
      // 释放阶段
      _listenLock!.complete();
      _listenLock = null;
    }
  }

  // 断线自动重连
  Future<void> _autoReconnect() async {
    // location 
    if (_state == TcpSocketState.connecting) return;
    const maxReconnectTime = Duration(minutes: 1); // 最大重连时间
    const retryInterval = Duration(seconds: 5); // 重试间隔
    final stopTime = DateTime.now().add(maxReconnectTime);

    while (DateTime.now().isBefore(stopTime)) {
      try {
        print('尝试重连，当前时间: ${DateTime.now()}');
        await connect();
        await _listen();
        print('重连成功');
        return;
      } catch (e) {
        print('重连失败: $e，等待 ${retryInterval.inSeconds} 秒后重试');
        await Future.delayed(retryInterval);
      }
    }
    print('断线重连失败，已超过最大重连时间');
    // 可以在这里添加更多的错误处理逻辑，比如通知上层调用者等
  }

  /// 发送数据到服务端
  /// [data] 要发送的字符串数据
  void send(String data) async {
    if (_state != TcpSocketState.connected) return print('连接未建立');
    // 消息间隔50ms
    socket!.write(data);  // 写入套接字缓冲区
  }

  /// 内部关闭连接方法
  void _close() {
    socket?.close();       // 安全关闭套接字
    subscription?.cancel(); // 取消数据监听
  }
}
import 'package:get/get.dart';
import 'languages/chinese.dart';  // 中文
import 'languages/chinese_traditional.dart'; // 繁体中文
import 'languages/english.dart';  // 英文
import 'languages/korean.dart';   // 韩文
import 'languages/german.dart';   // 德文
import 'languages/japanese.dart'; // 日文
import 'languages/russian.dart';  // 俄语
import 'languages/thai.dart';     // 泰语

class LanguageClass extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'en_US': english,
    'zh_CN': chinese,
    'zh_TW': chineseTraditional,
    'ko_KR': korean,
    'de_DE': german,
    'ja_JP': japanese,
    'ru_RU': russian,
    'th_TH': thai
  };
}
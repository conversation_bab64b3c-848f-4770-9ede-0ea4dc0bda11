const english = {
  'frequency':'Frequency',
  // login
  'login': 'Sign in',
  'email': 'Email',
  'psw': 'Password',
  'auto_login': 'Remember',
  'forgot_psw': 'Forgot password?',
  'reset_psw': 'Reset password',
  'success': 'Success',
  'reset_psw_msg':'We have sent you an email for your reset password request. Please check your email.',
  'reset_psw_text': 'Please fill in your email address above and we will send you an email to reset your password.',
  'email_err': 'Not a valid Email address.',
  'email_err_msg': 'The entered Email address is not valid.',
  'network_err': 'Network error',
  'network_err_msg': 'Please check your network connection and try again.',
  'user_not_found': 'User not found',
  'wrong_password': 'Wrong password',
  'request_fail': 'Request Fail',
  'psw_length': 'Password length should not be less than 6 characters.',
  'email_format': 'Invalid Email format',
  'skip': 'Skip',
  'goback': 'back',
  // 多房间
  'mulit_room_title': 'To Play Synchronously',
  // 未播放
  'no_music': 'No Music Selected',
  // register
  'register_title':'Not a member yet?',
  'register_text': 'Register',
  'sign_up': 'Sign up',
  'user_name': 'User',
  'psw2': 'Confirm Password',
  'have_member': 'Already a member?',
  'Sign_in_here': 'Sign in here',
  'psw_qr': 'Two passwords do not match.',
  'name_err': 'User name error',
  // navtop
  'top_title':'Device unavailable',
  // button
  'confirm': 'Confirm',
  'cancel': 'Cancel',
  // search device
  'select_device': 'Select Device',
  'loading': 'Loading...',
  'search_device': 'Searching for device. . .',
  'connect_device':'Connecting to device. . .',
  'set_network': '%s is currently not connected to the network. Would you like to configure it now?',
  // Rx Tx
  'btm_title': 'Waiting for connection...',
  'rx_title': 'Connect %s in phone settings',
  'tx_title': 'Click to pair',
  'btm_disconnect': 'Disconnect',
  'btm_disconnect_text': 'Disconnect the current device?',
  'btm_tips': 'No connection',
  'btm_tips_text': 'Please open Settings -> Bluetooth on your phone, find and connect %s',
  // audio source
  'audio_source': 'Audio Source',
  'usb_list': 'Song List',
  'bluetooth': 'Bluetooth',
  'line_in': 'Line in',
  'phono': 'Phono',
  'optical': 'Optical',
  'tv_arc': 'TV ARC',
  'usb': 'USB',
  'usb_dac': 'USB DAC',
  'wifi': 'WiFi',
   // sound
  'sound': 'Sound',
  'treble': 'Treble',
  'mid': 'Mid',
  'bass': 'Bass',
  'balance': 'Balance',
  'max_volume': 'Max Volume',
  'crossover': 'Crossover',
  'deep_bass': 'Deep Bass',
  // eq
  'eq': 'EQ',
  'preset_eq': 'Preset EQ',
  'custom_eq': 'Custom EQ',
  'setEq':'Create custom EQ',
  'setEqName': 'Custom EQ name',
  'namelong':'The name is too long to use.',
  'nameduan': 'The name cannot be less than 2 digits.',
  'eq_list': 'Preset List',
  'custom_eqlist':'Custom List',
  'save': 'Save',
  'no_select': 'No Select',
  'custom_del': 'Delete',
  'no_eq': 'No EQ',
  'add_custom': 'Add Custom',
  'tips': 'Tips',
  'del_eq_text': 'Are you sure you want to delete this custom EQ?',
  'color_select': 'Color Select',
  'Flat': 'Flat',
  'Classical': 'Classical',
  'Pop': 'Pop',
  'Jazz': 'Jazz',
  'Rock': 'Rock',
  'Vocal': 'Vocal',
  // setting
  'device': 'Device',
  'device_name': 'Device Name',
  'name_change': 'Change Device Name',
  'name_change_text' : 'The process may need about 30 - 60secs , refresh the App after rebooting',
  'led': 'Status Indicator',
  'key_sound': 'Beep',
  'phono_mode': 'Phono Mode',
  'arc_mode': 'ARC Standby',
  'volume_step': 'Volume Step',
  'firmware_version': 'Firmware Version',
  'user_manual': 'User Manual',
  'update': 'Update',
  'update_text': 'The device has found an upgrading file on U-disk with version %s (current %r), please confirm if update. It will light all LEDs in the updating process, and will last for about 10 seconds. Please make sure no power loss for the device.',
  'update_net': 'New firmware is available, you can click Confirm to go to our website to download the newest firmware. You can check the update instruction to see how to update the device.',
  // settings
  'settings': 'Settings',
  'app_version': 'App Version',
  'user': 'User',
  'user_login': 'Login',
  'delete_account': 'Delete Account',
  'feedback': 'FeedBack',
  'feedback_done': 'Thank you for your feedback! Your opinions are crucial to us, and we will take them seriously and strive to improve.',
  'fedback_hit': 'Please write your feedback here.',
  'fedback_title':'Feedback success',
  'fedback_text':'Thank you for your feedback',
  // user login out
  'user_login_out': 'Login out',
  'user_login_out_text': 'Login out this account?',
  'delete_account_text': 'Are you sure to delete this account? Deleted account cannot be recovered.',
  // reset connect device
  'connection': 'Connection',
  'dev_connection': 'Deivce Reconnection...',
  'error': 'Error',
  'reset_connect_err': 'Sorry,unable to reconnect the device',
  'blue_tooth_off': 'Bluetooth is off.',
  'blue_tooth_off_text': 'Bluetooth must be enabled to use this app.',
  // theme color
  'light': 'Light',
  'dark': 'Dark',
  'primary_color': 'Primary Color',
  'secondar_color': 'Secondary Color',
  'back1': 'Background Color',
  'default_color': 'Default Color',
  // Preview Mode
  'preview_title': 'Select Device',
  'preview_text': 'Whether to enter preview mode?',
  'preview_out': "Preview Mode Out",
  'intensity': 'Intensity',
  'reset_device': 'Factory reset',
  'reset_text': 'Are you sure you want to restore the device to factory Settings?',
  'device_info': 'Device Info',
  'wifi_info': 'Network Info',
  'set_wifi_msg': 'The network setup process might take approximately 30 seconds, please wait patiently.',
  'disable_speaker':'Dual Output',
  'network_type': 'Network',
  // language
  'language': 'Language',
  'de_DE': 'Deutsch',
  'en_US': 'English',
  'ja_JP': '日本語',
  'ko_KR': '한국어',
  'ru_RU': 'Русский',
  'th_TH': 'ภาษาไทย',
  'zh_CN': '简体中文',
  'zh_TW': '繁體中文',
  // permission
  'no_permission': 'The current app lacks the required permissions. Please enable permissions in the phone settings before using this app',
  'open_settings': 'Go to settings',
  // Privacy Policy
  'privacy_policy': 'Privacy Policy',
  'service_content': 'Content on the services',
  'service_content_text': 'You are responsible for the use of the services and for any content you provide, including compliance with applicable laws, rules and regulations. You should only provide content that you are comfortable sharing with others. Any use or reliance on any content or materials share by you through the services is at your own risk. We do not endorse, support, represent or guarantee the completeness, truthfulness, accuracy or reliability of any content shared via the services or endorse any opinions expressed via the services. You understand that by using the services, you may be exposed to content that might be offensive, harmful, inaccurate or otherwise inappropriate or in some cases stations that have been mislabeled or are otherwise deceptive. All content is the sole responsibility of the person who originated such content. We may not monitor or control the content shared via the services and, we cannot take responsibility for such content. We reserve the right to remove content that including unlawful conduct, harassment or any content inappropriate to spread.',
  'rights': 'Your Rights and Grant of Rights in the Content',
  'rights_text': 'You retain your rights to any content you submit, share through the services. By submitting, sharing content through the services, you grant us a worldwide, non-exclusive, royalty-free license (with the right to sublicense) to use, copy, reproduce, process, adapt, modify, publish, transmit, display and distribute such content in any and all media or distribution methods now known or later developed (for clarity, these rights include, for example, curating, transforming and translating). This license authorizes us to make your content available to the rest of the world and to let others do the same. You agree that this license includes the right for Arylic to provide, promote and improve the services and to make content submitted to or through the services available to other companies, organizations or individuals for the syndication, broadcast, distribution, promotion or publication of such content on other media and services, subject to our terms and conditions for such content use. Such additional uses by Arylic or other companies, organizations or individuals is made with no compensation paid to you with respect to the content that you submit, share or otherwise make available through the services as the use of the services by you is hereby agreed as being sufficient compensation for the content and grant of rights herein.',
  'privacy_notice': 'Privacy Notice',
  'privacy_notice_text': 'Protecting your privacy is important to us. We strive to keep your personal information confidential. We safeguard the security of the data you send us with physical, electronic, and managerial procedures. We urge you to take every precaution to protect your personal data when you are on the Internet. For the highest security, we suggest you use a combination of letters and numbers for your password, and make sure you use a secure browser. During registration you are required to give your contact information (such as name and email address). This information will be used to keep your account secure and for password recovery. We will NEVER ask for your password. This privacy policy is subject to change at any time. We encourage you to review the privacy policy regularly for any changes.',
  // xxx
  'scan_ap': 'Scanning AP...',
  'cn1s': 'Connecting to',
  'cn2s': 'Connected to WiFi network, checking for firmware udpate. It might take approximately 20 seconds, please wait ...',
  'cn3s': 'This device is up to date',
  'cn4s': 'If not, you could still get this popup by refreshing the device list.',
  'cn5s': 'Not now',
  'cnt1': 'Found new device',
  'cnt2': 'WiFi Setup',
  // Google cast
  'gcast1': 'Cast is enabled',
  'gcast2': 'Activate Cast',
  'gcast3': 'Send device usage and crash reports to Google',
  'gcast4': 'This selection does not affect your Google Account controls at ',
  'gcast5': 'Set up voice control and multiroom in the ',
  'gcast6': 'Learn about Google Cast',
  'gcast7': 'Google Cast for audio marketing site',
  'gcast8': 'Google Cast for audio apps site',
  'gcast9': 'Google Cast for audio Learn site',
  'gcast10': 'Google Cast help center',
  // gc2
  'g2c1': 'Activated',
  'g2c2': 'Inactive',
  'g3c3': 'Change your Device Name',
  'g4c4': 'Network Info',
  'g5c5': 'Time Zone',
  'g6c6': 'Report issues',
  'g6c7': 'hour : ',
  'g6c8': 'minute : ',
  'g6c9': 'Submit',
  // radio
  'rad1': 'Listening to TlDAL has never been easier',
  'rad2': 'Stream your favorite music seamlessly from the cloud straight to youidevices',
  'rad3': 'Learn more',
  'rad4': 'OPEN TIDAL APP',
  'rad5': 'GET TIDAL FREE',
  'rads1': 'Ready to play some music?',
  'rads2': 'Listen on your speakers or TV,',
  'rads3': 'using the Spotify app as a remote',
  'rads4': 'Learn more',
  'rads5': 'OPEN SPOTIFY APP',
  'rads6': 'GET SPOTIFY FREE',
  // toast
  'toast1': 'The network is set successfully!',
  'toast2': 'Connected timeout',
  'toast3': 'Connected faild',
  'toast4': 'Device is successfully renamed. Wait for the device to restart and reconnect.',
  'toast5': 'Successfully reported!',
};
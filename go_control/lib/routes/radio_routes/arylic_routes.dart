
import 'package:flutter/animation.dart';
import 'package:get/get.dart';
import 'package:gocontrol/pages/radioPage/radio/arylic_radio/arylic_radio.dart';
import 'package:gocontrol/pages/radioPage/radio/arylic_radio/pubilc_radio/arylic_public_radio.dart';
import 'package:gocontrol/pages/radioPage/radio/arylic_radio/pubilc_radio/components/all_station.dart';
import 'package:gocontrol/pages/radioPage/radio/arylic_radio/pubilc_radio/components/country/country.dart';
import 'package:gocontrol/pages/radioPage/radio/arylic_radio/pubilc_radio/components/genre/genre.dart';
import 'package:gocontrol/pages/radioPage/radio/arylic_radio/pubilc_radio/components/languaged/language.dart';

class ArylicRoutes {
  static const String arylicRadio = '/arylicRadio';
  static const String public = '/public';
  static const String allStation = '/allStation';
  static const String myStation = '/myStation';
  static const String genre = '/genre';
  static const String language = '/language';
  static const String country = '/country';
  
  static final List<GetPage> pages = [
    GetPage(
      name: arylicRadio, 
      page: ()=> const ArylicRadioPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 170),
      curve: Curves.easeInOut,
      children: [
        GetPage(
          name: public, 
          page: ()=> const ArylicPublicRadio(),
          transition: Transition.rightToLeft,
          transitionDuration: const Duration(milliseconds: 170),
          curve: Curves.easeInOut,
          children: [
            GetPage(
              name: allStation, 
              page: ()=> const ArylicPublicAllStation(), 
              transition: Transition.rightToLeft,
              transitionDuration: const Duration(milliseconds: 170),
              curve: Curves.easeInOut,
            ),
            GetPage(
              name: genre,
              page: ()=> const ArylicRadioGenre(), 
              transition: Transition.rightToLeft,
              transitionDuration: const Duration(milliseconds: 170),
              curve: Curves.easeInOut,
            ),
            GetPage(
              name: language, 
              page: ()=> const ArylicRadioLanguage(), 
              transition: Transition.rightToLeft,
              transitionDuration: const Duration(milliseconds: 170),
              curve: Curves.easeInOut,
            ),
            GetPage(
              name: country, 
              page: ()=> const ArylicRadioCountry(), 
              transition: Transition.rightToLeft,
              transitionDuration: const Duration(milliseconds: 170),
              curve: Curves.easeInOut,
            ),
          ]
        ),
      ]
    ),
  ];
}
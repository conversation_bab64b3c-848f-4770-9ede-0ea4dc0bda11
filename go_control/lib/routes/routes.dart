import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/route_manager.dart';
import 'package:gocontrol/pages/AudioPage/audio_page.dart';
import 'package:gocontrol/pages/Home/home.dart';
import 'package:gocontrol/pages/LoginPage/singin/login_page.dart';
import 'package:gocontrol/pages/LoginPage/singup/register_page.dart';
import 'package:gocontrol/pages/PlayerPage/SongListPage/libre_song_listpage.dart';
import 'package:gocontrol/pages/PlayerPage/components/fm_body.dart';
import 'package:gocontrol/pages/PlayerPage/components/fm_save_body.dart';
import 'package:gocontrol/pages/PlayerPage/components/usbplay_body.dart';
import 'package:gocontrol/pages/PlayerPage/play_page.dart';
import 'package:gocontrol/pages/SettingPage/b50info/b50info.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/libre_g_cast.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/libre_info.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/libre_report.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/set_g_cast.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/set_g_cast2.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/set_g_cast3.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/timezone.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/timezone2.dart';
import 'package:gocontrol/pages/SettingPage/libreinfo/update_version.dart';
import 'package:gocontrol/pages/home/<USER>/scan_page.dart';
import 'package:gocontrol/pages/home/<USER>/set_network_page.dart';
import 'package:gocontrol/pages/privacyAgreement/privacy_policy.dart';
import 'package:gocontrol/pages/radioPage/radio/local_music/local_music_page.dart';
import 'package:gocontrol/pages/radioPage/radio/spotify_page/get.dart';
import 'package:gocontrol/pages/SettingPage/setinfo/setinfo.dart';
import 'package:gocontrol/pages/PlayerPage/SongListPage/song_list_page.dart';
import 'package:gocontrol/pages/PlayerPage/b50_player_page/b50_player_page.dart';
import 'package:gocontrol/pages/home/<USER>/components/usbplay_page.dart';
import 'package:gocontrol/pages/radioPage/radio/aroha_radio/aroha.dart';
import 'package:gocontrol/pages/radioPage/radio/aroha_radio/genres/genres.dart';
import 'package:gocontrol/pages/radioPage/radio/aroha_radio/stations/stations.dart';
import 'package:gocontrol/pages/radioPage/radio/arylic_radio/pubilc_radio/components/radio_list/radio_list.dart';
import 'package:gocontrol/pages/radioPage/radio/tidal_page/get.dart';
import 'package:gocontrol/pages/radioPage/radio/upnp_radio/components/upnp_page_a.dart';
import 'package:gocontrol/pages/radioPage/radio/upnp_radio/upnp_radio.dart';
import 'package:gocontrol/pages/radioPage/radio_page.dart';
import 'package:gocontrol/pages/testpage.dart';
import 'package:gocontrol/routes/radio_routes/arylic_routes.dart';

class Routes {
  static const String homePage = '/';
  static const String testPage = '/test_page';
  static const String blePage = '/ble_page';
  static const String setpage = '/setpage';
  static const String playerPage = '/player_page';
  static const String songListPage = '/songListPage';
  static const String setinfo = '/setinfo';
  static const String b50setinfo = '/b50setinfo';
  static const String radioPage = '/radioPage';
  static const String flexList = '/flexList';
  static const String arohaRadio = '/arohaRadio';
  static const String arohaGenres = '/arohaGenres';
  static const String arohaStations = '/arohaStations';
  static const String upnpRadio = '/upnpRadio';
  static const String upnpRadioA = '/upnpRadioA';
  static const String upnpRadioB = '/upnpRadioB';
  static const String login = '/login';
  static const String register = '/register';
  static const String audioPage = '/audioPage';
  static const String usbplayPage = '/usbplayPage';
  static const String libreSetInfo = '/libreSetInfo';
  static const String libreGcast = '/libreGcast';
  static const String setGcast = '/setGcast';
  static const String setGcast2 = '/setGcast2';
  static const String setGcast3 = '/setGcast3';
  static const String timezone = '/timezone';
  static const String timezone2 = '/timezone2';
  static const String libreReport = '/libreReport';
  static const String fmPage = '/fmPage';
  static const String fmSaveBody = '/fmSaveBody';
  static const String usbplaybody = '/usbplayBody';
  static const String spotifyGet = '/spotifyGet';
  static const String tidalGet = '/tidalGet';
  static const String privacyPolicy = '/privacyPolicy';
  static const String libreUpdateVersionPage = '/libreUpdateVersionPage';
  static const String scanPage = '/scanPage';
  static const String setNetworkPage = '/setNetworkPage';
  static const String libreUSBSongPage = '/libreUSBSongPage';
  static const String localMusicPage = '/localMusicPage';
  
  

  static final List<GetPage> pages = [
    pageRoute(
      routeName: localMusicPage,
      page: const LocalMusicPage(),
    ),
    pageRoute(
      routeName: login,
      page: const SignInPage(),
    ),
    pageRoute(
      routeName: register,
      page: const SignUpPage(),
    ),
    pageRoute(
      routeName: homePage,
      page: const PageHome(),
    ),
    pageRoute(
      routeName: testPage, 
      page: const TestPage()
    ),
    pageRoute(
      routeName: '/scanPage', 
      page: ScanPage()
    ),
    pageRoute(
      routeName: '/setNetworkPage', 
      page: SetNetworkPage()
    ),
    pageRoute(
      routeName: '/libreUSBSongPage', 
      page: LibreUSBSongPage()
    ),
    pageRoute(
      routeName: spotifyGet,
      page: const SpotifyGetPage(),
    ),
    pageRoute(
      routeName: tidalGet,
      page: const TidalGetPage(),
    ),
    pageRoute(
      routeName: audioPage,
      page: const AudioPage(),
    ),
    pageRoute(
      routeName: privacyPolicy,
      page: const PrivacyPolicy(),
    ),
    pageRoute(
      routeName: playerPage,
      page: const PlayerBoxPage(),
      childRoutes: [
        pageRoute(
          routeName: usbplaybody,
          page: const UsbplayBody(),
        ),
      ]
    ),
    pageRoute(
      routeName: fmPage,
      page: const FMPage(),
      childRoutes: [
        pageRoute(
          routeName: fmSaveBody,
          page: const FmSaveBody()
        )
      ]
    ),
    pageRoute(
      routeName: blePage,
      page: const B50Page(),
      childRoutes: [
        pageRoute(
          routeName: usbplayPage,
          page: const USBplayPage(),
        ),
      ]
    ),
    pageRoute(
      routeName: songListPage,
      page: const PlaySongListPage(),
    ),
    pageRoute(
      routeName: setinfo,
      page: const SetInfo(),
    ),
    pageRoute(
      routeName: libreUpdateVersionPage,
      page: const LibreUpdateVersionPage(),
    ),
    pageRoute(
      routeName: libreSetInfo,
      page: const LibreSetInfo(),
      childRoutes: [
        pageRoute(
          routeName: libreGcast,
          page: const LibreGCastPage(),
        ),
        pageRoute(
          routeName: setGcast,
          page: const SetGCast(),
        ),
        pageRoute(
          routeName: setGcast2,
          page: const SetGCast2(),
        ),
        pageRoute(
          routeName: setGcast3,
          page: const SetGCast3(),
        )
      ]
    ),
    pageRoute(
      routeName: timezone,
      page: const TimeZonePage(),
    ),
    pageRoute(
      routeName: timezone2,
      page: const TimeZonePage2(),
    ),
    pageRoute(
      routeName: libreReport,
      page: const LibreReportPage(),
    ),
    pageRoute(
      routeName: b50setinfo,
      page: const B50InfoPage(),
    ),
    pageRoute(
      routeName: radioPage,
      page: const RadioPage(),
    ),
    pageRoute(
      routeName: flexList,
      page: const ArylicRadioFlexList(),
    ),
    pageRoute(
      routeName: arohaRadio,
      page: const ArohaRadioPage(),
      childRoutes: [
        pageRoute(
          routeName: arohaGenres, 
          page: const ArohaGenresPage(),
        ),
        pageRoute(
          routeName: arohaStations, 
          page: const ArohaStationsPage(),
        ),
      ]
    ),
    pageRoute(
      routeName: upnpRadio,
      page: const UpnpRadioPage(),
      childRoutes: [
        pageRoute(
          routeName: upnpRadioA,
          page: const UpnpPageA(),
        ),
      ]
    ),
    ...ArylicRoutes.pages,
  ];
}

GetPage pageRoute({
  required String routeName,
  required Widget page,
  List<GetPage>? childRoutes,
}) {
  return GetPage(
    name: routeName,
    page: ()=> page,
    children: childRoutes ?? [],
  );
}
import 'dart:io';
import 'package:get/get.dart';
import 'package:gocontrol/log.dart';
import 'package:permission_handler/permission_handler.dart';

class AppPermission {
  // 系统类型
  static late String phoneSystem;
  // 系统权限是否都有
  static final RxBool _isGranted = true.obs;
  static bool get isGranted => _isGranted.value;

  static final RxBool location = true.obs;
  static final RxBool storage = true.obs;
  static final RxBool bluetooth = true.obs;
  static final RxBool bluetoothConnect = true.obs;
  static final RxBool bluetoothAdvertise = true.obs;
  static final RxBool bluetoothScan = true.obs;

  // 初始化
  static Future<void> init() async {
    phoneSystem = getPhoneSystem();
    if (phoneSystem == 'mac') return;
    List<Permission> permissionList = [
      Permission.location,
      Permission.storage,
      Permission.bluetooth,
    ];
    List<Permission> permissionList2 = [
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.bluetoothAdvertise,
    ];
    if (phoneSystem == 'android') {  
      permissionList.addAll(permissionList2);
    }
    await checkPermission(permissionList);
  }

  // 获取手机系统类型
  static String getPhoneSystem(){
    if(Platform.isAndroid) return 'android';
    if(Platform.isIOS) return 'ios';
    if(Platform.isMacOS) return 'mac';
    return 'unknow';
  }

  // 获取相应权限
  // static Future<PermissionStatus> requestPermission(List<Permission> permissionList) async {
  //   Map<Permission, PermissionStatus> statuses = await permissionList.request();
  //   PermissionStatus currentPermissionStatus = PermissionStatus.granted;
  //   statuses.forEach((key, val) {
  //     if (!val.isGranted) {
  //       currentPermissionStatus = val;
  //       return;
  //     }
  //   });
  //   return currentPermissionStatus;
  // }
  
  // 检查相应权限是否开启
  static Future<void> checkPermission(List<Permission> permissionList, {String? errMsg}) async {
    // bool flag = true;
    for (Permission val in permissionList) {
      PermissionStatus statuses = await val.request();
      String name = val.toString();
      if (!statuses.isGranted) {
        switch (name) {
          case 'Permission.location':
            Log.d('位置 no');
            location.value = false;
            break;
          case 'Permission.storage':
            Log.d('存储 no');
            storage.value = false;
            break;
          case 'Permission.bluetooth':
            Log.d('蓝牙 no');
            bluetooth.value = false;
            break;
          case 'Permission.bluetoothConnect':
            Log.d('蓝牙连接 no');
            bluetoothConnect.value = false;
            break;
          case 'Permission.bluetoothAdvertise':
            Log.d('蓝牙广播 no');
            bluetoothAdvertise.value = false;
            break;
          case 'Permission.bluetoothScan':
            Log.d('蓝牙扫描 no');
            bluetoothScan.value = false;
            break;
          default:
            break;
        } 
      } else {
        switch (name) {
          case 'Permission.location':
            Log.d('位置 1');
            location.value = true;
            break;
          case 'Permission.storage':
            Log.d('存储 1');
            storage.value = true;
            break;
          case 'Permission.bluetooth':
            Log.d('蓝牙 1');
            bluetooth.value = true;
            break;
          case 'Permission.bluetoothConnect':
            Log.d('蓝牙连接 1');
            bluetoothConnect.value = true;
            break;
          case 'Permission.bluetoothAdvertise':
            Log.d('蓝牙广播 1');
            bluetoothAdvertise.value = true;
            break;
          case 'Permission.bluetoothScan':
            Log.d('蓝牙扫描 1');
            bluetoothScan.value = true;
            break;
          default:
            break;
        } 
      }
    }
  }
}
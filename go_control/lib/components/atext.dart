import 'package:flutter/material.dart';

enum TextFamily {
  medium,
  regular,
  bold
}

// 文本组件
class AText extends StatelessWidget {
  const AText({
    super.key,
    required this.text,
    this.weight,
    this.spacing,
    this.overflow,
    this.textAlign,
    this.softWrap,
    this.myShadow,
    this.color,
    this.size,
    this.family
  });

  final String text;
  final Color? color;
  final double? size;
  final FontWeight? weight;
  final double? spacing;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final bool? softWrap;
  final List<Shadow>? myShadow;
  final TextFamily? family;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        color: color,
        fontSize: size,
        fontWeight: weight,
        letterSpacing: spacing,
        overflow: overflow ?? TextOverflow.clip,
        shadows: myShadow,
        fontFamily: getFamily()
      ),
      softWrap: softWrap ?? false,
      textAlign: textAlign,
    );
  }

  String getFamily(){
    switch (family) {
      case TextFamily.medium:
        return 'Medium';
      case TextFamily.regular:
        return 'Regular';
      case TextFamily.bold:
        return 'Bold';
      case null:
        return 'Medium';
    }
  }
}
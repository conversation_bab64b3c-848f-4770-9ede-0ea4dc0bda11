import 'package:flutter/material.dart';

class GoIcon extends StatelessWidget {
  const GoIcon({
    super.key,
    required this.name,
    this.size,
    this.color
  });

  final IconData name;
  final double? size;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Icon(
      name,
      size: size,
      color: color,
    );
  }
}

/// Icon 类
class GoIcons {
  /// 设置
  static const IconData setting = IconData(0xe72f,fontFamily:'IconFont');
  /// 静音
  static const IconData mute = IconData(0xe65f,fontFamily:'IconFont');
  /// 音量低
  static const IconData volumeLow = IconData(0xe659,fontFamily:'IconFont');
  /// 音量中等
  static const IconData volumeMiddle = IconData(0xe654,fontFamily:'IconFont');
  /// 音量高
  static const IconData volumeHigh = IconData(0xe653,fontFamily:'IconFont');
  /// 输入源 Wi-Fi
  static const IconData srcWiFi = IconData(0xe98e,fontFamily:'IconFont');
  /// 输入源 BT
  static const IconData srcBT = IconData(0xe65e,fontFamily:'IconFont');
  /// 输入源 Line in(AUX)
  static const IconData srcAux = IconData(0xe64d,fontFamily:'IconFont');
  /// 输入源 PHONO
  static const IconData srcPhono = IconData(0xe666,fontFamily:'IconFont');
  /// 输入源 OPT
  static const IconData srcOPT = IconData(0xe66b,fontFamily:'IconFont');
  /// 输入源 DAC
  static const IconData srcDAC = IconData(0xe660,fontFamily:'IconFont');
  /// 输入源 HDMI(ARC)
  static const IconData srcARC = IconData(0xe643,fontFamily:'IconFont');
  /// 输入源 USB
  static const IconData srcUSB = IconData(0xe650,fontFamily:'IconFont');
  /// 输入源 COAX
  static const IconData srcCOAX = IconData(0xe66d,fontFamily:'IconFont');
  /// 输入源 FM
  static const IconData srcRadio = IconData(0xe697,fontFamily:'IconFont');
  /// 网络
  static const IconData wifi = IconData(0xe668,fontFamily:'IconFont');
  /// 返回
  static const IconData back = IconData(0xe670,fontFamily:'IconFont');
  /// 退出
  static const IconData out = IconData(0xe62e,fontFamily:'iconFont');
  /// 跳转
  static const IconData jump = IconData(0xe637,fontFamily:'IconFont');
  /// 更多
  static const IconData more = IconData(0xe671,fontFamily:'IconFont');
  /// 定时器
  static const IconData timer = IconData(0xe672,fontFamily:'IconFont');
  /// 列表循环 loop-list
  static const IconData loopList = IconData(0xe67b,fontFamily:'IconFont');
  /// 单曲循环 loop-single
  static const IconData loopSingle = IconData(0xe67a,fontFamily:'IconFont');
  /// 随机播放 loop-random
  static const IconData loopRandom = IconData(0xe674,fontFamily:'IconFont');
  /// 上一曲
  static const IconData prevSong = IconData(0xe679,fontFamily:'IconFont');
  /// 下一曲
  static const IconData nextSong = IconData(0xe676,fontFamily:'IconFont');
  /// 播放
  static const IconData play = IconData(0xe66e,fontFamily:'IconFont');
  /// 暂停
  static const IconData pause = IconData(0xe678,fontFamily:'IconFont');
  /// 播放和暂停
  static const IconData playPause = IconData(0xe63d,fontFamily:'IconFont');
  /// 喜欢(未选中)
  static const IconData nolike = IconData(0xe677,fontFamily:'IconFont');
  /// 喜欢(已选中)
  static const IconData like = IconData(0xe675,fontFamily:'IconFont');
  /// 播放列表
  static const IconData playList = IconData(0xe66f,fontFamily:'IconFont');
  /// 设备设置
  static const IconData devSetting = IconData(0xe67f,fontFamily:'IconFont');
  /// 无网络
  static const IconData noWiFi = IconData(0xe627,fontFamily:'IconFont');
  /// 锁
  static const IconData lock = IconData(0xe60c,fontFamily:'IconFont');
  /// add
  static const IconData add = IconData(0xe67c,fontFamily:'IconFont');
  /// add
  static const IconData noadd = IconData(0xe990,fontFamily:'IconFont');
  /// add
  static const IconData deladd = IconData(0xe991,fontFamily:'IconFont');
  /// 更多
  static const IconData down = IconData(0xe738,fontFamily: 'IconFont');
  // ip 设置页面
  static const IconData deviceIp = IconData(0xe67d,fontFamily: 'IconFont');
  // mac 设置页面
  static const IconData deviceMac = IconData(0xe618,fontFamily: 'IconFont');
  // name 设置页面
  static const IconData deviceName = IconData(0xe76b,fontFamily: 'IconFont');
  // version 设置页面
  static const IconData deviceVersion = IconData(0xe638,fontFamily: 'IconFont');
  // uuid 设置页面
  static const IconData deviceUUID = IconData(0xe649,fontFamily: 'IconFont');
  // Reset 设置页面
  static const IconData deviceReset = IconData(0xe737,fontFamily: 'IconFont');
  // ArohaRadio图标
  static const IconData arohaIcon = IconData(0xe67e,fontFamily: 'IconFont');
  // audioIcon图标
  static const IconData audioIcon = IconData(0xe63f,fontFamily: 'IconFont');
  // setadd
  static const IconData setAdd = IconData(0xe642,fontFamily: 'IconFont');
  // setsub
  static const IconData setSub = IconData(0xe646,fontFamily: 'IconFont');
  // led
  static const IconData led = IconData(0xe602,fontFamily: 'IconFont');
  // verUpdate
  static const IconData verUpdate = IconData(0xe66a,fontFamily: 'IconFont');
  // feedback
  static const IconData feedback = IconData(0xe894,fontFamily: 'IconFont');
  // notNetwork
  static const IconData notNetwork = IconData(0xe680,fontFamily: 'IconFont');
  // eyes
  static const IconData eyes = IconData(0xe683,fontFamily: 'IconFont');
  // close_eyes
  static const IconData closeEyes = IconData(0xe682,fontFamily: 'IconFont');
  // tick
  static const IconData tick = IconData(0xe685,fontFamily: 'IconFont');
  // chromecast
  static const IconData chromecast = IconData(0xe686,fontFamily: 'IconFont');
  // airplay2
  static const IconData airplay2 = IconData(0xe689,fontFamily: 'IconFont');
  // spotify
  static const IconData spotify = IconData(0xe687,fontFamily: 'IconFont');
  // spotify 2
  static const IconData spotify2 = IconData(0xe688,fontFamily: 'IconFont');
  // noBle
  static const IconData noBle = IconData(0xe68a,fontFamily: 'IconFont');
  // refresh
  static const IconData refresh  = IconData(0xe606,fontFamily: 'IconFont');
  // podcast -15
  static const IconData podcastSub  = IconData(0xe751,fontFamily: 'IconFont');
  // podcast +15
  static const IconData podcastAdd  = IconData(0xe750,fontFamily: 'IconFont');
  // tidal
  static const IconData tidal = IconData(0xe68b,fontFamily: 'IconFont');
  // tiadlLogo
  static const IconData tidalLogo = IconData(0xe681,fontFamily: 'IconFont');
  // leaduio
  static const IconData leaudio = IconData(0xe68c,fontFamily: 'IconFont');
  // 
  static const IconData nameChange = IconData(0xe667,fontFamily: 'IconFont');
  // 
  static const IconData timeZone = IconData(0xe6f5,fontFamily: 'IconFont');
  // 
  static const IconData versionInfo = IconData(0xe68d,fontFamily: 'IconFont');
  // 
  static const IconData roonIcon = IconData(0xe68f,fontFamily: 'IconFont');
}
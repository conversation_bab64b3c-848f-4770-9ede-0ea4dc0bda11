import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 主题模式
enum ThemeModes {
  light,
  dark,
}

ThemeBase themeBase = Get.put(ThemeBase());

/// 主题类
class ThemeBase extends GetxController {
  
  final Rx<ThemeModes> themeMode = Rx<ThemeModes>(ThemeModes.light); // 当前主题的模式

  final RxBool isiPad = false.obs;

  final Rx<Color> bgColor = Rx<Color>(const Color.fromRGBO(240, 240, 240,1)); // 当前的背景颜色 
  final Rx<Color> navColor = Rx<Color>(const Color.fromRGBO(67, 85, 105, 1)); // 导航栏颜色
  final Rx<Color> primaryColor = Rx<Color>(const Color.fromRGBO(67, 85, 105, 1)); // 主要颜色
  final Rx<Color> secondaryColor = Rx<Color>(const Color.fromRGBO(155, 173, 186, 1)); // 次要颜色
  final Rx<Color> secondaryColor2 = Rx<Color>(const Color.fromRGBO(155, 173, 186, 1)); // 次要颜色
  final Rx<Color> blockColor = Rx<Color>(const Color.fromRGBO(255, 255, 255, 1)); // 区块颜色
  final Rx<Color> errorColor = Rx<Color>(const Color.fromRGBO(235, 82, 82, 1)); // 错误颜色

  final Rx<Color> textColor1 = Rx<Color>(const Color.fromRGBO(255, 255, 255, 1)); // 主要文字颜色
  final Rx<Color> textColor2 = Rx<Color>(const Color.fromRGBO(128, 128, 128, 1)); // 次要文字颜色
  final Rx<Color> tipsColor = Rx<Color>(const Color.fromRGBO(62, 140, 230, 1)); // 强调文字颜色
  
  final Rx<Color> buttonColor1 = Rx<Color>(const Color.fromRGBO(57, 78, 94, 1)); // 按钮颜色 确认
  final Rx<Color> buttonColor2 = Rx<Color>(const Color.fromRGBO(166, 177, 183, 1)); // 按钮颜色 取消
  final Rx<Color> inputColor = Rx<Color>(const Color.fromRGBO(235, 235, 235, 1)); // 输入框颜色

  final RxDouble searHeight = 20.0.obs; // 手机刘海屏高度
  final double topBarHeight = 60.sp; // 导航栏高度
  final RxDouble bottomPadding = 0.0.obs;

  /// 标题文本尺寸 [22]
  final RxDouble headingFont = 22.sp.obs; 
  /// 副标题文本尺寸 [19]
  final RxDouble subHeadingFont = 19.sp.obs;
  /// 正文尺寸 [17]
  final RxDouble bodyFont = 17.sp.obs;
  /// 副文本尺寸 [15]
  final RxDouble subBodyFont = 15.sp.obs;
  /// 副文本尺寸2 [11]
  final RxDouble subBodyFont2 = 11.sp.obs;
  /// 按钮文本尺寸 [15]
  final RxDouble buttonFont = 15.sp.obs;
  /// 输入框文本尺寸 [15]
  final RxDouble inputFont = 15.sp.obs;
}
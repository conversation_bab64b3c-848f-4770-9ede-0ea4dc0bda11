import 'dart:io';
import 'dart:async';
import 'package:get_settings/getsettings.dart';
import 'package:gocontrol/common/lock.dart';
import 'package:gocontrol/log.dart';
import 'package:path_provider/path_provider.dart';
import '../common/audio_convert.dart';


class MusicServer {
  late final HttpServer _server;

  final Lock _serverStartLock = Lock();

  // 默认端口
  static const int _port = 7788;

  MusicServer.init() {
    //创建并启动http服务器
    _serverStartLock.mutex(() async {
      await _createServer();
    });
  }

  // 创建http服务器
  Future<void> _createServer() async {
    try {
      _server = await HttpServer.bind(InternetAddress.anyIPv4, _port);
      _server.listen((request) async {
        try {
          Log.h('收到http请求: ${request.uri}');

          if (request.method == 'GET') {
            final uriParame = request.uri.queryParameters;

            request.response.statusCode = HttpStatus.ok;
            request.response.headers.contentType = ContentType.text;

            String filePath;

            if (uriParame.containsKey('id')) {
              final id = uriParame['id'] ?? '';
              final ipodUrl = 'ipod-library://item/item.mp3?id=$id';
              Log.y('ipodUrl : $ipodUrl');
              final setting = GetSettings();
              filePath = await setting.ipodToPath(ipodUrl);
              // 创建临时文件
              final tempFilePath = await getApplicationCacheDirectory();
              final tempFile = File('${tempFilePath.path}/${filePath.split('/').last}.mp3');
              await tempFile.create(recursive: true);
              // 转格式
              // await compute(AudioConvert.audioConvert2mp3ByFFmpeg,
              //     [filePath, tempFile.path]);
              // 
              Log.w(DateTime.now());
              await AudioConvert.audioConvert2mp3ByFFmpeg([filePath, tempFile.path]);
              Log.w(DateTime.now());
              //判断转出来的文件不为空
              if (await tempFile.length() <= 0) {
                Log.e('文件转换失败');
                request.response.statusCode = HttpStatus.internalServerError;
                request.response.close();
                return;
              }
              filePath = tempFile.path;
            } else {
              filePath = uriParame['android'] ?? '';
            }
            
            // if (uriParame.containsKey('ios')) {
            //   filePath = uriParame['ios'] ?? '';
            // } else {
            //   filePath = uriParame['android'] ?? ''; 
            // }

            filePath = Uri.decodeFull(filePath);
            Log.r('请求文件: $filePath');
            final iFile = File(filePath);

            if (!await iFile.exists()) {
              request.response.statusCode = HttpStatus.notFound;
              request.response.close();
              return;
            }

            final fileLength = await iFile.length();
            Log.r('文件大小: $fileLength');
            final fileBytes = await iFile.readAsBytes();

            //获取header
            final header = request.headers;
            //获取range 请求头
            final range = header['range'];

            if (range?.isNotEmpty == true) {
              //处理range请求头
              // 原代码中 fileStream 未定义，推测应该使用前面的 iFile 变量
              // 同时，response.add 方法需要的是 List<int>，而 asUint8List 是 ByteData 的方法，需要先读取文件为字节流
              final rangeStart = range!.first.split('=').last.split('-').first;
              final start = int.tryParse(rangeStart) ?? 0;
              final fileLength = await iFile.length();
              final end = fileLength - 1;
              request.response.headers.set(HttpHeaders.acceptRangesHeader, 'bytes');
              request.response.headers.set(HttpHeaders.contentRangeHeader, 'bytes $start-$end/$fileLength');
              request.response.statusCode = HttpStatus.partialContent;
              request.response.headers.contentLength = fileLength - start;
              final fileBytes = await iFile.readAsBytes();
              request.response.add(fileBytes.sublist(start));
            } else {
              request.response.statusCode = HttpStatus.ok;
              //写入文件名
              request.response.headers.set(HttpHeaders.contentDisposition,'attachment; filename=${filePath.split('/').last}');
              request.response.headers.contentType = ContentType.binary;
              request.response.headers.contentLength = fileLength;
              // 读取文件并发送
              request.response.add(fileBytes);
              request.response.close();
            }
            return;
          }
        } catch (e, stack) {
          Log.r('处理http请求失败:$e $stack');
        }
      });
      Log.r('http服务器启动成功,端口:$_port');
    } catch (e, stack) {
      Log.r('创建http服务器失败:$e $stack');
    }
  }

  //销毁
  Future dispose() async {
    return _server.close(force: true);
  }
}

import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/input_box.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/SettingPage/components/set_volume_up.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/SettingPage/components/setting_box.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/sound/components/go_swtich.dart';
import 'package:gocontrol/theme/theme.dart';

class SetInfo extends StatelessWidget {
  const SetInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: 'settings'.tr,
      ),
      body: BodyBox(
        showTopBar: true,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: _getDeviceSetting()
        )
      )
    );
  }
}


Widget _getDeviceSetting(){
  DeviceBase deviceBase = homCon.selectDevice!;
  if(deviceBase.origin == Origins.net){
    A31 netDevice = deviceBase as A31;
    return SingleChildScrollView(
      child: _getNetInfo(netDevice),
    );
  }
  return const SizedBox();
}

Widget _getNetInfo(A31 a31){
  return Column(
    children: [
      SettingBox(
        title: 'wifi_info'.tr,
        children: [
          SetBox2(
            icon: GoIcon(
              name: GoIcons.deviceIp,
              size: 18.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'IP', 
            info: a31.ip,
          ),
          SetBox2(
            icon: GoIcon(
              name: GoIcons.deviceMac,
              size: 18.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'MAC', 
            info: a31.mac,
          ),
         SetBox2(
            icon: GoIcon(
              name: GoIcons.deviceMac,
              size: 18.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'network_type'.tr, 
            info: a31.eth2 != '' && a31.eth2 != '0.0.0.0'?'ETH':'WIFI',
            last: true,
          ),
        ],
      ),
      SettingBox(
        title: 'device_info'.tr,
        children: [
          Obx(() => SetBox2(
            icon: GoIcon(
              name: GoIcons.deviceName,
              size: 20.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'device_name'.tr, 
            info: a31.name.value,
            onTap: (){
              TextEditingController nameController = TextEditingController();
              showMyBottomSheet(
                height: 250.sp, 
                title: 'name_change'.tr,
                content: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.sp),
                  child: Column(
                    children: [
                      InputBox(
                        height: 45.sp, 
                        focusNode: FocusNode(), 
                        textController: nameController, 
                        textColor: themeBase.primaryColor.value,
                        placeholder: a31.name.value,
                      ),
                      Container(
                        alignment: Alignment.centerLeft,
                        margin: EdgeInsets.only(top: 10.sp,left: 5.sp),
                        child: AText(
                          text: 'name_change_text'.tr,
                          size: 14.sp,
                          softWrap: true,
                          color: themeBase.buttonColor2.value,
                        ),
                      )
                    ],
                  ),
                ), 
                showCancel: true.obs,
                cancel: ()=> Get.back(),
                confirm: (){
                  if(nameController.text.length > 2 &&nameController.text.length < 17){
                    String name = nameController.text;
                    String data = 'NAM:${convertToUTF8Hex(name)}';
                    a31.setDeviceName(data);
                    Get.back();
                  }
                }
              );
            },
          )),
          SetBox2(
            icon: GoIcon(
              name: GoIcons.deviceVersion,
              size: 20.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'firmware_version'.tr, 
            info: '${a31.version.value}.${a31.mcuVersion}',
          ),
          SetBox2(
            icon: GoIcon(
              name: GoIcons.deviceUUID,
              size: 18.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'UUID', 
            info: a31.uuid,
          ),
          Obx(() => Visibility(
            visible: a31.showLED.value,
            child: SetBox2(
              title: 'led'.tr, 
              info: '',
              icon: GoIcon(
                name: GoIcons.led,
                size: 24.sp,
                color: themeBase.primaryColor.value,
              ), 
              iconInfo: GoSwtich(
                switchVal: a31.led,
                tap: (){
                  if(a31.led.value){
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:LED:1&');
                  }else{
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:LED:0&');
                  }
                }
              ),
            ),
          )),
          Obx(() => Visibility(
            visible: a31.showMMC.value,
              child: SetBox2(
              title: 'phono_mode'.tr, 
              info: '',
              icon: GoIcon(
                name: GoIcons.srcPhono,
                size: 22.sp,
                color: themeBase.primaryColor.value,
              ),
              iconInfo: OnTapScaleToSmallBox(
                onTap: (){
                  // device.sendMsgToSocket('MCU+PAS+RAKOIT:VST&');
                  // if(device.vst.value < 10) device.vstAdd();
                  // phonoMode.value
                  if(a31.phonoMode.value){
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:MMC:0&');
                  }else{
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:MMC:1&');
                  }
                },
                child: Container(
                  width: 46.sp,
                  height: 46.sp,
                  alignment: Alignment.center,
                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                  decoration: BoxDecoration(
                    color: themeBase.primaryColor.value,
                    borderRadius: BorderRadius.circular(6.sp)
                  ),
                  child: AText(
                    text: a31.phonoMode.value?'MC':'MM',
                    size: 14.sp,
                    color: themeBase.textColor1.value,
                  )
                ),
              ),
            ),
          )),
          Obx(() => Visibility(
            visible: a31.showBEP.value,
            child: SetBox2(
              title: 'key_sound'.tr, 
              info: '',
              icon: GoIcon(
                name: GoIcons.volumeHigh,
                size: 21.sp,
                color: themeBase.primaryColor.value,
              ), 
              iconInfo: GoSwtich(
                switchVal: a31.keySound,
                tap: (){
                  if(a31.keySound.value){
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:BEP:1&');
                  }else{
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:BEP:0&');
                  }
                }
              ),
            ),
          )),
          Obx(() => Visibility(
            visible: a31.showARC.value,
            child: SetBox2(
              title: 'arc_mode'.tr, 
              info: '',
              iconInfo: GoSwtich(
                switchVal: a31.arcMode,
                tap: (){
                  if(a31.arcMode.value){
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:ARC:0&');
                  }else{
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:ARC:1&');
                  }
                }
              ),
            ),
          )),
          Obx(() => Visibility(
            visible: a31.showTLM.value,
            child: SetBox2(
              title: 'disable_speaker'.tr, 
              info: '',
              iconInfo: GoSwtich(
                switchVal: a31.tlmMode,
                tap: (){
                  if(a31.tlmMode.value){
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:TLM:1&');
                  }else{
                    a31.sendMsgToSocket('MCU+PAS+RAKOIT:TLM:0&');
                  }
                }
              ),
            ),
          )),
          SetBox2(
            icon: GoIcon(
              name: GoIcons.add,
              size: 21.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'volume_step'.tr, 
            info: '',
            iconInfo: const SetVolumeUpBox()
          ),
          SetBox2(
            onTap: (){
              Get.bottomSheet(
                SizedBox(
                  height: 240.h,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(
                          top: 22.sp,
                          bottom: 22.sp
                        ),
                        child: AText(
                          text: 'reset_device'.tr,
                          family: TextFamily.medium,
                          size: themeBase.subHeadingFont.value,
                          color: themeBase.primaryColor.value,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 22.sp),
                        child: AText(
                          text: 'name_change_text'.tr,
                          family: TextFamily.medium,
                          size: themeBase.subBodyFont.value,
                          color: themeBase.primaryColor.value,
                          softWrap: true,
                        )
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.sp,vertical: 16.sp),
                        child:  Row(
                          children: [
                            Flexible(
                              flex: 1,
                              child: GestureDetector(
                                onTap: ()=> Get.back(),
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 43.sp,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.r),
                                    color:const Color.fromRGBO(230, 230, 230, 1),
                                  ),
                                  child: AText(
                                    text: 'cancel'.tr,
                                    color: const Color.fromRGBO(128, 128, 128, 1),
                                    size: themeBase.subBodyFont.value + 1.sp,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 16.sp,),
                            Flexible(
                              flex: 1,
                              child: GestureDetector(
                                onTap: (){
                                  // if(mycontrol.text.length > 3) _sendConnectWifi(mycontrol.text,device);
                                  homCon.selectDevice!.resetDevice();
                                  Get.offAllNamed('/home');
                                  homCon.adevices.remove(homCon.selectDevice!);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 43.sp,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.r),
                                    color:themeBase.primaryColor.value.withAlpha(200),
                                  ),
                                  child: AText(
                                    text: 'confirm'.tr,
                                    color: const Color.fromRGBO(255, 255, 255, 1),
                                    size: themeBase.subBodyFont.value + 1.sp,
                                  ),
                                ),
                              ),
                            )
                          ],
                        )
                      )
                    ]
                  )
                ),
                backgroundColor: themeBase.textColor1.value,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
                )
              );
            },
            icon: GoIcon(
              name: GoIcons.deviceReset,
              size: 19.sp,
              color: themeBase.primaryColor.value,
            ), 
            title: 'reset_device'.tr, 
            info: '',
            iconInfo: Transform.rotate(
              angle: pi / 1,
              child: GoIcon(
                name: GoIcons.back,
                size: 12.sp,
                color: const Color.fromRGBO(128, 128, 128, 1),
              ),
            ),
            last: true,
          ),
          // SetBox2(
          //   title: 'Disconnect', 
          //   info: 'dis',
          //   onTap: () {
          //     netDevice.socket!.close();
          //     homCon.removeDevice(netDevice);
          //   },
          // ),
        ]
      ),
    ]
  );
}

String convertToUTF8Hex(String data) {
  List<int> utf8Bytes = utf8.encode(data); // 将字符串转换为 UTF-8 字节序列
  String hexString = bytesToHex(utf8Bytes); // 将字节序列转换为十六进制字符串
  return hexString;
}

String bytesToHex(List<int> bytes) {
  const hexDigits = '0123456789ABCDEF';
  StringBuffer buffer = StringBuffer();
  for (int byte in bytes) {
    buffer.write(hexDigits[(byte >> 4) & 0x0F]);
    buffer.write(hexDigits[byte & 0x0F]);
  }
  return buffer.toString();
}
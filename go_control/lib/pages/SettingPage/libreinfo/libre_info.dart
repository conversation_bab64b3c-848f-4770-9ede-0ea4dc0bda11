import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/api/api.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/input_box.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/SettingPage/components/setting_box.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

class LibreSetInfo extends StatelessWidget {
  const LibreSetInfo({super.key});

  @override
  Widget build(BuildContext context) {
    Libre libre = homCon.selectDevice as Libre;

    libre.getGast();
    libre.getTimeZone();

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: 'settings'.tr,
      ),
      body: BodyBox(
        showTopBar: true,
        scrollBox: false,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: SettingBox(
            children: [
              Obx(() => SetBox2(
                icon: GoIcon(
                  name: GoIcons.deviceName,
                  size: 20.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'device_name'.tr, 
                info: libre.name.value,
                iconInfo: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      margin: EdgeInsets.only(right: 6.sp),
                      child: Obx(()=> AText(
                        text: libre.name.value,
                        size: 14.sp,
                        color: themeBase.primaryColor.value.withOpacity(.7),
                      )),
                    ),
                    Obx(()=> Visibility(
                      visible: !libre.inAppleHome.value,
                      child: GoIcon(
                        name: GoIcons.nameChange,
                        size: 17.sp,
                        color: themeBase.primaryColor.value,
                      ),
                    )),
                  ],
                ),
                onTap: libre.inAppleHome.value? null : () {
                  TextEditingController nameController = TextEditingController();
                  showMyBottomSheet(
                    height: 250.sp, 
                    title: 'name_change'.tr, 
                    content: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.sp),
                      child: Column(
                        children: [
                          InputBox(
                            height: 45.sp, 
                            focusNode: FocusNode(), 
                            textController: nameController, 
                            textColor: themeBase.primaryColor.value,
                            placeholder: 'device_name'.tr,
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            margin: EdgeInsets.only(top: 10.sp,left: 5.sp),
                            child: AText(
                              text: 'g3c3'.tr,
                              size: 14.sp,
                              softWrap: true,
                              color: themeBase.buttonColor2.value,
                            ),
                          )
                        ],
                      )
                    ), 
                    showCancel: true.obs,
                    cancel: ()=> Get.back(),
                    confirm: (){
                      if(nameController.text.length > 2 &&nameController.text.length < 17){
                        String name = nameController.text;
                        libre.luciSocket!.add(ApiManage.luciApi.lsxInfo(
                          data: name,
                          command: 0x005A,
                          commandType: 0x02
                        ));
                        libre.name.value = name;
                        Get.back();
                        // device.setDeviceName(data);
                      }
                    }
                  );
                },
              )),
              Obx(() => SetBox2(
                icon: GoIcon(
                  name: GoIcons.deviceVersion,
                  size: 20.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'firmware_version'.tr,
                info: libre.version.value,
                // iconInfo: Row(
                //   mainAxisAlignment: MainAxisAlignment.end,
                //   children: [
                //     Container(
                //       margin: EdgeInsets.only(right: 6.sp),
                //       child: Obx(()=> AText(
                //         text: libre.version.value.substring(0,libre.version.value.lastIndexOf('.')),
                //         size: 14.sp,
                //         color: themeBase.primaryColor.value.withOpacity(.7),
                //       )),
                //     ),
                //     GoIcon(
                //       name: GoIcons.versionInfo,
                //       size: 14.sp,
                //       color: themeBase.primaryColor.value,
                //     ),
                //   ],
                // )
              )),
              SetBox2(
                icon: GoIcon(
                  name: GoIcons.deviceIp,
                  size: 18.sp,
                  color: themeBase.primaryColor.value,
                ),
                title: 'Device IP',
                info: libre.ip.value,
              ),
              SetBox2(
                icon: GoIcon(
                  name: GoIcons.deviceMac,
                  size: 19.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'g4c4'.tr,
                info: libre.netInfo.value,
              ),
              SetBox2(
                icon: GoIcon(
                  name: GoIcons.timeZone,
                  size: 20.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'g5c5'.tr,
                info: libre.timeZone.value,
                iconInfo: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Obx(()=> AText(
                      text: libre.timeZone.value,
                      size: 14.sp,
                      color: themeBase.primaryColor.value.withOpacity(.7),
                    )),
                    SizedBox(width: 7.sp),
                    Transform.rotate(
                      angle: pi / 1,
                      child: GoIcon(
                        name: GoIcons.back,
                        size: 12.sp,
                        color: themeBase.primaryColor.value,
                      ),
                      // onTap lolcation fl

                    ),
                  ],
                ),
                onTap: () => Get.toNamed('/timezone'),
              ),
              SetBox2(
                icon: Transform.scale(
                  scale: .72,
                  child: Image.asset(
                    'images/google_cast.png',
                    fit: BoxFit.cover,
                  ),
                ),
                title: 'Google Cast', 
                info: libre.netmode.value,
                onTap: () {
                  Get.toNamed('/libreSetInfo/libreGcast');
                },
                iconInfo: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Obx(()=> AText(
                      text: libre.tos.value? 'g2c1'.tr : 'g2c2'.tr,
                      size: 14.sp,
                      color: themeBase.primaryColor.value.withOpacity(.7),
                    )),
                    SizedBox(width: 7.sp),
                    Transform.rotate(
                      angle: pi / 1,
                      child: GoIcon(
                        name: GoIcons.back,
                        size: 12.sp,
                        color: themeBase.primaryColor.value,
                      ),
                    ),
                  ],
                )
              ),
              SetBox2(
                icon: GoIcon(
                  name: GoIcons.feedback,
                  size: 18.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'g6c6'.tr, 
                info: libre.netmode.value,
                last: true,
                iconInfo: Transform.rotate(
                  angle: pi / 1,
                  child: GoIcon(
                    name: GoIcons.back,
                    size: 12.sp,
                    color: themeBase.primaryColor.value,
                  ),
                ),
                onTap: (){
                  Get.toNamed('/libreReport');
                },
              ),
            ]
          )
        )
      )
    );
  }
}

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:url_launcher/url_launcher.dart';

class SetGCast extends StatelessWidget {
  const SetGCast({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      topbar: Container(
        height: themeBase.topBarHeight + themeBase.searHeight.value,
        padding: EdgeInsets.only(top: themeBase.searHeight.value),
      ),
      scroll: false,
      body: BodyBox(
        scrollBox: false,
        showTopBar: true,
        child: SafeArea(
          top: false,
          bottom: true,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.sp),
                child: Column(
                  children: [
                    // Container(
                    //   margin: EdgeInsets.symmetric(vertical: 12.sp),
                    //   child: 
                    //   // child: Row(
                    //   //   mainAxisAlignment: MainAxisAlignment.center,
                    //   //   children: [
                    //   //     GoIcon(
                    //   //       name: GoIcons.chromecast,
                    //   //       size: 39.sp,
                    //   //       color: themeBase.primaryColor.value,
                    //   //     ),
                    //   //     SizedBox(width: 9.sp),
                    //   //     AText(
                    //   //       text: 'Google Cast',
                    //   //       size: 28.sp,
                    //   //       color: themeBase.primaryColor.value,
                    //   //       family: TextFamily.bold,
                    //   //     )
                    //   //   ],
                    //   // ),
                    // ),
                    Transform.scale(
                      scale: .9,
                      child: Image.asset(
                        'images/google_logo.png',
                        fit: BoxFit.cover,
                      ),
                    ),
                    SizedBox(height: 33.sp),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.sp),
                      child: Text.rich(
                        softWrap: true,
                        TextSpan(
                          text: 'You can cast music from your favourite mobile apps to your device. Your use of Google Cast is subject to the ',
                          style: TextStyle(
                            color: themeBase.primaryColor.value,
                            fontSize: 17.sp,
                            fontFamily: 'Medium',
                          ),
                          children: [
                            TextSpan(
                              text: 'Google Terms of Service',
                              style:  TextStyle(
                                color: themeBase.tipsColor.value,
                                decoration: TextDecoration.underline,
                                decorationStyle: TextDecorationStyle.solid,
                                decorationColor: themeBase.tipsColor.value,
                              ),
                              mouseCursor: SystemMouseCursors.precise,
                              recognizer: TapGestureRecognizer()..onTap = () async {
                                final uri = Uri.parse('https://policies.google.com/terms?color_scheme=dark');
                                if (await canLaunchUrl(uri)) {
                                  await launchUrl(uri);
                                } else {
                                  Log.e('Could not launch $uri');
                                }
                              },
                            ),
                            TextSpan(
                              text: '.',
                              style: TextStyle(
                                color: themeBase.primaryColor.value,
                                fontSize: 17.sp,
                                fontFamily: 'Medium'
                              ),
                            ),
                            TextSpan(
                              text: ' The ',
                              style: TextStyle(
                                color: themeBase.primaryColor.value,
                                fontSize: 17.sp,
                                fontFamily: 'Medium'
                              ),
                            ),
                            TextSpan(
                              text: 'Google Privacy Policy',
                              style: TextStyle(
                                color: themeBase.tipsColor.value,
                                decoration: TextDecoration.underline,
                                decorationStyle: TextDecorationStyle.solid,
                                decorationColor: themeBase.tipsColor.value,
                              ),
                              mouseCursor: SystemMouseCursors.precise,
                              recognizer: TapGestureRecognizer()..onTap = () async {
                                final uri = Uri.parse('https://policies.google.com/privacy?color_scheme=dark');
                                if (await canLaunchUrl(uri)) {
                                  await launchUrl(uri);
                                } else {
                                  Log.e('Could not launch $uri');
                                }
                              },
                            ),
                            TextSpan(
                              text: ' describes how your data is handled by Google Cast.',
                              style: TextStyle(
                                color: themeBase.primaryColor.value,
                                fontSize: 17.sp,
                                fontFamily: 'Medium'
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 18.sp),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.sp),
                      child: Text.rich(
                        softWrap: true,
                        TextSpan(
                          text: 'To learn more about how Google Protects your privacy, go to ',
                          style: TextStyle(
                            color: themeBase.primaryColor.value.withOpacity(.7),
                            fontSize: 17.sp,
                            fontFamily: 'Medium'
                          ),
                          children: [
                            TextSpan(
                              text: 'g.co/cast/privacy.',
                              style: TextStyle(
                                color: themeBase.primaryColor.value,
                                fontSize: 17.sp,
                                fontFamily: 'Medium'
                              ),
                              mouseCursor: SystemMouseCursors.precise,
                              recognizer: TapGestureRecognizer()..onTap = () async {
                                final uri = Uri.parse('http://g.co/cast/privacy');
                                if (await canLaunchUrl(uri)) {
                                  await launchUrl(uri);
                                } else {
                                  Log.e('Could not launch $uri');
                                }
                              },
                            )
                          ]
                        ),
                      )
                    ),
                    Obx(()=> AnimatedOpacity(
                      opacity: homCon.showTos.value? 1 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: Container(
                        margin: EdgeInsets.only(top: 30.sp),
                        padding: EdgeInsets.symmetric(
                          vertical: 8.sp,
                          horizontal: 4.sp
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(15.r)
                        ),
                        alignment: Alignment.center,
                        child: Obx(()=> AText(
                          text: homCon.showTosContext.value,
                          color: themeBase.blockColor.value,
                          textAlign: TextAlign.center,
                        )),
                      ),
                    ))
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.sp,
                  vertical: 12.sp
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: OnTapScaleToSmallBox(
                        onTap: () {
                          try {
                            final libre = homCon.selectDevice! as Libre;
                            if (libre.skipGcast.value == true) {
                              return Get.offNamed('/home');
                            }
                            showMyBottomSheet(
                              height: 220.sp, 
                              title: 'Notice', 
                              content: Container(
                                padding: EdgeInsets.symmetric(horizontal: 20.sp),
                                alignment: Alignment.center,
                                child: AText(
                                  text: "You need to accept this terms to use Google Cast. And if you're not sure now, you could still accept it later in device settings.",
                                  size: themeBase.subBodyFont.value + 1.sp,
                                  color: themeBase.primaryColor.value.withAlpha((255 * .7).round()),
                                  softWrap: true,
                                  textAlign: TextAlign.center,
                                )
                              ), 
                              showCancel: true.obs,
                              cancel: () {
                                Get.back();
                              },
                              confirm: () async {
                                libre.skipGcast.value = true;
                                Get.back();
                                await Future.delayed(const Duration(milliseconds: 100));
                                Get.offAllNamed(Routes.homePage);
                                StorageClass.setStorage('${libre.name.value}&NFG', true);
                                if (libre.pairGoToGcast.value) {
                                  libre.pairGoToGcast.value = false;
                                }
                              }
                            );
                          } catch (e) {
                            Get.offNamed('/home');
                          }
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44.sp,
                          decoration: BoxDecoration(
                            color: themeBase.buttonColor2.value,
                            borderRadius: BorderRadius.circular(44.sp / 5)
                          ),
                          child: AText(
                            text: 'Skip',
                            size: 16.sp,
                            color: Colors.white,
                          ),
                        )
                      )
                    ),
                    SizedBox(width: 12.sp),
                    Flexible(
                      child: OnTapScaleToSmallBox(
                        onTap: () {
                          final libre = homCon.selectDevice! as Libre;
                          libre.gcastAndAccept();
                          if (libre.pairGoToGcast.value) {
                            libre.pairGoToGcast.value = false;
                          }
                        },
                        child: Container(
                          height: 44.sp,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: themeBase.primaryColor.value,
                            borderRadius: BorderRadius.circular(44.sp / 5)
                          ),
                          child: AText(
                            text: 'Accept',
                            size: 16.sp,
                            color: Colors.white,
                          ),
                        ),
                      )
                    ),
                  ],
                ),
              )
            ],
          ),
        )
      ),
    );
  }
}
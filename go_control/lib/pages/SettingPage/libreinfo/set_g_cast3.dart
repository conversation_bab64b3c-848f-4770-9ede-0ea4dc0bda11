import 'dart:io';
import 'package:android_intent_plus/android_intent.dart';
import 'package:android_intent_plus/flag.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:url_launcher/url_launcher.dart';

class SetGCast3 extends StatelessWidget {
  const SetGCast3({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      topbar: Top2tion(
        title: 'Setup',
        onTap: () {
          Get.back();
        },
      ),
      scroll: false,
      body: BodyBox(
        scrollBox: false,
        showTopBar: true,
        child: SafeArea(
          top: false,
          bottom: true,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Transform.scale(
                    scale: .88,
                    child: Image.asset(
                      'images/google_logo.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 22.sp),
                    margin: EdgeInsets.symmetric(vertical: 12.sp),
                    child: AText(
                      text: 'You are ready to start casting. Look for the Cast button in hundreds of apps',
                      size: 17.sp,
                      color: themeBase.primaryColor.value,
                      softWrap: true,
                    )
                  ),
                  Transform.scale(
                    scale: .7,
                    child: Image.asset(
                      'images/google_works.webp',
                      fit: BoxFit.cover,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 22.sp),
                    margin: EdgeInsets.symmetric(vertical: 12.sp),
                    child: AText(
                      text: 'To setup voice control from Google Assistant-enabled device and multiroom groups with Google Cast-enabled devices, download the Google Home app',
                      size: 16.sp,
                      color: themeBase.primaryColor.value,
                      softWrap: true,
                    )
                  ),
                ]
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.sp,
                  vertical: 12.sp
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: OnTapScaleToSmallBox(
                        onTap: () {
                          Get.offAllNamed('/home');
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44.sp,
                          decoration: BoxDecoration(
                            color: themeBase.buttonColor2.value,
                            // location functhe location
                            borderRadius: BorderRadius.circular(44.sp / 5)
                          ),
                          child: AText(
                            text: "Skip",
                            size: 16.sp,
                            color: Colors.white,
                          ),
                        )
                      )
                    ),
                    SizedBox(width: 12.sp),
                    Flexible(
                      child: OnTapScaleToSmallBox(
                        onTap: () async {
                          Get.offAllNamed('/home');
                          const url = 'chromecast://setup/device';
                          if (Platform.isAndroid) {
                            launchGoogleHomeApp();
                          } else {
                            if (await canLaunchUrl(Uri.parse(url))) {
                              await launchUrl(Uri.parse(url));
                              return;
                            } else {
                              Uri uri = Uri.parse('https://apps.apple.com/cn/app/google-home/id680819774');
                              if (await canLaunchUrl(uri)) await launchUrl(uri);
                            }
                          }
                        },
                        child: Container(
                          height: 44.sp,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: themeBase.primaryColor.value,
                            borderRadius: BorderRadius.circular(44.sp / 5)
                          ),
                          child: AText(
                            text: "Open Home app",
                            size: 16.sp,
                            color: Colors.white,
                          ),
                        ),
                      )
                    ),
                  ],
                ),
              )
            ],
          ),
        )
      )
    );
  }
}

void launchGoogleHomeApp() async {
  const packageName = 'com.google.android.apps.chromecast.app';

  final AndroidIntent intent = AndroidIntent(
    action: 'android.intent.action.VIEW',
    package: packageName,
    flags: [Flag.FLAG_ACTIVITY_NEW_TASK],
  );

  try {
    await intent.launch();
  } catch (e) {
    Uri uri = Uri.parse('https://play.google.com/store/search?q=Google%20Home&c=apps');
    if (await canLaunchUrl(uri)) await launchUrl(uri);
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/core/permission.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class LibreReportPage extends StatelessWidget {
  const LibreReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final ReportIssueCon reportIssueCon = Get.put(ReportIssueCon());
    return PagesBody(
      topbar: Top2tion(
        title: 'Report issue'.tr,
      ),
      scroll: false,
      body: BodyBox(
        scrollBox: false,
        showTopBar: true,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: SingleChildScrollView(
            child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  SizedBox(height: 20.sp),
                  const MainFeedBack(),
                  SizedBox(height: 20.sp),
                  const NameFeedBack(),
                  const EmailFeedBack(),
                  OnTapScaleToSmallBox(
                    onTap: () async {
                      TimeOfDay? result = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                        cancelText: "Cancel",
                        helpText: "When did the problem occur?",
                        confirmText: "Confirm"
                      );
                      if (result != null) {
                        reportIssueCon.hour.value = result.hour;
                        reportIssueCon.minute.value = result.minute;
                       
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.only(top: 12.sp),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r)
                      ),
                      height: 48.sp,
                      padding: EdgeInsets.symmetric(horizontal: 14.sp),
                      child: Row(
                        children: [
                          AText(
                            text: 'g6c7'.tr,
                            color: themeBase.primaryColor.value,
                          ),
                          Obx(()=> AText(
                            text: '${reportIssueCon.hour.value}',
                            color: themeBase.primaryColor.value.withOpacity(0.4),
                          )),
                          SizedBox(width: 18.sp),
                          AText(
                            text: 'g6c8'.tr,
                            color: themeBase.primaryColor.value,
                          ),
                          Obx(()=> AText(
                            text: '${reportIssueCon.minute.value}',
                            color: themeBase.primaryColor.value.withOpacity(0.4),
                          )),
                        ],
                      ),
                    ),
                  )
                ],
              ),
              OnTapScaleToSmallBox(
                child: Container(
                  height: 48.sp,
                  margin: EdgeInsets.only(bottom: 17.sp),
                  decoration: BoxDecoration(
                    color: themeBase.primaryColor.value,
                    borderRadius: BorderRadius.circular(10.r)
                  ),
                  alignment: Alignment.center,
                  child: AText(
                    text: 'g6c9'.tr,
                    color: Colors.white,
                    size: 17.sp,
                  ),
                ),
                onTap: () async {
                  String feedbackInfo = reportIssueCon.con1.text;
                  String name = reportIssueCon.con2.text;
                  String email = reportIssueCon.con3.text;
                  String uuid = homCon.generateUUID();
                  Log.w(uuid);
                  String data = '{"app_info":{"app_version":"v2.0.214","brand":"Go Control","date_time":"h:${reportIssueCon.hour.value} - m:${reportIssueCon.minute.value}","model":"${AppPermission.phoneSystem}"},"description":"$feedbackInfo","email":"$email","name":"$name","source":"App","time":"time","uuid":"$uuid"}';
                  Libre libre = homCon.selectDevice as Libre;
                  libre.reportFeedBack(data);
                  Get.defaultDialog(
                    title: 'Report',
                    titleStyle: TextStyle(
                      fontFamily: 'Medium',
                      fontSize: 17.sp
                    ),
                    content: Column(
                      children: [
                        const Loading(),
                        AText(
                          text: 'Reporting issue...',
                          size: 15.sp,
                          color: themeBase.primaryColor.value,
                          softWrap: true,
                        )
                      ],
                    ),
                  );
                  await Future.delayed(const Duration(seconds: 3),(){
                    AppToast.show('toast5'.tr);
                    Get.back();
                  });
                  await Future.delayed(const Duration(milliseconds: 50),()=> Get.back());
                },
              )
            ],
          ),
          )
        )
      )
    );
  }
}


class MainFeedBack extends StatelessWidget {
  const MainFeedBack({super.key});

  @override
  Widget build(BuildContext context) {
    final ReportIssueCon reportIssueCon = Get.put(ReportIssueCon());
    RxString contex = ''.obs;

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r)
          ),
          height: 160.sp,
          child: TextField(
            keyboardType: TextInputType.multiline,
            maxLines: null,
            maxLength: 250,
            autofocus: false,
            controller: reportIssueCon.con1,
            onTapOutside: (event) => FocusScope.of(context).unfocus(),
            onChanged: (v) {
              contex.value = v;
            },
            decoration:  InputDecoration(
              counterText: '',
              focusedBorder: OutlineInputBorder( 
                borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
                borderSide: BorderSide.none, // 取消边框
              ),
              enabledBorder: OutlineInputBorder( 
                borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
                borderSide: BorderSide.none, // 取消边框
              ),
              hintText: 'Please describe the issue with details.'.tr,
              hintStyle: TextStyle(
                color: themeBase.primaryColor.value.withOpacity(0.4),
                fontSize: 15.sp,
                fontFamily: 'Medium'
              )
            ),
            style: TextStyle(
              fontSize: 15.sp,
              color: themeBase.primaryColor.value,
              fontFamily: 'Medium'
            )
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 7.sp),
          alignment: Alignment.topRight,
          child: Obx(()=> AText(
            text: '${250 - contex.value.length} characters remaining',
            size: 12.sp,
            color: themeBase.primaryColor.value,
          )),
        )
      ],
    );
  }
}

class NameFeedBack extends StatelessWidget {
  const NameFeedBack({super.key});

  @override
  Widget build(BuildContext context) {
    final ReportIssueCon reportIssueCon = Get.put(ReportIssueCon());

    return Container(
      margin: EdgeInsets.only(top: 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r)
      ),
      height: 48.sp,
      child: TextField(
        keyboardType: TextInputType.multiline,
        autofocus: false,
        controller: reportIssueCon.con2,
        onTapOutside: (event) => FocusScope.of(context).unfocus(),
        decoration:  InputDecoration(
          counterText: '',
          focusedBorder: OutlineInputBorder( 
            borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
            borderSide: BorderSide.none, // 取消边框
          ),
          enabledBorder: OutlineInputBorder( 
            borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
            borderSide: BorderSide.none, // 取消边框
          ),
          hintText: 'Your name'.tr,
          hintStyle: TextStyle(
            color: themeBase.primaryColor.value.withOpacity(0.4),
            fontSize: 15.sp,
            fontFamily: 'Medium'
          )
        ),
        style: TextStyle(
          fontSize: 15.sp,
          color: themeBase.primaryColor.value,
          fontFamily: 'Medium'
        )
      ),
    );
  }
}

class EmailFeedBack extends StatelessWidget {
  const EmailFeedBack({super.key});

  @override
  Widget build(BuildContext context) {
    final ReportIssueCon reportIssueCon = Get.put(ReportIssueCon());
    
    return Container(
      margin: EdgeInsets.only(top: 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r)
      ),
      height: 48.sp,
      child: TextField(
        onTapOutside: (event) => FocusScope.of(context).unfocus(),
        keyboardType: TextInputType.multiline,
        autofocus: false,
        controller: reportIssueCon.con3,
        decoration:  InputDecoration(
          counterText: '',
          focusedBorder: OutlineInputBorder( 
            borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
            borderSide: BorderSide.none, // 取消边框
          ),
          enabledBorder: OutlineInputBorder( 
            borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
            borderSide: BorderSide.none, // 取消边框
          ),
          hintText: 'E-mail'.tr,
          hintStyle: TextStyle(
            color: themeBase.primaryColor.value.withOpacity(0.4),
            fontSize: 15.sp,
            fontFamily: 'Medium'
          )
        ),
        style: TextStyle(
          fontSize: 15.sp,
          color: themeBase.primaryColor.value,
          fontFamily: 'Medium'
        )
      ),
    );
  }
}


class ReportIssueCon extends GetxController {
  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();

  final RxInt hour = 0.obs;
  final RxInt minute = 0.obs;
}

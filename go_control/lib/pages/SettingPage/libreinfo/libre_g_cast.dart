import 'dart:io';
import 'dart:math';
import 'package:android_intent_plus/flag.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/sound/components/go_swtich.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:android_intent_plus/android_intent.dart';

class LibreGCastPage extends StatelessWidget {
  const LibreGCastPage({super.key});

  @override
  Widget build(BuildContext context) {
    Libre libre = homCon.selectDevice as Libre;
    
    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: 'Setup',
      ),
      body: BodyBox(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 22.sp,
            vertical: 16.sp
          ),
          child: Column(
            children: [
              Transform.scale(
                scale: .9,
                child: Image.asset(
                  'images/google_logo.png',
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(height: 5.sp),
              OnTapScaleToSmallBox(
                onTap: () {
                  if (!libre.tos.value) {
                    Get.toNamed('/libreSetInfo/setGcast');
                  }
                },
                child: Container(
                  height: 49.sp,
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 12.sp),
                  margin: EdgeInsets.symmetric(vertical: 9.sp),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(52.sp / 6),
                    border: Border.all(
                      width: 1.sp,
                      color: Colors.grey.withOpacity(.4)
                    )
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Obx(()=> AText(
                        text: libre.tos.value? 'gcast1'.tr : 'gcast2'.tr,
                        color: themeBase.primaryColor.value.withOpacity(.8),
                        size: 17.sp,
                        
                      )),
                      Obx(()=> Visibility(
                        visible: !libre.tos.value,
                        child: Transform.rotate(
                          angle: pi / 1,
                          child: GoIcon(
                            name: GoIcons.back,
                            color: themeBase.primaryColor.value.withOpacity(.8),
                            size: 13.sp,
                          ),
                        ),
                      ))
                    ],
                  )
                ),
              ),
              SizedBox(height: 12.sp),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.sp),
                margin: EdgeInsets.symmetric(vertical: 8.sp),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: AText(
                        text: 'gcast3'.tr,
                        size: 17.sp,
                        color: themeBase.primaryColor.value.withOpacity(.8),
                        softWrap: true,
                        
                      ),
                    ),
                    SizedBox(width: 20.sp),
                    Container(
                      margin: EdgeInsets.only(top: 4.sp),
                      child: GoSwtich(
                        switchVal: libre.crashReport,
                        tap: () {
                          if (libre.crashReport.value == true) {
                            libre.sendCrashReport(true);
                          } else {
                            libre.sendCrashReport(false);
                          }
                        },
                      ),
                    )
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.sp),
                child: AText(
                  text: '${'gcast4'.tr} myaccount.google.com',
                  size: 16.sp,
                  color: themeBase.primaryColor.value.withOpacity(.8),
                  softWrap: true,
                  
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.sp),
                margin: EdgeInsets.symmetric(vertical: 8.sp),
                child: Text.rich(
                  softWrap: true,
                  TextSpan(
                    text: 'gcast5'.tr,
                    style: TextStyle(
                      color: themeBase.primaryColor.value.withOpacity(.7),
                      fontSize: 16.sp,
                      fontFamily: 'Medium',
                    ),
                    children: [
                      TextSpan(
                        text: 'Google Home App',
                        style:  TextStyle(
                          color: themeBase.tipsColor.value,
                          decoration: TextDecoration.underline,
                          decorationStyle: TextDecorationStyle.solid,
                          decorationColor: themeBase.tipsColor.value,
                        ),
                        mouseCursor: SystemMouseCursors.precise,
                        recognizer: TapGestureRecognizer()..onTap = () async {
                          const url = 'chromecast://setup/device';
                          if (Platform.isAndroid) {
                            launchGoogleHomeApp();
                          } else {
                            if (await canLaunchUrl(Uri.parse(url))) {
                              await launchUrl(Uri.parse(url));
                              return;
                            } else {
                              Uri uri = Uri.parse('https://apps.apple.com/cn/app/google-home/id680819774');
                              if (await canLaunchUrl(uri)) await launchUrl(uri);
                            }
                          }
                        },
                      ),
                    ]
                  )
                )
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 3.sp),
                child: Column(
                // mainAxisAlignment: MainAxisAlignment,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: AText(
                      text: 'gcast6'.tr,
                      size: 17.sp,
                      color: themeBase.primaryColor.value.withOpacity(.7),
                      family: TextFamily.bold,
                      softWrap: true,
                      
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: GestureDetector(
                      onTap: ()=> goTou(),
                      child: Text(
                        'gcast7'.tr,
                        style: TextStyle(
                          color: themeBase.tipsColor.value,
                          decoration: TextDecoration.underline,
                          decorationStyle: TextDecorationStyle.solid,
                          decorationColor: themeBase.tipsColor.value,
                          fontSize: 14.sp,
                          
                        ),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: GestureDetector(
                      onTap: ()=> goTou(),
                      child: Text(
                        'gcast8'.tr,
                        style: TextStyle(
                          color: themeBase.tipsColor.value,
                          decoration: TextDecoration.underline,
                          decorationStyle: TextDecorationStyle.solid,
                          decorationColor: themeBase.tipsColor.value,
                          fontSize: 14.sp,
                          
                        ),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: GestureDetector(
                      onTap: ()=> goTou(),
                      child: Text(
                        'gcast9'.tr,
                        style: TextStyle(
                          color: themeBase.tipsColor.value,
                          decoration: TextDecoration.underline,
                          decorationStyle: TextDecorationStyle.solid,
                          decorationColor: themeBase.tipsColor.value,
                          fontSize: 14.sp,
                          
                        ),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: GestureDetector(
                      onTap: ()=> goTou(),
                      child: Text(
                        'gcast10'.tr,
                        style: TextStyle(
                          color: themeBase.tipsColor.value,
                          decoration: TextDecoration.underline,
                          decorationStyle: TextDecorationStyle.solid,
                          decorationColor: themeBase.tipsColor.value,
                          fontSize: 14.sp,
                          
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              )
            ],
          ),
        )
      ),
    );
  }
}

void goTou() async {
  Uri uri = Uri.parse('https://www.android.com/better-together/#cast');
  if (await canLaunchUrl(uri)) {
    await launchUrl(uri);
  } else {
    Log.e('Could not launch $uri');
  }
}


void launchGoogleHomeApp() async {
  const packageName = 'com.google.android.apps.chromecast.app';

  final AndroidIntent intent = AndroidIntent(
    action: 'android.intent.action.VIEW',
    package: packageName,
    flags: [Flag.FLAG_ACTIVITY_NEW_TASK],
  );

  try {
    await intent.launch();
  } catch (e) {
    Uri uri = Uri.parse('https://play.google.com/store/search?q=Google%20Home&c=apps');
    if (await canLaunchUrl(uri)) await launchUrl(uri);
  }
}

// 检查 Google Home App 是否安装
// Future<bool> checkIfGoogleHomeInstalled() async {
//   const packageName = 'com.google.android.apps.chromecast.app';
//   bool appIsInstalled = await InstalledApps.isAppInstalled(packageName) ?? false;
//   return appIsInstalled;
// }
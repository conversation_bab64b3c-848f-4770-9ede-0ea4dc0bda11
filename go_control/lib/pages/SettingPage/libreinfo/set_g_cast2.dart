import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class SetGCast2 extends StatelessWidget {
  const SetGCast2({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      topbar: Top2tion(
        title: 'Setup',
        onTap: () {
          Get.back();
        },
      ),
      scroll: false,
      body: BodyBox(
        scrollBox: false,
        showTopBar: true,
        child: SafeArea(
          top: false,
          bottom: true,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Transform.scale(
                    scale: .9,
                    child: Image.asset(
                      'images/google_logo.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      top: 33.sp,
                      bottom: 33.sp
                    ),
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(horizontal: 33.sp),
                    child: AText(
                      text: 'Help Improve Google Cast',
                      size: 26.sp,
                      color: themeBase.primaryColor.value,
                      family: TextFamily.bold,
                      softWrap: true,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      bottom: 22.sp
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 33.sp),
                    child: AText(
                      text: "Automatically send device usage and crash reports to Google. This setting does not affect your Google Account controls (myaccount.google.com)",
                      size: 17.sp,
                      color: themeBase.primaryColor.value,
                      softWrap: true,
                    ),
                  ),
                ]
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.sp,
                  vertical: 12.sp
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: OnTapScaleToSmallBox(
                        onTap: () {
                          final libre = homCon.selectDevice! as Libre;
                          libre.sendCrashReport(false);
                          Get.offNamed('/libreSetInfo/libreGcast');
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44.sp,
                          decoration: BoxDecoration(
                            color: themeBase.buttonColor2.value,
                            borderRadius: BorderRadius.circular(44.sp / 5)
                          ),
                          child: AText(
                            text: "No Thanks",
                            size: 16.sp,
                            color: Colors.white,
                          ),
                        )
                      )
                    ),
                    SizedBox(width: 12.sp),
                    Flexible(
                      child: OnTapScaleToSmallBox(
                        onTap: () {
                          final libre = homCon.selectDevice! as Libre;
                          libre.sendCrashReport(true);
                          Get.offNamed('/libreSetInfo/setGcast3');
                        },
                        child: Container(
                          height: 44.sp,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: themeBase.primaryColor.value,
                            borderRadius: BorderRadius.circular(44.sp / 5)
                          ),
                          child: AText(
                            text: "Yes I'm in",
                            size: 16.sp,
                            color: Colors.white,
                          ),
                        ),
                      )
                    ),
                  ],
                ),
              )
            ],
          ),
        )
      )
    );
  }
}
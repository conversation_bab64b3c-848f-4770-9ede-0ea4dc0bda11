
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/libre/timezongdata.dart';
import 'package:gocontrol/pages/SettingPage/components/setting_box.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

class TimeZonePage extends StatelessWidget {
  const TimeZonePage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: 'TimeZone'.tr,
      ),
      body: BodyBox(
        showTopBar: true,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: SingleChildScrollView(
            child: SettingBox(
              children: _getWidget(),
            ),
          )
        )
      )
    );
  }
}

List<Widget> _getWidget() {
  List<Widget> list = [];
  for (int i = 0; i < TimeZone.timeZoneList.length; i++) {
    list.add(SetBox2(
      title: TimeZone.timeZoneList[i], 
      info: '',
      iconInfo: Transform.rotate(
        angle: pi / 1,
        child: GoIcon(
          name: GoIcons.back,
          size: 14.sp,
          color: themeBase.primaryColor.value,
        ),
      ),
      onTap: () {
        // Libre libre = homCon.selectDevice! as Libre;
        // libre.setTimeZone(el);
        Get.toNamed(Routes.timezone2,arguments: i);
      },
    ));
  }
  return list;
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:simple_circular_progress_bar/simple_circular_progress_bar.dart';

class LibreUpdateVersionPage extends StatelessWidget {
  const LibreUpdateVersionPage({super.key});

  @override
  Widget build(BuildContext context) {
    if (homCon.selectDevice is Libre) {
      Libre libre = homCon.selectDevice as Libre;
      return PagesBody(
        scroll: false,
          topbar: Obx(()=> Top2tion(
            title: homCon.selectDevice!.name.value,
            ehBack: true,
          )),
          body: BodyBox(
            showTopBar: true,
            scrollBox: false,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Obx(()=> AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: _getBody(libre),
                ))
              ],
            )
          )
      );
    }
    return Container();
  }
}

Widget _getBody(Libre libre) {
  if (libre.updateState.value == 0) {
    return SizedBox(
      width: Get.width,
      height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Loading(),
          SizedBox(height: 22.sp),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.sp),
            child: AText(
              text: 'cn2s'.tr,
              color: themeBase.primaryColor.value,
              textAlign: TextAlign.center,
              size: 17.sp,
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  if (libre.updateState.value == 1) {
    // 升级开始
    return SizedBox(
      width: Get.width,
      height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Loading(),
          SizedBox(height: 22.sp),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.sp),
            child: AText(
              text: 'A new version is found and the new firmware version is downloaded'.tr,
              color: themeBase.primaryColor.value,
              textAlign: TextAlign.center,
              size: 17.sp,
              softWrap: true,
            ),
          )
        ],
      )
    );
  }

  if (libre.updateState.value == 2) {
    // 下载进度
    return SizedBox(
      width: Get.width,
      height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: Container(
              alignment: Alignment.bottomCenter,
              color: Colors.transparent,
              child: _getLoading(libre),
            ),
          ),
          SizedBox(height: 45.sp),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                const Loading(),
                SizedBox(height: 11.sp),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.sp),
                  child: AText(
                    text: 'Downloading the new firmware, please wait patiently...'.tr,
                    color: themeBase.primaryColor.value,
                    textAlign: TextAlign.center,
                    size: 17.sp,
                    softWrap: true,
                  ),
                ),
              ],
            ),
          ),
        ],
      )
    );
  }

  if (libre.updateState.value == 3) {
    // 下载完成
    // 升级进度
    return SizedBox(
      width: Get.width,
      height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: Container(
              alignment: Alignment.bottomCenter,
              color: Colors.transparent,
              child: _getLoading(libre),
            )
          ),
          SizedBox(height: 44.sp),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                const Loading(),
                SizedBox(height: 11.sp),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.sp),
                  child: AText(
                    text: 'Download complete, upgrading the current firmware...'.tr,
                    color: themeBase.primaryColor.value,
                    textAlign: TextAlign.center,
                    size: 17.sp,
                    softWrap: true,
                  ),
                ),
              ],
            )
          ),
          
        ],
      )
    );
  }

  if (libre.updateState.value == 4) {
    // 升级准备完成
    // 等待重启
    return SizedBox(
      width: Get.width,
      height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: Container(
              alignment: Alignment.bottomCenter,
              color: Colors.transparent,
              child: _getLoading(libre),
            )
          ),
          SizedBox(height: 44.sp),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                SizedBox(height: 11.sp),
                const Loading(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.sp),
                  child: AText(
                    text: 'All prepared, the device will reboot to finish the updating itself. And it will take about 1 ~ 2 minutes'.tr,
                    color: themeBase.primaryColor.value,
                    textAlign: TextAlign.center,
                    size: 17.sp,
                    softWrap: true,
                  ),
                ),
                Container(
                  width: Get.width,
                  padding: EdgeInsets.symmetric(horizontal: 6.sp),
                  margin: EdgeInsets.only(top: 18.sp),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Pointer,
                      GoIcon(
                        name: GoIcons.versionInfo,
                        color: Colors.amber,
                        size: 16.sp
                      ),
                      SizedBox(width: 8.sp),
                      Flexible(
                        child: AText(
                          text: 'Please do not cut power in this process.'.tr,
                          color: themeBase.primaryColor.value,
                          textAlign: TextAlign.center,
                          size: 17.sp,
                          softWrap: true,
                        ),
                      )
                    ],
                  )
                )
              ],
            )
          )
        ],
      )
    );
  }

  if (libre.updateState.value == 5) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 26.sp),
          child: AText(
            text: 'The upgrade failed, maybe it is a network problem, please try again later'.tr,
            color: themeBase.primaryColor.value,
            textAlign: TextAlign.center,
            size: 16.sp,
            softWrap: true,
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 17.sp),
          child: OnTapScaleToSmallBox(
            onTap: (){
              Get.back();
              libre.updateState.value = 0;
            },
            child: Container(
              height: 42.sp,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: themeBase.buttonColor1.value,
                borderRadius: BorderRadius.circular(10.r)
              ),
              child: AText(
                text: 'Back'.tr,
                size: themeBase.buttonFont.value,
                color: themeBase.textColor1.value,
              )
            ),
          ),
        )
      ],
    );
  }

  if (libre.updateState.value == 99) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 44.sp,
          height: 44.sp,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(44.sp),
            color: Colors.greenAccent
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 36.sp,
                height: 36.sp,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(33.sp),
                  color: Colors.white
                ),
                child: GoIcon(
                  name: GoIcons.tick,
                  color: Colors.greenAccent,
                  size: 33.sp,
                ),
              ),
            ],
          )
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 26.sp),
          child: AText(
            text: 'cn3s'.tr,
            color: themeBase.primaryColor.value,
            textAlign: TextAlign.center,
            size: 16.sp,
            softWrap: true,
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 17.sp),
          child: OnTapScaleToSmallBox(
            onTap: ()  {
              Get.back();
              libre.updateState.value = 0;
              Log.d('是否需要前往设置Gcast页面 ${libre.pairGoToGcast.value} 固件更新之后');
              if (libre.pairGoToGcast.value) {
                Get.offNamedUntil('/libreSetInfo/setGcast', ModalRoute.withName(Routes.homePage));
              }
            },
            child: Container(
              height: 42.sp,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: themeBase.buttonColor1.value,
                borderRadius: BorderRadius.circular(10.r)
              ),
              child: AText(
                text: 'OK'.tr,
                size: themeBase.buttonFont.value,
                color: themeBase.textColor1.value,
              )
            ),
          ),
        )
      ],
    );
  }
  return Container();
}


Widget _getLoading(libre) {
  return Obx(()=> SimpleCircularProgressBar(
    size: 120.sp,
    valueNotifier: libre.donwloadVerVal,
    progressStrokeWidth: 11.sp,
    backStrokeWidth: 11.sp,
    mergeMode: true,
    animationDuration: 0,
    onGetText: (value) {
      return Text(
        '${value.toInt()}%',
        style: TextStyle(
          fontSize: 18.sp,
          color: themeBase.primaryColor.value,
        ),
      );
    },
    progressColors: const [Colors.greenAccent],
    backColor: themeBase.primaryColor.value.withOpacity(0.4),
  ));
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/device/libre/timezongdata.dart';
import 'package:gocontrol/pages/SettingPage/components/setting_box.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class TimeZonePage2 extends StatelessWidget {
  const TimeZonePage2({super.key});

  @override
  Widget build(BuildContext context) {

    Log.d(Get.arguments);
    int mi = Get.arguments;

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: TimeZone.timeZoneList[mi].tr,
      ),
      body: BodyBox(
        scrollBox: false,
        showTopBar: true,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: SingleChildScrollView(
            child: SettingBox(
            children: getWidget(mi),
          ),
          )
        )
      )
    );
  }
}

List<Widget> getWidget(int mi) {
  List<Widget> list = [];
  List<String> mmlst = [];
  switch (mi) {
    case 0: 
      mmlst = TimeZone.africa;
    break;
    case 1: 
      mmlst = TimeZone.america;
    break;
    case 2: 
      mmlst = TimeZone.antarctica;
    break;
    case 3: 
      mmlst = TimeZone.arctic;
    break;
    case 4: 
      mmlst = TimeZone.asia;
    break;
    case 5: 
      mmlst = TimeZone.atlantic;
    break;
    case 6: 
      mmlst = TimeZone.australia;
    break;
    case 7: 
      mmlst = TimeZone.europe;
    break;
    case 8: 
      mmlst = TimeZone.indian;
    break;
    case 9: 
      mmlst = TimeZone.pacific;
    break;
    case 10: 
      mmlst = TimeZone.others;
    break;
  }

  for (var el in mmlst) {
    Libre libre = homCon.selectDevice! as Libre;
    list.add(SetBox2(
      title: el, 
      info: '',
      iconInfo: Obx(()=> libre.timeZone.value == el? GoIcon(
        name: GoIcons.tick,
        size: 18.sp,
        color: themeBase.primaryColor.value,
      ) : Container()),
      onTap: () {
        libre.setTimeZone(el);
      },
    ));
  }
  return list;
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class SettingBox extends StatelessWidget {
  const SettingBox({
    super.key,
    this.title,
    required this.children
  });
  
  final String? title;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Visibility(
          visible: title != null,
          child: Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(bottom: 9.sp, left: 17.sp),
            child: AText(
              text: title??'',
              family: TextFamily.medium,
              color: themeBase.primaryColor.value.withOpacity(0.7),
              size: themeBase.subBodyFont.value - 1.sp,
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.all(2.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14.sp),
            color: themeBase.textColor1.value
          ),
          child: Column(
            children: children,
          ),
        ),
        SizedBox(height: 16.sp,)
      ],
    );
  }
}

class SetBox2 extends StatelessWidget {
  const SetBox2({
    super.key,
    this.icon,
    required this.title,
    required this.info,
    this.showSide = true,
    this.last = false,
    this.onTap,
    this.iconInfo,
    this.active = false
    // this.disable
  });

  final Widget? icon;
  final String title;
  final String info;
  final bool showSide;
  final bool last;
  final Function? onTap;
  final Widget? iconInfo;
  final bool active;
  // final RxBool? disable;

  @override
  Widget build(BuildContext context) {
    
    final RxBool tap = false.obs;

    return GestureDetector(
      onTap: (){
        // if(disable != null && disable!.value == true) return;
        if(onTap != null){
          onTap!();
          tap.value = true;
          Future.delayed(const Duration(milliseconds: 50),()=> tap.value = false);
        }
      },
      onTapDown: (details) {
        // if(disable != null && disable!.value == true) return;
        tap.value = true;
      },
      onTapUp: (details) {
        // if(disable != null && disable!.value == true) return;
        tap.value = false;
      },
      onTapCancel: (){
        // if(disable != null && disable!.value == true) return;
        tap.value = false;
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(14.r),
        child: Obx(() => Container(
          height: 50.sp,
          color: active? Colors.black.withOpacity(.1) : onTap != null && tap.value? Colors.black.withOpacity(.1) : Colors.transparent,
          padding: EdgeInsets.symmetric(horizontal: 2.sp),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Visibility(
                visible: icon != null,
                child: SizedBox(
                  width: 45.sp,
                  height: 45.sp,
                  child: Transform.translate(
                    offset: Offset(0, !last?2.sp:0),
                    child: icon,
                  )
                )
              ),
              Flexible(
                child: Container(
                  margin: EdgeInsets.only(top: !last?1.sp:0),
                  decoration: BoxDecoration(
                    border: !last?Border(
                      bottom: BorderSide(
                        width: 0.1.sp,
                        color: Colors.black.withOpacity(.7)
                      )
                    ):null
                  ),
                  child: Row(
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        margin: EdgeInsets.only(left: icon == null?12.sp:0),
                        child: AText(
                          text: title,
                          family: TextFamily.medium,
                          color: themeBase.primaryColor.value,
                          size: themeBase.subBodyFont.value ,
                        ),
                      ),
                      Flexible(
                        // flex: 10,
                        child: Container(
                          margin: EdgeInsets.only(right: 12.sp),
                          alignment: Alignment.centerRight,
                          child: iconInfo??AText(
                            text: info,
                            family: TextFamily.medium,
                            color: themeBase.primaryColor.value.withOpacity(.7),
                            size: themeBase.subBodyFont.value - 1,
                          ),
                        )
                      )
                    ],
                  ),
                )
              )
            ],
          ),
        )),
      ),
    );
  }
}
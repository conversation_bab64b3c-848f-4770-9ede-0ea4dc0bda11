import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class SetVolumeUpBox extends StatelessWidget {
  const SetVolumeUpBox({super.key});

  @override
  Widget build(BuildContext context) {
    DeviceBase device = homCon.selectDevice as DeviceBase;

    return SizedBox(
      height: 48.sp,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          OnTapScaleToSmallBox(
            onTap: (){
              if(device.vst.value > 1) device.vstSub();
            },
            child: Container(
              width: 46.sp,
              height: 48.sp,
              alignment: Alignment.center,
              margin: EdgeInsets.symmetric(vertical: 10.sp),
              decoration: BoxDecoration(
                color: themeBase.primaryColor.value,
                borderRadius: BorderRadius.circular(6.sp)
              ),
              child: GoIcon(
                name: GoIcons.setSub,
                size: 14.sp,
                color: themeBase.textColor1.value,
              )
            ),
          ),
          Container(
            width: 46.sp,
            height: 48.sp,
            alignment: Alignment.center,
            child: Obx((() => AText(
              text: '${device.vst.value}',
              color: themeBase.primaryColor.value,
            )))
          ),
          OnTapScaleToSmallBox(
            onTap: (){
              if(device.vst.value < 10) device.vstAdd();
            },
            child: Container(
              width: 46.sp,
              height: 48.sp,
              alignment: Alignment.center,
              margin: EdgeInsets.symmetric(vertical: 10.sp),
              decoration: BoxDecoration(
                color: themeBase.primaryColor.value,
                borderRadius: BorderRadius.circular(6.sp)
              ),
              child: GoIcon(
                name: GoIcons.setAdd,
                size: 14.sp,
                color: themeBase.textColor1.value,
              )
            ),
          ),
        ],
      ),
    );
  }
}
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/input_box.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/pages/SettingPage/components/set_volume_up.dart';
import 'package:gocontrol/pages/SettingPage/components/setting_box.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/sound/components/go_swtich.dart';
import 'package:gocontrol/theme/theme.dart';

class B50InfoPage extends StatelessWidget {
  const B50InfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    if (homCon.selectDevice is! B50) return Container();
    B50 device = homCon.selectDevice as B50;
    device.sendMsgToBLE(msg: 'LED;');
    device.sendMsgToBLE(msg: 'VER;');
    device.getUpdateVer();
    device.sendMsgToBLE(msg: 'VST;');
    device.sendMsgToBLE(msg: 'MMC;');
    device.sendMsgToBLE(msg: 'ARC;');
    device.sendMsgToBLE(msg: 'TLM;');

    return PagesBody(
      topbar: Top2tion(
        title: 'settings'.tr,
        
      ),
      body: BodyBox(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: SettingBox(
            children: [
              Obx(() => SetBox2(
                icon: GoIcon(
                  name: GoIcons.deviceName,
                  size: 20.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'device_name'.tr, 
                info: device.name.value,
                onTap: (){
                  TextEditingController nameController = TextEditingController();
                  showMyBottomSheet(
                    height: 250.sp, 
                    title: 'name_change'.tr, 
                    content: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.sp),
                      child: Column(
                        children: [
                          InputBox(
                            height: 45.sp, 
                            focusNode: FocusNode(), 
                            textController: nameController, 
                            textColor: themeBase.primaryColor.value,
                            placeholder: 'device_name'.tr,
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            margin: EdgeInsets.only(top: 10.sp,left: 5.sp),
                            child: AText(
                              text: 'name_change_text'.tr,
                              size: 14.sp,
                              softWrap: true,
                              color: themeBase.buttonColor2.value,
                            ),
                          )
                        ],
                      )
                    ), 
                    showCancel: true.obs,
                    cancel: ()=> Get.back(),
                    confirm: (){
                      if(nameController.text.length > 2 &&nameController.text.length < 17){
                        String name = nameController.text;
                        String data = 'NAM:${convertToUTF8Hex(name)}';
                        Get.back();
                        device.setDeviceName(data);
                        AppToast.show('toast4'.tr);
                      }
                    }
                  );
                },
              )),
              Obx(() => SetBox2(
                icon: GoIcon(
                  name: GoIcons.deviceVersion,
                  size: 20.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'firmware_version'.tr, 
                info: device.version.value,
                iconInfo: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Obx(() => Visibility(
                      visible: device.updateVer.value,
                      child: GoIcon(
                        name: GoIcons.verUpdate,
                        size: 19.sp,
                        color: themeBase.primaryColor.value,
                      ),
                    )),
                    Obx(() => Visibility(
                      visible: device.updateVer.value,
                      child: SizedBox(width: 8.sp),
                    )),
                    AText(
                      text: device.version.value,
                      family: TextFamily.medium,
                      color: const Color.fromRGBO(128, 128, 128, 1),
                      size: themeBase.subBodyFont.value - 1,
                    ),
                  ],
                ),
                onTap: (device.updateVer.value)?(){
                  // rightNavControl.newUpdate.value = r;
                  showMyBottomSheet(
                    height: 260.sp,
                    title: 'update'.tr,
                    content: Container(
                      padding: EdgeInsets.symmetric(horizontal: 18.sp),
                      child: AText(
                        text: device.updateStr.value,
                        color: themeBase.primaryColor.value,
                        softWrap: true,
                      ),
                    ),
                    showCancel: true.obs,
                    cancel: ()=> Get.back(),
                    confirm: (){
                      device.updateConfirm();
                      Get.back();
                    }
                  );
                }:null,
              )),
              SetBox2(
                icon: GoIcon(
                  name: GoIcons.led,
                  size: 24.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'led'.tr, 
                info: '',
                iconInfo: GoSwtich(
                  switchVal: device.led,
                  tap: (){
                    if(device.led.value){
                      device.sendMsgToBLE(msg:'LED:1;');
                    }else{
                      device.sendMsgToBLE(msg:'LED:0;');
                    }
                  }
                ),
              ),
              SetBox2(
                icon: GoIcon(
                  name: GoIcons.add,
                  size: 21.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'volume_step'.tr, 
                info: '',
                iconInfo: const SetVolumeUpBox()
              ),
              Obx(() => Visibility(
                visible: device.deviceMode.value != '' && (device.deviceMode.value == 'B50' || device.deviceMode.value == 'BP50'),
                  child: SetBox2(
                  title: 'phono_mode'.tr, 
                  info: '',
                  icon: GoIcon(
                    name: GoIcons.srcPhono,
                    size: 22.sp,
                    color: themeBase.primaryColor.value,
                  ),
                  iconInfo: OnTapScaleToSmallBox(
                    onTap: (){
                      // device.sendMsgToSocket('MCU+PAS+RAKOIT:VST&');
                      // if(device.vst.value < 10) device.vstAdd();
                      // phonoMode.value
                      if(device.phonoMode.value){
                        device.sendMsgToBLE(msg: 'MMC:0;');
                      }else{
                        device.sendMsgToBLE(msg: 'MMC:1;');
                      }
                    },
                    child: Container(
                      width: 46.sp,
                      height: 48.sp,
                      alignment: Alignment.center,
                      margin: EdgeInsets.symmetric(vertical: 10.sp),
                      decoration: BoxDecoration(
                        color: themeBase.primaryColor.value,
                        borderRadius: BorderRadius.circular(6.sp)
                      ),
                      child: AText(
                        text: device.phonoMode.value?'MC':'MM',
                        size: 14.sp,
                        color: themeBase.textColor1.value,
                      )
                    ),
                  ),
                ),
              )),
              Obx(() => Visibility(
                visible: device.deviceMode.value != '' && (device.deviceMode.value == 'B50' || device.deviceMode.value == 'BP50'),
                child: SetBox2(
                  title: 'arc_mode'.tr, 
                  info: '',
                  iconInfo: GoSwtich(
                    switchVal: device.arcMode,
                    tap: (){
                      if(device.arcMode.value){
                        device.sendMsgToBLE(msg:'ARC:0;');
                      }else{
                        device.sendMsgToBLE(msg:'ARC:1;');
                      }
                    }
                  ),
                ),
              )),
              Obx(() => Visibility(
                visible: device.deviceMode.value != '' && (device.deviceMode.value == 'BT10' || device.deviceMode.value == 'B50' || device.deviceMode.value == 'BP50'),
                child: SetBox2(
                  title: 'disable_speaker'.tr, 
                  info: '',
                  iconInfo: GoSwtich(
                    switchVal: device.tlmMode,
                    tap: (){
                      if(device.tlmMode.value){
                        device.sendMsgToBLE(msg:'TLM:1;');
                      }else{
                        device.sendMsgToBLE(msg:'TLM:0;');
                      }
                    }
                  ),
                ),
              )),
              // ---
              SetBox2(
                onTap: (){
                  Get.bottomSheet(
                    SizedBox(
                      height: 240.h,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(
                              top: 22.sp,
                              bottom: 22.sp
                            ),
                            child: AText(
                              text: 'reset_device'.tr,
                              family: TextFamily.medium,
                              size: themeBase.subHeadingFont.value,
                              color: themeBase.primaryColor.value,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 22.sp),
                            child: AText(
                              text: 'name_change_text'.tr,
                              family: TextFamily.medium,
                              size: themeBase.subBodyFont.value,
                              color: themeBase.primaryColor.value,
                              softWrap: true,
                            )
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.sp,vertical: 16.sp),
                            child:  Row(
                              children: [
                                Flexible(
                                  flex: 1,
                                  child: GestureDetector(
                                    onTap: ()=> Get.back(),
                                    child: Container(
                                      alignment: Alignment.center,
                                      height: 43.sp,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10.r),
                                        color:const Color.fromRGBO(230, 230, 230, 1),
                                      ),
                                      child: AText(
                                        text: 'cancel'.tr,
                                        color: const Color.fromRGBO(128, 128, 128, 1),
                                        size: themeBase.subBodyFont.value + 1.sp,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 16.sp,),
                                Flexible(
                                  flex: 1,
                                  child: GestureDetector(
                                    onTap: (){
                                      // if(mycontrol.text.length > 3) _sendConnectWifi(mycontrol.text,device);
                                      homCon.selectDevice!.resetDevice();
                                      Get.offAllNamed('/home');
                                      homCon.adevices.remove(homCon.selectDevice!);
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      height: 43.sp,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10.r),
                                        color:themeBase.primaryColor.value.withAlpha(200),
                                      ),
                                      child: AText(
                                        text: 'confirm'.tr,
                                        color: const Color.fromRGBO(255, 255, 255, 1),
                                        size: themeBase.subBodyFont.value + 1.sp,
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            )
                          )
                        ]
                      )
                    ),
                    backgroundColor: themeBase.textColor1.value,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
                    )
                  );
                },
                icon: GoIcon(
                  name: GoIcons.deviceReset,
                  size: 19.sp,
                  color: themeBase.primaryColor.value,
                ), 
                title: 'reset_device'.tr, 
                info: '',
                iconInfo: Transform.rotate(
                  angle: pi / 1,
                  child: GoIcon(
                    name: GoIcons.back,
                    size: 12.sp,
                    color: const Color.fromRGBO(128, 128, 128, 1),
                  ),
                ),
                last: true,
              ),
            ]
          )
        )
      )
    );
  }
}

String convertToUTF8Hex(String data) {
  List<int> utf8Bytes = utf8.encode(data); // 将字符串转换为 UTF-8 字节序列
  String hexString = bytesToHex(utf8Bytes); // 将字节序列转换为十六进制字符串
  return hexString;
}

String bytesToHex(List<int> bytes) {
  const hexDigits = '0123456789ABCDEF';
  StringBuffer buffer = StringBuffer();
  for (int byte in bytes) {
    buffer.write(hexDigits[(byte >> 4) & 0x0F]);
    buffer.write(hexDigits[byte & 0x0F]);
  }
  return buffer.toString();
}
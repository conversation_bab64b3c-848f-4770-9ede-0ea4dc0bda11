import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import '../poise/poise_body.dart';

class AudioPage extends StatelessWidget {
  const AudioPage({super.key});

  @override
  Widget build(BuildContext context) {
    DeviceBase device = homCon.selectDevice!;

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: device.name.value,
      ),
      body: BodyBox(
        showTopBar: true,
        scrollBox: false,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.sp),
          child: const PoiseBody(),
        ),
      ),
    );
  }
}
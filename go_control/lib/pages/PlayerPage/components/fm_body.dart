import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/pages/PlayerPage/components/fm_search.dart';
import 'package:gocontrol/pages/PlayerPage/components/page_gradient.dart';
import 'package:gocontrol/pages/PlayerPage/components/player_volume_body.dart';
import 'package:gocontrol/pages/PlayerPage/components/slider_progress_bar.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

class FMPage extends StatelessWidget {
  const FMPage({super.key});

  @override
  Widget build(BuildContext context) {
    AbstractNET device = homCon.selectDevice as AbstractNET;
    SearchFMCon searchFMCon = Get.put(SearchFMCon());

    return PagesBody(
      showTopBar: false,
      scroll: false,
      playbody: true,
      body: BodyBox(
        showTopBar: false,
        child: Transform.translate(
          offset: Offset(0, -themeBase.searHeight.value),
          child: Stack(
            children: [
              // 图片层
              SizedBox(
                width: Get.width,
                height: Get.height,
                child: Stack(
                  children: [
                    Container(
                      width: Get.width,
                      height: Get.height,
                      decoration: const BoxDecoration(
                        color: Color.fromRGBO(88, 88, 88, 1)
                      ),
                      // child: Obx(()=> getPlaceHolder(device))
                    ),
                  ]
                )
              ),
              // 渐变遮罩层
              const PlayerBgGradient(),
              SizedBox(
                width: Get.width,
                height: Get.height,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // shang
                    Top2tion(
                      title: homCon.selectDevice!.name.value,
                      net: true,
                      settings: true,
                    ),
                    // zhong 
                    Column(
                      children: [
                        Container(
                          alignment: Alignment.center,
                          margin: EdgeInsets.only(bottom: 12.sp),
                          child: Obx(()=> AText(
                            text: '${device.fmVal.value} MHz',
                            color: themeBase.blockColor.value,
                            size: 35.sp,
                          )),
                        ),
                        Stack(
                          children: [
                            CarouselSlider.builder(
                              carouselController: device.fmCon,
                              options: CarouselOptions(
                                height: 88.sp,
                                viewportFraction: 0.046,
                                enableInfiniteScroll: false,
                                initialPage: ((device.fmVal.value * 10) - (87 * 10)).toInt(),
                                onPageChanged: (index, reason){
                                  double star = 87.0;
                                  device.fmVal.value = star
                                   + (index * 0.1);
                                  if (reason == CarouselPageChangedReason.manual) device.setFMPage();
                                },
                              ),
                              itemBuilder: (context, index, realIndex) {
                                if (index % 10 == 0) {
                                  return Column(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Container(
                                        width: 3.sp,
                                        height: 54.sp,
                                        margin: EdgeInsets.only(bottom: 5.sp),
                                        decoration: BoxDecoration(
                                          color: themeBase.blockColor.value,
                                          borderRadius: BorderRadius.circular(3.sp)
                                        ),
                                      ),
                                      AText(
                                        text: (index * 0.1 + 87).toStringAsFixed(0),
                                        color: themeBase.blockColor.value,
                                        size: 14.sp,
                                      )
                                    ],
                                  );
                                } else if (index % 5 == 0) {
                                  return Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 3.sp,
                                        height: 56.sp * 0.7,
                                        decoration: BoxDecoration(
                                          color: themeBase.blockColor.value,
                                          borderRadius: BorderRadius.circular(3.sp)
                                        ),
                                      )
                                    ],
                                  );
                                } else {
                                  return Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 3.sp,
                                        height: 56.sp * 0.6,
                                        decoration: BoxDecoration(
                                          color: const Color.fromARGB(255, 171, 171, 171),
                                          borderRadius: BorderRadius.circular(3.sp)
                                        ),
                                      )
                                    ],
                                  );
                                }
                              },
                              itemCount: 211,
                            ),
                            Positioned(
                              left: (MediaQuery.of(context).size.width * 0.5) - 4.sp,
                              child: Container(
                                width: 8.sp,
                                height: 66.sp,
                                decoration: BoxDecoration(
                                  color: Colors.red.withOpacity(.3),
                                  border: Border.all(
                                    width: 1.sp,
                                    color: Colors.red.withOpacity(.7)
                                  )
                                ),
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                    // xia
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.sp),
                      child: Column(
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Obx(()=> Container(
                                margin: EdgeInsets.only(bottom: 2.sp),
                                padding: EdgeInsets.only(left: 14.sp),
                                alignment: Alignment.centerLeft,
                                child: AText(
                                  text: 'FM',
                                  family: TextFamily.medium,
                                  size: themeBase.headingFont.value,
                                  color: themeBase.textColor1.value,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )),
                              Container(
                                padding: EdgeInsets.only(left: 14.sp),
                                alignment: Alignment.bottomLeft,
                                child: Obx(() => AText(
                                  text: '${device.fmVal.value} MHz',
                                  family: TextFamily.medium,
                                  size: themeBase.subBodyFont.value,
                                  color: themeBase.textColor1.value.withOpacity(.7),
                                  overflow: TextOverflow.ellipsis,
                                ))
                              ),
                            ],
                          ),
                          SizedBox(height: (themeBase.topBarHeight) / 6),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 14.sp),
                            child: const PlayerProgressBar(), 
                          ),
                          SizedBox(height: (themeBase.topBarHeight) / 2.2),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 5.sp),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Opacity(
                                  opacity: 0.5,
                                  child: GoIcon(
                                    name: GoIcons.loopRandom,
                                    size: 23.5.sp,
                                    color: themeBase.textColor1.value,
                                  ),
                                ),
                                _getIcons(
                                  ontap: (){
                                    device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMT:0&');
                                    searchFMCon.showSearchFM();
                                    // if (!device.searchFM.value) SearchFM.showSearchFM(device);
                                  },
                                  icon: GoIcon(
                                    name: GoIcons.prevSong,
                                    size: 33.sp,
                                    color: themeBase.textColor1.value,
                                  ),
                                ),
                                _getIcons(
                                  ontap: () {
                                    if (device.volume.value != 0) {
                                      device.lastVolume.value = device.volume.value;
                                      device.setVolume(0);
                                    } else {
                                      device.setVolume(device.lastVolume.value);
                                      device.lastVolume.value = 0;
                                    }
                                    // String pattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
                                    // RegExp regExp = RegExp(pattern);
                                    // Log.d(regExp.hasMatch('<EMAIL>'));
                                  },
                                  icon: GoIcon(
                                    name: GoIcons.playPause,
                                    size: 58.sp,
                                    color: themeBase.textColor1.value,
                                  ),
                                ),
                                _getIcons(
                                  ontap: () {
                                    device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMT:1&');
                                    searchFMCon.showSearchFM();
                                    // if (!device.searchFM.value) SearchFM.showSearchFM(device);
                                  },
                                  icon: GoIcon(
                                    name: GoIcons.nextSong,
                                    size: 33.sp,
                                    color: themeBase.textColor1.value,
                                  ),
                                ),
                                Opacity(
                                  opacity: .5,
                                    child: GoIcon(
                                    name: GoIcons.loopList,
                                    color: themeBase.textColor1.value,
                                    size: 25.sp,
                                  ),
                                )
                              ],
                            ), 
                          ),
                          SizedBox(height: (themeBase.topBarHeight) / 2.2),
                          Padding(
                            padding: EdgeInsets.only(
                              left: 15.sp,
                              right: 5.sp,
                            ),
                            child: PlayerVolumeBody(
                              onTap: (){
                                // Log.d('123');
                                device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMP&');
                                Get.toNamed('${Routes.fmPage}/${Routes.fmSaveBody}');
                              }
                            ), // 播放音量
                          ),
                          SizedBox(height: (themeBase.topBarHeight + themeBase.searHeight.value) / 1.8),
                        ],
                      ),
                    )
                  ]
                )
              )
            ],
          ),
        ),
      ),
    );
  }
}

Widget _getIcons({required void Function() ontap,required icon}) {
  final RxBool tag = false.obs;
  return GestureDetector(
    onTap: (){
      ontap();
      tag.value = true;
      tag.value = true;
      Future.delayed(const Duration(milliseconds: 100),((){ tag.value = false;}));
    },
    onTapDown: (v) => tag.value = true,
    onTapUp: (v)=> tag.value = false,
    onTapCancel: ()=> tag.value = false,
    child: Obx(()=> AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      alignment: Alignment.center,
      padding: EdgeInsets.all(9.sp),
      decoration: BoxDecoration(
        color: tag.value?themeBase.textColor1.value.withOpacity(0.3):Colors.transparent,
        borderRadius: BorderRadius.circular(360.sp)
      ),
      child: AnimatedScale(
        duration: const Duration(milliseconds: 200),
        scale: tag.value?0.9:1,
        child: icon
      ),
    )),
  );
}


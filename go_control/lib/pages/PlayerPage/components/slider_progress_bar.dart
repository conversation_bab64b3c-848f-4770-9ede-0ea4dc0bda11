import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class PlayerProgressBar extends StatelessWidget {
  const PlayerProgressBar({super.key});

  @override
  Widget build(BuildContext context) {
    if (homCon.selectDevice is Libre) {
      Libre device = homCon.selectDevice as Libre;
      return Obx(()=> AbsorbPointer(
        absorbing: !device.seek.value,
        child: Row(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              width: 40.sp,
              child: Obx(() => AText(
                text: device.playLengths.value > device.totTalVal.value? '00:00' : formatTime(int.parse((device.playLengths.value).toStringAsFixed(0))),
                size: themeBase.subBodyFont2.value + 2.sp,
                color: themeBase.textColor1.value,
                family: TextFamily.medium,
              )),
            ),
            Flexible(
              child: Transform.translate(
                offset: Offset(0, 0.5.sp),
                child: Opacity(
                  opacity: !device.seek.value ? .7 : 1,
                  child: SliderTheme(
                    data: SliderThemeData(
                      thumbColor: Colors.white,
                      activeTrackColor: themeBase.textColor1.value,
                      inactiveTrackColor: themeBase.textColor1.value.withAlpha((255 * 0.5).round()),
                      thumbShape: !device.seek.value? NoTuhumb() : RoundSliderThumbShape(enabledThumbRadius: 7.sp),
                      overlayShape: RoundSliderOverlayShape(
                        overlayRadius: 12.sp// 设置滑块覆盖层大小，
                      ),
                      trackHeight: 4.sp,
                    ),
                    child: Obx((){
                      if (device.playLengths.value > 0 && device.playLengths.value <= device.totTalVal.value) {
                        return Slider(
                          min: 0,
                          max: device.totTalVal.value,
                          value: device.playLengths.value,
                          onChanged: (v) {
                            device.playLengths.value = v;
                          },
                          onChangeEnd: (v) {
                            device.sendSeek(v);
                          },
                        );
                      } else {
                        return Slider(
                          min: 0,
                          max: 1,
                          value: 0.01,
                          onChanged: (v) {
                            device.playLengths.value = v;
                          },
                          onChangeEnd: (v) {
                            device.sendSeek(v);
                            // homCon.nowAdev.setVolume(v);
                          },
                        );
                      }
                    }),
                  ),
                ),
              ),
            ),
            Container(
              alignment: Alignment.centerRight,
              width: 40.sp,
                child: Obx(() => AText(
                text: formatTime(int.parse(device.totTalVal.value.toStringAsFixed(0))),
                size: themeBase.subBodyFont2.value + 2.sp,
                color: themeBase.textColor1.value,
                family: TextFamily.medium,
              ))
            )
          ],
        )
      ));
    } else {
      AbstractNET device = homCon.selectDevice as AbstractNET;
      return Row(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            width: 40.sp,
            child: Obx(() => AText(
              text: device.playLengths.value > device.totTalVal.value?'00:00':formatTime(int.parse((device.playLengths.value).toStringAsFixed(0))),
              size: themeBase.subBodyFont2.value + 2.sp,
              color: themeBase.textColor1.value,
              family: TextFamily.medium,
            )),
          ),
          Flexible(
            child: Transform.translate(
              offset: Offset(0, 0.5.sp),
              child: Opacity(
                opacity: .8,
                child: AbsorbPointer(
                  absorbing: true,
                  child: SliderTheme(
                    data: SliderThemeData(
                      thumbColor: Colors.white,
                      activeTrackColor: themeBase.textColor1.value,
                      inactiveTrackColor: themeBase.textColor1.value.withAlpha((255 * 0.5).round()),
                      thumbShape: NoTuhumb(),
                      overlayShape: RoundSliderOverlayShape(
                        overlayRadius: 12.sp// 设置滑块覆盖层大小，
                      ),
                      trackHeight: 4.sp,
                    ),
                    child: Obx(()=> Slider(
                      min: 0,
                      max: device.totTalVal.value,
                      value: (device.playLengths.value > device.totTalVal.value) || device.playLengths.value < 0? device.totTalVal.value - 0.1:device.playLengths.value,
                      onChanged: (v) {
                        device.playLengths.value = v;
                      },
                    )),
                  ),
                ),
                // child: Obx(() => CustomPlayerSlider(
                //   value: (device.playLengths.value > device.totTalVal.value) || device.playLengths.value < 0? device.totTalVal.value:device.playLengths.value,
                //   max: device.totTalVal.value,
                //   min: 0,  
                //   trackHeight: 6.sp, 
                //   inactiveTrackColor: themeBase.textColor2.value, 
                //   activeTrackColor: themeBase.textColor1.value,
                //   borderRadius: 3.sp, 
                //   onChanged: (double value) {
                //     device.playLengths.value = value;
                //   }, 
                // )),
              ),
            ),
          ),
          Container(
            alignment: Alignment.centerRight,
            width: 40.sp,
              child: Obx(() => AText(
              text: '-${formatTime(int.parse((device.totTalVal.value - device.playLengths.value).toStringAsFixed(0)))}',
              size: themeBase.subBodyFont2.value + 2.sp,
              color: themeBase.textColor1.value,
              family: TextFamily.medium,
            ))
          )
        ],
      );
    }
  }
}

// 把时间毫秒转换成 分:秒
String formatTime(int seconds) {
  int minutes = seconds ~/ 60;
  int remainingSeconds = seconds % 60;
  String minutesStr = minutes < 10 ? '0$minutes' : '$minutes';
  String secondsStr = remainingSeconds < 10 ? '0$remainingSeconds' : '$remainingSeconds';
  return '$minutesStr:$secondsStr';
}

class NoTuhumb extends SliderComponentShape {
  const NoTuhumb({
    this.thumbRadius = 10.0,
    this.strokeWidth = 1.2,
  });

  final double thumbRadius;
  final double strokeWidth;

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(0);
  }

  @override
  void paint(PaintingContext context, Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
    // 创建一个半透明的 Paint 对象，用于绘制环形
    final Paint trackPaint = Paint()
      ..color = Colors.transparent 
      ..style = PaintingStyle.stroke 
      ..strokeWidth = 0;
    // 绘制环形
    canvas.drawCircle(
      center,
      thumbRadius,
      trackPaint,
    );
  }
}
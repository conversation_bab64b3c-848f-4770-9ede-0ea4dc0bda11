import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class FmSaveBody extends StatelessWidget {
  const FmSaveBody({super.key});

  @override
  Widget build(BuildContext context) {
    A31 device = homCon.selectDevice! as A31;

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: 'Preset FM',
        onTap: (){
          Get.back();
          device.changeFMPage(jump: true);
        },
      ), 
      body: BodyBox(
        scrollBox: false, 
        showTopBar: true,
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: 12.sp,
            horizontal: 17.sp
          ),
          child: ListView.builder(
            itemBuilder: (context, index) {
              return OnTapScaleToSmallBox(
                onTap: () {
                  String nowV = (device.fmVal.value * 1000).toStringAsFixed(0);
                  if (device.fmPresetList[index] == '0') {
                    if (device.fmPresetList.contains(nowV)) return;
                    device.fmPresetList[index] = nowV;
                    device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMP:ADD:$index@$nowV&');
                    Log.d('保存');
                  } else {
                    if (device.fmPresetList[index] != nowV) {
                      Log.d('设置 ${device.fmPresetList[index]}');
                      device.fmVal.value = int.parse(device.fmPresetList[index]) / 1000;
                      device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMQ:${device.fmPresetList[index]}&');
                      Future.delayed(const Duration(milliseconds: 100),(){
                        device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMQ&');
                      });
                    }
                  }

                  // if (device.fmPresetList[index] != nowV) {
                  //   device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMP:ADD:$index@$nowV&');
                  // } else {
                  //   Log.d(device.fmPresetList.contains(nowV));
                  //   if (device.fmPresetList.contains(nowV)) {
                  //     return;
                  //   }
                  //   device.fmPresetList[index] = nowV;
                  //   device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMQ:${device.fmPresetList[index]}&');
                  //   Log.d(device.fmPresetList[index]);
                  //   Log.d((device.fmVal.value * 1000).toStringAsFixed(0));
                  // }
                },
                child: Obx(()=> Container(
                  height: 48.sp,
                  margin: EdgeInsets.only(
                    bottom: 12.sp
                  ),
                  decoration: BoxDecoration(
                    color: device.fmPresetList[index] == (device.fmVal.value * 1000).toStringAsFixed(0)? themeBase.primaryColor.value : themeBase.secondaryColor.value,
                    borderRadius: BorderRadius.circular(48.sp / 4)
                  ),
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 12.sp),
                        child: AText(
                          text: '${index + 1}',
                          color: themeBase.blockColor.value,
                          size: 17.sp,
                        )
                      ),
                      Flexible(
                        flex: 8,
                        child: Container(
                          width: double.maxFinite,
                          alignment: Alignment.center,
                          margin: EdgeInsets.only(left: 1.sp,top: 0.5.sp),
                          padding: EdgeInsets.only(left: 10.sp),
                          child: Obx(()=> AText(
                            text: device.fmPresetList[index] == '0'? 'Save Now FM' : '${device.fmPresetList[index]} MHz',
                            color: themeBase.blockColor.value,
                            size: 16.sp,
                          )),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: OnTapScaleToSmallBox(
                          onTap: () {
                            if (device.fmPresetList[index] == '0') return;
                            // String delS = device.fmPresetList[index];
                            device.fmPresetList[index] = '0';
                            device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMP:DEL:$index&');
                          },
                          child: Container(
                            height: double.maxFinite,
                            padding: EdgeInsets.only(right: 4.sp),
                            child: Obx(()=> Visibility(
                              visible: device.fmPresetList[index] != '0',
                              child: GoIcon(
                                name: GoIcons.deladd,
                                size: 25.sp,
                                color: themeBase.blockColor.value,
                              )
                            ))
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
              );
            },
            itemCount: device.fmPresetList.length,
          ),
        ),
      )
    );
  }
}
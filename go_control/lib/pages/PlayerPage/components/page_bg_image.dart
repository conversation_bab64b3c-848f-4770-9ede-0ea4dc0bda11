import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a5_playinfo.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

class PlayerBg extends StatelessWidget {
  const PlayerBg({super.key});

  @override // lovsyion ffulocatheee
  Widget build(BuildContext context) {
    if (homCon.selectDevice is A5Playinfo) {
      A5Playinfo device = homCon.selectDevice as A5Playinfo;
      return Stack(
        children: [
          ClipRRect(
            child: Container(
              width: Get.width,
              height: Get.height,
              decoration: BoxDecoration(
                color: Color.fromRGBO(10, 10, 10, 0.5),
              ),
              child: ImageFiltered(
                imageFilter: ImageFilter.blur(
                  sigmaX: 120, 
                  sigmaY: 120,
                ),
                child: Obx(() => CachedNetworkImage(
                  imageUrl: device.songImage.value,
                  fit: BoxFit.cover,
                  useOldImageOnUrlChange: true,
                  fadeInDuration: const Duration(milliseconds: 500),
                  fadeOutDuration: const Duration(milliseconds: 800),
                  errorWidget: (context, url, error) => Container(),
                  placeholder: (context, url) => Container(),
                )),
              ), 
            ),
          ),
          Container(
            width: Get.width,
            height: Get.height,
            color: Color.fromRGBO(8, 8, 8, 0.12),
          ),
          Obx(()=> AnimatedContainer(
            width: Get.width,
            height: Get.height,
            duration: const Duration(milliseconds: 500),
            color: homCon.playBgColor.value.withAlpha((255 * 0.35).round()),
          )),
        ],
      );
      //
    } else {
      return Container();
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PlayerBgGradient extends StatelessWidget {
  const PlayerBgGradient({super.key});

  @override
  Widget build(BuildContext context) {

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: Get.width,
      height: Get.height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(.2),
            Colors.black.withOpacity(.3),
            Colors.black.withOpacity(.9),
          ]
        )
      )
    );
  }
}
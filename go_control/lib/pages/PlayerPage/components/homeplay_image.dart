import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a5_playinfo.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/radioPage/radio/spotify_page/get.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:url_launcher/url_launcher.dart';

class HomePlayImage extends StatelessWidget {
  const HomePlayImage({super.key});

  @override
  Widget build(BuildContext context) {
    if (homCon.selectDevice is A5Playinfo) {
      final device = homCon.selectDevice as A5Playinfo;
      return GestureDetector(
        onTap: () async {
          if (device is Libre) {
            if (device.sourceCurrent.value == '4') {
              final spotifyGetCon = Get.put(SpotifyGetCon());
              spotifyGetCon.ihave.value = await canLaunchUrl(Uri.parse('spotify://'));
              if (spotifyGetCon.ihave.value) {
                await launchUrl(Uri.parse('spotify://'));
              } else {
                if (GetPlatform.isIOS) {
                  await launchUrl(Uri.parse('https://itunes.apple.com/cn/app/id324684580'));
                } else {
                  await launchUrl(Uri.parse('https://play.google.com/store/apps/details?id=com.spotify.music'));
                }
              }
            }
          }
        },
        child: Obx(()=> Container(
          width: themeBase.isiPad.value? 320.h : Get.width * 0.85,
          height: themeBase.isiPad.value? 320.h : Get.width * 0.85,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(0),
            boxShadow: [
              BoxShadow(
                blurRadius: 5,
                color: Color.fromRGBO(15, 15, 15, 0.15)
              )
            ]
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(0),
            child: Obx(() => CachedNetworkImage(
              imageUrl: device.songImage.value,
              fit: BoxFit.cover,
              useOldImageOnUrlChange: true,
              fadeInDuration: const Duration(milliseconds: 500),
              fadeOutDuration: const Duration(milliseconds: 800),
              errorWidget: (context, url, error) => Obx(()=> _placeHolder()),
              placeholder: (context, url) => Obx(()=> _placeHolder()),
            ))
          ),
        )),
      );
    } else {
      return Container();
    }
  }
}

Widget _placeHolder() {
  final device = homCon.selectDevice!;
  if (device is Libre) {
    if (device.sourceCurrent.value == '22') {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 25.sp),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(22, 22, 22, 1),
        ),
        child: Transform.translate(
          offset: Offset(0, 10.sp),
          child: Image.asset(
            'images/tidal_logo.png',
            fit: BoxFit.fitWidth,
          )
        ),
      );
    }
  }
  switch (device.sourceInput.value) {
    case 'BT':
      return _buildIcon(GoIcons.srcBT);
    case 'LINE-IN':
      return _buildIcon(GoIcons.srcAux);
    case 'USBPLAY':
      return _buildIcon(GoIcons.srcUSB);
    default:
      return _buildIcon(GoIcons.srcWiFi);
  }
}

Widget _buildIcon(IconData iconName) {
  return Container(
    width: Get.width * 0.85,
    height: Get.width * 0.85,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(0),
      color: const Color.fromRGBO(55, 55, 55, .6),
    ),
    child: Transform.translate(
      offset: Offset(-5.sp, 2.sp),
      child: GoIcon(
        name: iconName,
        color: themeBase.blockColor.value.withAlpha((255 * 0.6).toInt()),
        size: (Get.width * 0.85) / 2.8,
      ),
    ),
  );
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

import '../../home/<USER>/mmvlizer.dart';

class BtmBtn extends StatelessWidget {
  const BtmBtn({
    super.key,
    // required this.connectMap,
    required this.device,
    required this.id
  });
  // final RxMap connectMap;
  final B50 device;
  final int id;

  @override
  Widget build(BuildContext context) {
    return OnTapScaleToSmallBox(
      onTap: () async {
        if (!device.isBLEConnected.value) {
          final myIndex = homCon.adevices.indexWhere((dev)=> dev == device);
          homCon.adevInx.value = myIndex;
          homCon.selectDevice = device;
          await device.reconnectBLE();
        }
        if(_getBtm()['name'] == ''){
          device.sendMsgToBLE(msg: 'KEY:BT_PAIR;');
          if(device.sourceInput.value == 'BT'){
            showMyBottomSheet(
              height: 240.sp, 
              title: 'btm_tips'.tr, 
              content: Padding(
                padding: EdgeInsets.all(12.sp),
                child: AText(
                  text: 'btm_tips_text'.tr.replaceAll('%s', device.name.value),
                  color: themeBase.primaryColor.value,
                  size: themeBase.subBodyFont.value,
                  softWrap: true,
                )
              ), 
              showCancel: false.obs,
              confirm: ()=> Get.back(),
            );
          }
        }else{
          showMyBottomSheet(
            height: 240.sp, 
            title: 'btm_disconnect'.tr, 
            content: Padding(
              padding: EdgeInsets.all(12.sp),
              child: AText(
                text: 'btm_disconnect_text'.tr,
                color: themeBase.primaryColor.value,
                size: themeBase.subBodyFont.value,
                softWrap: true,
              ),
            ), 
            showCancel: true.obs,
            cancel: ()=> Get.back(),
            confirm: (){
              device.sendMsgToBLE(msg: 'KEY:BT_RESET;');
              device.btm1['name'] = '';
              device.btm2['name'] = '';
              device.btsPlay.value = false;
               Future.delayed(const Duration(milliseconds: 200),(){
                device.btm1['name'] = '';
                device.btm2['name'] = '';
              });
              Get.back();
            }
          );
        }
          // 开启RxTx
          //65635 点btm2没有连接 但显示连接 不正常：676767...会断开连接又连接上  正常：6537 653 675657Log.d('连接'); 
          // device.sendMsgToBLE(msg: 'KEY:BT_PAIR;');
          // if (device.sourceInput.value == 'BT') {
          //   CustomBottomSheet2.showBot(
          //     context,
          //     title: '没有连接',  
          //     text: '请打开手机的设置->蓝牙，找到并连接设备:%s'.replaceAll('%s', device.name.value),  
          //     confirmFn: () {
          //       Get.back();
          //     },
          //     cancelFn: () {
          //       Get.back();
          //     }
          //   );
          // }
        // }
      },
      child: Obx(()=> Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.sp)),
          //根据连接情况显示不同的颜色
          color: _getBtm()['name'] == '' ? themeBase.primaryColor.value.withOpacity(.4):themeBase.primaryColor.value
        ),
        height: 40.sp,
        padding: EdgeInsets.symmetric(horizontal: 4.sp),
        child: Row(
          children: [
            GoIcon(
              name: device.sourceInput.value == 'BT' || device.sourceInput.value == '' ? 
                const IconData(0xe669,fontFamily: 'IconFont'):const IconData(0xe65b,fontFamily: 'IconFont'),
              color: themeBase.blockColor.value,
              size: 19.sp,
            ),
            Flexible(
              child: Row(
                children: [
                  Flexible(
                    child: Container(
                      margin: EdgeInsets.only(
                        right: 4.sp,
                        left: 2.sp,
                        top: 2.sp
                      ),
                      child: AText(
                        text: _getName(),
                        color: themeBase.textColor1.value,
                        size: themeBase.subBodyFont2.value + 1.sp,
                        weight: FontWeight.bold,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Obx(() => Visibility(
                    visible: _getBtm()['codec']!='unknown' && _getBtm()['name']!='' && _getBtm()['state']=='3',
                    child: Container(
                      //连接后的编码器 ：codec
                      padding: EdgeInsets.symmetric(horizontal: 4.sp),
                      decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.sp),
                      color: themeBase.secondaryColor.value
                      ),
                      child: AText(
                        text: _getBtm()['codec'],
                        size: themeBase.subBodyFont2.value + 1.sp,
                        color: themeBase.textColor1.value,
                        spacing: 0.3.sp,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  )),
                  Obx(() => Visibility(
                    visible: _getBtm()['state'] == '3' && _getBtm()['name'] != '',
                    child: Container(
                      //连接后的播放状态：播放动画
                      alignment: Alignment.bottomCenter,
                      padding: EdgeInsets.only(bottom: 6.sp),
                      margin: EdgeInsets.symmetric(horizontal: 3.sp),
                      //需要替换的地方：换成播放动画的图标或动画的操作
                      child: Transform.scale(
                        scale: .8,
                        child: MiniMusicVisualizer(
                          color: themeBase.textColor1.value,
                          width: 2.5.sp,
                          height: 12.sp,
                          animate: true,
                        ),
                      ),
                    )
                  )),
                ],
              ),
            ),
            Visibility(
              //根据连接情况显示：未连接：不显示，已连接：显示
              visible: _getBtm()['name'] != '' , // device.name.value != '',
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 2.sp),
                child: GoIcon(
                  //需要在图标库中添加该图标：断开连接图标
                  name: const IconData(0xe905,fontFamily: 'IconFont'),
                  size: 16.sp,
                  color: themeBase.blockColor.value,
                ),
              )
            ),
          ],
        ),
      )),
    );
  }

  RxMap _getBtm(){
    if(id == 1){
      return device.btm1;
    }else{
      return device.btm2;
    }
  }

  String _getName(){
    RxMap map = _getBtm();
    if(map['name'] != ''){
      return map['name'];
    }else{
      if(device.btsPlay.value){  //判断是否正在连接
        return 'Wating for connection...';
      }else{
        if(device.sourceInput.value == 'BT' || device.sourceInput.value  == ''){
          return 'rx_title'.tr.replaceAll('%s', device.name.value);  // 'rx_title'.tr
        }else{
          return 'tx_title'.tr; //'tx_title'.tr
        }
      }
    }
  }
}
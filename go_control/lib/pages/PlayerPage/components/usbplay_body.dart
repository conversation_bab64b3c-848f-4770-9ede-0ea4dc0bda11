import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a4_usblist.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/home/<USER>/components/usb_song_list.dart';

class UsbplayBody extends StatelessWidget {
  const UsbplayBody({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: 'Play List',
        onTap: (){
          if (homCon.selectDevice is Libre) {
            final libre = homCon.selectDevice as Libre;
            List songList = [];
            for (var el in libre.dataList) {
              songList.add({
                'id': el['id'],
                'name': el['name'].value,
              });
            }
            String storageKey = '${libre.name.value}:USB:SONGLIST';
            StorageClass.setStorage(storageKey, json.encode(songList));
            Get.back();
          }
        },
      ), 
      body: BodyBox(
        scrollBox: false, 
        showTopBar: true, 
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 17.sp),
              child: UsbSongList(device: homCon.selectDevice as A4Usblist),
            ),
          ],
        )
      )
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/new_slider.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

class PlayerVolumeBody extends StatelessWidget {
  const PlayerVolumeBody({
    super.key,
    this.onTap
  });

  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    if (homCon.selectDevice is Libre) {
      Libre device = homCon.selectDevice as Libre;
      return Row(
        children: [
          Obx(() => GestureDetector(
            onTap: (){
              if (device.mute.value) {
                Log.b('To UNMUTE');
                device.setMute(false);
              } else {
                Log.b('To MUTE');
                device.setMute(true);
              }
            },
            child: SvgPicture.asset(
              device.mute.value? 'images/volume0.svg' : _getVolume(device),
              width: 20.sp,
              height: 20.sp,
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(
                themeBase.textColor1.value,
                BlendMode.modulate, // 混合模式，可以根据需要选择
              ),
            ),
          )),
          Flexible(
            child: GestureDetector(
              child: Obx(() => SliderTheme(
                data:  SliderThemeData(
                  activeTrackColor: Colors.white,
                  thumbColor: Colors.white,
                  inactiveTrackColor:Colors.white.withOpacity(.4),
                  thumbShape: RoundSliderThumbShape(
                    enabledThumbRadius: 6.sp, // 设置thumb的半径
                  ),
                  overlayShape: RoundSliderOverlayShape(
                    overlayRadius: 12.sp// 设置滑块覆盖层大小，
                  ),
                  trackHeight: 4.sp,
                  trackShape: const NewSliderTrackShape()
                ),
                child: Slider(
                  min: 0,
                  max: 100,
                  value: device.volume.value,
                  onChanged: (v) {
                    device.volume.value = v;
                  },
                  onChangeEnd: (v) {
                    device.setVolume(v);
                    // homCon.nowAdev.setVolume(v);
                  },
                ),
              )),
            ),
          ),
          // 进入播放列表
          Obx(()=> AbsorbPointer(
            absorbing: _libreAb(device),
            child: OnTapScaleToSmallBox(
              onTap: (){
                if (device.sourceInput.value == 'USB') {
                  Get.toNamed(Routes.libreUSBSongPage);
                } else {
                  device.getSongListForUSB();
                  device.sendMsgToTcpSocket('DSK:CUR;');
                  Get.toNamed('${Routes.playerPage}/${Routes.usbplaybody}');
                }
              },
              child: Obx(() => Container(
                margin: EdgeInsets.only(right: 7.sp),
                alignment: Alignment.center,
                child: Transform.translate(
                  offset: Offset(0 , -1.sp),
                    child: GoIcon(
                    name: GoIcons.playList,
                    size: 24.sp,
                    color: themeBase.textColor1.value.withOpacity(_libreAb(device)? 0.4 : 1),
                  ),
                )
              )),
            )
          )),
        ],
      );
    } else {
      AbstractNET device = homCon.selectDevice as AbstractNET;
      return Row(
        children: [
          Obx(() => SvgPicture.asset(
            _getVolume(device),
            width: 20.sp,
            height: 20.sp,
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              themeBase.textColor1.value,
              BlendMode.modulate, // 混合模式，可以根据需要选择
            ),
          )),
          Flexible(
            child: GestureDetector(
              child: Obx(() => SliderTheme(
                data:  SliderThemeData(
                  activeTrackColor: Colors.white,
                  thumbColor: Colors.white,
                  inactiveTrackColor:Colors.white.withOpacity(.4),
                  thumbShape: RoundSliderThumbShape(
                    enabledThumbRadius: 6.sp, // 设置thumb的半径
                  ),
                  overlayShape: RoundSliderOverlayShape(
                    overlayRadius: 12.sp// 设置滑块覆盖层大小，
                  ),
                  trackHeight: 4.sp,
                  trackShape: const NewSliderTrackShape()
                ),
                child: Slider(
                  min: 0,
                  max: 100,
                  value: device.volume.value,
                  onChanged: (v){
                    device.volume.value = v;
                  },
                  onChangeEnd: (v){
                    device.setVolume(v);
                    // homCon.nowAdev.setVolume(v);
                  },
                ),
              )),
            ),
          ),
          // 进入播放列表
          Obx(() => AbsorbPointer(
            absorbing: _getAbso(device,onTap),
            child: OnTapScaleToSmallBox(
              onTap: (){
                if (onTap != null) {
                  onTap!();
                } else {
                  if (device.project.contains('A100') && device.sourceInput.value == 'USBDAC') {
                    device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:CUR&');
                    Get.toNamed('${Routes.playerPage}/${Routes.usbplaybody}');
                  } else {
                    if (device.sourceInput.value == 'USBPLAY') {
                      device.getSongListForUSB();
                      device.sendMsgToSocket('MCU+PAS+RAKOIT:DSK:CUR&');
                      Get.toNamed('${Routes.playerPage}/${Routes.usbplaybody}');
                    } else {
                      Get.toNamed('/songListPage');
                    }
                  }
                }
              },
              child: Container(
                margin: EdgeInsets.only(right: 7.sp),
                alignment: Alignment.center,
                child: Transform.translate(
                  offset: Offset(0 , -1.sp),
                    child: Obx(()=> GoIcon(
                    name: GoIcons.playList,
                    size: 24.sp,
                    color: _getColor(device,onTap),
                  )),
                )
              ),
            )
          )) ,
        ],
      );
    }
  }
}

bool _getAbso(AbstractNET device, onTap) {
 
  if (device.project.contains('A100')) {
    if (device.sourceInput.value == 'USBDAC') {
      return device.dataTot.value == 0;
    } else {
      return device.songList.isEmpty && onTap == null;
    }
  } else {
    if (device.sourceInput.value == 'USBPLAY')  {
      return device.dataTot.value == 0;
    } else {
      return device.songList.isEmpty && onTap == null;
    }
  }
}

Color _getColor(AbstractNET device, onTap) {
  if (device.project.contains('A100')) {
    if (device.sourceInput.value == 'USBDAC') {
      if (device.dataTot.value != 0) {
        return themeBase.textColor1.value;
      } else {
        return themeBase.textColor1.value.withOpacity(.4);
      }
    } else {
      if (device.songList.isNotEmpty || onTap != null) {
        return themeBase.textColor1.value;
      } else {
        return themeBase.textColor1.value.withOpacity(.4);
      }
    }
  } else {
    if (device.sourceInput.value == 'USBPLAY')  {
      if (device.dataTot.value!= 0) {
        return themeBase.textColor1.value;
      } else {
        return themeBase.textColor1.value.withOpacity(.4);
      }
    } else {
      if (device.songList.isNotEmpty || onTap!= null) {
        return themeBase.textColor1.value;
      } else {
        return themeBase.textColor1.value.withOpacity(.4);
      }
    }
  }
  
}

String _getVolume(device) {
  if(device is AbstractNET && device.roomChildren.isNotEmpty){
    if(device.allVolume.value < 30){
      return 'images/volume3.svg';
    }else if(device.allVolume.value < 80 && device.allVolume.value > 29){
      return 'images/volume2.svg';
    }else{
      return 'images/volume1.svg';
    }
  }else{
    if(device.volume.value < 30){
      return 'images/volume3.svg';
    }else if(device.volume.value < 80 && device.volume.value > 29){
      return 'images/volume2.svg';
    }else{
      return 'images/volume1.svg';
    }
  }
}

bool _libreAb(Libre libre) {
  if (libre.sourceInput.value == 'USB') {
    return libre.usbTotalSongs.value <= 0;
  } else {
    return libre.dataTot.value <= 0;
  }
}
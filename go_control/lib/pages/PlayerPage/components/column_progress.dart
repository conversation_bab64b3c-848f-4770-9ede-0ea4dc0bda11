import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 自定义进度条，用于显示播放进度
class CustomPlayerSlider extends StatefulWidget {
  final double value;
  final double min;
  final double max;
  final double trackHeight;
  final Color inactiveTrackColor;
  final Color activeTrackColor;
  final double borderRadius;
  final ValueChanged<double> onChanged;
  final Widget? touchCircle;

  const CustomPlayerSlider({
    super.key,
    required this.value,
    required this.min,
    required this.max,
    required this.trackHeight,
    required this.inactiveTrackColor,
    required this.activeTrackColor,
    required this.borderRadius,
    required this.onChanged,
    this.touchCircle
  });

  @override
  CustomPlayerSliderState createState() => CustomPlayerSliderState();
}

class CustomPlayerSliderState extends State<CustomPlayerSlider> {
  double currentValue = 0;
  double sliderWidth = 0;

  @override
  void initState() {
    super.initState();
    currentValue = widget.value;
  }


  @override
  void didUpdateWidget(CustomPlayerSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != currentValue) {
      setState(() {
        currentValue = widget.value;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        sliderWidth = constraints.maxWidth;
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onHorizontalDragStart: (details) {
            _updateValue(details.localPosition.dx, context);
          },
          onHorizontalDragUpdate: (details) {
            _updateValue(details.localPosition.dx, context);

          },
          onHorizontalDragEnd: (details) {
            _tapUp(details.localPosition.dx, context);
          },
          child: Container(
            height: widget.trackHeight + 20.sp,
            width: sliderWidth,
            margin: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 10.sp),
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(widget.borderRadius)
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  height: widget.trackHeight,
                  decoration: BoxDecoration(
                    color: widget.inactiveTrackColor,
                    borderRadius: BorderRadius.circular(widget.borderRadius)
                  ),
                ),
                Positioned(
                  left: 0,
                  child: Container(
                    width: ((currentValue - widget.min) / (widget.max - widget.min)) * sliderWidth,
                    height: widget.trackHeight,
                    decoration: BoxDecoration(
                      color: widget.activeTrackColor,
                      borderRadius: BorderRadius.circular(widget.borderRadius)
                    ),
                  ),
                ),
                Positioned(
                  left: (((currentValue - widget.min) / (widget.max - widget.min)) * sliderWidth) - 7.sp,
                  child: widget.touchCircle ??Container(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _updateValue(double dx, BuildContext context) {
    final double newValue = dx / sliderWidth * (widget.max - widget.min) + widget.min;
    final double clampedValue = newValue.clamp(widget.min, widget.max);

    setState(() {
      currentValue = clampedValue;
    });
  }

  void _tapUp (double dx, BuildContext context) {
     final double newValue = dx / sliderWidth * (widget.max - widget.min) + widget.min;
    final double clampedValue = newValue.clamp(widget.min, widget.max);

    setState(() {
      currentValue = clampedValue;
    });
    
    widget.onChanged(clampedValue);
  }
}
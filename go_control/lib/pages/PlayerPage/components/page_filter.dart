import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/models/class/class_net.dart';

import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class PlayerBgFilter extends StatelessWidget {
  const PlayerBgFilter({super.key});

  @override
  Widget build(BuildContext context) {

    AbstractNET device = homCon.selectDevice as AbstractNET;
    return SizedBox(
      width: Get.width,
      height: Get.height,
      child: Obx(()=>AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
         child: device.songImage.value == ''?Center(
          child: Transform.translate(
            offset: Offset(0,-(99.sp / 1.5).sp),
            child: GoIcon(
              name: GoIcons.srcWiFi,
              color: themeBase.primaryColor.value.withOpacity(.5),
              size: 99.sp,
            ),
          ),
        ):const SizedBox()
      )),
    );
  }
}
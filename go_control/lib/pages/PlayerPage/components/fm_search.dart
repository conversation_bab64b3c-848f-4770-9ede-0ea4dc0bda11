
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/theme/theme.dart';

class SearchFMCon extends GetxController {
  RxBool searchState = false.obs;

  @override
  void onReady() {
    super.onReady();
    ever(searchState, (v){
      if (v) {
        Log.d('打开');
        _showPage();
      } else {
        Log.d('关闭');
        closePage();
      }
    });
  }

  void showSearchFM() {
    searchState.value = true;
  }

  void closePage() {
    Get.back();
  }

  void _showPage() {
    Get.defaultDialog(
      title: 'Searching...',
      barrierDismissible: false,
      titlePadding: EdgeInsets.only(top: 14.sp,bottom: 12.sp),
      titleStyle: TextStyle(
        fontFamily: 'Medium',
        color: themeBase.blockColor.value,
        fontSize: 23.sp
      ),
      content: SizedBox(
        width: 42.sp,
        height: 42.sp,
        child: CircularProgressIndicator(
          color: themeBase.blockColor.value,
          backgroundColor: themeBase.blockColor.value.withOpacity(.3),
        ),
      ),
      backgroundColor: const Color.fromARGB(255, 55, 55, 55)
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

class PlayerControlBody extends StatelessWidget {
  const PlayerControlBody({super.key});

  @override
  Widget build(BuildContext context) {
    if (homCon.selectDevice is Libre) {
      Libre device = homCon.selectDevice as Libre;

      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 28.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Obx(()=> AbsorbPointer(
              absorbing: !_getPrevUse(device),
              child: Obx(()=> Opacity(
                opacity: _getPrevUse(device)? 1.0 : 0.3,
                child: _getIcons(
                  ontap: (){
                    if (device.spotifyPodCast.value) {
                      if (device.playLengths.value - 15 < 0) {
                        device.sendSeek(0);
                      } else {
                        device.sendSeek(device.playLengths.value - 15);
                      }
                      return;
                    }
                    device.prevPlay();
                  },
                  icon: Obx(()=> device.spotifyPodCast.value? GoIcon(
                    name: GoIcons.podcastSub,
                    size: 32.sp,
                    color: themeBase.textColor1.value,
                  ) : GoIcon(
                    name: GoIcons.prevSong,
                    size: 28.sp,
                    color: themeBase.textColor1.value,
                  ),
                )),
              ))
            )),
            OnTapScaleToSmallBox(
              scale: 0.91,
              onTap: (){
                device.playSong();
                // device.sendMsgToTcpSocket('BWE:MXV:254');
                Log.d(device.isPlaying.value);
              },
              child: Obx(()=> Container(
                alignment: Alignment.center,
                width: 66.sp,
                height: 66.sp,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(360.r),
                  color: themeBase.textColor1.value.withOpacity(device.sourceInput.value == 'BT' ? 0 : 0.3)
                ),
                child: Obx(()=> AnimatedSwitcher(
                  duration: const Duration(milliseconds: 120),
                  child: device.sourceInput.value == 'BT' ?
                    GoIcon(
                      name: GoIcons.playPause,
                      size: 66.sp,
                      color: themeBase.textColor1.value.withAlpha((255 * 0.9).toInt()),
                    ) : 
                    device.isPlaying.value ?
                    Transform.translate(
                      offset: Offset(0.sp,0),
                      child: GoIcon(
                        name: GoIcons.pause,
                        size: 37.sp,
                        color: themeBase.textColor1.value,
                      )
                    ) : 
                    Transform.translate(
                      offset: Offset(2.sp,0),
                      child: GoIcon(
                        name: GoIcons.play,
                        size: 20.sp,
                        color: themeBase.textColor1.value,
                      ),
                    ),
                )),
              )),
            ),
            Obx(()=> AbsorbPointer(
              absorbing: !_geNextUse(device),
              child: Obx(()=> Opacity(
                opacity: _geNextUse(device)?1.0:0.3,
                child: _getIcons(
                  ontap: (){
                    if (device.spotifyPodCast.value) {
                      if (device.playLengths.value.toInt() + 15 >= device.totTalVal.value.toInt()) {
                        device.sendSeek(device.totTalVal.value - 1);
                      } else {
                        device.sendSeek(device.playLengths.value + 15);
                      }
                      return;
                    }
                    device.nextPlay();
                  },
                  icon: Obx(()=> device.spotifyPodCast.value? GoIcon(
                      name: GoIcons.podcastAdd,
                      size: 32.sp,
                      color: themeBase.textColor1.value,
                    ) : GoIcon(
                      name: GoIcons.nextSong,
                      size: 28.sp,
                      color: themeBase.textColor1.value,
                  )),
                ),
              ))
            )),
            // OnTapScaleToSmallBox(
            //   onTap: () {
            //     device.upItem();
            //   },
            //   child: GoIcon(
            //     name: GoIcons.prevSong,
            //     size: 28.sp,
            //     color: themeBase.textColor1.value,
            //   ),
            // ),
            // OnTapScaleToSmallBox(
            //   onTap: () {
            //     Log.d('播放暂停');
            //     // device.selectItem(3);
            //     device.oneTouch();
            //   },
            //   child: GoIcon(
            //     name: GoIcons.playPause,
            //     size: 35.sp,
            //     color: themeBase.textColor1.value,
            //   ),
            // ),
            // OnTapScaleToSmallBox(
            //   onTap: () {
            //     device.downItem();
            //   },
            //   child: GoIcon(
            //     name: GoIcons.nextSong,
            //     size: 28.sp,
            //     color: themeBase.textColor1.value,
            //   ),
            // )
          ],
        ),
      );
    } else {
      AbstractNET device = homCon.selectDevice as AbstractNET;
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _getIcons(
            ontap: ()=> setRandom(device),
            icon: Obx(() => Opacity(
              opacity: device.loopRandom.value?1:0.5,
              child: GoIcon(
                name: GoIcons.loopRandom,
                size: 23.5.sp,
                color: themeBase.textColor1.value,
              ),
            ))
          ),
          _getIcons(
            ontap: (){
              device.prevPlay();
            },
            icon: GoIcon(
              name: GoIcons.prevSong,
              size: 26.sp,
              color: themeBase.textColor1.value,
            ),
          ),
          Obx(() => AbsorbPointer(
            absorbing: device.playLoading.value,
            child: OnTapScaleToSmallBox(
              scale: 0.91,
              onTap: () {
                device.playSong();
                // device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:LST:0&');
              },
              child: Container(
                alignment: Alignment.center,
                width: 66.sp,
                height: 66.sp,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(360.r),
                  color: themeBase.textColor1.value.withOpacity(0.3)
                ),
                child: Obx(()=> AnimatedSwitcher(
                  duration: const Duration(milliseconds: 0),
                  child: device.playLoading.value?Transform.translate(
                    offset: Offset(0, 8.sp),
                    child: Loading(
                      color: themeBase.textColor1.value,
                      size: 30.sp,
                    ),
                  ) : device.isPlaying.value && device.songName.value != ''?
                    Transform.translate(
                      offset: Offset(0.sp,0),
                      child: GoIcon(
                        name: GoIcons.pause,
                        size: 37.sp,
                        color: themeBase.textColor1.value,
                      )
                    ):Transform.translate(
                      offset: Offset(2.sp,0),
                      child: GoIcon(
                        name: GoIcons.play,
                        size: 20.sp,
                        color: themeBase.textColor1.value,
                      ),
                    ),
                )),
              ),
            ),
          )),
          _getIcons(
            ontap: (){
              device.nextPlay();
              
            },
            icon: GoIcon(
              name: GoIcons.nextSong,
              size: 26.sp,
              color: themeBase.textColor1.value,
            ),
          ),
          _getIcons(
            ontap: ()=> setLoop(device),
            icon: Obx(() => _getLoopWdiget(device)),
          )
        ],
      );
    }
  }
}

Widget _getIcons({required void Function() ontap,required icon}){
  final RxBool tag = false.obs;
  return GestureDetector(
    onTap: (){
      ontap();
      tag.value = true;
      Future.delayed(const Duration(milliseconds: 100),((){ tag.value = false;}));
    },
    onTapDown: (v) => tag.value = true,
    onTapUp: (v)=> tag.value = false,
    onTapCancel: ()=> tag.value = false,
    child: Obx(()=>AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      alignment: Alignment.center,
      padding: EdgeInsets.all(9.sp),
      decoration: BoxDecoration(
        color: tag.value?themeBase.textColor1.value.withOpacity(0.3):Colors.transparent,
        borderRadius: BorderRadius.circular(360.sp)
      ),
      child: AnimatedScale(
        duration: const Duration(milliseconds: 200),
        scale: tag.value?0.9:1,
        child: icon
      ),
    )),
  );
}

Widget _getLoopWdiget(AbstractNET device){
  if(device.loopMode.value == 0){
    return Opacity(
      opacity: .5,
        child: GoIcon(
        name: GoIcons.loopList,
        color: themeBase.textColor1.value,
        size: 25.sp,
      ),
    );
  }else if(device.loopMode.value == 2){
    return GoIcon(
      name: GoIcons.loopSingle,
      color: themeBase.textColor1.value,
      size: 25.sp,
    );
  }else{
    return GoIcon(
      name: GoIcons.loopList,
      color: themeBase.textColor1.value,
      size: 25.sp,
    );
  }
}

void setLoop(AbstractNET device){
  // Log.r(wifiControl.loopMode.value);
  if(device.loopMode.value == 0){
    if(device.loopRandom.value){
      device.sendMsgToSocket('MCU+PLP+002');
    }else{
      device.sendMsgToSocket('MCU+PLP+000');
    }
  }else if(device.loopMode.value == 1){
    if(device.loopRandom.value){
      device.sendMsgToSocket('MCU+PLP+001');
    }else{
      device.sendMsgToSocket('MCU+PLP+001');
    }
  }else if(device.loopMode.value == 2){
    if(device.loopRandom.value){
      device.sendMsgToSocket('MCU+PLP+003');
    }else{
      device.sendMsgToSocket('MCU+PLP+004');
    }
  }
  device.sendMsgToSocket('MCU+PLP+GET');
}

void setRandom(AbstractNET device){
  if(device.loopRandom.value){
    if(device.loopMode.value == 0){
      device.sendMsgToSocket('MCU+PLP+004');
    }else if(device.loopMode.value == 1){
      device.sendMsgToSocket('MCU+PLP+000');
    }else if(device.loopMode.value == 2){
      device.sendMsgToSocket('MCU+PLP+001');
    }
  }else{
    if(device.loopMode.value == 0){
      device.sendMsgToSocket('MCU+PLP+003');
    }else if(device.loopMode.value == 1){
      device.sendMsgToSocket('MCU+PLP+002');
    }else if(device.loopMode.value == 2){
      device.sendMsgToSocket('MCU+PLP+003');
    }
  }
  device.sendMsgToSocket('MCU+PLP+GET');
}

bool _getPrevUse(device) {
  if (device is Libre ) {
    if (device.spotifyPodCast.value) return true;
  }
  if (device.sourceInput.value == 'BT') return true;
  if (device.sourceInput.value == 'USBPLAY') return true;
  return device.prevBtn.value;
}

bool _geNextUse(device) {
  if (device is Libre ) {
    if (device.spotifyPodCast.value) return true;
  }
  if (device.sourceInput.value == 'BT') return true;
  if (device.sourceInput.value == 'USBPLAY') return true;
  return device.nextBtn.value;
}
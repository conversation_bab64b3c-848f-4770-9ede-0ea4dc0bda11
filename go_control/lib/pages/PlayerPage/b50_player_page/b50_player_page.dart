import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/pages/PlayerPage/b50_player_page/components/play_cont_body.dart';
import 'package:gocontrol/pages/poise/poise_body.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

// 直接弄一个当前点击的设备，这样也可以

class B50Page extends StatelessWidget {
  const B50Page({super.key});

  @override
  Widget build(BuildContext context) {
    AbstractBLE device = homCon.selectDevice as AbstractBLE;
    return PagesBody(
      topbar: Top2tion(
        title: device.name.value,
        settings: true,
      ),
      scroll: false,
      body: BodyBox(
        showTopBar: true,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: ListView( 
            padding: EdgeInsets.all(0.sp),
            children: [
              Padding(
                padding: EdgeInsets.only(top: 12.sp),
                child: PlayContBody(device: device),
              ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 16.sp),
                child: const PoiseBody(),
              ),
            ],
          ),
        )
      ),
    );
  }
}
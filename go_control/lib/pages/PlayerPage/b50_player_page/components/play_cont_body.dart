import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/pages/home/<USER>/components/player_hl.dart';
import 'package:gocontrol/pages/poise/sound/components/new_slider_track_shape.dart';
import 'package:gocontrol/theme/theme.dart';

class PlayContBody extends StatelessWidget {
  const PlayContBody({
    super.key,
    required this.device
  });

  final AbstractBLE device;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.sp),
        color: Colors.white,
      ),
      padding: EdgeInsets.only(
        top: 22.sp,
        bottom: 6.sp,
        left: 14.sp
      ),
      child: Column(
        children: [
          PlayerHL(
            device: device,
            ble: true
          ),
          SizedBox(height: 6.sp,),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.sp),
            child: Container(
              margin: EdgeInsets.only(bottom: 8.sp,top: 6.sp),
              child: Row(
                children: [
                  Obx(() => SvgPicture.asset(
                    _getVolume(device),
                    width: 21.sp,
                    height: 21.sp,
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      themeBase.primaryColor.value,
                      BlendMode.modulate, // 混合模式，可以根据需要选择
                    ),
                  )),
                  Flexible(
                    child: Obx(() => SliderTheme(
                      data: SliderThemeData(
                        activeTrackColor: themeBase.primaryColor.value,
                        thumbColor: themeBase.primaryColor.value,
                        inactiveTrackColor: themeBase.primaryColor.value.withOpacity(.4),
                        thumbShape: RoundSliderThumbShape(
                          enabledThumbRadius: 6.sp, // 设置thumb的半径
                        ),
                        overlayShape: RoundSliderOverlayShape(
                          overlayRadius: 12.sp// 设置滑块覆盖层大小，
                        ),
                        trackHeight: 4.sp,
                        trackShape: const NewSliderTrackShape()
                      ),
                      child: Slider(
                        min: 0,
                        max: 100,
                        value: device.volume.value,
                        onChanged: (v){
                          device.volume.value = v;
                        },
                        onChangeEnd: (v){
                          device.setVolume(v);
                        },
                      ),
                    )),
                  ),
                  Container(
                    width: 45.sp,
                    alignment: Alignment.center,
                    child: Transform.translate(
                      offset: Offset(0,-0.5.sp),
                      child: Obx(() => AText(
                        text: '${device.volume.value.toStringAsFixed(0)}%',
                        color: themeBase.primaryColor.value,
                        size: themeBase.subBodyFont.value - 1.sp,
                        family: TextFamily.medium,
                      )),
                    )
                  ),
                ],
              ),
            )
          ),
        ],
      ),
    );
  }
}

String _getVolume(AbstractBLE device){
  if(device.volume.value < 30){
    return 'images/volume3.svg';
  }else if(device.volume.value < 80 && device.volume.value > 29){
    return 'images/volume2.svg';
  }else{
    return 'images/volume1.svg';
  }
}
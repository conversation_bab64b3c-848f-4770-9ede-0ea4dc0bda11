import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:gocontrol/components/atext.dart';

class AudioSourceBody extends StatelessWidget {
  const AudioSourceBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(
        horizontal: 18.sp,
        vertical: 14.sp
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.sp),
        color: themeBase.primaryColor.value,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(.2),
            offset: Offset(1.sp,1.sp),
            blurRadius: 8
          ),
        ]
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(
              bottom: 10.sp
            ),
            child: AText(
              text: 'Audio Source',
              color: themeBase.primaryColor.value,
              size: themeBase.subHeadingFont.value,
            ),
          ),
          // homCon.nowAdev.sourceInput.isEmpty?
          // Container(
          //   height: 120.sp,
          //   alignment: Alignment.center,
          //   child: LoadingAnimationWidget.staggeredDotsWave(
          //     color: themeBase.surfaceColor.value.withOpacity(.8),
          //     size: 25.w
          //   ),
          // ):Obx(() => Wrap(
          //   spacing: 14.w,
          //   runSpacing: 12.h,
          //   alignment: WrapAlignment.start,
          //   children: List.generate(homCon.nowAdev.sourceInput.length, (index){
          //     Map src = homCon.nowAdev.sourceInput[index];
              
          //     return GestureDetector(
          //       onTap: (){
          //         homCon.nowAdev.sendMsg('SRC:${src['id']};');
          //       },
          //       child: SizedBox(
          //         height: 65.sp + 24.sp + 4.sp,
          //         child: Column(
          //           children: [
          //             Container(
          //               alignment: Alignment.center,
          //               width: 65.sp,
          //               height: 65.sp,
          //               decoration: BoxDecoration(
          //                 borderRadius: BorderRadius.circular(17.r),
          //                 color: homCon.nowAdev.src.value == src['id']?
          //                 rightNavControl.dominantColor.value:rightNavControl.dominantColor.value.withOpacity(0.4),
          //                 boxShadow: homCon.nowAdev.src.value == src['id']?[
          //                   BoxShadow(
          //                     offset: Offset(0, 1.sp),
          //                     color: Colors.black.withOpacity(0.15),
          //                     blurRadius: 3
          //                   )
          //                 ]:null
          //               ),
          //               child: Icon(
          //                 src['icon'],
          //                 size: 28.sp,
          //                 color: themeBase.primaryColor.value,
          //               ),
          //             ),
          //             Container(
          //               alignment: Alignment.center,
          //               width: 65.sp,
          //               height: 24.sp,
          //               margin: EdgeInsets.only(top: 4.sp),
          //               child: AText(
          //                 text: src['name'],
          //                 color: homCon.nowAdev.src.value == src['id']?
          //                 rightNavControl.dominantColor.value:rightNavControl.dominantColor.value.withOpacity(0.4),
          //                 size: themeBase.subBodyFont2.value + 1.sp,
          //               ),
          //             )
          //           ],
          //         ),
          //       ),
          //     );
          //   })
          // ))
        ],
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class LibreUSBSongPage extends StatelessWidget {
  const LibreUSBSongPage({super.key});

  @override
  Widget build(BuildContext context) {
    final libre = homCon.selectDevice as Libre;

    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: 'Play List',
      ), 
      body: BodyBox(
        scrollBox: false,
        showTopBar: true,
        child: Column(
          children: [
            Flexible(
              child: SizedBox(
                child: Obx(()=> ListView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    final item = libre.usbSongList[index];
                    final name = item['name'];

                    final singer = '';

                    return OnTapScaleToSmallBox(
                      onTap: (){
                        libre.selectMusic(item['page']!, item['id']!);
                      },
                      child: Obx(()=> Container(
                        height: 60.sp + 12.sp,
                        decoration: BoxDecoration(
                          color: name == libre.songName.value? themeBase.secondaryColor.value : Colors.transparent,
                          borderRadius: BorderRadius.circular(16.sp)
                        ),
                        padding: EdgeInsets.all(6.sp),
                        margin: EdgeInsets.only(
                          left: 16.sp,
                          right: 16.sp,
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 60.sp,
                              height: 60.sp,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.r),
                                color:const Color.fromRGBO(70, 93, 110, .8),
                              ),
                              child: Transform.translate(
                                offset: Offset(0, 1.sp),
                                child: GoIcon(
                                  name: GoIcons.srcWiFi,
                                  size: 28.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    margin: EdgeInsets.only(
                                      bottom: 1.sp,
                                      left: 11.sp,
                                      right: 22.sp
                                    ),
                                    child: AText(
                                      text: '${index + 1} $name',
                                      family: TextFamily.medium,
                                      size: themeBase.bodyFont.value - 1.sp,
                                      color: themeBase.primaryColor.value,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    margin: EdgeInsets.only(
                                      bottom: 1.sp,
                                      left: 11.sp,
                                      right: 22.sp
                                    ),
                                    child: AText(
                                      text: singer,
                                      family: TextFamily.medium,
                                      size: themeBase.subBodyFont.value - 1.sp,
                                      color: themeBase.primaryColor.value.withOpacity(.6),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      )),
                    );
                  },
                  itemCount: libre.usbSongList.length,
                )),
              ),
            ),
            Container(
              margin: EdgeInsets.symmetric(
                vertical: 16.sp
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 17.sp
              ),
              height: 40.sp,
              child: Row(
                children: [
                  Flexible(
                    child: OnTapScaleToSmallBox(
                      onTap: () {
                        libre.upItem();
                      },
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.sp),
                          color: themeBase.primaryColor.value,
                        ),
                        child: AText(
                          text: 'Left',
                          color: themeBase.blockColor.value,
                          family: TextFamily.medium,
                          size: 16.sp,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(
                      horizontal: 16.sp
                    ),
                    child: Row(
                      children: [
                        Obx(()=> AText(
                          text: '${libre.usbPageIndex.value}',
                          size: 14.sp,
                          color: themeBase.primaryColor.value,
                        )),
                        AText(
                          text: ' / ',
                          size: 14.sp,
                          color: themeBase.primaryColor.value,
                        ),
                        Obx(()=> AText(
                          text: '${libre.usbPages}',
                          size: 14.sp,
                          color: themeBase.primaryColor.value,
                        ))
                      ],
                    ),
                  ),
                  Flexible(
                    child: OnTapScaleToSmallBox(
                      onTap: () {
                        libre.downItem();                      
                      },
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.sp),
                          color: themeBase.primaryColor.value,
                        ),
                        child: AText(
                          text: 'Right',
                          color: themeBase.blockColor.value,
                          family: TextFamily.medium,
                          size: 16.sp,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        )
      )
    );
  }
}

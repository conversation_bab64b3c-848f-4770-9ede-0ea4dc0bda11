import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class PlaySongListPage extends StatelessWidget {
  const PlaySongListPage({super.key});

  @override
  Widget build(BuildContext context) {
    AbstractNET device = homCon.selectDevice as AbstractNET;

    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: 'Play List',
      ), 
      body: BodyBox(
        scrollBox: false,
        showTopBar: true,
        child: SizedBox(
          child: Obx(()=> ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              int i = index + 1;
              Map item;
              String name;
              String singer;
              RxString imgUrl = ''.obs;
              String data = device.songList[index]['Track$i']['Metadata'];
              if(data.contains('<?xml version="1.0"')){
                item = Comm.translateInfo(data)['DIDL-Lite']['item'];
                Log.d(item);
                name = item['dc:title'];
                if(item['upnp:album'] != null) {
                  singer = item['upnp:album'];
                }else if(item['upnp:artist'] != null && item['upnp:album'] != null){
                  singer = '${item['upnp:album']},${item['upnp:artist']}';
                }else if(item['upnp:artist'] != null){
                  singer = item['upnp:artist'];
                }else{
                  singer = 'unknow';
                }
                if(item['upnp:albumArtURI'] != null) imgUrl.value = item['upnp:albumArtURI'];
              }else if(data.contains('<upnp:class>') && !data.contains('<?xml version="1.0"')){
                String header = '<?xml version="1.0" encoding="UTF-8"?><DIDL-Lite xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/" xmlns:song="www.wiimu.com/song/" xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/">';
                String off = '</DIDL-Lite>';
                item = Comm.translateInfo('$header$data$off')['DIDL-Lite']['item'];
                ///
                name = item['dc:title'];
                if(item['upnp:album'] != null) {
                  singer = item['upnp:album'];
                }else if(item['upnp:artist'] != null && item['upnp:album'] != null){
                  singer = '${item['upnp:album']},${item['upnp:artist']}';
                }else if(item['upnp:artist'] != null){
                  singer = item['upnp:artist'];
                }else{
                  singer = 'unknow';
                }
                if(item['upnp:albumArtURI'] != null) imgUrl.value = item['upnp:albumArtURI'];
                
              }else{
                item = json.decode(device.songList[index]['Track$i']['Metadata']);
                name = item['title'];
                singer = item['creator'];
                // imgUrl = item['albumArtURI'];
                if(item['albumArtURI'] != null) imgUrl.value = item['albumArtURI'];
              }
              // Map metadata = Comm.translateInfo(item['Track${index + 1}']['Metadata']);
              return GestureDetector(
                onTap: (){
                  device.setSongIndex(device.songlistName.value, i);
                },
                child: Obx(()=>Container(
                  height: 60.sp + 12.sp,
                  decoration: BoxDecoration(
                    color: name == device.songName.value?themeBase.secondaryColor.value:Colors.transparent,
                    borderRadius: BorderRadius.circular(16.sp)
                  ),
                  padding: EdgeInsets.all(6.sp),
                  margin: EdgeInsets.only(
                    left: 16.sp,
                    right: 16.sp,
                  ),
                  child: Row(
                    children: [
                      ImageSongList(imgUrl),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              margin: EdgeInsets.only(
                                bottom: 1.sp,
                                left: 11.sp,
                                right: 22.sp
                              ),
                              child: AText(
                                text: name,
                                family: TextFamily.medium,
                                size: themeBase.bodyFont.value - 1.sp,
                                color: themeBase.primaryColor.value,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Container(
                              alignment: Alignment.centerLeft,
                              margin: EdgeInsets.only(
                                bottom: 1.sp,
                                left: 11.sp,
                                right: 22.sp
                              ),
                              child: AText(
                                text: singer,
                                family: TextFamily.medium,
                                size: themeBase.subBodyFont.value - 1.sp,
                                color: themeBase.primaryColor.value.withOpacity(.6),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                )),
              );
            },
            itemCount: device.songList.length,
          )),
        )
      )
    );
  }
}

class ImageSongList extends StatelessWidget {
  const ImageSongList(this.imgUrl,{
    super.key,
  });

  final RxString imgUrl;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60.sp,
      height: 60.sp,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: const Color.fromRGBO(70, 93, 110, .5),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10.sp),
        child: CachedNetworkImage(
          imageUrl: imgUrl.value,
          fit: BoxFit.cover,
          useOldImageOnUrlChange: true,
          fadeInDuration: const Duration(milliseconds: 0),
          fadeOutDuration: const Duration(milliseconds: 0),
          memCacheWidth: 300,
          memCacheHeight: 300,
          errorWidget: (context, url, error) => _forPlaceholder(),
          placeholder: (context, url) => _forPlaceholder(),
        ),
      )
    );
  }

  Widget _forPlaceholder() {
    return Center(
      child: Transform.translate(
        offset: Offset(0, 1.sp),
        child: GoIcon(
          name: GoIcons.srcWiFi,
          size: 28.sp,
          color: Colors.white.withAlpha((255 * 0.4).toInt()),
        ),
      ),
    );
  }
}
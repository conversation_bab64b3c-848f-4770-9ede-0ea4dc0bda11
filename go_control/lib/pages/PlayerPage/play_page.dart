import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/PlayerPage/components/homeplay_image.dart';
import 'package:gocontrol/pages/PlayerPage/components/page_bg_image.dart';
import 'package:gocontrol/pages/PlayerPage/components/player_control_body.dart';
import 'package:gocontrol/pages/PlayerPage/components/player_song_name.dart';
import 'package:gocontrol/pages/PlayerPage/components/player_volume_body.dart';
import 'package:gocontrol/pages/PlayerPage/components/slider_progress_bar.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class PlayerBoxPage extends StatelessWidget {
  const PlayerBoxPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      showTopBar: false,
      scroll: false,
      playbody: true,
      body: BodyBox(
        showTopBar: false,
        child: Transform.translate(
          offset: Offset(0, -themeBase.searHeight.value),
          child: Stack(
            children: [
              // 背景
              const PlayerBg(),
              // 渐变遮罩层
              SingleChildScrollView(
                physics: NeverScrollableScrollPhysics(),
                child: SizedBox(
                  width: Get.width,
                  height: Get.height,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Obx(()=> Top2tion(
                        title: homCon.selectDevice!.name.value,
                        net: true,
                        settings: true,
                      )),
                      const HomePlayImage(),
                      Obx(()=> Transform.scale(
                        scale: themeBase.isiPad.value? 0.65 : 1,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 12.sp),
                          child: Column(
                            children: [
                              const PlayerSongName(), // 歌曲信息
                              SizedBox(height: (themeBase.topBarHeight) / 6),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 14.sp),
                                child: const PlayerProgressBar(), 
                              ),
                              Obx(()=> themeBase.isiPad.value? SizedBox(height: (themeBase.topBarHeight) / 3) : SizedBox(height: (themeBase.topBarHeight) / 2.2)),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 5.sp),
                                child: const PlayerControlBody() // 播放控制部分
                              ),
                              Obx(()=> themeBase.isiPad.value? SizedBox(height: (themeBase.topBarHeight) / 3) : SizedBox(height: (themeBase.topBarHeight) / 2.2)),
                              Padding(
                                padding: EdgeInsets.only(
                                  left: 15.sp,
                                  right: 5.sp,
                                ),
                                child: const PlayerVolumeBody(), // 播放音量
                              ),
                              Obx(()=> themeBase.isiPad.value? Container() : SizedBox(height: (themeBase.topBarHeight + themeBase.searHeight.value) / 1.8)),
                              // lotion funthe locathe locatuh
                            ],
                          ),
                        ),
                      ))
                    ]
                  ),
                )
              )
            ],
          ),
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/radioPage/controller/arylic_controller.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';
// 隐私政策
class PrivacyPolicy extends StatelessWidget {
  const PrivacyPolicy({super.key});

  @override
  Widget build(BuildContext context) {
     
    return PagesBody(
      scroll: false,
      topbar:  Top2tion(
        title: 'privacy_policy'.tr,
      ),
      body: BodyBox(
        showTopBar: true,
        child:  Column(
          children: [
            // 内容
            Expanded(
              flex:4,
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal: 18.sp,
                  // vertical: 12.sp,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AText(
                      text:'service_content'.tr,
                      size: 22.sp,
                      color: themeBase.primaryColor.value,
                      softWrap: true,
                      family: TextFamily.bold,
                    ),
                    SizedBox(
                      height: 10.sp,
                    ),
                    AText(
                      text:'service_content_text'.tr,
                      size: 15.sp,
                      color: themeBase.primaryColor.value.withAlpha((255 * 0.8).round()),
                      spacing: 0.4,
                      softWrap: true,
                    ),
                    SizedBox(
                      height: 30.sp,
                    ),
                    AText(
                      text:'rights'.tr,
                      size: 22.sp,
                      color: themeBase.primaryColor.value,
                      softWrap: true,
                      family: TextFamily.bold,
                    ),
                    SizedBox(
                      height: 10.sp,
                    ),
                    AText(
                      text:'rights_text'.tr,
                      size: 15.sp,
                      color: themeBase.primaryColor.value.withAlpha((255 * 0.8).round()),
                      softWrap: true,
                      spacing: 0.4,
                    ),
                    SizedBox(
                      height: 30.sp,
                    ),
                    AText(
                      text: 'privacy_notice'.tr,
                      size: 22.sp,
                      color: themeBase.primaryColor.value,
                      softWrap: true,
                      family: TextFamily.bold,
                    ),
                    SizedBox(
                      height: 10.sp,
                    ),
                    AText(
                      text:'privacy_notice_text'.tr,
                      size: 15.sp,
                      color: themeBase.primaryColor.value.withAlpha((255 * 0.8).round()),
                      softWrap: true,
                      spacing: 0.4,
                    ),
                    SizedBox(height: 100.sp)
                  ],
                ),
              ),
            ),
            // 按钮
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 18.sp, vertical: 22.sp),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: OnTapScaleToSmallBox(
                      onTap: (){
                        // 取消的操作
                        Get.back();
                      },
                      child: Container(
                        height: 42.sp,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: themeBase.buttonColor2.value,
                          borderRadius: BorderRadius.circular(10.r)
                        ),
                        child: AText(
                          text: 'cancel'.tr,
                          size: themeBase.buttonFont.value,
                          color: themeBase.textColor1.value,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 14.sp,
                  ),
                  Flexible(
                    child: OnTapScaleToSmallBox(
                      onTap: (){
                        // 确认的操作
                        StorageClass.setStorage('ArylicRadioPrivacy', true);
                        arylicRadioMusic.getSummaryCode();
                        Get.offNamedUntil('/arylicRadio/public', ModalRoute.withName(Routes.radioPage));
                      },
                      child: Container(
                        height: 42.sp,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: themeBase.buttonColor1.value,
                          borderRadius: BorderRadius.circular(10.r)
                        ),
                        child: AText(
                          text: 'confirm'.tr,
                          size: themeBase.buttonFont.value,
                          color: themeBase.textColor1.value,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/ble_api.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/SettingPage/components/setting_box.dart';
import 'package:gocontrol/pages/home/<USER>/loading_page.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

class ScanPage extends StatefulWidget {
  const ScanPage({super.key});

  @override
  State<ScanPage> createState() => _ScanPageState();
}

class _ScanPageState extends State<ScanPage> with SingleTickerProviderStateMixin {
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _rotationAnimation = CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: 'Search Device',
        onTap: () {
          homCon.scanDevList.clear();
          homCon.setScanDevList.clear();
          BleApi.offScan();
          Get.back();
        },
        customIcon: OnTapScaleToSmallBox(
          onTap: () {
            homCon.scanDevList.clear();
            homCon.setScanDevList.clear();
            BleApi.scan(
              searchLibre: true,
              searchNET: true
            );
            _rotationController.reset();
            _rotationController.forward();
          },  
          child: Container(
            color: Colors.transparent,
            width: themeBase.topBarHeight,
            height: themeBase.topBarHeight,
            child: RotationTransition(
              turns: _rotationAnimation,  // 使用带有缓动效果的动画
              child: GoIcon(
                name: GoIcons.refresh,
                size: 25.sp,
                color: themeBase.primaryColor.value,
              ),
            ),
          ),
        )
      ),
      body: BodyBox(
        showTopBar: true,
        child: Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.symmetric(horizontal: 17.sp),
          margin: EdgeInsets.only(top: 30.sp, bottom: 17.sp),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Obx(()=> _setUpPages()),
          ),
        )
      )
    );
  }
}

Widget _setUpPages() {
  if (homCon.scanDevList.isNotEmpty) {
    return Container(
      decoration: BoxDecoration(
        color: themeBase.blockColor.value,
        borderRadius: BorderRadius.circular(10.sp),
      ),
      child: Obx(()=> ListView.builder(
        shrinkWrap: true, // 使ListView自适应内容高度
        padding: EdgeInsets.all(0.sp),
        itemCount: homCon.scanDevList.length,
        itemBuilder: (context, index) {
          return Obx(()=> SetBox2(
            title: homCon.scanDevList[index].name.value,
            info: 'rssi',
            iconInfo: GoIcon(
              name: GoIcons.srcBT,
              size: 20.sp,
              color: themeBase.primaryColor.value,
            ),
            onTap: () {
              // BleApi.connect(homCon.scanDevList[index]);
              // Get.back();
              showMyBottomSheet(
                height: 240.sp, 
                title: 'Connect Device',
                content: Container(
                  padding: EdgeInsets.symmetric(horizontal: 17.sp),
                  alignment: Alignment.center,
                  child: AText(
                    textAlign: TextAlign.center,
                    text: 'Whether to configure the network for device ${homCon.scanDevList[index].name.value}? The network configuration process will take a period of time. During this period, keep the device powered?',
                    size: 16.sp,
                    softWrap: true,
                    color: themeBase.primaryColor.value,
                  ),
                ), 
                showCancel: true.obs,
                cancel: () {
                  Get.back();
                },
                confirm: () {
                  // BleApi.connect(homCon.scanDevList[index]);
                  // Get.back();
                  homCon.setNetDev = homCon.scanDevList[index];
                  homCon.ref.value = false;
                  BleApi.offScan();
                  Get.offNamedUntil(Routes.setNetworkPage, ModalRoute.withName(Routes.homePage));
                }
              );
            }
          ));
        },
      )),
    );
  } else {
    return LoadingBox(
      text: 'Scan Device ...',
    );
  }
}


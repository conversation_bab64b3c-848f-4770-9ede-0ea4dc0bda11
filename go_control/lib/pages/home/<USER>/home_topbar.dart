import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/ble_api.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

class HomeTopBar extends StatelessWidget {
  const HomeTopBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      height: themeBase.topBarHeight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 18.sp),
            child: Row(
              children: [
                AText(
                  text: 'Go Control',
                  size: themeBase.headingFont.value + 8.sp,
                  color: themeBase.navColor.value,
                  family: TextFamily.bold,
                ),
                Obx(()=> Visibility(
                  visible: homCon.ref.value,
                  child: Container(
                    width: 18.sp,
                    height: 18.sp,
                    margin: EdgeInsets.only(left: 12.sp),
                    child: CircularProgressIndicator(
                      color: themeBase.primaryColor.value.withAlpha((255 * 0.7).round()),
                      strokeWidth: 2.5.sp,
                    ),
                  ),
                ))
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 6.sp),
            child: Row(
              children: [
                OnTapScaleToSmallBox(
                  onTap: () async {
                    homCon.scanDevList.clear();
                    homCon.setScanDevList.clear();
                    homCon.setNetDev = null;
                    Get.toNamed(Routes.scanPage);
                    BleApi.scan(
                      searchLibre: true,
                      searchNET: true
                    );
                  },
                  child: Container(
                    alignment: Alignment.center,
                    child: GoIcon(
                      name: GoIcons.setAdd,
                      size: 24.sp,
                      color: themeBase.navColor.value,
                    ),
                  ),
                ),
                SizedBox(width: 8.sp),
                _getSettings(
                  ontap: (){
                    Log.d('跳转settigns');
                    Scaffold.of(context).openEndDrawer();
                  },
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

// App设置按钮
Widget _getSettings({required void Function() ontap}){
  final RxBool tag = false.obs;
  return GestureDetector(
    onTap: (){
      ontap();
      tag.value = true;
      Log.d(themeBase.isiPad.value);
      Future.delayed(const Duration(milliseconds: 120),((){ tag.value = false;}));
    },
    onTapDown: (v) => tag.value = true,
    onTapUp: (v)=> tag.value = false,
    onTapCancel: ()=> tag.value = false,
    child: Obx(()=> AnimatedContainer(
      duration: const Duration(milliseconds: 120),
      decoration: BoxDecoration(
        color: themeBase.secondaryColor.value.withOpacity(tag.value? 0.3 : 0),
        borderRadius: BorderRadius.circular(360.r)
      ),
      margin: EdgeInsets.only(right: 5.sp),
      width: themeBase.topBarHeight * 0.8,
      height: themeBase.topBarHeight * 0.8,
      child: AnimatedScale(
        scale: tag.value? 0.93 : 1,
        duration: const Duration(milliseconds: 180),
        child: Transform.translate(
          offset: Offset(1.sp,0.5.sp),
          child: Obx(()=> GoIcon(
            name: GoIcons.setting,
            size: 25.sp,
            color: themeBase.navColor.value,
          )),
        ),
      )
    )),
  );
}


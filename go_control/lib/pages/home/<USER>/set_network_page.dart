import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/ble_api.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/set_network.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

class SetNetworkPage extends StatefulWidget {
  const SetNetworkPage({super.key});

  @override
  State<SetNetworkPage> createState() => _SetNetworkPageState();
}

class _SetNetworkPageState extends State<SetNetworkPage> with SingleTickerProviderStateMixin {

  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    if (homCon.setNetDev == null) return;
    final SetDeviceNetWork dev = homCon.setNetDev! as SetDeviceNetWork;
    dev.connectDevice();

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _rotationAnimation = CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _rotationController.dispose();
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final SetDeviceNetWork device = homCon.setNetDev! as SetDeviceNetWork;

    return WillPopScope(
      onWillPop: () async => false,
      child: PagesBody(
        scroll: false,
        topbar: Top2tion(
          title: 'cnt2'.tr,
          ehBack: true,
          customIcon: OnTapScaleToSmallBox(
            onTap: () {
              if (device.wifiSetupIndex.value == 2) return;
              homCon.scanDevList.clear();
              homCon.setScanDevList.clear();
              device.refreshWiFi();
              _rotationController.reset();
              _rotationController.forward();
            },
            child: Container(
              color: Colors.transparent,
              width: themeBase.topBarHeight,
              height: themeBase.topBarHeight,
              child: Obx(() => Visibility(
                visible: device.wifiSetupIndex.value == 2 || device.wifiSetupIndex.value == 3,
                child: RotationTransition(
                  turns: _rotationAnimation, 
                  child: GoIcon(
                    name: GoIcons.refresh,
                    size: 25.sp,
                    color: themeBase.primaryColor.value,
                  ),
                )
              )),
            ),
          ),
        ),
        body: BodyBox(
          showTopBar: true,
          child: Container(
            alignment: Alignment.topCenter,
            padding: EdgeInsets.symmetric(horizontal: 17.sp),
            margin: EdgeInsets.only(top: 30.sp, bottom: 17.sp),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Obx(()=> _setUpPages()),
            ),
          )
        )
      )
    );
  }
}

Widget _setUpPages() {
  if (homCon.setNetDev == null) return Container();
  final SetDeviceNetWork dev = homCon.setNetDev! as SetDeviceNetWork;

  switch (dev.wifiSetupIndex.value) {
    case 1:
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Loading(
            color: themeBase.primaryColor.value,
            size: 22.w,
          ),
          SizedBox(height: 14.sp,),
          AText(
            text: 'scan_ap'.tr,
            family: TextFamily.medium,
            size: themeBase.subBodyFont2.value + 2.sp,
            color: themeBase.primaryColor.value,
          ),
        ],
      );
    case 2:
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Loading(
            color: themeBase.primaryColor.value,
            size: 22.w,
          ),
          SizedBox(height: 14.sp,),
          AText(
            text: 'scan_ap'.tr,
            family: TextFamily.medium,
            size: themeBase.subBodyFont2.value + 2.sp,
            color: themeBase.primaryColor.value,
          ),
        ],
      );
    case 3:
      if (dev.wifiScanList.isEmpty) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Loading(
              color: themeBase.primaryColor.value,
              size: 22.w,
            ),
            SizedBox(height: 14.sp,),
            AText(
              text: 'scan_ap'.tr,
              family: TextFamily.medium,
              size: themeBase.subBodyFont2.value + 2.sp,
              color: themeBase.primaryColor.value,
            ),
          ],
        );
      } else {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 17.sp),
          decoration: BoxDecoration(
            color: themeBase.blockColor.value,
            borderRadius: BorderRadius.circular(10.sp),
          ),
          child: ListView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.only(top: 0),
            itemBuilder: (context, index) {
              final Map wifi = dev.wifiScanList[index];

              return GestureDetector(
                onTap: (){
                  dev.selectWiFi.value = wifi;
                  dev.wifiSetupIndex.value = 4;
                },
                child: Container(
                  height: 44.sp,
                  margin: EdgeInsets.only(bottom: 3.sp, top: 5.sp),
                  decoration: BoxDecoration(
                    border: index + 1 != dev.wifiScanList.length? Border(
                      bottom: BorderSide(
                        width: 1.sp,
                        color: themeBase.primaryColor.value.withAlpha((255 * 0.3).toInt())
                      )
                    ):null
                  ),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      GoIcon(
                        name: GoIcons.wifi,
                        size: 22.sp,
                        color: themeBase.primaryColor.value,
                      ),
                      SizedBox(width: 9.sp,),
                      Flexible(
                        flex: 1,
                        child: Container(
                          alignment: Alignment.centerLeft,
                          child: AText(
                            text: dev is Libre? dev.wifiScanList[index]['ssid'] : Comm.hexStrToString(dev.wifiScanList[index]['ssid']),
                            family: TextFamily.medium,
                            size: themeBase.subBodyFont.value + 1.sp,
                            color: themeBase.primaryColor.value,
                          ),
                        ),
                      ),
                      SizedBox(width: 18.sp,),
                      GoIcon(
                        name: GoIcons.lock,
                        size: 20.sp,
                        color: themeBase.primaryColor.value,
                      ),
                    ],
                  ),
                ),
              );
            },
            itemCount: dev.wifiScanList.length,
          ),
        );
      }
    case 4: 
      final FocusNode focusNode = FocusNode();
      final TextEditingController mycontrol = TextEditingController();
      final RxBool eyes = false.obs;
      mycontrol.text = StorageClass.getStorage(dev.selectWiFi['ssid']) ?? '';

      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              Obx(() => Container(
                height: 44.sp,
                margin: EdgeInsets.only(
                  bottom: 12.sp,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  color:const Color.fromRGBO(230, 230, 230, 1)
                ),
                child: TextField(
                  obscureText: !eyes.value, // 是否为密码选项
                  autofocus: false,  // 自动获取焦点
                  focusNode: focusNode, // 轻触屏幕时触发的回调
                  controller: mycontrol, //文本控制器
                  onTapOutside: (e) => focusNode.unfocus(),
                  cursorColor: themeBase.primaryColor.value,
                  // onChanged: (value)=> Log.h(value),
                  decoration: InputDecoration(
                    hintText: 'psw'.tr,
                    filled: true,
                    fillColor: Colors.transparent,
                    hintStyle: TextStyle(
                      color: themeBase.primaryColor.value.withAlpha((255 * 0.5).toInt()),
                      fontSize: themeBase.subBodyFont.value + 1.sp,
                      fontFamily: 'Medium'
                    ),
                    suffixIcon: GestureDetector(
                      onTap: () => eyes.value = !eyes.value,
                      child: SizedBox(
                        height: 44.sp,
                        width: 44.sp,
                        child: GoIcon(
                          name: eyes.value?GoIcons.eyes:GoIcons.closeEyes,
                          size: 23.sp,
                          color: themeBase.primaryColor.value,
                        ),
                      ),
                    ),
                    // 设置内容区域内边距
                    contentPadding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 12.sp),
                    border: InputBorder.none,
                    focusedBorder: OutlineInputBorder( 
                      borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
                      borderSide: BorderSide.none, // 取消边框
                    ), // 设置聚焦时的边框
                    enabledBorder: OutlineInputBorder( 
                      borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
                      borderSide: BorderSide.none, // 取消边框
                    ), // 设置取消时的边框
                  ),
                  style: TextStyle(
                    color: themeBase.primaryColor.value,
                    fontSize: themeBase.subBodyFont.value + 1.sp,
                    fontFamily: 'Medium'
                  ),
                )
              )),
              Container(
                padding: EdgeInsets.only(left: 9.sp,top: 8.sp),
                alignment: Alignment.centerLeft,
                child: AText(
                  text: 'set_wifi_msg'.tr,
                  family: TextFamily.medium,
                  size: themeBase.subBodyFont2.value  + 3.sp,
                  color: const Color.fromRGBO(166, 166, 166, 1),
                  softWrap: true,
                ),
              ),
            ],
          ),
          SizedBox(height: 34.sp),
          Row(
            children: [
              Flexible(
                flex: 1,
                child: GestureDetector(
                  onTap: (){
                    dev.wifiSetupIndex.value = 3;
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 43.sp,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color:themeBase.buttonColor2.value,
                    ),
                    child: AText(
                      text: 'cancel'.tr,
                      color: themeBase.textColor1.value,
                      size: themeBase.subBodyFont.value + 1.sp,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 16.sp,),
              Flexible(
                flex: 1,
                child: GestureDetector(
                  onTap: () async {
                    dev.connectToSlecetWiFi(
                      ssid: dev.selectWiFi['ssid'],
                      pwd: mycontrol.text
                    );

                    StorageClass.setStorage(dev.selectWiFi['ssid'], mycontrol.text);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 43.sp,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: themeBase.buttonColor1.value,
                    ),
                    child: AText(
                      text: 'confirm'.tr,
                      color: themeBase.textColor1.value,
                      size: themeBase.subBodyFont.value + 1.sp,
                    ),
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: 3.sp,)
        ],
      );
    case 5: 
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Loading(
            color: themeBase.primaryColor.value,
            size: 22.w,
          ),
          SizedBox(height: 4.sp,),
          AText(
            text: '${'cn1s'.tr} "${ dev is Libre? dev.selectWiFi['ssid'] : Comm.hexStrToString(dev.selectWiFi['ssid'])}"...',
            family: TextFamily.medium,
            size: themeBase.subBodyFont2.value + 2.sp,
            color: themeBase.primaryColor.value,
          ),
        ],
      );
    case 6:
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Loading(),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.sp),
            child: AText(
              text: 'cn2s'.tr,
              color: themeBase.primaryColor.value,
              textAlign: TextAlign.center,
              size: 16.sp,
              softWrap: true,
            ),
          )
        ],
      );
    case 7:
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 44.sp,
            height: 44.sp,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(44.sp),
              color: Colors.greenAccent
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 36.sp,
                  height: 36.sp,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(33.sp),
                    color: Colors.white
                  ),
                  child: GoIcon(
                    name: GoIcons.tick,
                    color: Colors.greenAccent,
                    size: 33.sp,
                  ),
                ),
              ],
            )
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 26.sp),
            child: AText(
              text: 'cn3s'.tr,
              color: themeBase.primaryColor.value,
              textAlign: TextAlign.center,
              size: 16.sp,
              softWrap: true,
            ),
          ),
          OnTapScaleToSmallBox(
            onTap: () async {
              Get.back();
              Log.d('是否需要设置cast ${homCon.needSetGCast.value} 配网之后');
              if (homCon.needSetGCast.value) {
                homCon.needSetGCast.value = false;
                await Future.delayed(const Duration(milliseconds: 100));
                BleApi.offScan();
                Get.offNamedUntil('/libreSetInfo/setGcast', ModalRoute.withName(Routes.homePage));
              }
            },
            child: Container(
              height: 42.sp,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: themeBase.buttonColor1.value,
                borderRadius: BorderRadius.circular(10.r)
              ),
              child: AText(
                text: 'OK'.tr,
                size: themeBase.buttonFont.value,
                color: themeBase.textColor1.value,
              )
            ),
          )
        ],
      );
  }
  return Container();
}
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/pages/home/<USER>/loading_page.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:gocontrol/pages/AppSetting/setting.dart';
import 'package:gocontrol/pages/home/<USER>/home_topbar.dart';
import 'package:gocontrol/pages/home/<USER>/home_list.dart';

class PageHome extends StatefulWidget {
  const PageHome({super.key});

  @override
  State<PageHome> createState() => _PageHomeState();
}

class _PageHomeState extends State<PageHome> {

  @override
  Widget build(BuildContext context) {
    // 获取设备的宽度和高度
    final double width = MediaQuery.of(context).size.width;
    final double height = MediaQuery.of(context).size.height;

    // 判断是否是iPad布局
    final bool isiPad = width > 600 && height > 600;
    if (isiPad) themeBase.isiPad.value = true;
    // Log.w('是否是iPad布局：$isiPad');

    return Scaffold(
      endDrawer: const AppSetting(),
      body: Obx(()=> AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        color: themeBase.bgColor.value,
        child: CustomScrollView(
          physics: const NeverScrollableScrollPhysics(),
          slivers: [
            SliverAppBar(
              backgroundColor: themeBase.bgColor.value, 
              toolbarHeight: themeBase.topBarHeight,
              automaticallyImplyLeading: false,
              elevation: 0,
              pinned: true,
              systemOverlayStyle: SystemUiOverlayStyle.dark,
              actions: [Container()],
              flexibleSpace: Transform.translate(
                offset: Offset(0, 2.sp),
                child: const SafeArea(
                  bottom: false,
                  child: HomeTopBar(),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: SizedBox(
                  height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
                  child: EasyRefresh.builder(
                    header: MaterialHeader(color: themeBase.primaryColor.value),
                    onRefresh: () async {
                      homCon.initMode(refresh: true);
                      await Future.delayed(const Duration(milliseconds: 1000));
                    },
                    childBuilder: (context, physics) {
                      return CustomScrollView(
                        physics: physics,
                        slivers: [
                          Obx(() => homCon.adevices.isEmpty? SliverToBoxAdapter(
                            child: LoadingBox(
                              text: 'search_device'.tr,
                            ),
                          ) : const HomeList())
                        ],
                      );
                    }
                  ),
                )
              )
            ),
          ],
        )
      )),
    );
  }
}

// Widget _getWidget() {
//   if (
//     Platform.isAndroid &&
//    (!AppPermission.location.value ||
//     （!AppPermission.bluetooth.value ||
//     （!AppPermission.bluetoothConnect.value ||
//     （!AppPermission.bluetoothAdvertise.value ||
//     （!AppPermission.bluetoothScan.value )
//   ) {
//     return Container(
//       height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
//       padding: EdgeInsets.all(16.sp),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           AText(
//             text: 'no_permission'.tr,
//             color: themeBase.primaryColor.value,
//             size: 24.sp,
//             softWrap: true,
//           ),
//           Visibility(
//             visible: AppPermission.phoneSystem == 'ios',
//             child: Container(
//               margin: EdgeInsets.all(12.sp),
//               child: Obx(()=> AText(
//                 text: 
// '''
// location = ${AppPermission.location.value},
// storage = ${AppPermission.storage.value},
// bluetooth = ${AppPermission.bluetooth.value},
// ''',
//                 color: themeBase.primaryColor.value,
//                 size: 24.sp,
//                 softWrap: true,
//               ),)
//             ),
//           ),
//           Visibility(
//             visible: AppPermission.phoneSystem != 'ios',
//             child: Container(
//               margin: EdgeInsets.all(12.sp),
//               child: Obx(()=> AText(
//                 text: 
// '''
// location = ${AppPermission.location.value},
// storage = ${AppPermission.storage.value},
// bluetooth = ${AppPermission.bluetooth.value},
// bluetoothConnect = ${AppPermission.bluetoothConnect.value},
// bluetoothAdvertise = ${AppPermission.bluetoothAdvertise.value},
// bluetoothScan = ${AppPermission.bluetoothScan.value},
// ''',
//                 color: themeBase.primaryColor.value,
//                 size: 24.sp,
//                 softWrap: true,
//               ),)
//             ),
//           ),
//           Container(
//             margin: EdgeInsets.only(top: 42.sp),
//             width: 188.sp,
//             height: 52.sp,
//             child: ElevatedButton(
//               onPressed: (){
//                 openAppSettings();
//               },
//               child: AText(
//                 text: 'open_settings'.tr,
//                 color: themeBase.primaryColor.value,
//                 size: 18.sp,
//               )
//             ),
//           )
//         ],
//       )
//     );
//   }else{
//     return SizedBox(
//       height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
//       child: EasyRefresh.builder(
//         header: MaterialHeader(color: themeBase.primaryColor.value),
//         onRefresh: () async {
//           homCon.initMode(refresh: true);
//           await Future.delayed(const Duration(milliseconds: 1000));
//         },
//         childBuilder: (context, physics) {
//           return CustomScrollView(
//             physics: physics,
//             slivers: [
//               Obx(() => homCon.adevices.isEmpty? SliverToBoxAdapter(
//                 child: LoadingBox(
//                   text: 'search_device'.tr,
//                 ),
//               ) : const HomeList())
//             ],
//           );
//         }
//       ),
//     );
//   }
// }
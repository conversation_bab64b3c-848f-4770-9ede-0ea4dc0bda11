
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class SongNameHL extends StatelessWidget {
  const SongNameHL(this.inx,{super.key});

  final int inx;

  String _getSongName(DeviceBase device){
    if(device is Libre) {
      if (device.songName.value != '') {
        return device.songName.value;
      } else {
        if (device.sourceInput.value == 'NET') {
          if (device.origin == Origins.ble) {
            return 'btm_title'.tr;
          } else {
            return 'no_music'.tr;
          }
        }
        if (device.sourceInput.value == 'USBPLAY' && device.usbNowSongItem.value != '') {
          return device.usbNowSongItem.value;
        }
        for (var el in device.sourceInputList) { 
          if(el['id'] == device.sourceInput.value) return el['name'];
        }
      }
    }
    
    if (device is! AbstractNET) {
      for (var el in device.sourceInputList) {
        if(el['id'] == device.sourceInput.value) return el['name'];
      }
    } else { // 网络
      if (device.songName.value != '') {
        return device.songName.value;
      } else {
        if (device.sourceInput.value == 'NET' && device.origin == Origins.ble) return 'btm_title'.tr;
        if (device.sourceInput.value == 'NET') return 'no_music'.tr;
        for (var el in device.sourceInputList) { 
          if (device.project.contains('A100') && device.sourceInput.value == 'USBDAC') {
            return 'USBPLAY';
          } else {
            if(el['id'] == device.sourceInput.value) return el['name'];
          }
        }
      }
    }
    if (device is B50 && !device.isBLEConnected.value) return 'Select to connection';
    return 'UnKnown'.tr;
  }

  @override
  Widget build(BuildContext context) {
    DeviceBase device = homCon.adevices[inx];
    return Container(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(top: 6.sp ,right: 12.sp),
      child: Obx(() => AText(
        text: _getSongName(device),
        family: TextFamily.medium,
        size: themeBase.bodyFont.value - 1.sp,
        color: themeBase.textColor1.value,
        overflow: TextOverflow.ellipsis,
      )),
    );
  }
}

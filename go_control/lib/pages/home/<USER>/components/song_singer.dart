import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:gocontrol/components/atext.dart';

import '../../components/mmvlizer.dart';

class SongSingerHL extends StatelessWidget {
  const SongSingerHL(this.inx,{super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    DeviceBase device = homCon.adevices[inx];
    if(device is AbstractNET){
      return Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(top: 2.sp),
        child: Row(
          children: [
            Flexible(
              flex: 2,
              child: Obx(()=> AText(
                text: _songMetaInfo(device),
                size: themeBase.subBodyFont.value - 1.sp,
                color: themeBase.textColor1.value.withOpacity(.7),
                softWrap: false,
                overflow: TextOverflow.ellipsis,
              )),
            ),
            Flexible(
              child: Transform.translate(
                offset: Offset(0,-1.sp),
                child: SingleChildScrollView(
                  child: Container(
                    width: 22.sp,
                    height: 12.sp,
                    margin: EdgeInsets.only(left: 6.sp),
                    alignment: Alignment.centerLeft,
                    child: Obx(() => Visibility(
                      visible: device.isPlaying.value && device.songName.value != '',
                      child: MiniMusicVisualizer(
                        color: themeBase.textColor1.value,
                        width: 2.5.sp,
                        height: 12.sp,
                        animate: device.isPlaying.value,
                      ),
                    ))
                  ),
                ),
              )
            )
          ],
        )
      );
    } else if (device is Libre) {
      return Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(top: 2.sp),
        child: Row(
          children: [
            Flexible(
              flex: 2,
              child: Obx(()=> AText(
                text: _songMetaInfo2(device),
                size: themeBase.subBodyFont.value - 1.sp,
                color: themeBase.textColor1.value.withOpacity(.7),
                softWrap: false,
                overflow: TextOverflow.ellipsis,
              )),
            ),
            Flexible(
              child: Transform.translate(
                offset: Offset(0,-1.sp),
                child: SingleChildScrollView(
                  child: Container(
                    width: 22.sp,
                    height: 12.sp,
                    margin: EdgeInsets.only(left: 6.sp),
                    alignment: Alignment.centerLeft,
                    child: Obx(() => Visibility(
                      visible: device.isPlaying.value && device.songName.value != '',
                      child: MiniMusicVisualizer(
                        color: themeBase.textColor1.value,
                        width: 2.5.sp,
                        height: 12.sp,
                        animate: device.isPlaying.value,
                      ),
                    ))
                  ),
                ),
              )
            )
          ],
        )
      );
    } else {
      return const AText(text: '');
    }
  }

  String _songMetaInfo(AbstractNET device){
    if(device.songAlbum.value != '' && device.songArtist.value != ''){
      return device.songArtist.value;
    }else if(device.songAlbum.value != '' && device.songArtist.value == ''){
      return device.songAlbum.value;
    }else if(device.songAlbum.value == '' && device.songArtist.value != ''){
      return device.songArtist.value;
    }else {
      return '';
    }
  }

  String _songMetaInfo2(Libre device){
    if(device.songAlbum.value != '' && device.songArtist.value != ''){
      return device.songArtist.value;
    }else if(device.songAlbum.value != '' && device.songArtist.value == ''){
      return device.songAlbum.value;
    }else if(device.songAlbum.value == '' && device.songArtist.value != ''){
      return device.songArtist.value;
    }else {
      return '';
    }
  }
}
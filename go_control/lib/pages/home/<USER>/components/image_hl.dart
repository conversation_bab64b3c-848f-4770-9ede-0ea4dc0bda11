import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a5_playinfo.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class SongImage extends StatelessWidget {
  const SongImage(this.index, {super.key});

  final int index;

  @override
  Widget build(BuildContext context) {
    if (homCon.adevices[index] is A5Playinfo) {
      A5Playinfo device = homCon.adevices[index] as A5Playinfo;
      return SizedBox(
        width: 56.sp,
        height: 56.sp,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(0),
          child: Obx(()=> CachedNetworkImage(
            imageUrl: device.songImage.value,
            useOldImageOnUrlChange: true,
            fit: BoxFit.cover,
            errorWidget: (context, url, error) => Obx(()=> _forPlaceholder(device as DeviceBase)),
            placeholder: (context, url) => Obx(()=> _forPlaceholder(device as DeviceBase)),
          )),
        ),
      );
    } else {
      return _forPlaceholder(homCon.adevices[index]);
    }
  }
}

Widget _forPlaceholder(DeviceBase device){
  IconData icon = GoIcons.srcWiFi;
  double iconSize = 26.sp;

  try {
    final Map item = device.sourceInputList.firstWhere((el)=> el['id'] == device.sourceInput.value);
    final a100DacToUsb = device is AbstractNET
      && device.project.contains('A100')
      && device.sourceInput.value == 'USBDAC';
    if (device.sourceInput.value == 'AURA') {
      iconSize = 22.sp;
    }
    if (a100DacToUsb) {
      icon = GoIcons.srcUSB;
      iconSize = 29.sp;
    } else {
      icon = item['icon'];
    }
  } catch(_) {}

  return Container(
    width: 60.sp,
    height: 60.sp,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(0),
      color: themeBase.primaryColor.value.withAlpha((255 * 0.4).toInt()),
    ),
    child: GoIcon(
      name: icon,
      size: iconSize,
      color: Colors.white.withAlpha((255 * 0.4).toInt()),
    ),
  );
}
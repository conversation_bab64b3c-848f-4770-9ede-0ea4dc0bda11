import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/pages/PlayerPage/b50_player_page/components/play_cont_body.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/home/<USER>/components/usb_song_list.dart';
import 'package:gocontrol/pages/poise/poise_body.dart';
  
class USBplayPage extends StatelessWidget {
  const USBplayPage({super.key});

  @override
  Widget build(BuildContext context) {
    AbstractBLE device = homCon.selectDevice! as AbstractBLE;

    return PagesBody(
      topbar: Top2tion(
        title: device.name.value,
        settings: true,
      ),
      body: BodyBox(
        showTopBar: true,
        child: SingleChildScrollView(
          child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.sp),
          child: Column(
            children: [
              PlayContBody(
                device: device
              ),
              SizedBox(height: 16.sp),
              Obx(() => Visibility(
                visible: device.dataTot.value != 0 && device.sourceInput.value == 'USBPLAY',
                child: UsbSongList(
                  device: device,
                  b50page: true
                ),
              )),
              Obx(() => Padding(
                padding: EdgeInsets.only(top: device.dataTot.value == 0? 0.sp : 0, bottom: 12.sp),
                child: const PoiseBody(),
              )),
            ],
          )
        ),
        ),
      ),
    );
  }
}
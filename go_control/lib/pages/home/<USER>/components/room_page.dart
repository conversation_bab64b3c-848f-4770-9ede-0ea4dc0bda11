import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/new_slider.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class RoomPage extends StatelessWidget {
  const RoomPage(this.inx,{super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    DeviceBase dev = homCon.adevices[inx];
    if (dev is AbstractNET) {
      return Obx(() => AnimatedContainer(
        duration: const Duration(milliseconds: 220),
        curve: Curves.easeInOut,
        padding: EdgeInsets.only(
          left: 16.sp
        ),
        height: dev.showAllVolume.value && dev.roomChildren.isNotEmpty? dev.roomChildren.length * 60.sp + 66.sp: 0,
        child: Obx(() => AnimatedOpacity(
          opacity: dev.showAllVolume.value && dev.roomChildren.isNotEmpty? 1:0,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
          child: ListView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.all(0.sp),
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              try{
                AbstractNET forDev = homCon.adevices.firstWhere((el) => el is AbstractNET && dev.roomChildren[index] == el.ip) as AbstractNET;
                if(index == 0){
                  return Column(
                    children: [
                      _getSilider(dev,dev),
                      _getSilider(forDev,dev)
                    ],
                  );
                } else if (index + 1 == dev.roomChildren.length) {
                  return Column(
                    children: [
                      _getSilider(forDev,dev),
                      SizedBox(height: 6.sp,)
                    ],
                  );
                } else {
                  return _getSilider(forDev,dev);
                }
              } catch(e) {
                Log.e(e);
                return Container();
              }
            },
            itemCount: dev.roomChildren.length,
          ),
        )),
      ));
    }else{
      return const SizedBox();
    }
  }
}

Widget _getSilider(AbstractNET device,AbstractNET masterDev){
  return Obx(() => SizedBox(
    height: 60.sp,
    child: Row(
      children: [
        Flexible(
          child: Column(
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: AText(
                  text: device.name.value,
                  color: themeBase.textColor1.value,
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 6.sp),
                child: Row(
                  children: [
                    Obx(() => SvgPicture.asset(
                      _getVolume(device),
                      width: 20.sp,
                      height: 20.sp,
                      fit: BoxFit.cover,
                      colorFilter: ColorFilter.mode(
                        themeBase.textColor1.value, // 更改SVG图像的颜色，这里使用红色
                        BlendMode.dst, // 混合模式，可以根据需要选择
                      ),
                    )),
                    Flexible(
                      child: Obx(() => SliderTheme(
                        data:  SliderThemeData(
                          activeTrackColor: themeBase.textColor1.value,
                          thumbColor: themeBase.textColor1.value,
                          inactiveTrackColor: themeBase.textColor1.value.withOpacity(.4),
                          thumbShape: RoundSliderThumbShape(
                            enabledThumbRadius: 6.sp, // 设置thumb的半径
                          ),
                          overlayShape: RoundSliderOverlayShape(
                            overlayRadius: 12.sp// 设置滑块覆盖层大小，
                          ),
                          trackHeight: 4.sp,
                          trackShape: const NewSliderTrackShape()
                        ),
                        child: Slider(
                          min: 0,
                          max: 100,
                          value: device.volume.value,
                          onChanged: (v){
                            device.volume.value = v;
                          },
                          onChangeEnd: (v){
                            device.setVolume(v);
                            Future.delayed(const Duration(milliseconds: 100),(){
                              masterDev.getMatchingVolumes();
                            });
                          },
                        ),
                      )),
                    ),
                  ],
                )
              )
            ],
          ),
        ),
        GestureDetector(
          onTap: (){
            Log.d(device.name.value);
            homCon.selectDevice = device;
            if(device is BaseAudio) {
              BaseAudio audioDevice = device as BaseAudio;
              audioDevice.initAudioData();
            }
            Get.toNamed('/audioPage');
          },
          child: Container(
            width: 36.sp,
            height: 60.sp,
            color: Colors.transparent,
            child: Transform.translate(
              offset: Offset(-6.sp, 8.sp),
              child: GoIcon(
                name: GoIcons.audioIcon,
                size: 21.sp,
                color: themeBase.textColor1.value,
              ),
            ),
          ),
        )
      ],
    )
  ));
}

String _getVolume(DeviceBase device){
  if(device.volume.value < 30){
    return 'images/volume3.svg';
  }else if(device.volume.value < 80 && device.volume.value > 29){
    return 'images/volume2.svg';
  }else{
    return 'images/volume1.svg';
  }
}
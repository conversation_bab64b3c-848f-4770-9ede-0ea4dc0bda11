import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

import '../../components/mmvlizer.dart';

class RxTxBody extends StatelessWidget {
  const RxTxBody(this.inx, {
    super.key,
  });

  final int inx;

  @override
  Widget build(BuildContext context) {
    final device = homCon.adevices[inx];

    if (device is A31) {
      return Obx(()=> Visibility(
        visible: device.deviceMode.value == 'H50',
        child: Container(
          margin: EdgeInsets.only(top: 6.sp),
          padding: EdgeInsets.symmetric(horizontal: 17.sp),
          child: Column(
            children: [
              _btmBody(1),
              _btmBody(2)
            ],
          ),
        ),
      ));
    } else if (device is Libre) {
      return Obx(()=> Visibility(
        visible: (device.deviceMode.value == 'LP100' || device.deviceMode.value == 'LA150') && device.sourceInput.value != 'NET',
        child: Container(
          margin: EdgeInsets.only(top: 6.sp),
          padding: EdgeInsets.symmetric(horizontal: 17.sp),
          child: Column(
            children: [
              OnTapScaleToSmallBox(
                onTap: () {
                  if (device.btsState.value == BT_STATUE.TL_BT_STATE_PLAYING) {
                    showMyBottomSheet(
                      height: 240.sp, 
                      title: 'Tips',
                      content: Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(horizontal: 17.sp),
                        child: AText(
                          text: 'Are you sure you want to disconnect the current connection?',
                          color: themeBase.primaryColor.value,
                          size: themeBase.subBodyFont.value + 1.sp,
                          weight: FontWeight.bold,
                          textAlign: TextAlign.center,
                          softWrap: true,
                        ),  
                      ),
                      showCancel: true.obs,
                      cancel: () => Get.back(),
                      confirm: () {
                        device.disPair();
                        Get.back();
                      }
                    );
                  } else {
                    device.getPair();
                  }
                },
                child: Obx(()=> Container(
                  height: 32.sp,
                  padding: EdgeInsets.symmetric(horizontal: 7.sp),
                  decoration: BoxDecoration(
                    color: themeBase.primaryColor.value.withAlpha((255 * .4).round()),
                    borderRadius: BorderRadius.circular(8.r)
                  ),
                  child: Row(
                    children: [
                      Obx(() {
                        if (device.sourceInput.value == 'BT') {
                          return GoIcon(
                            name: const IconData(0xe669, fontFamily: 'IconFont'),
                            color: themeBase.blockColor.value,
                            size: 20.sp,
                          );
                        } else if (device.sourceInput.value == 'AURA') {
                          return GoIcon(
                            name: const IconData(0xe68c, fontFamily: 'IconFont'),
                            color: themeBase.blockColor.value,
                            size: 16.sp,
                          );
                        } else {
                          return GoIcon(
                            name: const IconData(0xe65b, fontFamily: 'IconFont'),
                            color: themeBase.blockColor.value,
                            size: 20.sp,
                          );
                        }
                      }),
                      SizedBox(width: 6.sp),
                      Flexible(
                        child: Row(
                          children: [
                            Obx(()=> AText(
                              text: () {
                                if (device.btsState.value == BT_STATUE.TL_BT_STATE_PLAYING) {
                                  return 'Connected';
                                } else if (device.btsState.value == BT_STATUE.TL_BT_STATE_PAIRING) {
                                  return 'Pairing...';
                                } else {
                                  if (device.sourceInput.value == 'BT') {
                                    return 'Waiting for connect';
                                  } else {
                                    return 'Click to pair';
                                  }
                                }
                              } (),
                              color: themeBase.textColor1.value,
                              size: themeBase.subBodyFont2.value + 1.sp,
                              weight: FontWeight.bold,
                              overflow: TextOverflow.ellipsis,
                            )),
                            SizedBox(width: 6.sp),
                            Obx(() => Visibility(
                              visible: () {
                                if (device.btsState.value == BT_STATUE.TL_BT_STATE_PLAYING) {
                                  return true;
                                } else if (device.btsState.value == BT_STATUE.TL_BT_STATE_PAIRING) {
                                  return false;
                                } else {
                                  return false;
                                }
                              } (),
                              child: Container(
                                //连接后的播放状态：播放动画
                                alignment: Alignment.bottomCenter,
                                padding: EdgeInsets.only(bottom: 6.sp),
                                margin: EdgeInsets.symmetric(horizontal: 3.sp),
                                //需要替换的地方：换成播放动画的图标或动画的操作
                                child: Transform.translate(
                                  offset: Offset(0, -3.sp),
                                  child: MiniMusicVisualizer(
                                    color: themeBase.textColor1.value,
                                    width: 2.5.sp,
                                    height: 12.sp,
                                    animate: true,
                                  ),
                                ),
                              )
                            )),
                          ],
                        )
                      ),
                      Obx(()=> Visibility(
                        //根据连接情况显示：未连接：不显示，已连接：显示
                        visible: () {
                          if (device.btsState.value == BT_STATUE.TL_BT_STATE_PLAYING) {
                            return true;
                          } else if (device.btsState.value == BT_STATUE.TL_BT_STATE_PAIRING) {
                            return false;
                          } else {
                            return false;
                          }
                        } (), // device.name.value != '',
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 2.sp),
                          child: GoIcon(
                            //需要在图标库中添加该图标：断开连接图标
                            name: const IconData(0xe905,fontFamily: 'IconFont'),
                            size: 16.sp,
                            color: themeBase.blockColor.value,
                          ),
                        )
                      )),
                    ],
                  ),
                )),
              )
            ],
          ),
        ),
      ));
    } else {
      return Container();
    }
    
  }

  Widget _btmBody(int btmInx) {
    if (homCon.adevices[inx] is! A31) return Container();
    final device = homCon.adevices[inx] as A31;
    return OnTapScaleToSmallBox(
      onTap: () {
        if (_getBtmms(btmInx)['name'] == '') {
          device.btmOn();
        } else {
          showMyBottomSheet(
            height: 180.sp, 
            title: 'btm_disconnect'.tr, 
            content: Padding(
              padding: EdgeInsets.all(18.sp),
              child: AText(
                text: 'btm_disconnect_text'.tr,
                color: themeBase.primaryColor.value,
                size: themeBase.subBodyFont.value,
                softWrap: true,
              ),
            ), 
            showCancel: true.obs,
            cancel: ()=> Get.back(),
            confirm: (){
              device.sendMsgToSocket('MCU+PAS+RAKOIT:KEY:BT_RESET&');
              device.btmState.value = true;
              Future.delayed(const Duration(seconds: 15),()=> device.btmState.value = false);
              Future.delayed(const Duration(milliseconds: 200),(){
                device.btm1['name'] = '';
                device.btm2['name'] = '';
              });
              Get.back();
            }
          );
        }
      },
      child: Obx(()=> Container(
        height: 32.sp,
        margin: EdgeInsets.only(bottom: btmInx == 1? 8.sp : 0.sp),
        padding: EdgeInsets.symmetric(horizontal: 7.sp),
        decoration: BoxDecoration(
          color: _getColor(btmInx),
          borderRadius: BorderRadius.circular(8.r)
        ),
        child: Row(
          children: [
            Obx(()=> GoIcon(
              name: device.sourceInput.value == 'BT' || device.sourceInput.value == '' ? 
                const IconData(0xe669, fontFamily: 'IconFont') : const IconData(0xe65b, fontFamily: 'IconFont'),
              color: themeBase.blockColor.value,
              size: 20.sp,
            )),
            SizedBox(width: 6.sp),
            Flexible(
              child: Row(
                children: [
                  Obx(()=> AText(
                    text: _getName(btmInx),
                    color: themeBase.textColor1.value,
                    size: themeBase.subBodyFont2.value + 1.sp,
                    weight: FontWeight.bold,
                    overflow: TextOverflow.ellipsis,
                  )),
                  SizedBox(width: 10.sp),
                  Obx(() => Visibility(
                    visible: _getBtmms(btmInx)['codec'] != 'unknown' && _getBtmms(btmInx)['name'] != '' && _getBtmms(btmInx)['state'] == '3',
                    child: Container(
                      //连接后的编码器 ：codec
                      padding: EdgeInsets.symmetric(horizontal: 4.sp),
                      decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.sp),
                      color: themeBase.secondaryColor.value
                      ),
                      child: AText(
                        text: _getBtmms(btmInx)['codec'],
                        size: themeBase.subBodyFont2.value + 1.sp,
                        color: themeBase.textColor1.value,
                        spacing: 0.3.sp,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  )),
                  SizedBox(width: 6.sp),
                  Obx(() => Visibility(
                    visible: _getBtmms(btmInx)['state'] == '3' && _getBtmms(btmInx)['name'] != '',
                    child: Container(
                      //连接后的播放状态：播放动画
                      alignment: Alignment.bottomCenter,
                      padding: EdgeInsets.only(bottom: 6.sp),
                      margin: EdgeInsets.symmetric(horizontal: 3.sp),
                      //需要替换的地方：换成播放动画的图标或动画的操作
                      child: Transform.translate(
                        offset: Offset(0, -3.sp),
                        child: MiniMusicVisualizer(
                          color: themeBase.textColor1.value,
                          width: 2.5.sp,
                          height: 12.sp,
                          animate: true,
                        ),
                      ),
                    )
                  )),
                ],
              )
            ),
            Obx(()=> Visibility(
              //根据连接情况显示：未连接：不显示，已连接：显示
              visible: _getBtmms(btmInx)['name'] != '' , // device.name.value != '',
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 2.sp),
                child: GoIcon(
                  //需要在图标库中添加该图标：断开连接图标
                  name: const IconData(0xe905,fontFamily: 'IconFont'),
                  size: 16.sp,
                  color: themeBase.blockColor.value,
                ),
              )
            )),
          ],
        ),
      )),
    );
  }

  Map _getBtmms(btmInx) {
    final device = homCon.adevices[inx] as A31;
    if (btmInx == 1) {
      return device.btm1;
    } else {
      return device.btm2;
    }
  }

  String _getName(btmInx) {
    final device = homCon.adevices[inx] as A31;
    String name;
    if (btmInx == 1) {
      name = device.btm1['name'];
    } else {
      name = device.btm2['name'];
    }
    if (name != '') {
      return name;
    } else {
      if (device.btmState.value) {
        return 'Wating for connection...';
      } else {
        if (device.sourceInput.value == 'BT') return 'rx_title'.tr.replaceAll('%s', device.name.value);
        return 'tx_title'.tr;
      }
    }
  }

  Color _getColor(btmInx) {
    final device = homCon.adevices[inx] as A31;
    String name;
    if (btmInx == 1) {
      name = device.btm1['name'];
    } else {
      name = device.btm2['name'];
    }
    if (name == '') {
      return themeBase.primaryColor.value.withAlpha((255 * .4).round());
    } else {
      return themeBase.primaryColor.value;
    }
    
  }
}
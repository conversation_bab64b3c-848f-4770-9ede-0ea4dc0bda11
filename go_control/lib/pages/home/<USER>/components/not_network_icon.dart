import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/home/<USER>/components/auracast_pop.dart';
import 'package:gocontrol/theme/theme.dart';

class SetupNetBodyIcon extends StatelessWidget {
  const SetupNetBodyIcon(this.inx,{super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    DeviceBase device = homCon.adevices[inx];
    
    return Visibility(
      // 必须是wifi设备且处于ble模式下才显示
      visible: device is AbstractNET && device.origin == Origins.ble,
      child: Positioned(
        top: 14.sp,
        right: 12.sp,
        child: GoIcon(
          name: GoIcons.notNetwork,
          size: 21.sp,
          color: themeBase.textColor1.value,
        ),
      ),
    );
  }
}

class BleDeviceBodyIcon extends StatelessWidget {
  const BleDeviceBodyIcon(this.inx,{super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    if (homCon.adevices[inx] is B50) {
      B50 device = homCon.adevices[inx] as B50;
      Widget iconBody = Obx((){
        return Container(
          width: 32.sp,
          height: 32.sp,
          alignment: Alignment.topCenter,
          child: device.reconnecting.value? Loading(
            size: 16.sp,  
            color: Colors.white,
          ) : GoIcon(
            name: device.isBLEConnected.value? GoIcons.srcBT : GoIcons.noBle,
            size: device.isBLEConnected.value? 22.sp : 18.sp,
            color: themeBase.textColor1.value,
          ),
        );
      });
      if (device.deviceMode.value == 'Up2Cast' && device.isBLEConnected.value)  {
        return Positioned(
          right: 2.sp,
          child: Obx((){
            return GestureDetector(
              onTap: (){
                if (device.auracastType.value == 0) AuracastPop.show(device);
                if (device.auracastType.value == 2 || device.auracastType.value == 4) {
                  showMyBottomSheet(
                    height: 220.sp, 
                    title: 'Auracast Off', 
                    content: AText(
                      text: 'Is the Auracast transmitting status disabled?',
                      size: themeBase.subBodyFont.value + 2.sp,
                      color: themeBase.primaryColor.value,
                    ), 
                    showCancel: true.obs,
                    cancel: ()=> Get.back(),
                    confirm: () async {
                      await device.offAuracast();
                      Get.back();
                    }
                  );
                }
              },
              child: Container(
                width: 32.sp + 20.sp,
                height: 32.sp + 18.sp,
                padding: EdgeInsets.only(
                  left: 10.sp,
                  top: 14.sp,
                  right: 10.sp
                ),
                color: Colors.transparent,
                alignment: Alignment.topCenter,
                child: device.reconnecting.value? Loading(
                  size: 16.sp,  
                  color: Colors.white,
                ) : _getUpCastBody(device),
              ),
            );
          })
        );
      }
      return Positioned(
        top: 14.sp,
        right: 12.sp,
        child: iconBody,
      );
    }
    return Container(height: 0.sp);
  }
}

Widget _getUpCastBody(B50 device){
  if (device.auracastType.value == 2) {
    return GoIcon(
      name: GoIcons.leaudio,
      size: 18.sp,
      color: themeBase.textColor1.value,
    );
  }
  return GoIcon(
    name: device.isBLEConnected.value? GoIcons.add : GoIcons.noBle,
    size: device.isBLEConnected.value? 24.sp : 18.sp,
    color: themeBase.textColor1.value,
  );
}

class LibreDeviceBodyIcon extends StatelessWidget {
  const LibreDeviceBodyIcon(this.inx,{super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    if (homCon.adevices[inx] is! Libre) {
      return Container(height: 0.sp);
    } else {
      Libre device = homCon.adevices[inx] as Libre;
      return Obx(()=> Visibility(
        visible: !(device.updateState.value == 0 || device.updateState.value == 99),
        child: Positioned(
          top: 14.sp,
          right: 12.sp,
          child: GoIcon(
            name: GoIcons.deviceVersion,
            size: 24.sp,
            color: themeBase.textColor1.value,
          ),
        ),
      ));
    }
  }
}
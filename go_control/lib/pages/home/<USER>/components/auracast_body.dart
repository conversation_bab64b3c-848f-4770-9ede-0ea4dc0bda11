import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/new_slider.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class AuracastBodyAndChilds extends StatelessWidget {
  const AuracastBodyAndChilds(this.inx,{super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    DeviceBase dev = homCon.adevices[inx];
    if(dev is B50 && dev.deviceMode.value == 'Up2Cast') {
      Log.w(dev.name.value);
      if (dev.auracastType.value == 2 || dev.auracastType.value == 4) {
        return Obx(()=> ListView.builder(
          shrinkWrap: true,
          padding: EdgeInsets.all(0),
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            return Container(
              // height: 40.sp,
              // color: Colors.red,
              padding: EdgeInsets.symmetric(horizontal: 17.sp),
              margin: EdgeInsets.only(bottom: 8.sp),
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: AText(
                      text: dev.childForAuracast[index].name.value,
                      size: 15.sp,
                      color: themeBase.blockColor.value,
                      family: TextFamily.medium,
                    ),
                  ),
                  SizedBox(height: 6.sp),
                  Row(
                    children: [
                      SvgPicture.asset(
                        _getVolume(dev.childForAuracast[index]),
                        width: 21.sp,
                        height: 21.sp,
                        fit: BoxFit.cover,
                        colorFilter: ColorFilter.mode(
                          themeBase.textColor1.value,
                          BlendMode.modulate, // 混合模式，可以根据需要选择
                        ),
                      ),
                      Flexible(
                        child: SizedBox(
                          height: 24.sp,
                          child: _getSilider(dev.childForAuracast[index]),
                        ),
                      ),
                      GestureDetector(
                        onTap: (){
                          homCon.selectDevice = dev.childForAuracast[index];
                          var device = dev.childForAuracast[index];
                          if (!device.isBLEConnected.value) {
                            device.reconnectBLE();
                            homCon.adevInx.value = inx;
                            homCon.selectDevice = device;
                            return;
                          }
                          BaseAudio audioDevice = device as BaseAudio;
                          audioDevice.initAudioData();
                          Get.toNamed('/audioPage');
                        },
                        child: Container(
                          width: 36.sp,
                          height: 24.sp,
                          color: Colors.transparent,
                          // padding: EdgeInsets.only(right: 8.sp),
                          child: Transform.translate(
                            offset: Offset(2.sp , -0.5.sp),
                            child: GoIcon(
                              name: GoIcons.audioIcon,
                              size: 22.sp,
                              color: themeBase.textColor1.value,
                            ),
                          ),
                        ),
                      )
                    ],
                  )
                ],
              ),
            );
          },
          itemCount: dev.childForAuracast.length,
        ));
      }
    }
    return SizedBox();
  }
}

String _getVolume(DeviceBase device){
  if (device.volume.value < 30) {
    return 'images/volume3.svg';
  } else if (device.volume.value < 80 && device.volume.value > 29) {
    return 'images/volume2.svg';
  } else {
    return 'images/volume1.svg';
  }
}
Widget _getSilider(DeviceBase device){
  return Obx(() => SliderTheme(
    data: SliderThemeData(
      activeTrackColor: Colors.white,
      thumbColor: Colors.white,
      inactiveTrackColor: Colors.white.withOpacity(.4),
      thumbShape: RoundSliderThumbShape(
        enabledThumbRadius: 6.sp, // 设置thumb的半径
      ),
      overlayShape: RoundSliderOverlayShape(
        overlayRadius: 18.sp// 设置滑块覆盖层大小，
      ),
      trackHeight: 4.sp,
      trackShape: const NewSliderTrackShape()
    ),
    child: Slider(
      min: 0,
      max: 100,
      value: device.volume.value,
      onChanged: (v){
        device.volume.value = v;
      },
      onChangeEnd: (v){
        device.setVolume(v);
      },
    ),
  ));
}
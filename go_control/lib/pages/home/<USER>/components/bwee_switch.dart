import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:rk_get/rk_get.dart';

import '../../../../models/device/libre/libre.dart';
import '../../controller/home_control.dart';

class BweeSwitch extends StatelessWidget {
  const BweeSwitch(
    this.index, {
    super.key,
  });

  final int index;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final device = homCon.adevices[index];
      if (device is! Libre ||
          homCon.selectDevice != device ||
          !device.supportBweeRx.value) {
        return SizedBox.shrink();
      }
      return Positioned(
        top: 14.w,
        right: 12.w,
        child: OnTapAnimation(
          onTap: () {
            device.showBweeControl.value = !device.showBweeControl.value;
          },
          child: SvgAssetIcon(
            size: 21.w,
            path: !device.showBweeControl.value
                ? 'images/light.svg'
                : 'images/music.svg',
          ),
        )
      );
    });
  }
}

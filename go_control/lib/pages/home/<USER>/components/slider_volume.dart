import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/new_slider.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class SliderVolume extends StatelessWidget {
  const SliderVolume(this.inx,{
    super.key,
    this.b50page2 = false
  });

  final int inx;
  final bool b50page2;

  @override
  Widget build(BuildContext context) {
    DeviceBase device = homCon.adevices[inx];
    return Container(
      padding: EdgeInsets.only(
        left: b50page2? 0 : 17.sp,
        right: b50page2? 0 : 6.sp,
        bottom: 2.sp
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Obx(() => GestureDetector(
            onTap: (){
              if (device is Libre) {
                if (device.mute.value) {
                  device.setMute(false);
                } else {
                  device.setMute(true);
                }
              }
            },
            child: SvgPicture.asset(
              (device is Libre && device.mute.value)? 'images/volume0.svg' : _getVolume(device),
              width: 21.sp,
              height: 21.sp,
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(
                b50page2?themeBase.primaryColor.value:themeBase.textColor1.value,
                BlendMode.modulate, // 混合模式，可以根据需要选择
              ),
            ),
          )),
          Flexible(
            child: device is AbstractNET? _getNetSilider(device) : _getSilider(b50page2,inx),
          ),
          Obx(() => Visibility(
            visible: device is AbstractNET && device.roomChildren.isNotEmpty,
            child: GestureDetector(
              onTap: (){    
                AbstractNET dev = device as AbstractNET;
                dev.showAllVolume.value = !device.showAllVolume.value;
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 14.sp,
                  vertical: 4.sp
                ),
                margin: EdgeInsets.symmetric(horizontal: 6.sp),
                decoration: BoxDecoration(
                  color: themeBase.textColor1.value.withOpacity(.4),
                  borderRadius: BorderRadius.circular(14.r)
                ),
                child: Obx(()=> Transform.rotate(
                  angle: device is AbstractNET && device.showAllVolume.value? 180 * (pi / 180) : 0,
                  child: Transform.translate(
                    offset: Offset(0,device is AbstractNET && device.showAllVolume.value? 0 : 1.sp),
                    child: GoIcon(
                      name: GoIcons.down,
                      size: 13.sp,
                      color: themeBase.textColor1.value,
                    ),
                  ),
                )),
              ),
            ),
          )),
          Obx(()=> Visibility(
            visible: _showAudioIcon(device),
            child: GestureDetector(
              onTap: (){
                homCon.selectDevice = device;
                if(device is BaseAudio) {
                  if (device is B50 && !device.isBLEConnected.value) {
                    device.reconnectBLE();
                    homCon.adevInx.value = inx;
                    homCon.selectDevice = device;
                    return;
                  }
                  BaseAudio audioDevice = device as BaseAudio;
                  audioDevice.initAudioData();
                }
                Get.toNamed('/audioPage');
              },
              child: Container(
                width: 36.sp,
                height: 45.sp,
                color: Colors.transparent,
                padding: EdgeInsets.only(right: 8.sp),
                child: Transform.translate(
                  offset: Offset(-1.sp , -0.5.sp),
                  child: GoIcon(
                    name: GoIcons.audioIcon,
                    size: 22.sp,
                    color: themeBase.textColor1.value,
                  ),
                ),
              ),
            ),
          )),
          Visibility(
            visible: b50page2 || (device is AbstractNET && device.origin == Origins.ble && device.write == null) || device is Libre,
            child: Container(
              width: 45.sp,
              height: 45.sp,
              alignment: Alignment.center,
              child: Transform.translate(
                offset: Offset(0, -2.sp),
                child: Obx(() => AText(
                  text: '${device.volume.value.toStringAsFixed(0)}%',
                  color: b50page2? themeBase.primaryColor.value:Colors.white,
                  size: themeBase.subBodyFont.value ,
                  // location 
                  family: TextFamily.medium,
                )),
              )
            ),
          ),
        ],
      ),
    );
  }

  String _getVolume(DeviceBase device){
    if(device is AbstractNET && device.roomChildren.isNotEmpty){
      if(device.allVolume.value < 30){
        return 'images/volume3.svg';
      }else if(device.allVolume.value < 80 && device.allVolume.value > 29){
        return 'images/volume2.svg';
      }else{
        return 'images/volume1.svg';
      }
    }else{
      if(device.volume.value < 30){
        return 'images/volume3.svg';
      }else if(device.volume.value < 80 && device.volume.value > 29){
        return 'images/volume2.svg';
      }else{
        return 'images/volume1.svg';
      }
    }
  }
}


bool _showAudioIcon(DeviceBase device){
  if (device is Libre ) return false;
  if(device is AbstractNET && device.origin == Origins.ble && device.write == null) return false;
  if(device is AbstractNET && device.roomChildren.isEmpty || device is AbstractBLE && device.origin == Origins.ble){
    return true;
  }else{
    return false;
  }
}


// 音量显示 
Widget _getSilider(b50page2,inx){
  return Obx(() => SliderTheme(
    data: SliderThemeData(
      activeTrackColor: b50page2? themeBase.primaryColor.value:Colors.white,
      thumbColor: b50page2 ?themeBase.primaryColor.value:Colors.white,
      inactiveTrackColor: b50page2? themeBase.primaryColor.value.withOpacity(.4) : Colors.white.withOpacity(.4),
      thumbShape: RoundSliderThumbShape(
        enabledThumbRadius: 6.sp, // 设置thumb的半径
      ),
      overlayShape: RoundSliderOverlayShape(
        overlayRadius: 18.sp// 设置滑块覆盖层大小，
      ),
      trackHeight: 4.sp,
      trackShape: const NewSliderTrackShape()
    ),
    child: Slider(
      min: 0,
      max: 100,
      value: homCon.adevices[inx].volume.value,
      onChanged: (v){
        homCon.adevices[inx].volume.value = v;
      },
      onChangeEnd: (v){
        homCon.adevices[inx].setVolume(v);
      },
    ),
  ));
}

Widget _getNetSilider(AbstractNET device){
  return Obx(() => SliderTheme(
    data: SliderThemeData(
      activeTrackColor: themeBase.textColor1.value,
      thumbColor: themeBase.textColor1.value,
      inactiveTrackColor: themeBase.textColor1.value.withOpacity(.4),
      thumbShape: RoundSliderThumbShape(
        enabledThumbRadius: 6.sp, // 设置thumb的半径
      ),
      overlayShape: RoundSliderOverlayShape(
        overlayRadius: 18.sp// 设置滑块覆盖层大小，
      ),
      trackHeight: 4.sp,
      trackShape: const NewSliderTrackShape(),
    ),
    child: Slider(
      min: 0,
      max: 100,
      value: device.roomChildren.isNotEmpty?device.allVolume.value:device.volume.value,
      onChanged: (v){
        if(device.roomChildren.isNotEmpty){
          device.allVolume.value = v;
        }else{
          device.volume.value = v;
        }
      },
      onChangeEnd: (v){
        if (device.roomChildren.isNotEmpty) {
          device.setAllVolume(v);
          device.lastAllVolume.value = device.allVolume.value;
        } else {
          device.setVolume(v);
        }
      },
    ),
  ));
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/theme/theme.dart';

class PlayerHL extends StatelessWidget {
  const PlayerHL({
    super.key,
    required this.device,
    this.ble = false
  });

  final AbstractBLE device;
  final bool ble;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        top: 3.sp,
        bottom: 5.sp,
        right: 18.sp
      ),
      padding: EdgeInsets.symmetric(horizontal: 23.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TapIcon(
            child: GoIcon(
              color: ble? themeBase.primaryColor.value : themeBase.textColor1.value,
              name: GoIcons.prevSong,
              size: 31.sp,
            ),
            onTap: (){
              Log.d('上一曲');
              device.prevPlay();
            },
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 0.sp),
            child: TapIcon(
              child: GoIcon(
                name: GoIcons.playPause,
                color: ble? themeBase.primaryColor.value : themeBase.textColor1.value,
                size: 55.sp,
              ),
              onTap: (){
                // Log.d('播放or暂停');
                // device.sendMsgToBLE(msg: 'ASN;');
                device.playAndPauseSong();
                // if (device is B50) {
                // (device as B50).toAuraCastSave();
                // }
                // 722512
              },
            ),
          ),
          TapIcon(
            child: GoIcon(
              color: ble? themeBase.primaryColor.value : themeBase.textColor1.value,
              name: GoIcons.nextSong,
              size: 31.sp,
            ),
            onTap: (){
              device.nextPlay();
            },
          )
        ],
      ),
    );
  }
}

class TapIcon extends StatelessWidget {
  const TapIcon({
    super.key,
    required this.child,
    required this.onTap
  });

  final Function onTap;
  final Widget child;

  @override
  Widget build(BuildContext context) {

    final RxBool ta = false.obs;

    return GestureDetector(
      onTap: (){
        onTap();
        ta.value = true;
        Future.delayed(const Duration(milliseconds: 75),(){
          ta.value = false;
        });
      },
      onTapDown: (v){
        ta.value = true;
      },
      onTapUp: (v){
        ta.value = false;
      },
      onTapCancel: (){
        ta.value = false;
      },
      child: Obx(()=> AnimatedOpacity(
        opacity: ta.value?0.8:1,
        duration: const Duration(milliseconds: 150),
        child: AnimatedScale(
          scale: ta.value?0.95:1,
          duration: const Duration(milliseconds: 150),
          child: child
        ),
      )),
    );
  }
}
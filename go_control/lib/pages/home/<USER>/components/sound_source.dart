import 'dart:async';
import 'dart:ui';
import 'package:bwee_mod/bwee_mod.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class SoundSource extends StatelessWidget {
  const SoundSource(this.inx, {super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    final device = homCon.adevices[inx];
    if (device is AbstractNET &&
        device.write == null &&
        device.origin == Origins.ble) {
      return SizedBox.shrink();
    }

    final height = 50.sp + 24.sp;

    return Obx(() {
      if (device is Libre && device.sourceInputList.isEmpty) return Container();
      if (device is B50 && !device.isBLEConnected.value) return Container();
      return AnimatedContainer(
        alignment: Alignment.topLeft,
        duration: const Duration(milliseconds: 160),
        curve: Curves.linear,
        constraints: BoxConstraints(
          maxHeight: homCon.adevInx.value == inx ? height : 0,
        ),
        margin: EdgeInsets.only(
            left: 16.sp,
            top: homCon.adevInx.value == inx ? 3.sp : 0.sp,
            bottom: homCon.adevInx.value == inx ? 8.sp : 0.sp),
        child: SingleChildScrollView(
            physics: const NeverScrollableScrollPhysics(),
            child: AnimatedScale(
              duration: const Duration(milliseconds: 220),
              curve: Curves.linear,
              scale: homCon.adevInx.value == inx ? 1 : .8,
              child: Container(
                  alignment: Alignment.centerLeft,
                  height: height,
                  child: ScrollConfiguration(
                      behavior: ScrollConfiguration.of(context).copyWith(
                          scrollbars: false, //必须设置此事件，不然无法滚动
                          dragDevices: {
                            PointerDeviceKind.touch,
                            PointerDeviceKind.mouse,
                          }),
                      child: Obx(() {
                        if (device is! Libre) {
                          return _getAllSource(inx);
                        }
                        if (!device.showBweeControl.value) {
                          return _getAllSource(inx);
                        } else {
                          return BweeControl(deviceIP: device.ip.value);
                        }
                      }))),
            )),
      );
    });
  }
}

Widget _getAllSource(inx) {
  DeviceBase device = homCon.adevices[inx];
  RxList<Map> list;
  if (device is AbstractNET) {
    if (device.sourceInputList.isEmpty &&
        device.plmSourceInputList.isNotEmpty) {
      list = device.plmSourceInputList;
    } else {
      list = device.sourceInputList;
    }
  } else {
    list = device.sourceInputList;
  }
  return AnimatedSwitcher(
    duration: const Duration(milliseconds: 220),
    child: list.isEmpty
        ? Center(
            child: LoadingAnimationWidget.staggeredDotsWave(
                color: themeBase.textColor1.value.toOpacity(.8), size: 25.w),
          )
        : ListView.builder(
            itemCount: list.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              Map src = list[index];
              return Sourcehll(
                inx: index,
                index: inx,
                source: src,
              );
            },
          ),
  );
}

class Sourcehll extends StatelessWidget {
  const Sourcehll(
      {super.key,
      required this.inx,
      required this.index,
      required this.source});

  final int inx;
  final int index;
  final Map source;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _changeSource(source, index),
      child: Obx(() => Container(
            margin: EdgeInsets.only(right: 13.sp),
            child: Column(
              children: [
                AnimatedContainer(
                  width: 50.sp,
                  height: 50.sp,
                  margin: EdgeInsets.only(bottom: 5.sp),
                  duration: Duration(
                    milliseconds:
                        homCon.adevices[index].sourceInput.value == source['id']
                            ? 120
                            : 0,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color:
                        homCon.adevices[index].sourceInput.value == source['id']
                            ? Colors.white.toOpacity(0.3)
                            : const Color.fromARGB(255, 22, 22, 22)
                                .toOpacity(0.2),
                  ),
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: homCon.adevices[index].newSourceInput.value ==
                                source['id'] &&
                            homCon.adevices[index].swingSRC.value
                        ? Transform.translate(
                            offset: Offset(0, 7.sp),
                            child: Loading(
                              size: 18.w,
                              color: themeBase.textColor1.value,
                            ),
                          )
                        : _getIcon(homCon.adevices[index], source),
                  ),
                ),
                _getName(homCon.adevices[index], source),
              ],
            ),
          )),
    );
  }
}

void _changeSource(Map source, int ffinx) {
  // 表示需要进入网络音源界面
  if (source['id'] == 'NET' &&
      source['id'] == homCon.adevices[ffinx].sourceInput.value) {
    homCon.selectDevice = homCon.adevices[ffinx];
    Get.toNamed('/radioPage');
  }
  if (source['id'] == homCon.adevices[ffinx].sourceInput.value) return;
  homCon.adevices[ffinx].swingSRC.value = true;
  homCon.adevices[ffinx].newSourceInput.value = source['id'];
  homCon.adevices[ffinx].setSourceInput(source['id']);
  Timer(const Duration(seconds: 4), () {
    if (homCon.adevices[ffinx].swingSRC.value) {
      homCon.adevices[ffinx].swingSRC.value = false;
      homCon.adevices[ffinx].newSourceInput.value = '';
    }
  });
}

Widget _getIcon(device, src) {
  if (device is AbstractNET &&
      device.project.contains('A100') &&
      src['id'] == 'USBDAC') {
    return Icon(
      GoIcons.srcUSB,
      size: 24.sp,
      color: device.sourceInput.value == src['id']
          ? themeBase.textColor1.value
          : themeBase.textColor1.value.toOpacity(.5),
    );
  } else if (src['id'] == 'NET') {
    return Transform.translate(
        offset: Offset(0.sp, 1.5.sp),
        child: Obx(() => Icon(
              src['icon'],
              size: 22.sp,
              color: device.sourceInput.value == src['id']
                  ? themeBase.textColor1.value
                  : themeBase.textColor1.value.toOpacity(.5),
            )));
  } else if (src['id'] == 'AURA') {
    return Transform.translate(
        offset: Offset(2.5.sp, 0),
        child: Obx(() => Icon(
              src['icon'],
              size: 22.sp,
              color: device.sourceInput.value == src['id']
                  ? themeBase.textColor1.value
                  : themeBase.textColor1.value.toOpacity(.5),
            )));
  } else {
    return Obx(() => Icon(
          src['icon'],
          size: 24.sp,
          color: device.sourceInput.value == src['id']
              ? themeBase.textColor1.value
              : themeBase.textColor1.value.toOpacity(.5),
        ));
  }
}

Widget _getName(device, src) {
  if (device is AbstractNET &&
      device.project.contains('A100') &&
      src['id'] == 'USBDAC') {
    return Obx(() => Text(
          'USBPLAY',
          style: TextStyle(
            fontFamily: 'Medium',
            fontSize: 11.sp,
            color: device.sourceInput.value == src['id']
                ? themeBase.textColor1.value
                : themeBase.textColor1.value.toOpacity(.5),
          ),
        ));
  } else if (src['id'] == 'NET') {
    return Obx(() => Text(
          'Music',
          style: TextStyle(
            fontFamily: 'Medium',
            fontSize: 11.sp,
            color: device.sourceInput.value == src['id']
                ? themeBase.textColor1.value
                : themeBase.textColor1.value.toOpacity(.5),
          ),
        ));
  } else {
    return Obx(() => Text(
          src['name'],
          style: TextStyle(
            fontFamily: 'Medium',
            fontSize: 11.sp,
            color: device.sourceInput.value == src['id']
                ? themeBase.textColor1.value
                : themeBase.textColor1.value.toOpacity(.5),
          ),
        ));
  }
}

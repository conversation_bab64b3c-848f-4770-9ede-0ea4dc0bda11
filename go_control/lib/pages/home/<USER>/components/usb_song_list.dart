import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a4_usblist.dart';
import 'package:gocontrol/pages/AppSetting/scroll_control.dart';
import 'package:gocontrol/theme/theme.dart';

import '../../components/mmvlizer.dart';

class UsbSongList extends StatelessWidget {
  const UsbSongList({
    super.key,
    required this.device,
    this.b50page = false
  });
  
  final A4Usblist device;
  final bool b50page;

  @override
  Widget build(BuildContext context) {
    final scc = Get.put(ScrollViewControl());

    final DeviceBase theDevBase = device as DeviceBase;

    try {
      Future.delayed(const Duration(milliseconds: 0),(){
        if (device.dataList.length >= device.usbPlayIndex.value && device.usbPlayIndex.value > 5) {
          final index = device.usbPlayIndex.value - 1;
          if (index + 5 < device.dataList.length) {
            scc.scrollController.jumpTo(index * 46.sp);
          } else {
            scc.scrollController.jumpTo((index - 5) * 44.sp);
          }
        }
      });
    } catch(_) {}

    Future.delayed(const Duration(seconds: 1),(){
      if (device.dataTot.value != 0 && device.dataList.isEmpty) {
        device.getSongListForUSB();
      }
    });

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.sp),
        color: themeBase.blockColor.value,
      ),
      padding: EdgeInsets.all(18.sp),
      margin: EdgeInsets.only(bottom: 16.sp),
      height: b50page? 300.sp : Get.height - 200.sp,
      child: Column( 
        children: [
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(bottom: 8.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AText(
                  text: 'usb_list'.tr,
                  color: themeBase.primaryColor.value,
                  size: 16.sp,
                ),
                Obx(()=> Visibility(
                  visible: b50page && theDevBase is B50 && theDevBase.sourceInput.value == 'USBPLAY',
                  child: OnTapScaleToSmallBox(
                    onTap: () {
                      if (theDevBase is B50) theDevBase.randomPlay();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.all(6.sp),
                      child: GoIcon(
                        name: _getUsbPlayLoop(theDevBase),
                        color: themeBase.primaryColor.value,
                        size: 19.sp,
                      ),
                    ),
                  ),
                ))
              ],
            )
          ),
          Flexible(
            child: Obx(()=> 
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: device.dataList.isEmpty ?
                  const Center(child: Loading()) : ListView.builder(
                  padding: const EdgeInsets.all(0),
                  controller: scc.scrollController,
                  itemBuilder:(context, index) {
                    final item = device.dataList[index];
                    if (device is Libre) {
                      final libre = device as Libre;
                      if (item['name'].value == '') {
                        libre.sendMsgToTcpSocket('DSK:LST:${libre.dataList[index]['id']};');
                      }
                    }
                    return Column(
                      children: [
                        Obx(() => Visibility(
                          visible: _getRepat(device.dataList[index],device) && !(scc.fetching.value && (index + 1) > device.dataKey.value && device.dataKey.value > 8),
                          child: OnTapScaleToSmallBox(
                            onTap: () {
                              if (device is AbstractNET) {
                                final net = device as AbstractNET;
                                if (net.project.contains('A100')) {
                                  if (item['name'].value == '') net.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:LST:${device.dataList[index]['id']}&');
                                  net.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:SEL:${device.dataList[index]['id']}&');
                                  Future.delayed(const Duration(milliseconds: 200),()=> net.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:CUR&'));
                                }
                              } else if (device is Libre) {
                                final libre = device as Libre;
                                if (item['name'].value == '') {
                                  libre.sendMsgToTcpSocket('DSK:LST:${libre.dataList[index]['id']};');
                                } 
                                libre.sendMsgToTcpSocket('DSK:SEL:${libre.dataList[index]['id']};');
                                Future.delayed(const Duration(milliseconds: 200),()=> libre.sendMsgToTcpSocket('DSK:CUR;'));
                              } else if (device is AbstractBLE && device is! AbstractNET) {
                                final ble = device as AbstractBLE;
                                if (item['name'].value == '') ble.sendMsgToBLE(msg: 'DSK:LST:${ble.dataList[index]['id']};');
                                ble.sendMsgToBLE(msg: 'DSK:SEL:${ble.dataList[index]['id']};');
                                Future.delayed(const Duration(milliseconds: 200),()=> ble.sendMsgToBLE(msg: 'DSK:CUR;'));
                              }
                              device.usbPlayIndex.value = index + 1;
                            },
                            child: Obx(() => Container(
                              width: double.maxFinite,
                              height: 40.sp,
                              alignment: Alignment.centerLeft,
                              padding: EdgeInsets.symmetric(horizontal: 12.sp),
                              margin: EdgeInsets.only(
                                bottom: index != device.dataList.length?6.sp:0.sp,
                              ),
                              decoration: BoxDecoration(
                                color: device.usbPlayIndex.value == device.dataList[index]['id'] + 1?
                                  themeBase.primaryColor.value:themeBase.primaryColor.value.withOpacity(.5),
                                borderRadius: BorderRadius.circular(12.sp)
                              ),
                              child: Flex(
                                direction: Axis.horizontal,
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: Obx(()=>AText(
                                      text: '${device.dataList[index]['id'] + 1}. ${device.dataList[index]['name'].value == ''? '...' : device.dataList[index]['name'].value}',
                                      color: themeBase.textColor1.value,
                                      size: 14.sp,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.left,
                                    )),
                                  ),
                                  Obx(() => Visibility(
                                    visible: device.usbPlayIndex.value == device.dataList[index]['id'] + 1,
                                    child: Flexible(
                                      child: Container(
                                        width: 20.sp,
                                        alignment: Alignment.bottomCenter,
                                        // color: Colors.red,
                                        child: Transform.translate(
                                          offset: Offset(4.sp, -15.sp),
                                          child: MiniMusicVisualizer(
                                            color: themeBase.textColor1.value,
                                            width: 2.5.sp,
                                            height: 10.sp,
                                            animate: true,
                                          ),
                                        )
                                      )
                                    )
                                  ))
                                ],
                              )
                            ))
                          ),
                        )),
                        Obx(()=> Visibility(
                          visible: scc.fetching.value && device.dataKey.value > 8 && (index + 1) == device.dataKey.value && device.dataTot.value != device.dataKey.value,
                          child: Container(
                            margin: EdgeInsets.only(top: 12.sp),
                            alignment: Alignment.center,
                            child: const Loading(),
                          ),
                        ))
                      ],
                    );
                  },
                  itemCount: device.dataList.length,
                ),
              )
            )
          )
        ],
      )
    );
  }
}

bool _getRepat(Map item,device){
  bool flag = false;
  for (Map el in device.dataList) {
    if(el['id'] == item['id']) flag = true;
  }
  return flag;
}

IconData _getUsbPlayLoop(device){
  if (device is! B50) return GoIcons.loopList;
  switch(device.usbPlayIndexForRandom.value){
    case 0:
      return GoIcons.loopList;
    case 1:
      return GoIcons.loopRandom;
    case 2:
      return GoIcons.loopSingle;
    default:
      return GoIcons.loopList;
  }
}
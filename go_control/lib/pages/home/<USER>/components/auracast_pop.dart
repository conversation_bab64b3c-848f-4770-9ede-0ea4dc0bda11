import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class AuracastPop {

  static RxBool loading = false.obs;
  static RxInt castStep = 0.obs;

  static show(B50 b50){
    if (homCon.masterId != '') return;
    castStep.value = 0;
    Get.bottomSheet(
      SizedBox(
        height: 355.h,
        child: Column(
          children: [
            Container(
              alignment: Alignment.center,
              margin: EdgeInsets.only(
                top: 22.sp,
                bottom: 22.sp
              ),
              child: AText(
                text: 'Auracast mode'.tr,
                family: TextFamily.bold,
                size: themeBase.subHeadingFont.value,
                color: themeBase.primaryColor.value,
              ),  
            ),
            Flexible(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 22.sp),
                child: Obx(()=> castBody(b50))
              ),
            )
          ],
        ),
      ),
      backgroundColor: themeBase.blockColor.value
    );
  }

  static Widget castBody(B50 b50){
    if (castStep.value == 0) return castStep0(b50);
    if (castStep.value == 1 || castStep.value == 2) return castStep1and2(b50);
    // if (castStep.value == 0) return castStep2(b50);
    return Container();
  }

  static Widget castStep0(B50 b50){
    return Column(
      children: [
        OnTapScaleToSmallBox(
          onTap: (){
            castStep.value = 1;
          },
          child: Container(
            width: double.maxFinite,
            height: 44.sp,
            margin: EdgeInsets.only(bottom: 12.sp),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: themeBase.secondaryColor.value,
              borderRadius: BorderRadius.circular(10.r)
            ),
            padding: EdgeInsets.symmetric(horizontal: 12.sp),
            child: AText(
              text: 'Enable Auracast to public'.tr,
              color: themeBase.textColor1.value,
              size: themeBase.subBodyFont.value + 1.sp,
            ),
          ),
        ),
        OnTapScaleToSmallBox(
          onTap: (){
            castStep.value = 2;
          },
          child: Container(
            width: double.maxFinite,
            height: 44.sp,
            margin: EdgeInsets.only(bottom: 12.sp),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: themeBase.secondaryColor.value,
              borderRadius: BorderRadius.circular(10.r)
            ),
            padding: EdgeInsets.symmetric(horizontal: 12.sp),
            child: AText(
              text: 'Enable Auracast to Private'.tr,
              color: themeBase.textColor1.value,
              size: themeBase.subBodyFont.value + 1.sp,
            ),
          ),
        ),
      ],
    );
  }

  static Widget castStep1and2(B50 b50) {
    List<B50> castList = [];
    RxList<B50> selectCastList = RxList<B50>([]);
    castList.add(b50);
    for (var el in homCon.adevices) {
      if (el is B50 && el != b50) {
        if (el.deviceMode.value != 'Up2Cast') continue;
        Log.y('添加到castList ${el.name.value}');
        castList.add(el);
      }
    }
    return Column(
      children: [
        Flexible(
          child: ListView.builder(
            itemBuilder: (context, index) {
              var self = castList[index];
              return GestureDetector(
                onTap: (){
                  if (selectCastList.contains(self)) {
                    selectCastList.remove(self);
                  } else {
                    selectCastList.add(self);
                  }
                },
                child: Container(
                  height: 44.sp,
                  margin: EdgeInsets.only(bottom: 3.sp),
                  decoration: BoxDecoration(
                    border: index + 1 != castList.length? Border(
                      bottom: BorderSide(
                        width: 0.1.sp,
                        color: themeBase.primaryColor.value.withOpacity(.7)
                      )
                    ) : null
                  ),
                  child: Row(
                    children: [
                      Flexible(
                        flex: 1,
                        child: Container(
                          alignment: Alignment.centerLeft,
                          child: AText(
                            text: castList[index].name.value,
                            family: TextFamily.medium,
                            size: themeBase.subBodyFont.value + 1.sp,
                            color: themeBase.primaryColor.value,
                          ),
                        ),
                      ),
                      SizedBox(width: 18.sp),
                      Visibility(
                        visible: index != 0,
                        child: Obx(() => Container(
                          width: 21.sp,
                          height: 21.sp,
                          // color: Colors,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.r),
                            border: Border.all(
                              color: themeBase.primaryColor.value.withOpacity(index==0?0.5:1),
                              width: 1.5.sp
                            )
                          ),
                          child: Obx(()=> Visibility(
                            visible: selectCastList.contains(self),
                            child: GoIcon(
                              name: GoIcons.tick,
                              size: 18.sp,
                              color: themeBase.primaryColor.value.withOpacity(index == 0? 0.5 : 1),
                            ),
                          )),
                        )),
                      )
                    ],
                  ),
                ),
              );
            },
            itemCount: castList.length,
          ),
        ),
        SizedBox(height: 22.sp,),
        OnTapScaleToSmallBox(
          onTap: () async {
            // 提交
            loading.value = true;
            Log.d(selectCastList.map((e) => e.name.value).toList());
            if (castStep.value == 1) {
              await b50.auracastAddToPublic(selectCastList);
            } else {
              await b50.auracastAddToPrivate(selectCastList);
            }
            loading.value = false;
            Get.back();
          },
          child: Container(
            width: double.maxFinite,
            height: 44.sp,
            margin: EdgeInsets.only(bottom: 22.sp),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: themeBase.primaryColor.value,
              borderRadius: BorderRadius.circular(10.r)
            ),
            child: Obx((){
              return loading.value? Container(
                width: 44.sp,
                height: 44.sp,
                padding: EdgeInsets.all(12.sp),
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(themeBase.blockColor.value),
                  strokeWidth: 2.6.sp,
                ),
              ) : AText(
                text: 'Confirm'.tr,
                color: themeBase.textColor1.value,
                size: themeBase.subBodyFont.value + 1.sp,
              );
            }),
          ),
        )
      ],
    );
  }

}
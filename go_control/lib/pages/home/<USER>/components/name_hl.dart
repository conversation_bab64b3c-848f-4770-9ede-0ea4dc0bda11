import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart'; 
import 'package:gocontrol/theme/theme.dart';

class NameHL extends StatelessWidget {
  const NameHL(this.inx,{super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.topLeft,
      padding: EdgeInsets.only(
        // left: 16.sp,
        top: 11.sp,
        bottom: 11.sp
      ),
      child: Obx(() => AText(
         text: homCon.adevices[inx].name.value,
         color: themeBase.textColor1.value,
         size: themeBase.bodyFont.value,
       )),
    );
  }
}
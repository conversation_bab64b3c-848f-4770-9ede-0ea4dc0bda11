import "package:flutter/material.dart";
import 'dart:math';

class MiniMusicVisualizer extends StatelessWidget {
  /// 音乐可视化迷你组件
  /// 
  /// [color] 条形颜色（默认使用主题色）
  /// [width] 单个条形宽度（默认10.w）
  /// [height] 组件总高度（默认自动适应）
  /// [radius] 条形圆角半径（默认0）
  /// [animate] 是否启用动画（默认false）
  /// [shadows] 条形阴影效果（可选）
  const MiniMusicVisualizer({
    super.key,
    this.color,
    this.width,
    this.height,
    this.radius = 0,
    this.animate = true,
    this.shadows,
  });

  final Color? color;  

  final double? width;

  final double? height;

  final bool animate;
  final double radius;
  final List<BoxShadow>? shadows;

  @override
  Widget build(BuildContext context) {
    final baseDuration = 900;
    final List<int> duration = List.generate(4, (i) => baseDuration - i * 100);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        for (int i = 0; i < 4; i++)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 1),
            child: VisualComponent(
              curve: Curves.bounceOut,
              duration: duration[i % duration.length],
              color: color ?? Theme.of(context).colorScheme.primary,
              width: width ?? 2.5,
              height: height,
              radius: radius,
              shadows: shadows,
              animate: animate,
            ),
        )
      ]
    );
  }
}

class VisualComponent extends StatefulWidget {
  /// 可视化动画组件
  ///
  /// [duration] 动画周期（毫秒）
  /// [color] 条形颜色
  /// [curve] 动画曲线
  /// [width] 条形宽度（默认4）
  /// [height] 条形最大高度（默认15）
  /// [radius] 条形圆角半径
  /// [shadows] 阴影效果
  /// [animate] 是否启用动画
  const VisualComponent({
    super.key,
    required this.duration,
    required this.color,
    required this.curve,
    this.width,
    this.height,
    this.radius = 0,
    this.shadows,
    this.animate = false,
  });

  final int duration;
  final Color color;
  final Curve curve;
  final double? width;
  final double? height;
  final double radius;
  final List<BoxShadow>? shadows;
  final bool animate;

  @override
  VisualComponentState createState() => VisualComponentState();
}

class VisualComponentState extends State<VisualComponent>
    with SingleTickerProviderStateMixin {
  late Animation<double> animation;
  late AnimationController animationController;
  late double width;
  late double radius;
  late double height;
  late List<BoxShadow>? shadows;

  @override
  void initState() {
    super.initState();
    width = widget.width ?? 4;
    height = widget.height ?? 15;
    radius = min(widget.radius, height / 2);
    shadows = widget.shadows;
    addAnimate();
    if (widget.animate) {
      start();
    } else {
      pause();
    }
  }

  @override
  void didUpdateWidget(VisualComponent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.animate != widget.animate) {
      if (widget.animate) {
        start();
      } else {
        pause();
      }
    }
  }

  void addAnimate() {
    animationController = AnimationController(
        duration: Duration(milliseconds: widget.duration), vsync: this);
    final curvedAnimation =
        CurvedAnimation(parent: animationController, curve: widget.curve);
    animation = Tween<double>(begin: 2, end: height).animate(curvedAnimation)
      ..addListener(() {
        update();
      });
  }

  void start() {
    animationController.repeat(reverse: true);
  }

  void pause() {
    animationController.stop();
    animationController.reverse();
  }

  void update() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (mounted) setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          height: animation.value,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(min(radius, animation.value / 2)),
            color: widget.color,
            boxShadow: shadows,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    animation.removeListener(update);
    animationController
      ..stop()
      ..dispose();
    super.dispose();
  }
}
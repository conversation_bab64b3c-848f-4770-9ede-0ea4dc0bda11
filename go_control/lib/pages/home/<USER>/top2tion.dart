import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class Top2tion extends StatelessWidget {
  const Top2tion({
    super.key,
    required this.title,
    this.settings = false,
    this.net = false,
    this.ehBack = false,
    this.customIcon,
    this.onTap,
  });

  final String title; // 标题
  final bool settings; // 是否显示右侧设置图标
  final bool net;  // 网络设备
  final Function? onTap;
  final bool ehBack;
  final Widget? customIcon;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      child: Container(
        height: themeBase.topBarHeight + themeBase.searHeight.value,
        padding: EdgeInsets.only(top: themeBase.searHeight.value),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AbsorbPointer(
              absorbing: ehBack,
              child: GestureDetector(
                onTap: (){
                  if(onTap != null){
                    onTap!();
                    return;
                  }
                  Get.back();
                },
                child: Container(
                  color: Colors.transparent,
                  width: themeBase.topBarHeight,
                  height: themeBase.topBarHeight,
                  padding: EdgeInsets.all(12.sp),
                  child: Transform.translate(
                    offset: Offset(-(themeBase.topBarHeight/2) + 19.sp + 6.sp,0),
                    child: ehBack? null : GoIcon(
                      name: GoIcons.back,
                      color: net? themeBase.textColor1.value : themeBase.primaryColor.value,
                      size: 17.sp,
                    ),
                  ),
                ),
              ),
            ),
            // 设备名
            Flexible(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.sp),
                child: Obx(()=> AText(
                  text: title,
                  size: themeBase.bodyFont.value + 2.sp,
                  color: net? themeBase.textColor1.value : themeBase.primaryColor.value,
                  family: TextFamily.bold,
                  textAlign: TextAlign.center,
                  softWrap: false,
                  overflow: TextOverflow.ellipsis,
                )),
              ),
            ),
            // Visibility(
            //   visible: rightIconChild == null,
            //   child: OnTapScaleToSmallBox(
            //     onTap: () => rightIconTap?.call(),
            //     child: () {
            //       if (rightIconChild != null) {
            //         return Container(
            //           width: themeBase.topBarHeight,
            //           height: themeBase.topBarHeight,
            //           color: Colors.transparent,
            //           child: GoIcon(
            //             name: GoIcons.refresh,
            //             color: themeBase.primaryColor.value,
            //           ),
            //         );
            //       } else {
            //         return Container(); 
            //       }
            //     } (),
            //   ),
            // ),
            // 设置图标
            customIcon ?? GestureDetector(
              onTap: () {
                if (settings) {
                  if (homCon.selectDevice is B50) {
                    Get.toNamed('/b50setinfo');
                  } else if (homCon.selectDevice is Libre) {
                    Libre libre = homCon.selectDevice! as Libre;
                    libre.getInChangeNameOff();
                    Get.toNamed('/libreSetInfo');
                  } else if (homCon.selectDevice is A31){
                    Get.toNamed('/setinfo');
                  }
                }
              },
              child: Container(
                color: Colors.transparent,
                width: themeBase.topBarHeight,
                height: themeBase.topBarHeight,
                child: Transform.translate(
                  offset: Offset(-(themeBase.topBarHeight/2) + ((28.sp*0.5) + 3.sp) + 14.sp,-1.sp),
                  child: settings? GoIcon(
                    name: GoIcons.devSetting,
                    color: net? themeBase.textColor1.value : themeBase.primaryColor.value,
                    size: 25.sp,
                  ) : Container(),
                ),
              ),
            )
          ],
        )
      ),
    );
  }
}
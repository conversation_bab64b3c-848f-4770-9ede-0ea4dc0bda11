// ignore: unused_import
import 'package:gocontrol/log.dart';

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/theme/theme.dart';

class LoadingBox extends StatelessWidget {
  const LoadingBox({
    super.key,
    this.text,
  });

  final String? text;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
      child: Transform.translate(
        offset: Offset(0,-(themeBase.topBarHeight + themeBase.searHeight.value) / 2),
        child: Loading(
          color: themeBase.primaryColor.value,
          size: 28.sp,
          text: text,
        ),
      )
    );
  }
}
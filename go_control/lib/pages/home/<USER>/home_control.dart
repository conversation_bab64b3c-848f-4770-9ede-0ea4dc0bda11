import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:ui';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:gocontrol/api/api.dart';
import 'package:gocontrol/common/ble_api.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/radioPage/controller/upnp_radio_controller.dart';

final HomCon homCon = () {
  if (Get.isRegistered<HomCon>()) {
    return Get.find<HomCon>();
  } else {
    return Get.put(HomCon());
  }
}();

class HomCon extends GetxController {
  // 当前添加的所有设备
  final adevices = RxList<DeviceBase>([]);
  // 扫描的列表
  final scanDevList = RxList<DeviceBase>([]);
  // 
  DeviceBase? setNetDev;
  Set<String> setScanDevList = {};

  final auracastChildListDevice = RxList<B50>([]);
  List auracastChildList = [];
  String masterId = '';

  Libre? libreSetUpName;
  // 当前选择设备的index-
  final RxInt adevInx = 0.obs;
  // 当前点击的设备 
  DeviceBase? selectDevice;
  // 设备语言code
  final RxString appLanguageCode = ''.obs;
  // 设备国家code
  final RxString appCountryCode = ''.obs;
  // 记录错误信息
  final RxString errMsg = ''.obs;
  // 防止重复刷新/
  final RxBool ref = false.obs;
  // 蓝牙列表连接
  final List<String> bleConnectList = [];
  // 
  final List<B50> connectB50List = [];
  final RxInt connectingAp = 0.obs;
  final RxBool needSetGCast = false.obs;
  final RxBool showTos = false.obs;
  final RxString showTosContext = ''.obs;

  Timer? resetTimer;

  final List<String> upDeviceList = [];
  final List<String> libreUpList = [];

  Rx<Color> playBgColor = Rx<Color>(Color.fromRGBO(10, 10, 10, 1));

  final List languages = [
    'de_DE',
    'en_US',
    'ja_JP',
    'ko_KR',
    'ru_RU',
    'th_TH',
    'zh_CN',
    'zh_TW',
  ];

  late final String deviceUUID;

  @override
  void onInit() {
    super.onInit();
    deviceUUID = generateUUID();
    
    ApiManage.luciApi.watch((List<String> result) => watchForLibre(result));
  
    initBleMode();
    
    nasRadio.discoverClients();
  }

  String generateUUID(){
    const alphanumeric = '0123456789ABCDEF';
    final random = Random();
    String part1 = '';
    String part2 = '';
    String part3 = '';
    for (int i = 0; i < 4; i++) {
      part1 += alphanumeric[random.nextInt(alphanumeric.length)];
      part2 += alphanumeric[random.nextInt(alphanumeric.length)];
      part3 += alphanumeric[random.nextInt(alphanumeric.length)];
    }
    return '1770B849-$part1-$part2-$part3-6FD6E68C7321';
  }

  void addScanList(DeviceBase dev, String remoteId) {
    scanDevList.add(dev);
    setScanDevList.add(remoteId);
  }

 
  // 加载相应的设备f
  void initMode({bool refresh = false}) async {
    await Future.delayed(const Duration(milliseconds: 50));

    String? info = StorageClass.getStorage('Auracast:Master');
    if (info != null) {
      Map item = json.decode(info);
      homCon.masterId = item['remoteId'];
      homCon.auracastChildList = item['child'];
      Log.w('当前有组广播设备 ---- $info');
    }

    if(ref.value) return Log.e('不允许重复执行 initMode');
    for (var dev in adevices) {
      if (dev is B50) dev.disconnectBLE();
      if (dev is A31) dev.closeSocket();
      if (dev is Libre) dev.closeAll();

    }
    adevices.clear();

    ref.value = true;
    
    await Future.delayed(const Duration(milliseconds: 100));
    
    ApiManage.luciApi.startScan((List<String> result) {
      watchForLibre(result);
    });

    // initNetDevice();

    await Future.delayed(const Duration(seconds: 2));
    
    BleApi.scan(
      searchB50: true
    );
    
    await Future.delayed(const Duration(seconds: 2));
    
    ref.value = false;
    BleApi.offScan();
  }

  Future<void> watchForLibre(List<String> result) async {
    // 检查结果数组长度是否足够
    if (result.length < 9) return;
    // 从结果中获取设备型号
    final mymode = result[8].split(':').last;
    // 支持的设备模式列表
    const mode = ['LP10', 'LP20', 'LA50', 'LP100', 'LA150', 'A1000', 'A-NTC', 'Opera3'];
    // 不在mode列表里的型号过滤
    if (!mode.contains(mymode)) return;
    // 从结果中提取IP地址
    final libreIp = result[0].length > 3 ? result[0].substring(3) : '';
    // 检查IP地址是否有效
    if (libreIp.isEmpty) return;
    // 查找已连接的Libre设备
    final libreDevice = adevices.firstWhereOrNull(
      (el) => el is Libre && el.ip.value == libreIp 
    ) as Libre?;

    if (libreDevice != null && libreDevice.needWakeUp) {
      libreDevice.needWakeUp = false;
      await Future.delayed(const Duration(seconds: 3));
      libreDevice.initNetwork(rev: true);
    }

    final String deviceName = result[10].replaceAll('DeviceName:', '');
    if (upDeviceList.contains(deviceName) && libreDevice != null) { 
      if (libreDevice.wakeUp) return;
      // 
      upDeviceList.remove(deviceName);
      // 标记设备为已唤醒状态
      libreDevice.wakeUp = true;
      
      // 等待500毫秒
      await Future.delayed(const Duration(milliseconds: 500));
      // 从结果中获取版本信息
      final vers = result.length > 5 ? result[5] : '';
      // 如果版本信息存在则更新设备版本
      libreDevice.version.value = vers.isNotEmpty ? vers.replaceAll(RegExp(r'FWVERSION:'), '') : 'unknow';
      // 初始化网络连接
      libreDevice.initNetwork(rev: true);
      // 
      homCon.resetTimer?.cancel();
      return;
    }

    if (libreDevice != null) {
      if (!libreDevice.tcpSocketState.value) {
        await Future.delayed(const Duration(milliseconds: 500));
        libreDevice.initNetwork(rev: true);
      }
      return;
    }

    // 创建Libre设备
    final libre = Libre.connectTheNetwork(result);
    // 设置IP地址
    libre.ip.value = libreIp;
    // 设置设备型号
    libre.deviceMode.value = mymode;
    // 初始化网络连接
    libre.initNetwork();
  }

  // WiFi设备添加逻辑
  void initNetDevice() {
    ApiManage.netWorkApi.createDiscovery((String ip) async {
      Log.d(ip);
      List list = homCon.adevices.whereType<AbstractNET>().map((el) => el.ip).toList();
      if (list.contains(ip)) return;
      A31 a31 = A31.createToNetwork(ip);
      a31.connectSocket();
    });
  }

  // 加载蓝牙设备
  void initBleMode() async {
    BleApi bleApi = Get.put(BleApi());
    // 监听BLE扫描结果
    bleApi.bleScanResults((ScanResult scanResult) async {
      // Log.y('扫描到的设备 ${scanResult.device.platformName} $scanResult rssi ${scanResult.rssi}');
      String platformName = scanResult.device.platformName;
      if (platformName.contains('Remote')) return;

      String plName2 = platformName.replaceAll(RegExp(r'-BLE$'), '');
      
      // if (bleConnectList.contains(platformName)) return Log.e('$platformName 正在连接中，直接过滤');
      
      // 用 manufacturerData 筛选设备
      String remoteId = scanResult.device.remoteId.toString();
      Map<int, List<int>> manufacturerData = scanResult.advertisementData.manufacturerData;
      List serviceKeys = scanResult.advertisementData.serviceData.keys.toList();

      if (serviceKeys.isNotEmpty) {
        final Map<Guid, List<int>> serviceData = scanResult.advertisementData.serviceData;
        
         // if (!libreTest) {
            // Log.r('扫描到Libre需要配网的设备 $scanResult');
            // 判断是否是同一个设备
            // final List<String> lblt = adevices.whereType<Libre>().map((e)=> e.name.value).toList();
            // if (lblt.contains(plName2)) return Log.d('已存在相同设备 $platformName');
            // Libre libre = Libre.connectTheBLE(scanResult.device);
            // libre.name.value = plName2;
            // if (setScanDevList.contains(remoteId)) return;
            // Log.r('添加了Libre设备');
            // addScanList(libre, remoteId);
            // 判断是否是同一个设备
              // final List<String> lblt = adevices.whereType<Libre>().map((e)=> e.name.value).toList();
              // if (lblt.contains(plName2)) return Log.d('已存在相同设备 $platformName');
          // } else {
        final Guid target = Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1');

        if (serviceData.containsKey(target)) {
          final List<String> lblt = adevices.whereType<Libre>().map((e)=> e.name.value).toList();
          if (lblt.contains(plName2)) return Log.d('已存在liber相同设备 $platformName');

          // Log.r('扫描到Libre需要配网的设备 $scanResult');
          Libre libre = Libre.connectTheBLE(scanResult.device);
          libre.name.value = plName2;
          if (setScanDevList.contains(remoteId)) return;
          addScanList(libre, remoteId);
    

        /// libreTest 测试模式
        // if (setScanDevList.contains(remoteId)) return;
        //   final List<int> targetData = serviceData[target]!;
        //   final bool isFlag1Valid = targetData[1] == 17 || targetData[1] == 18;
        //   final bool isFlag2Valid = targetData[2] >= 1 && targetData[2] <= 4;
        //   // final bool isFlag3Valid = targetData[2] == 0 && targetData[2] == 0;
         
        //   if (isFlag1Valid && isFlag2Valid) {
        //     Log.r('扫描到Libre需要配网的设备 $scanResult');
        //     Libre libre = Libre.connectTheBLE(scanResult.device);
        //     libre.name.value = plName2;
        //     if (setScanDevList.contains(remoteId)) return;
        //     addScanList(libre, remoteId);
        //   } else {
        //     Log.r('被过滤的Libre 配网设备 $scanResult');
        //   }
        }
      }


        if (adevices.isNotEmpty) {
          final Set<String> a31Names = adevices.whereType<A31>().map((el) => el.name.value).toSet();
          if (a31Names.contains(plName2)) return Log.d('此设备是网络设备 $platformName');
        }

        // A31配网设备处理
        if (manufacturerData.containsKey(20556)) {
          Log.r('配网设备 $scanResult');
          A31 a31 = A31.createToBLE(
            nam: platformName.replaceAll(RegExp(r'-BLE$'), ''),
            scanResult: scanResult.device
          );
          if (setScanDevList.contains(remoteId)) return;
          addScanList(a31, remoteId);
          // SetupNetworkBottomSheet.addListAndAwaitSetup(a31);
        }

        // BLE设备 
        if (manufacturerData.containsKey(16722)) {
          // final List<int> data = manufacturerData[16722] ?? [];
          // if (!ListEquality().equals(data, [75, 65, 80, 73]) ) {
          //   Log.w('这个是libre设备，但是没有serviceData $plName2 $data');
          //   return;
          // }
          final Set<String> b50Names = adevices.whereType<B50>().map((el) => '${el.name.value}-BLE').toSet();
          if (b50Names.contains(platformName)) return Log.d('已存在 $platformName BLE设备');
          var b50 = B50(scanResult);
          var remoteId = b50.device.remoteId.toString();
          var theKey = '$remoteId:BMO';
          b50.name.value = platformName.replaceAll(RegExp(r'-BLE$'), '');
          final storedValue = StorageClass.getStorage(theKey);
          if (storedValue != null) {
            b50.deviceMode.value = storedValue;
          } else if (platformName.contains('Up2Cast')) {
            b50.deviceMode.value = 'Up2Cast';
            StorageClass.setStorage(theKey, 'Up2Cast');
          }
          await Future.delayed(const Duration(milliseconds: 100));
          final isAuracastChild = auracastChildList.contains(remoteId);
          if (isAuracastChild) b50.auracastChild.value = true;
          addToDeviceList(b50);
          if (isAuracastChild) return;
          if (adevices.length == 1 && adevices[0] == b50) {
            homCon.selectDevice = b50;
            b50.connectBLE();
          }
      }
    });
  }

  // 添加设备到列表里
  void addToDeviceList(DeviceBase deviceBase){
    
    // NET设备过滤规则
    if (deviceBase is A31) {
      final Set<String> nets = adevices.whereType<AbstractNET>().map((el)=> el.ip).toSet();
      if (nets.contains(deviceBase.ip)) return Log.d('${deviceBase.ip} 重复添加了');
    }
    // BLE设备过滤规则
    if (deviceBase is B50) {
      // 需要先检查是否和已连接的网络设备重复 条件是网络设备和BLE设备名字重复
      final Set<String> netDevNames = adevices.whereType<AbstractNET>().map((el) => el.name.value).toSet();
      if (netDevNames.contains(deviceBase.name.value)) return Log.d('蓝牙设备 ${deviceBase.name} 的名字与已存在的NET设备重名，过滤');
      // 检查蓝牙设备是否重复添加
      final Set<String> bleDeviceId = adevices.whereType<AbstractBLE>().map((el) => el.device.remoteId.toString()).toSet();
      if (bleDeviceId.contains(deviceBase.device.remoteId.toString())) {
        return Log.r('${deviceBase.device.platformName} 重复添加了');
      }
    }

    // Libre设备过滤规则
    if (deviceBase is Libre) { 
      final Set<String> nets = adevices.whereType<Libre>().map((el)=> el.ip.value).toSet();
      final Set<String> netsN = adevices.whereType<Libre>().map((el)=> el.name.value).toSet();
      if (nets.contains(deviceBase.ip.value)) return Log.d('${deviceBase.ip} 重复添加了');
      if (netsN.contains(deviceBase.name.value)) return Log.d('名字重复');
    }

    // 添加设备
    if (!adevices.contains(deviceBase)) {
      adevices.add(deviceBase);
      if (deviceBase is B50) {
        if (masterId != '' && auracastChildList.isNotEmpty) {
          final List<String> ids = [masterId, ...auracastChildList.map((el)=> el.toString())];
          int num = 0;
          B50? master;
          for (var el in adevices) {
            if (el is B50 && el.device.remoteId.toString() == masterId) {
              master = el;
              if (!master.isBLEConnected.value) el.connectBLE();
            }
          }
          for (var i = 0; i < adevices.length; i++) {
            var el = adevices[i];
            if (el is B50 && ids.contains(el.device.remoteId.toString())) num++;
          }
          if (num == ids.length) {
            Log.w('现在可以添加了');
            for (var el in adevices) {
              if (el is B50) {
                var remoteId = el.device.remoteId.toString();
                if (ids.contains(remoteId) && master != null) {
                  if (masterId == remoteId) continue;
                  if (!master.childForAuracast.contains(el)) {
                    master.childForAuracast.add(el);
                    if (!el.isBLEConnected.value) el.connectBLE2();
                  }
                }
              }
            }
          } else {
            var remoteId = deviceBase.device.remoteId.toString();
            if (master != null) {
              if (auracastChildList.contains(remoteId)) {
                deviceBase.auracastChild.value = true;
                if (!master.childForAuracast.contains(deviceBase)) {
                  master.childForAuracast.add(deviceBase);
                  if (!deviceBase.isBLEConnected.value) deviceBase.connectBLE2();
                }
              }
            }
          }
        }
      }
    }
    // 按名称排序
    adevices.sort((a, b) {
      if (a.origin == Origins.net && b.origin == Origins.ble) {
        return -1;
      } else if (a.origin == Origins.net && b.origin == Origins.net || 
        a.origin == Origins.ble && b.origin == Origins.ble) {
        return a.name.value.compareTo(b.name.value);
      } else {
        return 1;
      }
    });
    // 确保当前选择的设备的index对得上selectDevice
    if (selectDevice == null) return;
    for (var el in adevices) {
      if (el == selectDevice) {
        adevInx.value = adevices.indexOf(el);
        break;
      }
    }
    
  }
}
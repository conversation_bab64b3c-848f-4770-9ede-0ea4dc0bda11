import 'package:flutter/material.dart';
import 'package:get/get.dart';

final RegisterControl registerCon = Get.put(RegisterControl());
class RegisterControl extends GetxController {
  //注册的邮箱
  final TextEditingController emailController = TextEditingController();
  final FocusNode emailFocusNode = FocusNode();
  RxBool emailError = RxBool(false);

  //注册的用户名
  final TextEditingController userController = TextEditingController();
  final FocusNode userFoucusNode = FocusNode();
  RxBool nameError = RxBool(false);

  //密码
  final TextEditingController pawController = TextEditingController();
  final FocusNode pawFocusNode = FocusNode();
  RxBool pawError = RxBool(false);

  //确认密码
  final TextEditingController conPawController = TextEditingController();
  final FocusNode conPawFocusNode = FocusNode();
  RxBool conPawError = RxBool(false);

  final RxString errMsg =''.obs;
  final RxBool btnDisable = RxBool(false);

  void clearVal(){  //清空文本输入框内容
    emailController.text = '';
    userController.text = '';
    pawController.text = '';
    conPawController.text = '';
    emailError.value = false;
    nameError.value = false;
    pawError.value = false;
    conPawError.value = false;
    errMsg.value = '';
  }

  void resetErrorInput(){
    emailError.value = false;
    nameError.value = false;
    pawError.value = false;
    conPawError.value = false;
    errMsg.value = '';
  }
}
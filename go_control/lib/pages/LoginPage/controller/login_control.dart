

import 'dart:async';
import 'dart:convert';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/log.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

final LoginController loginCon = Get.put(LoginController());

class LoginController extends GetxController {
  // 重置信息 
  // 登录30分钟刷新一次token
  Timer? timerResetlogin;
  // 错误提示
  final RxString errMsg = ''.obs;
  // 登录按钮是否能使用
  final RxBool loginBtnDisable = RxBool(false);

  // 邮箱
  final FocusNode emailNode = FocusNode();
  final TextEditingController emailControl = TextEditingController();  // 用于控制邮箱输入框的文本内容
  final RxBool emailError = RxBool(false);  //用于标识邮箱输入是否存在错误

  // 密码
  final TextEditingController pswControl = TextEditingController();
  final FocusNode pswWordNode = FocusNode();
  final RxBool pawError = RxBool(false);

  final RxString resetEmail = RxString('');
  final RxString resetPas = RxString('');
  
  final RxMap userInfo = RxMap({
    'name': '',
    'id': '',
    'token': '',
  });

  void clearVal(){
    emailControl.text = '';
    pswControl.text = '';
    emailError.value = false;
    pawError.value = false;
    errMsg.value = '';
  }

  void resetLogin(){
    timerResetlogin = Timer(const Duration(seconds: 2000),() async {
      if(userInfo['token'] != '' && resetEmail.value != ''){
        try{
          final response = await http.post(
          Uri.parse('https://radio.arylic.com/user/login'),
          body: {
            "mail": resetEmail.value,
            "password": resetPas.value
          }
        );
        if (response.statusCode == 200) {
        final jsonMap = jsonDecode(response.body);
          Log.d('刷新登录信息: $jsonMap');
          if (jsonMap['status'] == 0) {
            userInfo['token'] = jsonMap['token'];
            userInfo['id'] = jsonMap['id'];
            userInfo['name'] = jsonMap['name'];
            resetLogin();
          }
        }
        }catch(_){}
      }
    });
  }
  
  //邮箱格式
  bool isEmailValid(String email){
    String pattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
    RegExp regExp = RegExp(pattern);
    return regExp.hasMatch(email);
  }

  // 加密密码
  String md5Pas(String pas){
    final bytes = utf8.encode(pas); // 转成字节
    final md5Hash = md5.convert(bytes); // 转成hash值
    return md5Hash.toString(); // 转成字符串
  }

  Future<Map> requst({required String url,required Map data}) async {
    try{
      final response = await http.post(
        Uri.parse(url),
        body: data
      );
      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        return {};
      }
    }catch(err){
      return {};
    }
  }


  // 初始化
  @override
  void onReady() {
    emailControl.text = StorageClass.getStorage('email')??'';
    pswControl.text = StorageClass.getStorage('password')??'';
    super.onReady();
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/pages/LoginPage/controller/register_control.dart';
import 'package:gocontrol/theme/theme.dart';

class RegisterErrMsgs extends StatelessWidget {
  const RegisterErrMsgs({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
      alignment: Alignment.topLeft,
      margin: EdgeInsets.only(top: registerCon.errMsg.value == ''?0:8.sp),
      height: registerCon.errMsg.value == ''?0:20.sp,
      child: AText(
        text: registerCon.errMsg.value,
        color: themeBase.errorColor.value,
        size: 14.sp,
      ),
    ));
  }
}
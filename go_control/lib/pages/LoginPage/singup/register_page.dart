import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/input_box.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/LoginPage/controller/login_control.dart';
import 'package:gocontrol/pages/LoginPage/controller/register_control.dart';
import 'package:gocontrol/pages/LoginPage/singin/components/singIn_button.dart';
import 'package:gocontrol/pages/LoginPage/singup/components/register_msg.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/theme/theme.dart';

class SignUpPage extends StatelessWidget {
  const SignUpPage({super.key});

  @override
  Widget build(BuildContext context) {
    final RxBool openEyes = false.obs;

    return PagesBody(
      showTopBar: false,
      scroll: false,
      body: BodyBox(
        showTopBar: false,
        child: Padding(
          padding: EdgeInsets.all(18.sp),
          child: SingleChildScrollView( 
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Transform.translate(
                  offset: Offset(-15.sp, 0),
                  child: const Top2tion(title: ''),
                ),
                Transform.scale(
                  scale: themeBase.isiPad.value? 0.67 : 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                          top: themeBase.isiPad.value? 0 : 0.13.sh,
                          bottom: (0.13.sh) / 1.5
                        ),
                        child: AText(
                          text: 'sign_up'.tr,
                          size: 50.sp,
                          color: themeBase.primaryColor.value,
                        ),
                      ),
                      InputBox(
                        height: 50.sp, 
                        margin: EdgeInsets.only(bottom: 12.sp),
                        focusNode: registerCon.emailFocusNode, 
                        textController: registerCon.emailController, 
                        textColor: themeBase.primaryColor.value,
                        errorBorder: registerCon.emailError,
                        color: themeBase.textColor1.value,
                        autofocus: true,
                        leftIcon: Icon(
                          Icons.email,
                          size: 23.sp,
                          color: themeBase.primaryColor.value,
                        ),
                        placeholder: 'email'.tr,
                        change: (value)=> registerCon.resetErrorInput(),
                      ),
                      InputBox(
                        height: 50.sp, 
                        margin: EdgeInsets.only(bottom: 12.sp),
                        focusNode: registerCon.userFoucusNode, 
                        textController: registerCon.userController, 
                        textColor: themeBase.primaryColor.value,
                        errorBorder: registerCon.nameError,
                        color: themeBase.textColor1.value,
                        leftIcon: Icon(
                          Icons.people_alt_rounded,
                          size: 23.sp,
                          color: themeBase.primaryColor.value,
                        ),
                        placeholder: 'user_name'.tr,
                        change: (value)=> registerCon.resetErrorInput(),
                      ),
                      InputBox(
                        height: 50.sp, 
                        margin: EdgeInsets.only(bottom: 12.sp),
                        focusNode: registerCon.pawFocusNode, 
                        textController: registerCon.pawController, 
                        textColor: themeBase.primaryColor.value,
                        errorBorder: registerCon.pawError,
                        color: themeBase.textColor1.value,
                        display: openEyes,
                        leftIcon: Icon(
                          Icons.lock,
                          size: 23.sp,
                          color: themeBase.primaryColor.value,
                        ),
                        rightIcon: GestureDetector(
                          child: Obx(() => GoIcon(
                            name: openEyes.value?GoIcons.eyes:GoIcons.closeEyes,
                            color: themeBase.primaryColor.value,
                            size: 22.sp,
                          )),
                          onTap: (){
                            openEyes.value = !openEyes.value;
                            // focusNode.requestFocus();
                          },
                        ),
                        placeholder: 'psw'.tr,
                        change: (value)=> registerCon.resetErrorInput(),
                      ),
                      InputBox(
                        height: 50.sp, 
                        focusNode: registerCon.conPawFocusNode, 
                        textController: registerCon.conPawController, 
                        textColor: themeBase.primaryColor.value,
                        errorBorder: registerCon.conPawError,
                        color: themeBase.textColor1.value,
                        display: openEyes,
                        leftIcon: Icon(
                          Icons.lock,
                          size: 23.sp,
                          color: themeBase.primaryColor.value,
                        ),
                        rightIcon: GestureDetector(
                          child: Obx(() => GoIcon(
                            name: openEyes.value? GoIcons.eyes: GoIcons.closeEyes,
                            color: themeBase.primaryColor.value,
                            size: 22.sp,
                          )),
                          onTap: (){
                            openEyes.value = !openEyes.value;
                            // focusNode.requestFocus();
                          },
                        ),
                        placeholder: 'psw2'.tr,
                        change: (value)=> registerCon.resetErrorInput(),
                      ),
                      const RegisterErrMsgs(),
                      SingInButton(
                        text: 'sign_up'.tr,
                        distancetop: 60.sp,
                        loading: registerCon.btnDisable,
                        onTap: () async => await register(),
                      ),
                      //如果你已经有了账号，返回登录
                      Container(
                        margin: EdgeInsets.only(top: 10.sp),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Obx(() => AText(
                              text: 'have_member'.tr,
                              color: themeBase.primaryColor.value.withAlpha(150),
                              size: 15.sp
                            )),
                            SizedBox(width: 8.sp,),
                            GestureDetector(
                              onTap: () {
                                registerCon.clearVal();
                                Get.offNamed('login',parameters: {'back':'1'});
                              },
                              child: Obx(() => AText(
                                text: 'Sign_in_here'.tr,
                                color: themeBase.primaryColor.value,
                                size: 15.sp
                              )),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        )
      ),
    );
  }
}

Future<void> register() async {
  //如果按钮被禁用，直接返回
  if(registerCon.btnDisable.value) return;
  //验证邮箱格式
  if (registerCon.emailController.text != '' && loginCon.isEmailValid(registerCon.emailController.text)){
    //验证用户名不为空
    if (registerCon.userController.text != ''){
      //验证密码长度
      if(registerCon.pawController.text.length >= 6){
        //验证确认密码长度
        if(registerCon.conPawController.text.length >= 6){
          //验证密码和确认密码是否一致
          if(registerCon.pawController.text == registerCon.conPawController.text){
            //对密码进行MD5加密
            final bytes = utf8.encode(registerCon.pawController.text); // 转成字节
            final md5Hash = md5.convert(bytes); // 转成hash值
            final md5Pas = md5Hash.toString(); // 转成字符串
            try{
              // 禁用按钮
              registerCon.btnDisable.value = true;
              // 发送注册请求
              
              final Map data = await loginCon.requst(
                url: 'https://radio.arylic.com/user/register',
                data: {
                  "mail": registerCon.emailController.text, 
                  "password": md5Pas,
                  "name": registerCon.userController.text,
                  "desc": "absolutely empty"
                }
              );
              // 释放按钮
              registerCon.btnDisable.value = false;
              if(data.keys.toList().isNotEmpty){
                Log.w(data['status']);
                if(data['status'] == 0){
                  showMyBottomSheet(
                    height: 220.sp, 
                    title: 'Register Completed', 
                    content:  AText(
                      text: 'Please check your email',
                      size: themeBase.subBodyFont.value,
                      color: themeBase.primaryColor.value,
                    ),
                    showCancel: RxBool(false),
                    confirm: (){
                      loginCon.emailControl.text = registerCon.emailController.text;
                      loginCon.pswControl.text = registerCon.pawController.text;
                      Get.offNamed('/login');
                      registerCon.clearVal();
                    }
                  );
                }else if(data['status'] == 1002){
                  // 邮箱格式错误
                  registerCon.emailError.value = true;
                  registerCon.errMsg.value = data['msg'];
                }else if(data['status'] == 1003){
                  //邮箱已存在
                  registerCon.emailError.value = true;
                  registerCon.errMsg.value = '邮箱已存在';
                }else if(data['status'] == 1004){
                  // 用户名错误
                  registerCon.nameError.value = true;
                  registerCon.errMsg.value = data['msg'];
                }else{
                  registerCon.emailError.value = true;
                  registerCon.nameError.value = true;
                  
                }
              }else{
                registerCon.errMsg.value = 'Request error';
              }
            }catch(_){
              registerCon.errMsg.value = 'Request error';
            }
          }else{
            //密码和确认密码不一致
            registerCon.errMsg.value = 'psw_qr'.tr;
            registerCon.pawError.value = true;
            registerCon.conPawError.value = true;
          }
        }else{
          //确认密码长度不足
          registerCon.errMsg.value = 'psw_length'.tr;
          registerCon.conPawError.value = true;
        }
      }else{
        //密码长度不足
        registerCon.errMsg.value = 'psw_length'.tr;
        registerCon.pawError.value = true;
      }
    }else{
      //用户名为空
      registerCon.errMsg.value = 'name_err'.tr;
      registerCon.nameError.value = true;
    }
  }else{
    //邮箱格式无效
    registerCon.errMsg.value = 'email_format'.tr;
    registerCon.emailError.value = true;
  }
}
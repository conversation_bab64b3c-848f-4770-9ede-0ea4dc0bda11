import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/input_box.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/LoginPage/controller/login_control.dart';
import 'package:gocontrol/pages/LoginPage/singin/components/auto_login.dart';
import 'package:gocontrol/pages/LoginPage/singin/components/login_msg.dart';
import 'package:gocontrol/pages/LoginPage/singin/components/singIn_button.dart';
import 'package:gocontrol/pages/LoginPage/singin/components/singUp.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:http/http.dart' as http;

class SignInPage extends StatelessWidget {
  const SignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    final RxBool back = false.obs;
    if(Get.parameters.containsKey('back')) back.value = true;

    final RxBool openEyes = false.obs;
    StorageClass.setStorage('notFirstApp',true);

    return PagesBody(
      showTopBar: back.value,
      scroll: false,
      topbar: const Top2tion(
        title: '',
      ),
      body: BodyBox(
        child: Transform.translate(
          offset: Offset(0, back.value? -(themeBase.topBarHeight) : 0),
          child: Padding(
            padding: EdgeInsets.all(18.sp),
            child: SingleChildScrollView(
              child: Obx(()=> Transform.scale(
                scale: themeBase.isiPad.value? 0.7 : 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(vertical: 0.13.sh),
                      child: AText(
                        text: 'login'.tr,
                        size: 50.sp,
                        color: themeBase.primaryColor.value,
                      ),
                    ),
                    Column(
                      children: [
                        InputBox(
                          height: 50.sp,
                          margin: EdgeInsets.only(bottom: 12.sp),
                          focusNode: loginCon.emailNode, 
                          textController: loginCon.emailControl, 
                          textColor: themeBase.primaryColor.value,
                          errorBorder: loginCon.emailError,
                          color: themeBase.textColor1.value,
                          autofocus: true,
                          leftIcon: Icon(
                            Icons.email,
                            size: 22.sp,
                            color: themeBase.primaryColor.value,
                          ),
                          placeholder: 'email'.tr,
                          change: (value){
                            loginCon.emailError.value = false;
                            loginCon.errMsg.value = '';
                          },
                        ),
                        InputBox(
                          height: 50.sp,
                          margin: EdgeInsets.only(bottom: 12.sp),
                          focusNode: loginCon.pswWordNode, 
                          textController: loginCon.pswControl, 
                          textColor: themeBase.primaryColor.value,
                          errorBorder: loginCon.pawError,
                          color: themeBase.textColor1.value,
                          leftIcon: Icon(
                            Icons.lock,
                            size: 22.sp,
                            color: themeBase.primaryColor.value,
                          ),
                          display: openEyes,
                          placeholder: 'psw'.tr,
                          change: (value){
                            loginCon.pawError.value = false;
                            loginCon.errMsg.value = '';
                          },
                          rightIcon: GestureDetector(
                            child: Obx(() => GoIcon(
                              name: openEyes.value?GoIcons.eyes:GoIcons.closeEyes,
                              color: themeBase.primaryColor.value,
                              size: 22.sp,
                            )),
                            onTap: (){
                              openEyes.value = !openEyes.value;
                              // focusNode.requestFocus();
                            },
                          ),
                        ),
                        const LoginErrorMsg(),
                        const AutoSingIn(),
                      ],
                    ),
                    Column(
                      children: [
                        SingInButton(
                          text: 'login'.tr,
                          distancetop: 60.sp,
                          loading: loginCon.loginBtnDisable,
                          onTap: ()=> login(),
                        ),
                        SingInButton(
                          text: 'skip'.tr,
                          distancetop: 12.sp,
                          onTap: () {
                            loginCon.clearVal();
                            Get.offAllNamed('/home');
                          },
                        ),
                        const SignUp()
                      ],
                    )
                  ],
                ),
              )),
            ),
          ),
        )
      )
    );
  }
}

void login() async {
  
  String email = loginCon.emailControl.text;
  String paw = loginCon.pswControl.text;

  if(email == '' || paw == '') return;
  //检查邮箱的格式

  if(!loginCon.isEmailValid(email)){
    loginCon.emailError.value = true;
    loginCon.errMsg.value = 'email_format'.tr;
    return;
  }

  //检查密码的格式
  if(paw.length < 6){
    loginCon.pawError.value = true;
    loginCon.errMsg.value = 'psw_length'.tr;
    return;
  }

  loginCon.loginBtnDisable.value = true;
  final bytes = utf8.encode(paw); // 转成字节
  final md5Hash = md5.convert(bytes); // 转成hash值
  final md5Pas = md5Hash.toString(); // 转成字符串
  
  //请求登录
  final response = await http.post(
    Uri.parse('https://radio.arylic.com/user/login'),
    body: {'mail':email , 'password': md5Pas }
  );

  // 请求的结果处理
  final Map jsonMap = jsonDecode(response.body);
  if(response.statusCode == 200) {
    if(jsonMap['status'] == 0){
      // 如果选择自动登录，保持邮箱和密码
      bool auto = StorageClass.getStorage('autologin') ?? false;
      if(auto){
        StorageClass.setStorage('email', email);
        StorageClass.setStorage('password', paw);
        StorageClass.setStorage('user_name', jsonMap['name']);
        StorageClass.setStorage('user_id', jsonMap['id']);
        StorageClass.setStorage('user_token', jsonMap['token']);
      }
      // 保存信息
      loginCon.userInfo.value = {
        'name': jsonMap['name'],
        'id': jsonMap['id'],
        'token': jsonMap['token'],
      };
      loginCon.resetEmail.value = email;
      loginCon.resetPas.value = md5Pas;
      loginCon.resetLogin();
      Get.offNamed('/home');
      // loginCon.userInfo['token'] = ;
      // loginCon.userInfo['id'] = ;
      // loginCon.userInfo['name'] = ;
    }else if(jsonMap['status'] == 1009){
      // 邮箱未注册
      loginCon.emailError.value = true;
      loginCon.errMsg.value = 'user_not_found'.tr; 
    }else if(jsonMap['status'] == 1010){
      // 密码错误
      loginCon.pawError.value = true;
      loginCon.errMsg.value = 'wrong_password'.tr;  
    }
  }else{
    loginCon.errMsg.value = 'request_fail'.tr;  //请求错误
  }
  loginCon.loginBtnDisable.value = false;
}
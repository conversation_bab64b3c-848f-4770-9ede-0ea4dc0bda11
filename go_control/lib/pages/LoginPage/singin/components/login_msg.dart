import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/pages/LoginPage/controller/login_control.dart';
import 'package:gocontrol/theme/theme.dart';

class LoginErrorMsg extends StatelessWidget {
  const LoginErrorMsg({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
      alignment: Alignment.topLeft,
      margin: EdgeInsets.only(
        bottom: loginCon.errMsg.value == ''?0:8.sp
      ),
      height: loginCon.errMsg.value == ''?0:18.sp,
      child: AText(
        text: loginCon.errMsg.value,
        size: 14.sp,
        color: themeBase.errorColor.value
      ),
    ));
  }
}
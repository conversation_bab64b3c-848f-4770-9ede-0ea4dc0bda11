// ignore_for_file: file_names
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class SignUp extends StatelessWidget {
  const SignUp({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 14.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AText(
            text:'register_title'.tr,  //还没有账号?
            color: themeBase.primaryColor.value.withAlpha(150),
            size: 15.sp,
          ),
          SizedBox(width: 8.sp,),
          GestureDetector(
            onTap: (){
              // 跳转到注册页面
              Get.offNamed('register');
            },
            child: AText(
              text:'register_text'.tr,  //立即注册
              color: themeBase.primaryColor.value,
              size: 15.sp,
            ),
          ),
        ],
      ),
    );
  }
}
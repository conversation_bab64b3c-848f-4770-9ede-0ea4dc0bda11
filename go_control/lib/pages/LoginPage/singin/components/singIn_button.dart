// ignore_for_file: file_names
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/theme/theme.dart';

class SingInButton extends StatelessWidget {
  const SingInButton({
    super.key,
    required this.text,
    required this.distancetop,
    required this.onTap,
    this.loading,
  });

  final RxBool? loading;
  final String text;
  final Function onTap;
  final double distancetop;

  @override
  Widget build(BuildContext context) {
    return Obx(() => OnTapScaleToSmallBox(
      onTap: () => onTap(),
      child: Container(
        alignment: Alignment.center,
        height: 43.sp,
        width: double.infinity,
        margin: EdgeInsets.only(top: distancetop),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: themeBase.primaryColor.value
        ),
        child: myChild(),
      ),
    ));
  }

  Widget myChild(){
    if(loading != null && loading!.value){
      return SizedBox(
        width: 24.sp,
        height: 24.sp,
        //圆形进度指示器
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(themeBase.blockColor.value),
          strokeWidth: 2.6.sp,
        ),
      );
    }
    return Obx(()=>AText(
      text: text,
      size: 17.sp,
      color: themeBase.textColor1.value
    ));
  }
}
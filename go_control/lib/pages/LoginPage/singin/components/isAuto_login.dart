// ignore_for_file: file_names
import 'dart:convert';
import 'package:gocontrol/common/storage.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/LoginPage/controller/login_control.dart';

Future<void> isAutoLogin() async {
  var auto = StorageClass.getStorage('autologin') ?? false;
  Log.d('是否自动登录 ${StorageClass.getStorage('autologin')}');
  if(auto){
    //从本地存储中获取保存的邮箱和密码
    var myEmail = StorageClass.getStorage('email');
    var myPaw = StorageClass.getStorage('password');
    var namee = StorageClass.getStorage('user_name');
    var idd = StorageClass.getStorage('user_id');
    var tokenn = StorageClass.getStorage('user_token');
    
    if(namee != null && idd != null && tokenn != null){
      loginCon.userInfo['name'] = namee;
      loginCon.userInfo['id'] = idd;
      loginCon.userInfo['token'] = tokenn;
    }
    
    try{
      final bytes = utf8.encode(myPaw);
      final md5Hash = md5.convert(bytes);
      final md5Pas = md5Hash.toString();
      final response = await http.post(
        Uri.parse('https://radio.arylic.com/user/login'),
        body: {"mail": myEmail, "password": md5Pas}
      );
      if (response.statusCode == 200) {
        final jsonMap = jsonDecode(response.body);
        if (jsonMap['status'] == 0) {
          loginCon.userInfo['token'] = jsonMap['token'];
          loginCon.userInfo['id'] = jsonMap['id'];
          loginCon.userInfo['name'] = jsonMap['name'];
          loginCon.resetEmail.value = myEmail;
          loginCon.resetPas.value = md5Pas;
          loginCon.resetLogin();
          Log.d('自动登录账号${loginCon.userInfo['name']}');
        }else if(jsonMap['status'] == 1010){
          Log.e('密码错误');
          loginCon.userInfo['name'] = '';
          loginCon.userInfo['id'] = '';
          loginCon.userInfo['token'] = '';
        }else{
          Log.e('请求失败1 $jsonMap');
          Future.delayed(const Duration(seconds: 1),()=> isAutoLogin());
        }
      }else{
        Log.e('请求失败2');
        Future.delayed(const Duration(seconds: 1),()=> isAutoLogin());
      }
    }catch(_){
      Log.e('请求失败3');
      Future.delayed(const Duration(seconds: 1),()=> isAutoLogin());
    }
  }
}
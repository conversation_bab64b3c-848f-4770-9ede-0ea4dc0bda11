import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/input_box.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/LoginPage/controller/login_control.dart';
import 'package:gocontrol/theme/theme.dart';

class AutoSingIn extends StatelessWidget {
  const AutoSingIn({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            const AutoTap(),
            SizedBox(width: 8.sp),
            Obx(() => AText(
              text: 'auto_login'.tr,  //自动登录
              color: themeBase.primaryColor.value,
              size: 13.sp,
            ))
          ],
        ),
        GestureDetector(
          onTap: (){
            final FocusNode resetInputNode = FocusNode();
            final TextEditingController textController = TextEditingController();
            final RxBool loading = false.obs;
            final RxString errMsg = ''.obs;
            final RxBool isOk = false.obs;

            showMyBottomSheet(
              height: 270.sp,
              title: 'reset_psw'.tr,
              content: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.sp),
                child: Column(
                  children: [
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: Obx(() => !isOk.value? InputBox(
                        height: 45.sp, 
                        focusNode: resetInputNode, 
                        textController: textController,
                        textColor: themeBase.primaryColor.value,
                        placeholder: 'email'.tr,
                        change: (String value){
                          if(errMsg.value.isNotEmpty) errMsg.value = '';
                          Log.d(value);
                        },
                      ):
                      AText(
                        text: 'reset_psw_msg'.tr,
                        color: themeBase.primaryColor.value,
                        size: themeBase.subBodyFont.value,
                        softWrap: true,
                      )),
                    ),
                    Obx(() => !isOk.value?Container(
                      alignment: Alignment.topLeft,
                      padding: EdgeInsets.only(top: 12.sp),
                      child: AText(
                        text: 'reset_psw_text'.tr,
                        color: themeBase.textColor2.value,
                        size: themeBase.subBodyFont.value - 2.sp,
                        softWrap: true,
                      ),
                    ):Container()),
                    Obx(() => Visibility(
                      visible: errMsg.value != '',
                      child: Container(
                        alignment: Alignment.topLeft,
                        margin: EdgeInsets.only(top: 6.sp),
                        child: AText(
                          text: errMsg.value,
                          size: themeBase.subBodyFont.value,
                          color: themeBase.errorColor.value,
                        ),
                      ),
                    ))
                  ],
                ),
              ),
              cancel: ()=> Get.back(),
              showCancel: isOk,
              changeOnlyConfirm: isOk,
              confirmFuture: loading,
              confirm: () async {
                if(isOk.value) Get.back();
                final Map data = await loginCon.requst(
                  url: 'https://radio.arylic.com/user/recovery',
                  data: {'mail': textController.text}
                );
                if(data.keys.toList().isNotEmpty){
                  if(data['status'] == 0){
                    isOk.value = true;
                  }else{
                    errMsg.value = data['msg'];
                  }
                }else{
                  errMsg.value = 'request_fail'.tr;
                }
              }
            );
          },
          child: Obx(() => AText(
            text: 'forgot_psw'.tr,  //忘记密码？
            size: 13.sp,
            color: themeBase.primaryColor.value.withOpacity(.7),  
            textAlign: TextAlign.right,
          )),
        )
      ],
    );
  }
}


class AutoTap extends StatelessWidget {
  const AutoTap({super.key});
  
  @override
  Widget build(BuildContext context) {
    final RxBool isAuto = false.obs;

    //从存储中获取自动登录标志
    isAuto.value = StorageClass.getStorage('autologin')??false;

    return GestureDetector(
      onTap: ()=> _setAutoLogin(isAuto),
      child: Container(
        width: 20.sp,
        height: 20.sp,
        decoration: BoxDecoration(
          color: themeBase.blockColor.value,
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: Obx(()=> Visibility(
          visible: isAuto.value, 
          child: GoIcon(
            name: GoIcons.tick,
            color: themeBase.primaryColor.value,
            size: 19.sp,
          ),
        )),
      ),
    );
  }

  // 是否自动登录
  void _setAutoLogin(RxBool isAuto){
    isAuto.value = !isAuto.value;
    StorageClass.setStorage('autologin', isAuto.value);
    if(!isAuto.value){
      StorageClass.delStorage('email');
      StorageClass.delStorage('password');
    }
  }
}


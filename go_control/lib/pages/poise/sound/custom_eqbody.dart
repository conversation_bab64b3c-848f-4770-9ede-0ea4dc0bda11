import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/diyclass.dart';
import 'package:gocontrol/pages/poise/eq/components/slider_eq.dart';
import 'package:gocontrol/theme/theme.dart';

class CustomEQbody extends StatelessWidget {
  const CustomEQbody({super.key});

  @override
  Widget build(BuildContext context) {
    // 设备
    BaseAudio device = homCon.selectDevice as BaseAudio;
    // 
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 14.sp,
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: 16.sp,
              bottom: 6.sp
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(
                    left: 4.sp,
                  ),
                  child: Obx(()=> AText(
                    text: 'custom_eq'.tr, // 预设
                    color: themeBase.primaryColor.value,
                    size: themeBase.subBodyFont.value,
                  )),
                ),
                Row(
                  children: [
                    CustomEQControlBtn(
                      title: 'custom_del'.tr,
                      onTap: (){
                        Diyclass.diyPop(
                          title: 'tips'.tr, //温馨提示
                          text: 'del_eq_text'.tr, //确定要删除此自定义EQ么
                          read: true,
                          confirmFn: (){
                            device.delectCustomEQ();
                            Get.back();
                          }
                        );
                      },
                    ),
                    CustomEQControlBtn(
                      title: 'save'.tr,
                      isLoading: true,
                      onTap: (){
                        device.saveCustomEQ();
                      },
                    ),
                  ],
                )
              ],
            ),
          ),
          GestureDetector(
            onTap: (){
              Get.bottomSheet(
                const SelectCustomEQ(),
              );
            },
            child: Container(
              height: 42.sp,
              padding: EdgeInsets.symmetric(horizontal: 14.sp),
              decoration: BoxDecoration(
                color: themeBase.primaryColor.value,
                borderRadius: BorderRadius.circular(12.sp)
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() => AText(
                    text: getNowCustomEQName(),
                    color: themeBase.textColor1.value,
                    size: themeBase.subBodyFont.value
                  )),
                  Obx(() => Visibility(
                    visible: device.eqList.isNotEmpty,  
                    child: Icon(
                      const IconData(0xe648,fontFamily:'IconFont'),
                      color: themeBase.textColor1.value,
                      size: 19.sp,
                    ),
                  )),
                ],
              ),
            ),
          ),
          Obx(() => Opacity(
            opacity: device.eqIndex.value > 9?1:.5,
            child: AbsorbPointer(
              absorbing: device.eqIndex.value > 9?false:true,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: List.generate(device.customEQValueList.length, (index){  //
                  return Obx(()=> SliderEQ(
                    onChanged: (double value) { 
                      device.customEQValueList[index]['gain'].value = value.floor().toDouble();  //
                    },
                    onChangeEnd: (double value) {
                      String freq = (int.parse(device.customEQValueList[index]['freq']) * 1).toRadixString(16).toUpperCase().padLeft(4, '0');
                      int v = value.floor().toInt() * 256;
                      String gain = (v & 0xFFFF).toRadixString(16).toUpperCase().padLeft(4, '0');
                      gain = gain.substring(gain.length - 4,gain.length);
                      String flt = 'CEQ:FLT:$index:$freq,0599,$gain';
                      device.setFLT(flt);
                    },
                    min: -10,
                    max: 10,
                    name: device.customEQValueList[index]['freq'],  //
                    value: device.customEQValueList[index]['gain'].value,  //
                  ));
                }),
              ),
            ),
          )),
        ],
      ),
    );
  }

  String getNowCustomEQName(){
    BaseAudio device = homCon.selectDevice as BaseAudio;
    if(device.customEQList.isNotEmpty && device.eqIndex.value > 9){
      // themeBase.primaryColor.value.withOpacity(device.customEQList[index]['id'] == '${(device.eqIndex.value - 10)}'?1:0.5)
      for (var el in device.customEQList) {
        if(el['id'] == '${device.eqIndex.value - 10}') return el['name'];
      }
      
    }
    return 'no_select'.tr;
  }
}

class SelectCustomEQ extends StatelessWidget {
  const SelectCustomEQ({super.key});

  @override
  Widget build(BuildContext context) {
    // 设备
    BaseAudio device = homCon.selectDevice as BaseAudio;

    return Container(
      height: 280.sp,
      padding: EdgeInsets.symmetric(
        vertical: 18.sp,
        horizontal: 24.sp
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(25.sp)
        ),
        color: themeBase.bgColor.value,
      ),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(bottom: 14.sp),
            child: Obx(()=> AText(
              text: 'custom_eqlist'.tr,  // 预设列表
              color: themeBase.primaryColor.value,
              size: themeBase.subBodyFont.value + 2.sp
            )),
          ),
          Obx(() => Visibility(
            visible: device.customEQList.isEmpty,
            child: Column(
              children: [
                AddSelectEQBox(
                  onTap: () => addCustomEq(),
                  title: 'setEq'.tr,
                )
              ]
            ),
          )),
          Obx(() => Visibility(
            visible: device.customEQList.isNotEmpty,
            child: Flexible(
              child: ListView.builder(
                itemBuilder: (context, index) {
                  if (index == (device.customEQList.length - 1) && device.customEQList.length != 4) {
                    return Column(
                      children: [
                        SelectEQBox(
                          index: index,
                          onTap: (){
                            int id = int.parse(device.customEQList[index]['id']) + 10;
                            device.setEqIndex(id);
                          },
                          title: device.customEQList[index]['name'],
                        ),
                        AddSelectEQBox(
                          onTap: () => addCustomEq(),
                          title: 'setEq'.tr,
                        )
                      ],
                    );
                  }
                  return SelectEQBox(
                    index: index,
                    onTap: (){
                      int id = int.parse(device.customEQList[index]['id']) + 10;
                      device.setEqIndex(id);
                      // Log.d(id);
                    },
                    title: device.customEQList[index]['name'],
                  );
                },
                itemCount: device.customEQList.length,
              )
            ),
          ))
        ],
      ),
    );
  }
}

class SelectEQBox extends StatelessWidget {
  const SelectEQBox({
    super.key,
    required this.title,
    required this.index,
    this.onTap,
  });

  final String title;
  final int index;
  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;

    return OnTapScaleToSmallBox(
      onTap: () {
        if (onTap != null) onTap!();
      },
      child: Obx(() => Container(
        height: 38.sp,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(
          horizontal: 12.sp
        ),
        margin: EdgeInsets.only(bottom: 8.sp,left: 2.sp,right: 2.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.sp),
          color: themeBase.primaryColor.value.withOpacity(device.customEQList[index]['id'] == '${(device.eqIndex.value - 10)}'?1:0.5)
        ),
        child: AText(
          // 预选均衡器的名称
          text: title,  
          color: themeBase.textColor1.value,
          size: 14.sp
        ),
      ))
    );
  }
}

class AddSelectEQBox extends StatelessWidget {
  const AddSelectEQBox({
    super.key,
    required this.title,
    this.onTap,
  });

  final String title;
  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    return OnTapScaleToSmallBox(
      onTap: () {
        if (onTap != null) onTap!();
      },
      child: Obx(() => Container(
        height: 38.sp,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(
          horizontal: 12.sp
        ),
        margin: EdgeInsets.only(bottom: 8.sp,left: 2.sp,right: 2.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.sp),
          color: themeBase.primaryColor.value.withOpacity(0.5)
        ),
        child: AText(
          // 预选均衡器的名称
          text: title,  
          color: themeBase.textColor1.value,
          size: 14.sp
        ),
      ))
    );
  }
}

//创建均衡器的弹窗
void addCustomEq(){
  final RxString nameVal = ''.obs;
  // 设备
  BaseAudio device = homCon.selectDevice as BaseAudio;

  Diyclass.diyPop(  
    title: 'setEq'.tr,  //创建自定义均衡器
    text: '',
    read: true,
    diyContent: Container(
      width: double.maxFinite,
      height: 48.sp,
      margin: EdgeInsets.only(bottom: 12.sp,top: 6.sp),
      padding: EdgeInsets.all(6.sp),
      constraints: BoxConstraints(
        maxHeight: 200.sp
      ),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha((255 * 0.3).round()),
        borderRadius: BorderRadius.circular(12.sp),
      ),
      child: TextField(
        keyboardType: TextInputType.name,
        maxLengthEnforcement: null,
        onChanged: (value){
          nameVal.value = value;
        },
        autofocus: false,
        decoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(vertical: 0.sp, horizontal: 8.sp),
          focusedBorder: OutlineInputBorder( 
            borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
            borderSide: BorderSide.none, // 取消边框
          ),
          enabledBorder: OutlineInputBorder( 
            borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
            borderSide: BorderSide.none, // 取消边框
          ),
          hintText: 'setEqName'.tr,  //自定义均衡器名字
          hintStyle: TextStyle(
            color: themeBase.primaryColor.value
          )
        ),
        style: TextStyle(
          fontSize: 15.sp,
          color: themeBase.primaryColor.value
        )
      ),
    ),
    //点击确认后，要对名字进行判断
    confirmFn: () async {
      if (nameVal.value.length > 5) {
        Diyclass.diyPop(
          title: 'error'.tr,   //错误
          text: 'namelong'.tr  //名称太长无法使用
        );
      } else if(nameVal.value.length < 2) {
        Diyclass.diyPop(
          title: 'error'.tr, //错误
          text: 'nameduan'.tr  //名称不能小于2位数
        );
      } else {
        //判断自定义列表是否为空,清空所有的数据并添加创建自定义均衡器的按钮
        // Log.d('添加自定义均衡器:$index');
        List<String> list1 = ['0', '1', '2', '3'];
        String id = checkAndReturn(list1, device.customEQList);
        device.customEQList.add({
          'id': id,
          'name': nameVal.value
        });
        // Log.d('${device.customEQList}');
        device.setEqIndex(int.parse(id) + 10);
        Get.back();
        // 初始化数据
        await Future.delayed(const Duration(milliseconds:200),() async {
          for (int i = 0; i < 8;i++){
            await Future.delayed(const Duration(milliseconds:50),(){
              String data = 'CEQ:FLT:$i:${int.parse(device.customEQValueList[i]['freq']).round().toRadixString(16).toUpperCase().padLeft(4, '0')},0599,0000';  //
              Log.h(data);
              device.getCEQandFLT(data); 
            });
          }
        });
      }
    }
  );
}

String checkAndReturn(List<String> list1, RxList<Map<dynamic, dynamic>> list2) {
  // 如果 list2 为空，返回 list1 的第一个元素
  if (list2.isEmpty) {
    return list1[0];
  }
  // 如果list1长度等于list2 则不返回
  if (list1.length == list2.length) {
    return '-1';
  }
  // 创建一个可变列表，用于存储要保留的 list1 元素
  List<String> filteredList = List.from(list1);
  // 遍历 list2，删除在 list1 中对应的 id
  for (var item in list2) {
    if (filteredList.contains(item['id'].toString())) {
      filteredList.remove(item['id'].toString());
    }
  }
  // 如果 filteredList 仍然为空，返回 list1 的第一个元素
  if (filteredList.isEmpty) {
    return list1[0];
  }
  // 否则返回 filteredList 的第一个元素
  return filteredList[0];
}

class CustomEQControlBtn extends StatelessWidget {
  const CustomEQControlBtn({
    super.key,
    required this.title,
    this.onTap,
    this.isLoading = false
  });

  final Function? onTap;
  final String title;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;
    final loading = false.obs;

    return Obx(()=> AbsorbPointer(
      absorbing: !(device.eqIndex > 9),
      child: OnTapScaleToSmallBox(
        onTap: () async {
          loading.value = true;
          if(onTap != null) onTap!();
          await Future.delayed(const Duration(milliseconds: 600));
          loading.value = false;
        },
        child: Container(
          alignment: Alignment.center,
          width: 52.sp,
          height: 28.sp,
          margin: EdgeInsets.only(left: 6.sp, right: 4.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6.r),
            color: themeBase.primaryColor.value.withOpacity((device.eqIndex > 9)? 1 : 0.5)
          ),
          child: loading.value && isLoading?
            const ColumnSaveLoad() : AText(
            text: title, // 预设
            color: themeBase.textColor1.value,
            size: themeBase.subBodyFont.value - 4.sp,
          )
        ),
      )),
    );
  }
}

//加载指示器
class ColumnSaveLoad extends StatelessWidget {
  const ColumnSaveLoad({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 16.w,
      height: 16.w,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(themeBase.textColor1.value),
        strokeWidth: 1.8.sp,
      ),
    );
  }
}

class TestButton extends StatelessWidget {
  const TestButton({super.key});

  @override
  Widget build(BuildContext context) {
    return OnTapScaleToSmallBox(
      onTap: () async {
        BaseAudio device = homCon.selectDevice as BaseAudio;
        // device.sendMsgToSocket('MCU+PAS+RAKOIT:CEQ:SAV:1:22&');
        // device.sendMsgToSocket('MCU+PAS+RAKOIT:CEQ:LST&');
        Log.d(device.customEQList);
      },
      child: Container(
        height: 34.sp,
        color: Colors.red,
        child: AText(
          text: 'click',
          color: themeBase.textColor1.value,
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/sound/components/new_slider_track_shape.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class Intensity extends StatelessWidget {
  const Intensity({super.key});

  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;

    return Column(
      children: [
         Obx(() => AbsorbPointer(
          // 根据重低音的开关来决定是否阻止点击事件
          absorbing: !device.deepBass.value,  
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            // 根据重低音的开关来显示透明度
            opacity: !device.deepBass.value?0.6:1,  
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 14.sp),
              child: Flex(
                direction: Axis.horizontal,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.only(
                      left: 3.sp
                    ),
                    child: Obx(()=> AText(
                      text: 'intensity'.tr,
                      color: themeBase.primaryColor.value,
                      size: themeBase.subBodyFont.value
                    )),
                  ),
                  Obx(() => AText(
                    text: '${device.intensity.value.toStringAsFixed(0)}%',  
                    color: themeBase.primaryColor.value,
                    size: themeBase.subBodyFont.value
                  )),
                ],
              ),
            ),
          ),
        )),
        Obx(() => AbsorbPointer(
          absorbing: !device.deepBass.value,  
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            // 根据重低音的开关来显示透明度
            opacity: !device.deepBass.value?0.6:1,  
            child: SizedBox(
              height: 28.h,
              child: Obx(()=>SliderTheme(
                data: SliderThemeData(
                  activeTrackColor: themeBase.primaryColor.value,
                  inactiveTrackColor: themeBase.primaryColor.value.withOpacity(.2),
                  thumbColor: themeBase.primaryColor.value,
                  overlayColor: themeBase.primaryColor.value.withAlpha(40),
                  activeTickMarkColor: themeBase.primaryColor.value,
                  inactiveTickMarkColor: themeBase.primaryColor.value,
                  overlayShape: RoundSliderOverlayShape(
                    overlayRadius: 18.sp// 设置滑块覆盖层大小
                  ),
                  trackHeight: 4.sp,
                  trackShape: const NewSliderTrackShape(),
                  thumbShape: RoundSliderThumbShape(
                    enabledThumbRadius: 7.sp, // 设置thumb的半径
                  ),
                ),
                child: Obx(() => Slider(
                  min: 0,
                  max: 100,
                  value: device.intensity.value,  
                  // divisions: 5,
                  onChanged: (value) => device.intensity.value = value,  
                  onChangeEnd: (value){
                    device.setIntensity(value);
                  },
                )),
              )),
            ),
          ),
        )),
      ],
    );
  }
}
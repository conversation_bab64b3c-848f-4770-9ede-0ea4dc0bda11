import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class SliderSound extends StatelessWidget {
  const SliderSound({
    super.key,
    required this.name,
    required this.sliderVal,

    required this.sendVal,
    required this.id
  });
  final String name;
  final RxDouble sliderVal;

  final void Function(double) sendVal;
  final String id;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.sp),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 18.sp),
            child: Flex(
              direction: Axis.horizontal,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() => AText(
                  text: name,
                  color: themeBase.primaryColor.value,
                  size: themeBase.subBodyFont.value
                )),
                Obx(() => AText(
                  text: '${sliderVal.value.toStringAsFixed(0)}${id == 'balance'?'%':'dB'}',  
                  color: themeBase.primaryColor.value,
                  size: themeBase.subBodyFont.value
                ))
              ],
            ),
          ),
          SizedBox(
            height: 28.h,
            child: Obx(()=>SliderTheme(
              data: SliderThemeData(
                activeTrackColor: themeBase.primaryColor.value,
                inactiveTrackColor: themeBase.primaryColor.value.withOpacity(.2),
                thumbColor: themeBase.primaryColor.value,
                overlayColor: themeBase.primaryColor.value.withAlpha(40),
                overlayShape: RoundSliderOverlayShape(
                  overlayRadius: 18.sp,// 设置滑块覆盖层大小,
                ),
                trackHeight: 4.sp,
                trackShape:  BalanceShape(
                  fillColor: themeBase.primaryColor.value,
                  trackColor: themeBase.primaryColor.value.withOpacity(.2)
                ),
                thumbShape: RoundSliderThumbShape(
                  enabledThumbRadius: 7.sp, // 设置thumb的半径
                ),
              ),
              child: Obx(() => Slider(
                min: id == 'balance'?-100:-10,
                max: id == 'balance'?100:10,
                value: sliderVal.value,
                onChanged: (value) {
                  sliderVal.value = value.ceil().toDouble();
                  // Log.e(sliderVal.value);
                },
                onChangeEnd: sendVal,
              )),
            )),
          )
        ],
      ),
    );
  }
}

class BalanceShape extends SliderTrackShape with BaseSliderTrackShape {
  final Color fillColor;
  final Color trackColor;

  BalanceShape({
    required this.fillColor,
    required this.trackColor,
  });

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? startThumbCenter,
    Offset? endThumbCenter,
    bool isDiscrete = false,
    bool isEnabled = false,
    Offset? secondaryOffset,
    double additionalActiveTrackHeight = 2,
  }) {
    final Size size = parentBox.size;
    final double thumbRadius = sliderTheme.thumbShape!.getPreferredSize(false, false).width / 4;
    final Rect fullTrackSegment = Rect.fromLTRB(
      offset.dx + thumbRadius * 4 ,
      offset.dy + size.height / 2 - sliderTheme.trackHeight! / 2,
      size.width + offset.dx - thumbRadius * 4,
      offset.dy + size.height / 2 + sliderTheme.trackHeight! / 2,
    );

    final Rect fillTrackSegment = Rect.fromLTRB(
      size.width / 2 + offset.dx ,
      offset.dy + size.height / 2 - sliderTheme.trackHeight! / 2,
      thumbCenter.dx,
      offset.dy + size.height / 2 + sliderTheme.trackHeight! / 2,
    );                                

    final Paint fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill
      ..strokeWidth = 1.0;

    final Paint trackPaint = Paint()
      ..color = trackColor
      ..style = PaintingStyle.fill
      ..strokeWidth = 1.0;
          
    // Paint the full track 
    context.canvas.drawRRect(
      RRect.fromRectAndRadius(fullTrackSegment, Radius.circular(sliderTheme.trackHeight!)),
      trackPaint,
    );

    // Paint the filled portion of the track
    context.canvas.drawRRect(
      RRect.fromRectAndRadius(fillTrackSegment, Radius.circular(sliderTheme.trackHeight!)),
      fillPaint,
    );
  }
}


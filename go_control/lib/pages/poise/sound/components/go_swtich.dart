import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/theme/theme.dart';

class GoSwtich extends StatelessWidget {
  const GoSwtich ({
    super.key,
    required this.switchVal,
    this.tap,
    this.width,
    this.height,
    this.thumeSize,
    this.rever = false
  });
  final Function? tap;  //点击开关的回调函数
  final RxBool switchVal;  //控制开关状态
  final double? width;  //控件的宽度
  final double? height;  //控件的高度
  final double? thumeSize;  //开关滑块大小
  final bool rever;  //是否反转开关

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        //点击时，切换开关状态
        switchVal.value = !switchVal.value;
        if (tap != null) tap!();
      },
      child: SizedBox(
        width: width ?? 48.sp,
        height: height ?? 24.sp,
        child: Obx(()=>Stack(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 220),
              curve: Curves.easeInOut,
              width: width ?? 51.sp,
              height: height ?? 24.sp,
              decoration: BoxDecoration(
                color: 
                rever?
                themeBase.primaryColor.value.withOpacity(switchVal.value?0.2:1):
                themeBase.primaryColor.value.withOpacity(switchVal.value?1:0.2)
                ,
                borderRadius: BorderRadius.circular(width ?? 51.sp),
              ),
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 180),
              curve: Curves.easeInOut,
              width: thumeSize ?? 18.sp,
              height: thumeSize ?? 18.sp,
              margin: EdgeInsets.all(3.sp),
              decoration: BoxDecoration(
                color: themeBase.textColor1.value,
                borderRadius: BorderRadius.circular(width ?? 51.sp)
              ),
              transform: rever?
                Matrix4.translationValues(switchVal.value?0:(width ?? 48.sp) - ((thumeSize ??  20.sp) + 4.sp),switchVal.value?0:0, 0):
                Matrix4.translationValues(switchVal.value?(width ?? 48.sp) - ((thumeSize ??  20.sp) + 4.sp):0, 0, 0),
            )
          ],
        )),
      ),
    );
  }
}
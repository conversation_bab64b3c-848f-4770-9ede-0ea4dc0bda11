import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/sound/components/go_swtich.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class DeepBass extends StatelessWidget {
  const DeepBass({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.sp),
      margin: EdgeInsets.only(top: 9.sp,bottom: 8.sp),
      child: Flex(
        direction: Axis.horizontal,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: EdgeInsets.only(
              left: 3.sp
            ),
            child: Obx(()=> AText(
              text: 'deep_bass'.tr,
              color: themeBase.primaryColor.value,
              size: themeBase.subBodyFont.value
            )),
          ),
          GoSwtich(
            switchVal: device.deepBass,
            tap: (){
              //点击时发送消息：开启或者关闭的消息
              if(device.deepBass.value){
                device.setDeepBass(true);
              }else{
                device.setDeepBass(false);
              }
            },
          )
        ],
      ),
    );
  }
}
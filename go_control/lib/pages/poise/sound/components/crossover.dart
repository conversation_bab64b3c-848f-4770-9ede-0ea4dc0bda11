import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/poise/sound/components/go_swtich.dart';
import 'package:gocontrol/pages/poise/sound/components/new_slider_track_shape.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:gocontrol/components/atext.dart';

class Crossover extends StatelessWidget {
  const Crossover({super.key});

  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;
    
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 14.sp),
          margin: EdgeInsets.only(bottom: 9.sp),
          child: Flex(
            direction: Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: EdgeInsets.only(
                  left: 3.sp
                ),
                child: Obx(()=> AText(
                  text: 'crossover'.tr,
                  color: themeBase.primaryColor.value,
                  size: themeBase.subBodyFont.value
                )),
              ),
              GoSwtich(
                //控制开关 打开时才能控制频率
                switchVal: device.crossover,
                tap: (){
                  if(device.crossover.value){
                    device.setCrossover(true);
                  }else{
                    device.setCrossover(false);
                  }
                  //点击时发送消息：开启或者关闭的消息
                },
              )
            ],
          ),
        ),
        Obx(() => AbsorbPointer(
          //当开关状态为false时，阻止点击事件，为true时，可以触发点击事件
          absorbing: !device.crossover.value, 
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            //根据开关的状态来深圳透明度
            opacity: !device.crossover.value?0.6:1,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 14.sp),
              child: Flex(
                direction: Axis.horizontal,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.only(
                      left: 3.sp
                    ),
                    child: Obx(()=> AText(
                      text:'frequency'.tr,  
                      color: themeBase.primaryColor.value,
                      size: themeBase.subBodyFont.value
                    )),
                  ),
                  Obx(() => AText(
                    text: '${device.frequency.value.toStringAsFixed(0)}.Hz',
                    color: themeBase.primaryColor.value,
                    size: themeBase.subBodyFont.value
                  )),
                ],
              ),
            ),
          ),
        )),
        Obx(() => AbsorbPointer(
          absorbing: !device.crossover.value,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: !device.crossover.value?0.6:1,
            child: SizedBox(
              height: 28.h,
              child: Obx(()=>SliderTheme(
                //滑块的样式
                data: SliderThemeData(
                  activeTrackColor: themeBase.primaryColor.value,
                  inactiveTrackColor: themeBase.primaryColor.value.withOpacity(.2),
                  thumbColor: themeBase.primaryColor.value,
                  overlayColor: themeBase.primaryColor.value.withAlpha(40),
                  activeTickMarkColor: themeBase.primaryColor.value,
                  inactiveTickMarkColor: themeBase.primaryColor.value,
                  overlayShape: RoundSliderOverlayShape(
                    overlayRadius: 18.sp// 设置滑块覆盖层大小
                  ),
                  trackHeight: 4.sp,
                  trackShape: const NewSliderTrackShape(),  
                  thumbShape: RoundSliderThumbShape(
                    enabledThumbRadius: 7.sp, // 设置thumb的半径
                  ),
                ),
                child: Obx(() => Slider(
                  min: 50,
                  max: 300,
                  value: device.frequency.value,  
                  // divisions: 5,
                  onChanged: (value){
                    device.frequency.value = value;  
                  },
                  //值发生变化时发送消息
                  onChangeEnd: (value){
                    device.setFrequency(value);
                  },
                )),
              )),
            ),
          ),
        )),
      ],
    );
  }
}


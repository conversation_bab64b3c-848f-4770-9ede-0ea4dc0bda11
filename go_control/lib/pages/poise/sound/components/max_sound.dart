import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/pages/poise/sound/components/new_slider_track_shape.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class MaxSlidersound extends StatelessWidget {
  const MaxSlidersound({
    super.key,
    required this.name,
    required this.sliderVal,
    required this.change,
    this.sendVal
  });
  final String name;
  final RxDouble sliderVal;
  final Function(double)? change;
  final Function(double)? sendVal;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.sp),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 18.sp),
            child: Flex(
              direction: Axis.horizontal,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() => AText(
                  text: name,
                  color: themeBase.primaryColor.value,
                  size: themeBase.subBodyFont.value
                )),
                Obx(() => AText(
                  text: '${sliderVal.value.toStringAsFixed(0)}%',
                  color: themeBase.primaryColor.value,
                  size: themeBase.subBodyFont.value
                ))
              ],
            ),
          ),
          SizedBox(
            height: 28.h,
            child: Obx(()=>SliderTheme(
              data: SliderThemeData(
                activeTrackColor: themeBase.primaryColor.value,
                inactiveTrackColor: themeBase.primaryColor.value.withOpacity(.2),
                thumbColor: themeBase.primaryColor.value,
                overlayColor: themeBase.primaryColor.value.withAlpha(40),
                overlayShape: RoundSliderOverlayShape(
                  overlayRadius: 18.sp, // 设置滑块覆盖层大小
                ),
                trackHeight: 4.sp,
                trackShape: const NewSliderTrackShape(),  
                thumbShape: RoundSliderThumbShape(
                  enabledThumbRadius: 7.sp, // 设置thumb的半径
                ),
              ),
              child: Obx(() => Slider(
                min: 30,
                max: 100,
                value: sliderVal.value,
                onChanged: change,
                onChangeEnd: sendVal,
              )),
            )),
          )
        ],
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

//自定义类
class Diyclass {
  // 自定义弹窗
  static void diyPop({
    required String title, //弹窗标题
    required String text,  //弹窗文本内容
    bool read = false,  //是否包含取消按钮
    Function? cancelFn,  //点击取消按钮时的回调函数
    Function? confirmFn,  //点击确认按钮时的回调函数
    Function? timeOutFn,  //点击超时按钮时的回调函数
    Widget? diyContent,  //自定义内容
    RxBool? disable,  //是否禁用确认按钮
    RxBool? timeOut  //控制是否显示超时按钮
  }){
    List<Widget> list = [
      Expanded(
        flex: 1,
        child: Container(
          height: 38.sp,
          margin: EdgeInsets.only(left: !read?0:8.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.sp),
          ),
          child: ElevatedButton(
            onPressed: (){
              // 判断确认按钮是否为空
              if(confirmFn != null){
                // 判断确认按钮是否被禁用
                if(disable != null && disable.value) return;
                confirmFn();
              }else{
                Get.back();
              }
            }, 
            style: ButtonStyle(
              elevation: const WidgetStatePropertyAll(0),
              shape: WidgetStatePropertyAll(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.sp)
                ),
              ),
              backgroundColor: WidgetStatePropertyAll(themeBase.primaryColor.value)
            ),
            //确定按钮
            child: Text(
              'confirm'.tr,  //确认
              style: TextStyle(
                fontSize: 15.5.sp,
                color: themeBase.textColor1.value
              ),
            )
          ),
        ),
      )
    ];
    // 如果包含取消按钮，添加取消按钮
    if(read){
      list.insert(
        0,
        Expanded(
          flex: 1,
          child: Container(
            margin: EdgeInsets.only(right: !read?0:8.sp),
            height: 38.sp,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.sp),
            ),
            child: ElevatedButton(
              onPressed: (){
                Get.back();
                if(cancelFn!=null) cancelFn();
              }, 
              style: ButtonStyle(
                elevation: const WidgetStatePropertyAll(0),
               shape: WidgetStatePropertyAll(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.sp)
                  )
                ),
                backgroundColor: WidgetStatePropertyAll(themeBase.textColor2.value.withOpacity(.5))
              ),
              child: Text(
                'cancel'.tr,  //取消
                style: TextStyle(
                  fontSize: 15.5.sp,
                  color: themeBase.textColor1.value
                ),
              )
            ),
          ),
        )
      );
    }
    //调用Dialog
    Get.defaultDialog(
      //是否可以通过点击背景关闭弹窗
      barrierDismissible: !read,
      title: title,
      backgroundColor: themeBase.textColor1.value,
      titleStyle: TextStyle(
        fontSize: 18.sp,
        color: themeBase.primaryColor.value,
      ),
      // 标题的内边距
      titlePadding: EdgeInsets.only(
        top: diyContent != null ?22.sp:22.sp,
      ),
      content: Column(
        children: [
          SizedBox(height: diyContent == null ? 0.sp: 8.h),
          Container(
            child: diyContent ?? AText(
              text: text,
              textAlign: TextAlign.center,
              color: themeBase.primaryColor.value.withOpacity(.5),
              softWrap: true,
              size: themeBase.subBodyFont.value -1,
            ),
          ),
          SizedBox(height: diyContent == null ? 14.sp: 8.sp),
          timeOut == null? Flex(
              direction: Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: list,
          ) : Obx(() => Container(
            width: double.maxFinite,
            height: 35.sp,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.sp)
            ),
            child: ElevatedButton(
              style: ButtonStyle(
                backgroundColor: WidgetStatePropertyAll(themeBase.primaryColor.value),
                elevation: const WidgetStatePropertyAll(0),
                shape: WidgetStatePropertyAll(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.sp),
                  )
                )
              ),
              onPressed: (){
                if(timeOutFn != null) timeOutFn();
                // Get.back();
              },
              child: AText(
                text: 'cancel'.tr,
                size: themeBase.subBodyFont.value +0.5,
                color: themeBase.primaryColor.value
              ),
            ),
          ))
        ],
      ),
      // 内容的内边距
      contentPadding: EdgeInsets.only(
        top: 8.sp,
        left: 22.sp,
        right: 22.sp,
      )
    );
  }      
}


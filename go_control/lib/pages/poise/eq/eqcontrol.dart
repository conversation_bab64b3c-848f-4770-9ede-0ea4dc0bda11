import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
final EqCon eqCon = Get.put(EqCon());

class EqCon extends GetxController {
  final RxString myEqname = RxString('Not selected');
  final RxInt peqIndex = RxInt(-1); 
  final RxList<Map> eqlist = RxList([]);

  final RxBool customEq = RxBool(false);
  final RxList<Map> customEqList = RxList([]);
  final RxInt customEqindex = RxInt(-1);
  final RxBool saveLoad = RxBool(false);

  final List customList = [
    {
      "freq": '125',
      "gain": RxDouble(0)
    },
    {
      "freq": '250',
      "gain": RxDouble(0)
    },
    {
      "freq": '500',
      "gain": RxDouble(0)
    },
    {
      "freq": '1000',
      "gain": RxDouble(0)
    },
    {
      "freq": '2000',
      "gain": RxDouble(0)
    },
    {
      "freq": '4000',
      "gain": RxDouble(0)
    },
    {
      "freq": '8000',
      "gain": RxDouble(0)
    },
    {
      "freq": '16000',
      "gain": RxDouble(0)
    }
  ];

  final List eqList = [
    'Flat'.tr,  //默认
    'Classical'.tr,  //古典
    'Pop'.tr,  //流行
    'Jazz'.tr,  //爵士
    'Rock'.tr,  //摇滚
    'Vocal'.tr,  //人声
  ];

  void returnEqList() {
    peqIndex.value = 0;
    for(int i = 0; i < eqList.length; i++) {
      eqlist.add({
        'id':i,
        'name':eqList[i]
      });
    }

  }


  void customListReset(){
    for (var el in customList) {
      el['gain'].value = 0.0;
    }
  }

  // void setEqlist(String str,{bool?custom}) async {
  //   List list = str.split(',');
  //   if(custom != null && custom){
  //     customEqList.clear();
  //     for(var item in list) {
  //       customEqList.add({
  //         'id': int.parse(item.substring(0,item.indexOf('@'))),
  //         'name': eqName(item),
  //       });
  //     }
  //   }else{
  //     eqlist.clear();
  //     for(var item in list) {
  //       eqlist.add({
  //         'id': int.parse(item.substring(0,item.indexOf('@'))),
  //         'name': eqName(item),
  //       });
  //     }
  //     Log.h('当前得到的预设列表 -- $eqlist');
  //   }
  // }

  String eqName(String item){
    try{
      return item.substring(item.indexOf('@') + 1,item.length).tr;
    }catch(e){
      return item.substring(item.indexOf('@') + 1,item.length);
    }
  }

  String getName(BaseAudio device){
    if(device.eqList.isNotEmpty){
      if(device.eqIndex.value < device.eqList.length){
        return device.eqList[device.eqIndex.value]['name'];
      }else{
        return 'custom_eq'.tr;
      }
    }else{
      return 'no_select'.tr;
    }
    // if(peqIndex.value != -1 && eqlist.isNotEmpty){
    //   try{
    //     String name = '';
    //     for (var el in eqlist) {
    //       if(el['id'] == peqIndex.value){
    //         name = el['name'];
    //       }
    //     }
    //     return name;
    //   }catch(_){
    //     return 'no_select'.tr;
    //   }
    // }else if(peqIndex.value == -1 && customEqList.isNotEmpty){
    //   return 'custom_eq'.tr;
    // }else{
    //   return 'no_select'.tr;
    // }
  }


}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class SliderEQ extends StatelessWidget {
  final double value;
  final ValueChanged<double> onChanged;
  final ValueChanged<double> onChangeEnd;
  final double min;
  final double max;
  final String name;

  const SliderEQ({
    super.key,
    required this.value,
    required this.onChanged,
    required this.onChangeEnd,
    required this.name ,
    this.min = 0,
    this.max = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //滑动区域
        SizedBox(
          width: 38.sp,
          height: 240.sp,
          child: RotatedBox(
            quarterTurns: 3,  //旋转的角度  3表示顺时针旋转90
            child: Obx(() => SliderTheme(
              data: SliderThemeData(
                overlayShape: RoundSliderOverlayShape(
                  overlayRadius: 15.sp// 设置滑块覆盖层大小，
                ),
                overlayColor: themeBase.primaryColor.value.withAlpha(40),
                activeTrackColor: themeBase.primaryColor.value,  //滑块经过的颜色
                inactiveTrackColor: themeBase.primaryColor.value.withOpacity(.2),  //滑块未经过的颜色
                thumbColor: themeBase.primaryColor.value,  //滑块的颜色
                thumbShape: RoundSliderThumbShape(  //滑块的形状
                  enabledThumbRadius: 7.sp, // 设置thumb的半径
                ),
                trackShape:  BalanceShape(  //轨道的形状
                  fillColor: themeBase.primaryColor.value,
                  trackColor: themeBase.primaryColor.value.withOpacity(.2)
                ),
                trackHeight: 4.sp,
              ),
              child: Slider(
                value: value,
                min: min,
                max: max,
                onChanged: onChanged,
                onChangeEnd: onChangeEnd
              ),
            ))
          ),
        ),
        //分贝
        Obx(() => AText(
          text: '${value.toStringAsFixed(0) == '-0'?'0':value.toStringAsFixed(0)}dB',
          size: themeBase.subBodyFont2.value,
          color: themeBase.primaryColor.value
        )),
        //频率
        Obx(() => AText(
          text: name,
          size: 9.sp,
          color: themeBase.primaryColor.value.withAlpha(150)
        ))
      ],
    );
  }
}

class BalanceShape extends SliderTrackShape with BaseSliderTrackShape {
  final Color fillColor;
  final Color trackColor;

  BalanceShape({
    required this.fillColor,
    required this.trackColor,
  });

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? startThumbCenter,
    Offset? endThumbCenter,
    bool isDiscrete = false,
    bool isEnabled = false,
    Offset? secondaryOffset,
    double additionalActiveTrackHeight = 2,
  }) {
    final Size size = parentBox.size;
    final double thumbRadius = sliderTheme.thumbShape!.getPreferredSize(false, false).width / 4;
    final Rect fullTrackSegment = Rect.fromLTRB(
      offset.dx + thumbRadius * 4 ,
      offset.dy + size.height / 2 - sliderTheme.trackHeight! / 2,
      size.width + offset.dx - thumbRadius * 4,
      offset.dy + size.height / 2 + sliderTheme.trackHeight! / 2,
    );

    final Rect fillTrackSegment = Rect.fromLTRB(
      size.width / 2 + offset.dx ,
      offset.dy + size.height / 2 - sliderTheme.trackHeight! / 2,
      thumbCenter.dx,
      offset.dy + size.height / 2 + sliderTheme.trackHeight! / 2,
    );                                

    final Paint fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill
      ..strokeWidth = 1.0;

    final Paint trackPaint = Paint()
      ..color = trackColor
      ..style = PaintingStyle.fill
      ..strokeWidth = 1.0;
          
    // Paint the full track 
    context.canvas.drawRRect(
      RRect.fromRectAndRadius(fullTrackSegment, Radius.circular(sliderTheme.trackHeight!)),
      trackPaint,
    );

    // Paint the filled portion of the track
    context.canvas.drawRRect(
      RRect.fromRectAndRadius(fillTrackSegment, Radius.circular(sliderTheme.trackHeight!)),
      fillPaint,
    );
  }
}


import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class PreEq extends StatelessWidget {
  const PreEq({super.key});
  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;

    return Obx(() => Container(
      height: 300.sp,
      padding: EdgeInsets.symmetric(
        vertical: 18.sp,
        horizontal: 24.sp
      ),
      decoration: BoxDecoration(
        color: themeBase.textColor1.value,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(25.sp)
        )
      ),
      child: SizedBox(
        height: 240.sp,
        child: Obx(()=> ListView.builder(
          itemBuilder: (context, index){
            return Column(
              children: [
                Visibility(
                  visible: index == 0,  
                  child: Container(
                    alignment: Alignment.centerLeft,
                    margin: EdgeInsets.only(bottom: 14.sp),
                    child: Obx(()=> AText(
                      text: 'eq_list'.tr,  //预设列表
                      color: themeBase.primaryColor.value,
                      size: themeBase.subBodyFont.value + 2.sp
                    )),
                  ),
                ),
                Obx(() => AbsorbPointer(
                  //当预设均衡器列表的id等于预设均衡器索引时
                  absorbing: _getSelected(device,index),  
                  child: GestureDetector(
                    //点击预设均衡器名称时发送相对应的消息和操作
                    onTap: (){
                      device.eqIndex.value = index;
                      device.setEqIndex(index);
                    },
                    child: Container(
                      height: 38.sp,
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.sp
                      ),
                      margin: EdgeInsets.only(bottom: 8.sp,left: 2.sp,right: 2.sp),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.sp),
                        color: _getSelected(device,index)?
                          themeBase.primaryColor.value:
                          themeBase.primaryColor.value.withAlpha(110),
                      ),
                      child: Obx(()=> AText(
                        // 预选均衡器的名称
                        text: '${device.eqList[index]['name']}'.tr,  
                        color: themeBase.textColor1.value,
                        size: 14.sp
                      )),
                    )
                  ),
                ))
              ],
            );
          },
          itemCount: device.eqList.length,
        ))
      ),
    ));
  }
}

bool _getSelected(BaseAudio device,int index){
  if(device.eqList.isNotEmpty){
    if(device.eqIndex.value < device.eqList.length){
      return device.eqList[index]['id'] == device.eqIndex.value;
    }else{
      return false;
    }
  }else{
    return false;
  }
}

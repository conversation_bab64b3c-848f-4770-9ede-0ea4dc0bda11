import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/eq/components/pre_eq.dart';
import 'package:gocontrol/pages/poise/eq/eqcontrol.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';

class MyPEQbody extends StatelessWidget {
  const MyPEQbody({super.key});

  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;

    return Column(
      children: [
        Container(
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(left: 18.sp,right: 18.sp,top: 18.sp),
          child: Obx(()=> AText(
            text: 'preset_eq'.tr, // 预设
            color: themeBase.primaryColor.value,
            size: themeBase.subBodyFont.value,
          )),
        ),
        GestureDetector(
          //点击下滑出现预设均衡器列表
          onTap: (){
            // 判断预选均衡器列表是否为空
            if(device.eqList.isNotEmpty){
              Get.bottomSheet(const PreEq());
            }
          },
          child: Obx(()=>Container(
            width: double.maxFinite,
            height: 42.sp,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: 14.sp),
            margin: EdgeInsets.only(top: 6.sp,left: 14.sp,right: 14.sp),
            decoration: BoxDecoration(
              color: themeBase.primaryColor.value,
              borderRadius: BorderRadius.circular(12.sp)
            ),
            child: Flex(
              direction: Axis.horizontal,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 预选均衡器名字
                Obx(() => AText(
                  text: eqCon.getName(device).tr,
                  color: themeBase.textColor1.value,
                  size: themeBase.subBodyFont.value
                )),
                // 下拉图标
                Obx(() => Visibility(
                  visible: device.eqList.isNotEmpty,  
                  child: Icon(
                    const IconData(0xe648,fontFamily:'IconFont'),
                    color: themeBase.textColor1.value,
                    size: 19.sp,
                  ),
                ))
              ],
            ),
          )),
        )
      ],
    );
  }
}



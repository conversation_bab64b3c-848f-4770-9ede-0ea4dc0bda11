import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/poise/eq/components/pre_body.dart';
import 'package:gocontrol/pages/poise/sound/components/crossover.dart';
import 'package:gocontrol/pages/poise/sound/components/deepbass.dart';
import 'package:gocontrol/pages/poise/sound/components/intensity.dart';
import 'package:gocontrol/pages/poise/sound/components/max_sound.dart';
import 'package:gocontrol/pages/poise/sound/components/slider_sound.dart';
import 'package:gocontrol/pages/poise/sound/custom_eqbody.dart';
import 'package:gocontrol/theme/theme.dart';


class PoiseBody extends StatelessWidget {
  const PoiseBody({super.key});

  @override
  Widget build(BuildContext context) {
    BaseAudio device = homCon.selectDevice as BaseAudio;
    if (homCon.selectDevice is Libre) {
      return SingleChildScrollView(
        child: Obx(() => Container(
          margin: EdgeInsets.only(bottom: 16.sp),
          padding: EdgeInsets.symmetric(horizontal: 4.sp,vertical: 9.sp),
          decoration: BoxDecoration(
            color: themeBase.textColor1.value,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: Column(
            children: [
              SizedBox(height: 12.sp,),
              SliderSound(
                name:'treble'.tr,
                id:'treble',
                sliderVal: device.treble,  
                //发生变化时发送消息
                sendVal: (value){
                  device.setTreble(value);
                }
              ),
              SliderSound(
                name:'mid'.tr,
                id:'mid',
                sliderVal: device.mid,  
                //发生变化时发送消息
                sendVal: (value){
                  device.setMid(value);
                }
              ),
              // SliderSound(
              //   name: 'bass'.tr,
              //   id:'bass',
              //   sliderVal: device.bass,
              //   //发生变化时发送消息
              //   sendVal: (value){
              //     // 设置bass的值
              //     device.setBass(value);
              //   }
              // ),
              // SliderSound(
              //   name: 'balance'.tr,
              //   id:'balance',
              //   sliderVal: device.balance,
              //   //发生变化时发送消息
              //   sendVal: (value){
              //     device.setBalance(value);
              //   }
              // ),
              // MaxSlidersound(
              //   name: 'max_volume'.tr, 
              //   sliderVal: device.maxVolume,  
              //   change: (value){
              //     device.maxVolume.value = value.roundToDouble();  
              //   },
              //   //发生变化时发送消息
              //   sendVal: (value){
              //     device.setMaxVolume(value);
              //   },
              // ),
              // // 频率
              // const Crossover(),
              // // 重低音
              // const DeepBass(),
              // const Intensity(),
              // const MyPEQbody(),
              // const CustomEQbody(),
            ],
          )
        )),
      );
    } else {
      return SingleChildScrollView(
        child: Obx(() => Container(
          margin: EdgeInsets.only(bottom: 16.sp),
          padding: EdgeInsets.symmetric(horizontal: 4.sp,vertical: 18.sp),
          decoration: BoxDecoration(
            color: themeBase.textColor1.value,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: Column(
            children: [
              SizedBox(height: 6.sp,),
              SliderSound(
                name:'treble'.tr,
                id:'treble',
                sliderVal: device.treble,  
                //发生变化时发送消息
                sendVal: (value){
                  device.setTreble(value);
                }
              ),
              SliderSound(
                name:'mid'.tr,
                id:'mid',
                sliderVal: device.mid,  
                //发生变化时发送消息
                sendVal: (value){
                  device.setMid(value);
                }
              ),
              SliderSound(
                name: 'bass'.tr,
                id:'bass',
                sliderVal: device.bass,
                //发生变化时发送消息
                sendVal: (value){
                  // 设置bass的值
                  device.setBass(value);
                }
              ),
              SliderSound(
                name: 'balance'.tr,
                id:'balance',
                sliderVal: device.balance,
                //发生变化时发送消息
                sendVal: (value){
                  device.setBalance(value);
                }
              ),
              MaxSlidersound(
                name: 'max_volume'.tr, 
                sliderVal: device.maxVolume,  
                change: (value){
                  device.maxVolume.value = value.roundToDouble();  
                },
                //发生变化时发送消息
                sendVal: (value){
                  device.setMaxVolume(value);
                },
              ),
              // 频率
              const Crossover(),
              // 重低音
              const DeepBass(),
              const Intensity(),
              const MyPEQbody(),
              const CustomEQbody(),
            ],
          )
        )),
      );
    }
    
  }
}
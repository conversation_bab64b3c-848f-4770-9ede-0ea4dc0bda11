import 'dart:async';
import 'dart:convert';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/lock.dart';
import 'package:gocontrol/common/net_api.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:upnp2/upnp.dart';
import 'package:xml2json/xml2json.dart';

// 实例化对象
final NASRadio nasRadio = () {
  if (Get.isRegistered<NASRadio>()) {
    return Get.find<NASRadio>();
  } else {
    return Get.put(NASRadio());
  }
}();

class NASRadio extends GetxController {

  /// 用于发现UPnP设备的实例。
  final disc = DeviceDiscoverer();
  /// 用于监听设备发现事件的流订阅。
  StreamSubscription<DiscoveredClient>? _discListener;
  /// 用于控制设备发现操作的锁，确保同一时间只有一个发现操作在进行。
  final Lock _lock = Lock();
  /// 用于控制获取内容操作的锁，确保同一时间只有一个获取内容操作在进行。
  final Lock lock2 = Lock();

  // 定义搜索的UPnP设备类型，这里是媒体服务器
  final String query = 'urn:schemas-upnp-org:device:MediaServer:1';
  // 定义搜索的UPnP服务类型，这里是媒体服务器的内容目录服务
  final String serviceType = 'urn:schemas-upnp-org:service:ContentDirectory:1';
  // 搜索到的upnp服务器
  final services = RxList<NASservice>([]);
  // 当前选择的服务器
  NASservice? selectService;

  // 目录A
  RxList<SubContent> mllsA = RxList<SubContent>([]);
  //
  RxBool mllsAisEmpty = false.obs;
  // 数据池
  final List<List<SubContent>> dataPool = [];

  final RxBool showSort = false.obs;

  final RxInt currentSort = 0.obs;
  
  /// 发现附近的UPnP设备。
  ///
  /// 该方法通过 `DeviceDiscoverer` 实例启动设备发现流程，并监听返回的设备信息。
  /// 对于每个发现的设备，执行以下操作：
  /// 1. 获取设备详细信息 (`Device`) 和指定的服务类型 (`Service`)。
  /// 2. 如果设备或服务为空，则跳过处理。
  /// 3. 检查设备是否已存在于 `services` 列表中，若存在则跳过添加。
  /// 4. 检查设备名称是否为空，若为空则跳过添加。
  /// 5. 创建一个新的 `NASservice` 实例，包含设备名称、设备对象和服务对象。
  /// 6. 将新创建的 `NASservice` 添加到 `services` 列表中。
  ///
  /// 异常处理：如果在处理过程中发生错误，会记录错误日志。
  ///
  /// 注意：此方法会在 `_discListener` 中持续监听设备发现事件，直到手动取消订阅。
  Future<void> discoverClients() async {
    if (_lock.isUsing) {
      return;
    }

    _lock.mutex(() async {
      // 启动设备发现，禁用IPv6
      await disc.start(ipv6: false);

      // 记录开始发现设备的日志
      Log.d('开始UPnP设备发现...');
      // 发现符合指定查询条件的设备，并监听发现的设备信息
      _discListener = disc.quickDiscoverClients(query: query).listen((client) async {
        try {
          // 获取设备详细信息
          final Device? dev = await client.getDevice();
          // 如果设备信息为空，则跳过处理
          if (dev == null) return;
          // 获取设备的指定服务
          final Service? ser = await dev.getService(serviceType);
          // 如果服务信息为空，则跳过处理
          if (ser == null) return;

          // 记录发现的设备名称
          Log.r('find [${dev.friendlyName}]');

          // 获取已存在的服务名称列表
          final sevs = services.map((ud) => ud.name).toList();
          // 检查设备是否已存在于服务列表中
          if (sevs.contains(dev.friendlyName)) {
            // 若存在，则记录日志并跳过添加
            Log.d('服务 ${dev.friendlyName} 已存在，跳过添加');
            return;
          }

          // 检查设备名称是否为空
          if (dev.friendlyName == null) return;

          // 创建新的NASservice实例
          final nasService = NASservice(
            name: dev.friendlyName!,
            device: dev,
            service: ser,
          );

          // 添加服务
          services.add(nasService);
        } catch (e) {
          // 异常信息
          Log.e(e);
        }
      });
      // 等待3秒关闭
      await Future.delayed(const Duration(seconds: 2));
      disc.discoverClients();
      _discListener?.cancel();
    });
  }

  void navigateToDirectory(SubContent content) {
    final currentPath = Get.parameters['currentPath'] ?? '';
    final newPath = '$currentPath/${content.id}';

    Get.toNamed(
      '/upnpRadio/upnpRadioA',
      parameters: {
        'parentId': content.parentID,
        'currentPath': newPath,
        'title': content.title,
      },
    );
  }

  void handleBack() {
    if (dataPool.length == 1) {
      Get.back();
      dataPool.clear();
      return;
    } else {
      if (dataPool.isNotEmpty) dataPool.removeLast();
      if (dataPool.isNotEmpty) nasRadio.mllsA.value = dataPool.last;
      Get.back();
      Log.d(dataPool.map((el) {
        return el.map((item)=> item.title).toList();
      }));
    }
  }

  // 解析xml并携带标签属性
  String xmlToJsonString(String xml) {
    try {
      final Xml2Json xml2json = Xml2Json();
      xml2json.parse(xml);
      return xml2json.toParkerWithAttrs().replaceAll('\u0000', '_');
    } catch (e) {
      Log.d('xml解析发生错误 $e');
      return '';
    }
  }

  String escapeXml(String input) {
    return input .replaceAll('&', '&amp;')
      .replaceAll('<', '&lt;')
      .replaceAll('>', '&gt;')
      .replaceAll('"', '&quot;')
      .replaceAll("'", '&apos;');
  }

  String transFormTracks ({
    required String listName,
    required List<SubContent> tracks,
    String selectNasService = '',
  }) {

    String listInfo() {
      final radio = '<Radio>0</Radio>';
      final sourceName = '<SourceName>MyUPnPServer</SourceName>';
      final picUrl = '<PicUrl></PicUrl>';
      final trackNumber = '<TrackNumber>${tracks.length}</TrackNumber>';
      final searchUrl = '<SearchUrl></SearchUrl>';
      final quality = '<Quality>0</Quality>';
      return '<ListInfo>$radio$sourceName$picUrl$trackNumber$searchUrl$quality</ListInfo>';
    }

    final body = '<?xml version="1.0"?><PlayList><ListName>$listName</ListName>${listInfo()}${tracksInfo(tracks)}</PlayList>';
    return escapeXml(body);
  }

  String tracksInfo(List<SubContent> tracks) {
      String info = '';
      for (var i = 0; i < tracks.length; i++) {
        final index = i + 1;
        final data = tracks[i];
        
        String metadata = '''<?xml version="1.0" encoding="UTF-8"?>
        <DIDL-Lite xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/" xmlns:song="www.wiimu.com/song/" xmlns:custom="www.wiimu.com/custom/" xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/">
          <upnp:class>object.item.audioItem.musicTrack</upnp:class>
          <item>
            <song:id></song:id>
            <song:albumid></song:albumid>
            <song:singerid></song:singerid>
            <dc:title>${data.title}</dc:title>
            <upnp:artist>${data.artist}</upnp:artist>
            <upnp:album>${data.album}</upnp:album>
            <res protocolInfo="http-get:*:audio/mpeg:DLNA.ORG_PN=MP3;DLNA.ORG_OP=01;" duration="0">${data.playUrl}</res>
            <upnp:albumArtURI>${data.imageUrl}</upnp:albumArtURI>
          </item>
        </DIDL-Lite>'''.replaceAll(RegExp(r'>(\s|\n)+<'), '><');
        metadata = escapeXml(metadata);

        String trackRes = '<Track$index><URL>${data.playUrl}</URL><Source>MyUPnPServer</Source><Key></Key><Id></Id><Metadata>$metadata</Metadata></Track$index>';

        // 去掉 trackRes 中 > 和 < 之间的空格和换行
        info += trackRes;
      }

      return '<Tracks>${info.replaceAll(' ', ' ')}</Tracks>';
    }

  void playRadio(AbstractNET device, String tracks, String queueName) {
    try {
      device.requestPost(
        url: WCreateQueue.url(device.ip),
        hsa: WCreateQueue.hsa,
        dat: WCreateQueue.dat(tracks: tracks)
      ).then((value) async {
        Log.d(value);
        device.playQueue(queueName);
        await Future.delayed(const Duration(milliseconds: 100), () {});
        Get.toNamed('/player_page')?.then((_) {
          Log.d(dataPool.length);
        });
      }).catchError((e) {
        Log.e(e);
        AppToast.show('Play failure!');
      });
    } catch (e) {
      Log.e('失败了');
    }
  }

  void handleSort() {
    final List list = [
      'Song Title',
      'Song Artist',
    ];
    
    showMyBottomSheet(
      height: 280.sp,
      title: 'Sort By',
      content: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.sp),
        child: ListView.builder(
          padding: EdgeInsets.only(top: 6.sp),
          physics: NeverScrollableScrollPhysics(),
          itemCount: list.length,
          itemBuilder: (BuildContext context, int index) {
            final title = list[index];

            return GestureDetector(
              onTap: () {
                currentSort.value = index;
                StorageClass.setStorage('NAS:SORT', index);
              },
              child: Obx(()=> Container(
                height: 44.sp,
                alignment: Alignment.center,
                margin: EdgeInsets.only(bottom: 12.sp),
                decoration: BoxDecoration(
                  color: themeBase.primaryColor.value.withAlpha(
                    index == currentSort.value? (255 * 0.5).round() : 0
                  ),
                  borderRadius: BorderRadius.circular(10.r)
                ),
                child: AText(
                  text: title,
                  size: 16.sp,
                  color: index == currentSort.value? themeBase.blockColor.value : themeBase.primaryColor.value ,
                ),
              )),
            );
          },
        ),
      ),
      showCancel: false.obs,
      confirm: () {
        sortList();
        Get.back();
      },
    );
  }

  void sortList() {
    mllsA.sort((a, b) {
      switch (currentSort.value) {
        // 从歌曲名的大小
        case 0:
          return a.title.compareTo(b.title);
        case 1:
        // 判断Artist是不是一起的
          return a.artist.compareTo(b.artist);
        default:
          return a.title.compareTo(b.title);
      }
    });
    Log.d(mllsA.map((el)=> {
      'title': el.title,
      'artist': el.artist,
    }).toList());
  }

  @override
  void dispose() {
    _discListener?.cancel();
    disc.stop();
    super.dispose();
  }
}

/// 表示NAS服务的类，用于存储NAS服务的相关信息。
///
/// 参数：
/// - [name]：NAS服务的名称。
/// - [device]：关联的UPnP设备。
/// - [service]：关联的UPnP服务。
class NASservice {
  /// 构造函数，用于创建NASservice实例。
  NASservice({
    required this.name,
    required this.device,
    required this.service
  });

  /// NAS服务的名称。
  final String name;
  /// 关联的UPnP设备。
  final Device device;
  /// 关联的UPnP服务。
  final Service service;

  /// 获取指定对象的内容。
  ///
  /// 该方法通过调用UPnP服务的 `Browse` 操作来获取指定对象的内容。
  ///
  /// 参数：
  /// - [objectId]：要浏览的对象的ID，默认为 '0'。
  /// - [getMetadata]：是否获取对象的元数据，默认为 `false`。
  /// - [filter]：过滤条件，默认为空字符串。
  /// - [startingIndex]：起始索引，默认为 0。
  /// - [requestedCount]：请求的记录数，默认为 99999。
  /// - [sortCriteria]：排序条件，默认为空字符串。
  /// 
  ///  在UPnP协议中，`sortCriteria` 参数用于指定对浏览操作返回的结果进行排序的规则。
  ///  它是一个字符串，格式为以逗号分隔的属性列表，每个属性前面可以有一个可选的 '+' 或 '-' 符号，用于指定升序或降序排序。
  ///
  /// 例如：
  /// - `"+dc:title"` 表示按标题（`dc:title`）升序排序。
  /// - `"-upnp:artist"` 表示按艺术家（`upnp:artist`）降序排序。
  /// - `"+dc:date,-dc:title"` 表示先按日期（`dc:date`）升序排序，日期相同的情况下再按标题（`dc:title`）降序排序。
  ///
  /// 常见的可用于排序的属性包括：
  /// - `dc:title`：标题
  /// - `dc:creator`：创作者
  /// - `upnp:artist`：艺术家
  /// - `upnp:album`：专辑
  /// - `dc:date`：日期
  /// - `upnp:genre`：流派
  ///
  /// 具体可以使用哪些属性，取决于UPnP设备所支持的元数据。不同的设备可能支持不同的属性，因此在使用时需要参考设备的文档。
  ///
  /// 返回：
  /// - 一个 `Future<Map<String, String>>`，包含操作结果。
  ///
  /// 异常：
  /// - 如果在调用 `Browse` 操作时发生错误，将抛出异常。
  Future<List<SubContent>> getContent({
    String objectId = '0',
    bool getMetadata = false,
    String filter = '*',
    int startingIndex = 0,
    int requestedCount = 99999,
    sortCriteria = ''
    // `sortCriteria` 参数用于指定对浏览操作返回的结果进行排序的规则。
    // 它是一个字符串，格式为以逗号分隔的属性列表，每个属性前面可以有一个可选的 '+' 或 '-' 符号，用于指定升序或降序排序。
    // 
    // 例如：
    // - `"+dc:title"` 表示按标题（`dc:title`）升序排序。
    // - `"-upnp:artist"` 表示按艺术家（`upnp:artist`）降序排序。
    // - `"+dc:date,-dc:title"` 表示先按日期（`dc:date`）升序排序，日期相同的情况下再按标题（`dc:title`）降序排序。
    // 
    // 常见的可用于排序的属性包括：
    // - `dc:title`：标题
    // - `dc:creator`：创作者
    // - `upnp:artist`：艺术家
    // - `upnp:album`：专辑
    // - `dc:date`：日期
    // - `upnp:genre`：流派
    // 
    // 具体可以使用哪些属性，取决于UPnP设备所支持的元数据。不同的设备可能支持不同的属性，因此在使用时需要参考设备的文档。
  }) async {

    if (nasRadio.lock2.isUsing) {
      return [];
    }

    return nasRadio.lock2.mutex(() async {
      if (nasRadio.currentSort.value == 0) {
        sortCriteria = '+dc:title';
      } else if (nasRadio.currentSort.value == 1) {
        sortCriteria = '+upnp:artist';
      }

      // try {
        Log.p('开始--------------------');
        // 调用服务的 'Browse' 操作，传入所需参数
        final result = await service.invokeAction('Browse', {
          'ObjectID': objectId,
          'BrowseFlag': getMetadata ? 'BrowseMetadata' : 'BrowseDirectChildren',
          'Filter': filter,
          'StartingIndex': startingIndex,
          'RequestedCount': requestedCount,
          'SortCriteria': sortCriteria,
        });
        Log.p('结束--------------------');

        
        // 定义结果键名
        final key = 'Result';
        // 检查结果中是否包含指定键
        if (result.containsKey(key)) {
          // 结果中包含指定键
          assert(result.containsKey(key));
          // 获取结果中的 XML 数据
          final xml = result[key]!;

          // 如果 getMetadata 为 true，调用 getMetadata 方法获取元数据
          if (!getMetadata) {
            // 调用 getContainer 方法解析 XML 数据，获取 SubContent 列表
            final List<SubContent> containers = getContainer(xml);
            bool isSort = false;
            
            for (var el in containers) {
              if (el.type == 'musicTrack') isSort = true;
            }

            if (containers.isNotEmpty) {
              nasRadio.mllsA.value = containers;
              if (isSort) nasRadio.sortList();
              nasRadio.dataPool.add(containers);
            }

            // 返回解析后的 SubContent 列表
            return containers;
          } 
          // if () {
          //   // 将XML字符串转换为JSON字符串
          //   final String results = nasRadio.xmlToJsonString(xml);
          //   // 解析JSON字符串为Map对象
          //   final data = json.decode(results);
          //   // 
          //   Log.r(data);
          // }
        }
        // 若结果中不包含指定键，返回空列表
        return [];
      // } catch (e) {
      //   // 若发生异常，返回错误信息
      //   return Future.error(e);
      // }
    });
  }

  // //Future<void> getMetadata(String objectId) async {
  // //  final encodedArtist = Uri.encodeQueryComponent('邓丽君');
  // //  (dc:title contains "$encodedArtist}")
  // //  try {
  // //    final result = await service.invokeAction('Search', {
  // //      'ContainerID': objectId,
  // //      'SearchCriteria': '', 
  // //      'Filter': '*',
  // //      'StartingIndex': 0,
  // //      'RequestedCount': 99999,
  // //      'SortCriteria': '',
  // //    });
  // //    Log.d(result);
  // //  } catch (e) {
  // //    Log.e(e);
  // //  }
  // //}

  /// 从XML字符串中解析出 `SubContent` 对象列表。
  ///
  /// 该方法将输入的XML字符串转换为JSON格式，然后解析JSON数据以提取 `SubContent` 对象。
  ///
  /// 参数：
  /// - [xml]：要解析的XML字符串。
  ///
  /// 返回：
  /// - 一个包含解析出的 `SubContent` 对象的列表。如果解析过程中发生错误，返回空列表。
  List<SubContent> getContainer(String xml) {
    try {
      // 将XML字符串转换为JSON字符串
      final String result = nasRadio.xmlToJsonString(xml);
      // 解析JSON字符串为Map对象
      final data = json.decode(result);
      // 定义要查找的JSON键
      final key1 = 'DIDL-Lite';
      final key2 = 'container';
      final key3 = 'item';
      // 用于存储解析出的SubContent对象的列表
      final List<SubContent> subList = [];

      // 确保data是Map类型
      assert(data is Map);
      // 如果data中不包含key1，返回空列表
      if (!data.containsKey(key1)) return [];
      // 确保data[key1]是Map类型
      assert(data[key1] is Map);
      // 获取data[key1]的Map对象
      final Map<String, dynamic> map = data[key1];

      // Log.h(map);

      List metaDatas = [];

      // 获取map[key2] 列表有多首歌曲的情况
      if (map.containsKey(key2)) {
        metaDatas = map[key2];
      }

      // 获取map[key3] 只有一首歌曲的情况
      if (map.containsKey(key3)) {
        if (map[key3] is Map) {
          metaDatas = [map[key3]];
        } else {
          metaDatas = map[key3];
        }
      }

      // 遍历containers列表
      for (var cons in metaDatas) {
        // 如果当前容器是Map类型
        if (cons is Map) {
          final itemType = getType(cons['upnp:class']);

          String iplayUrl = '';
          if (cons.containsKey('res')) {
            if (cons['res'] is Map) {
              final Map consResIsMap = cons['res'];
              if (consResIsMap.containsKey('value')) iplayUrl = consResIsMap['value'];
            }
            if (cons['res'] is List) {
              final List consList = cons['res'];
              final Map consResIsMap = consList[0];
              if (consResIsMap.containsKey('value')) iplayUrl = consResIsMap['value'];
            }
          }

          // 创建一个新的SubContent对象
          final SubContent sub = SubContent(
            // 从容器中获取ID
            id: cons['_id'],
            // 从容器中获取父ID
            parentID: cons['_parentID'],
            // 从容器中获取标题
            title: cons['dc:title'],
            // 从容器中获取图片URL
            imageUrl: cons.containsKey('upnp:icon') ? cons['upnp:icon'] : '',
            artist: cons.containsKey('upnp:artist')? cons['upnp:artist'] : '',
            album: cons.containsKey('upnp:album')? cons['upnp:album'] : '',
            playUrl: iplayUrl,
            // 从容器中获取类型
            type: itemType
          );

          if (itemType == 'musicTrack') {
            getContent(
              objectId: cons['_id'],
              getMetadata: true
            );
          }

          // 将新的SubContent对象添加到subList中
          subList.add(sub);
        }
      }

      // 返回解析出的SubContent对象列表
      return subList;
    } catch (e) {
      // 如果发生异常，返回空列表
      return [];
    }
  }

  /// 根据输入的字符串返回对应的类型字符串。
  ///
  /// 该方法根据输入的字符串，使用 `switch` 语句匹配并返回对应的类型字符串。
  ///
  /// 参数：
  /// - [str]：要匹配的字符串。
  ///
  /// 返回：
  /// - 匹配到的类型字符串。如果没有匹配到，返回 '0'。
  String getType(String str) {
    // 记录当前类型信息
    // 根据输入的字符串进行匹配
    switch (str) {
      case 'object.container':
        return 'container';
      case 'object.container.album.musicAlbum':
        return 'musicAlbum';
      case 'object.item.audioItem.musicTrack':
        return 'musicTrack';
      default:
        return 'null';
    }
  }
}

/// 表示子内容的类，用于存储子内容的相关信息。
///
/// 参数：
/// - [id]：子内容的唯一标识符。
/// - [parentID]：父内容的唯一标识符。
/// - [title]：子内容的标题。
/// - [type]：子内容的类型。
/// - [imageUrl]：子内容的图片URL，默认为空字符串。
class SubContent {
  /// 构造函数，用于创建 SubContent 实例。
  SubContent({
    required this.id,
    required this.parentID,
    required this.title,
    required this.type,
    this.imageUrl = '',
    this.artist = '',
    this.album = '',
    this.playUrl = ''
  });

  /// 子内容的唯一标识符。
  String id;
  /// 父内容的唯一标识符。
  String parentID;
  /// 子内容的标题。
  String title;
  /// 子内容的类型。
  String type;
  /// 子内容的图片URL。
  String imageUrl;
  /// 子内容的播放URL。
  String playUrl;
  /// 
  String artist;
  ///
  String album;

  @override
  String toString() {
    Map map = {
      'id': id,
      'parentID': parentID,
      'title': title,
      'type': type,
      'imageUrl': imageUrl,
      'playUrl': playUrl,
      'artist': artist,
      'album': album,
    };
    return map.toString();
  }
}



import 'package:get/get.dart';
import 'package:gocontrol/common/net_api.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/core/permission.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/pages/music_service.dart';
import 'package:on_audio_query/on_audio_query.dart';

final LocalMusicCon localMusicCon = () {
  if (Get.isRegistered<LocalMusicCon>()) {
    return Get.find<LocalMusicCon>();
  } else {
    return Get.put(LocalMusicCon());
  }
}();


class LocalMusicCon extends GetxController  {

  final OnAudioQuery audioQuery = OnAudioQuery();

  final audios = RxList<SongModel>([]);

  void init() async {

    MusicServer.init();

    try {
      bool hasPermission = await audioQuery.permissionsRequest();
      if (!hasPermission && AppPermission.phoneSystem != 'ios') {
        AppToast.show('File access permission is required!');
      }

      audios.value = await audioQuery.querySongs();

    } catch(e){
      print(e);
    }
  }

  void playRadio(AbstractNET device,Map data) {
    Log.w('播放 ${data['play_url']}');
    try{
      device.requestPost(
        url: CreateQueue.url(device.ip), 
        hsa: CreateQueue.hsa,
        dat: CreateQueue.dat(
          CreateQueue.queueContext(data,'Arylic Radio')
        )
      ).then((value) async {
        Log.d(value);
        device.playQueue(CreateQueue.queueName);
        await Future.delayed(const Duration(milliseconds: 100),(){});
        Get.toNamed('/player_page');
      });
    }catch(e){
      Log.e('失败了');  
    }
  }
}
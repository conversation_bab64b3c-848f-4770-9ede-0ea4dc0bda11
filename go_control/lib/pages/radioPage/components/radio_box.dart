import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/theme/theme.dart';

class RadioBoxList extends StatelessWidget {
  const RadioBoxList({
    super.key,
    required this.children
  });

  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.all(2.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.sp),
            color: themeBase.primaryColor.value
          ),
          child: Column(
            children: children,
          ),
        ),
        SizedBox(height: 16.sp,)
      ],
    );
  }
}

class RadioBox extends StatelessWidget {
  const RadioBox({
    super.key,
    required this.name,
    this.imgUrl = '',
    required this.onTap,
    this.song = false,
    this.assetImage
  });

  final String name;
  final String imgUrl;
  final Function onTap;
  final bool song;
  final Widget? assetImage;

  @override
  Widget build(BuildContext context) {

    final RxBool palytion = false.obs;
    return OnTapScaleToSmallBox(
      onTap: () {
        onTap();
        palytion.value = true;
        final random = Random();
        int randomNumber = 400 + random.nextInt(401); 
        Future.delayed(Duration(milliseconds: randomNumber),()=> palytion.value = false);
      },
      child: Container(
        height: 58.sp,
        margin: EdgeInsets.only(bottom: 12.sp),
        decoration: BoxDecoration(
          color: themeBase.textColor1.value,
          borderRadius: BorderRadius.circular(8.r)
        ),
        child: Row(
          children: [
            Container(
              height: 58.sp,
              width: 58.sp,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  bottomLeft: Radius.circular(8.r),
                ),
                color: Colors.black12
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Visibility(
                    visible: imgUrl == '',
                    child: Center(
                      child: GoIcon(
                        name: GoIcons.srcWiFi,
                        size: 24.sp,
                        color: themeBase.primaryColor.value,
                      ),
                    ),
                  ),
                  _getImage()
                ],
              )
            ),
            SizedBox(width: 12.sp),
            Flexible(
              child: Container(
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Container(
                        margin: EdgeInsets.only(right: 24.sp),
                        child: AText(
                          text: name,
                          size: themeBase.subBodyFont.value + 1.sp,
                          color: themeBase.primaryColor.value,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ),
                    song? Obx(() => Visibility(
                      visible: palytion.value && song,
                      child: Container(
                        width: 58.sp,
                        height: 58.sp,
                        padding: EdgeInsets.all(18.sp),
                        child: CircularProgressIndicator(
                          color: themeBase.primaryColor.value,
                          strokeWidth: 3.sp,
                        )
                      ),
                    )) : Container(
                      margin: EdgeInsets.only(right: 16.sp),
                      child: Transform.rotate(
                      angle: pi / 1,
                        child: GoIcon(
                          name: GoIcons.back,
                          size: 15.sp,
                          color: themeBase.primaryColor.value.withAlpha((255 * 0.5).toInt()),
                        ),
                      ),
                    ),
                  ],
                )
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getImage() {
    final Widget body = GoIcon(
      name: GoIcons.srcWiFi,
      size: 24.sp,
      color: themeBase.primaryColor.value,
    );
    if (assetImage != null) {
      return ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.r),
          bottomLeft: Radius.circular(8.r),
        ),
        child: assetImage!
      );
    } else {
      if (imgUrl == '') {
        return body;
      } else {
        return SizedBox(
          height: 58.sp,
          width: 58.sp,
          child: ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.r),
            bottomLeft: Radius.circular(8.r),
          ),
          child: CachedNetworkImage(
            fit: BoxFit.cover,
            imageUrl: imgUrl,
            memCacheWidth: 300,
            memCacheHeight: 300,
            fadeInDuration: const Duration(milliseconds: 150),
            fadeOutDuration: const Duration(milliseconds: 150),
            placeholder: (context, url) => body,
            errorWidget: (context, url, error) => body,
          )),
        );
      }
    }
  }
}
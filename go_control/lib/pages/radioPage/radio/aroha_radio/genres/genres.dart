import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/aroha_controller.dart';
import 'package:gocontrol/theme/theme.dart';

class ArohaGenresPage extends StatelessWidget {
  const ArohaGenresPage({super.key});

  @override
  Widget build(BuildContext context) {
    String title = 'Radio';
    if (Get.parameters['name']!=null) title = Get.parameters['name']!;

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: title,
      ), 
      body: BodyBox(
        showTopBar: true,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          // alignment: Alignment.center,
          child: Obx(()=> _getBox())
        )
      ),
    );
  }

  Widget _getBox(){
    if(arohaController.genres.isEmpty){
      return SizedBox(
        width: Get.width,
        height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value * 2),
        child: Loading(
          color: themeBase.primaryColor.value,
          size: 28.w,
        ),
      );
    }else{
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp,bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arohaController.genres[index];
          Log.y(item);
          return RadioBox(
            name: item['name'], 
            imgUrl: '${arohaController.imgBaseGenre}${item['logo']}',
            onTap: () async {
              arohaController.getStations('${item['id']}');
              Get.toNamed('/arohaRadio/arohaStations', parameters: {
                'name': item['name']
              });
            },
          );
        },
        itemCount: arohaController.genres.length,
      );
    }
  }
}
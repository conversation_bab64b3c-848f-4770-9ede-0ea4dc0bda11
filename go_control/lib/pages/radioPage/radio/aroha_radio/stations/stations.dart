import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/aroha_controller.dart';
import 'package:gocontrol/theme/theme.dart';

class ArohaStationsPage extends StatelessWidget {
  const ArohaStationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    String title = 'Radio';
    if(Get.parameters['name']!=null) title = Get.parameters['name']!;

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: title,
      ), 
      body: BodyBox(
        showTopBar: true,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 0.sp
          ),
          child: Obx(()=> _getBox())
        )
      ),
    );
  }

  Widget _getBox() {
    if(arohaController.stations.isEmpty){
      return SizedBox(
        width: Get.width,
        height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value * 2),
        child: Loading(
          color: themeBase.primaryColor.value,
          size: 28.w,
        ),
      );
    } else {
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp,bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arohaController.stations[index];
          return RadioBox(
            name: item['name'], 
            imgUrl: '${arohaController.imgBaseStation}${item['logo']}',
            song: true,
            onTap: () async {
              // Log.d(item);
              if (homCon.selectDevice is AbstractNET) {
                AbstractNET device = homCon.selectDevice as AbstractNET;
                arohaController.playRadio(device,item);
                Log.d(item['stream_url']);
              }
              
              if (homCon.selectDevice is Libre) {
                Libre libre = homCon.selectDevice as Libre;
                final String info = await arohaController.getStationInfo(item['id']);
                if (info != '') {
                  Map infoMap = json.decode(info);
                  final url = '${infoMap['stream_url']}::::${item['name']}::::Radio::::Music::::${arohaController.imgBaseStation}${item['logo']}';
                  libre.playRadio(url);
                  await Future.delayed(const Duration(milliseconds: 100),(){});
                  Get.toNamed('/player_page');
                }
              }
            },
          );
        },
        itemCount: arohaController.stations.length,
      );
    }
  }
}
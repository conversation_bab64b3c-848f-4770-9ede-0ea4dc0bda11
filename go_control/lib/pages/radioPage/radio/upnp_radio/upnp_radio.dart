import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/upnp_radio_controller.dart';
import 'package:gocontrol/theme/theme.dart';

class UpnpRadioPage extends StatelessWidget {
  const UpnpRadioPage({super.key});

  @override
  Widget build(BuildContext context) {
  
    return PagesBody(
      scroll: true,
      topbar: const Top2tion(
        title: 'NAS PLAY',
      ), 
      body: BodyBox(
        child: Container(
          height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: Obx(()=> ListView.builder(
            padding: EdgeInsets.all(0.sp),
            itemBuilder: ((context, index) {
              NASservice item = nasRadio.services[index];
              return RadioBox(
                imgUrl: '',
                name: item.name,
                onTap: () async {
                  final list = await item.getContent();
                  if (list.isEmpty) {
                    AppToast.show('The directory is empty!');
                    return;
                  }
                  nasRadio.mllsA.value = list;
                  nasRadio.selectService = item;
                  Get.toNamed('/upnpRadio/upnpRadioA', parameters: {'name': item.name});
                },
              );
            }), 
            itemCount: nasRadio.services.length,
          ))
        ),
      )
    );
  }
}
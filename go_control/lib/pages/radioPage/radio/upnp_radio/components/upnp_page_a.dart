import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/upnp_radio_controller.dart';
import 'package:gocontrol/theme/theme.dart';

class UpnpPageA extends StatelessWidget {
  const UpnpPageA({super.key});

  @override
  Widget build(BuildContext context) {
    final title = Get.parameters['title'] ?? 'Radio';

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: title,
        onTap: ()=> nasRadio.handleBack(),
        customIcon: !nasRadio.showSort.value? null : GestureDetector(
          onTap: () {
            nasRadio.handleSort();
          },
          child: Container(
            height: themeBase.topBarHeight,
            width: themeBase.topBarHeight,
            color: Colors.transparent,
            child: GoIcon(
              name: GoIcons.playList,
              color: themeBase.primaryColor.value,
              size: 25.sp,
            ),
          ),
        )
      ),
      body: BodyBox(
        showTopBar: true,
        scrollBox: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp),
          alignment: Alignment.center,
          child: Obx(() => _getBox())
        )
      ),
    );
  }

  Widget _getBox() {
    if (nasRadio.mllsA.isEmpty) {
      return SizedBox(
        width: Get.width,
        height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value * 2),
        child: Loading(
          text: 'Loading...',
          color: themeBase.primaryColor.value,
          size: 28.w,
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.only(top: 12.sp),
        child: ListView.builder(
          padding: EdgeInsets.only(top: 0.sp, bottom: 12.sp),
          itemBuilder: (context, index) {
            final SubContent item = nasRadio.mllsA[index];
            return RadioBox(
              name: item.title,
              song: item.type == 'musicTrack',
              imgUrl: item.imageUrl,
              onTap: () async {
                // if (item.type == 'musicAlbum' || item.type.isEmpty) {
                //   nasRadio.showSort.value = true;
                // } else {
                //   nasRadio.showSort.value = false;
                // }
                // Log.d(nasRadio.showSort.value);
                if (item.type == 'musicTrack') {
                  if (homCon.selectDevice is AbstractNET) {
                    AbstractNET device = homCon.selectDevice as AbstractNET;
                    final List<SubContent> tracks = nasRadio.mllsA.sublist(index);
                    final data = nasRadio.transFormTracks(
                      listName: 'myUpnpPlayList',
                      tracks: tracks,
                      selectNasService: nasRadio.selectService?.name ?? '',
                    );
                    nasRadio.playRadio(device, data, 'myUpnpPlayList');
                  }
                  return;
                }

                assert(nasRadio.selectService != null);
                final List<SubContent> list = await nasRadio.selectService!.getContent(objectId: item.id);

                for (var el in list) {
                  Log.d(el.type);
                  if (el.type == 'musicTrack') {
                    nasRadio.showSort.value = true;
                    break; // 发现是musicTrack，直接终止循环
                  } else {
                    nasRadio.showSort.value = false;
                  }
                }

                if (list.isEmpty) {
                  nasRadio.mllsAisEmpty.value = true;
                  AppToast.show('The directory is empty!');
                  return;
                } else {
                  nasRadio.mllsAisEmpty.value = false;
                }

                Log.r('msg === ${nasRadio.mllsAisEmpty.value}');
                nasRadio.navigateToDirectory(item);
              },
            );
          },
          itemCount: nasRadio.mllsA.length,
        ),
      );
    }
  }
}

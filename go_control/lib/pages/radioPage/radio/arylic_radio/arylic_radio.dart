import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/arylic_controller.dart';
import 'package:gocontrol/theme/theme.dart';

class ArylicRadioPage extends StatelessWidget {
  const ArylicRadioPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: 'Arylic Radio',
      ), 
      body: BodyBox(
        showTopBar: true,
        child: Container(
          height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: Column(
            children: [
              RadioBox(
                imgUrl: '',
                name: 'Pubilc Radio',
                onTap: (){
                  arylicRadioMusic.getSummaryCode();
                  Get.toNamed('/arylicRadio/public');
                },
              ),
              RadioBox(
                imgUrl: '',
                name: 'My Station',
                onTap: (){},
              ),
            ],
          ),
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/arylic_controller.dart';
import 'package:gocontrol/theme/theme.dart';

class ArylicRadioCountry extends StatelessWidget {
  const ArylicRadioCountry({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: 'Country',
      ), 
      body: BodyBox(
        showTopBar: true,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: Obx(()=> _getBox())
        )
      ),
    );
  }

  Widget _getBox(){
  if(arylicRadioMusic.genresList.isEmpty){
    return SizedBox(
      width: Get.width,
      height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value * 2),
      child: Loading(
        color: themeBase.primaryColor.value,
        size: 28.w,
      ),
    );
  }else{
    return ListView.builder(
      padding: EdgeInsets.only(top: 0.sp,bottom: 68.sp),
      itemBuilder: (context, index) {
        Map item = arylicRadioMusic.countryList[index];
        return RadioBox(
          name: item['name'], 
          imgUrl: '',
          onTap: () async {
            arylicRadioMusic.flexList.clear();
            arylicRadioMusic.getQuery(country: item['id']).then((relist){
              arylicRadioMusic.flexList.value = relist;
            });
            Get.toNamed('/flexList',parameters: {'name':item['name']});
          },
        );
      },
      itemCount: arylicRadioMusic.countryList.length,
    );
  }
}

}


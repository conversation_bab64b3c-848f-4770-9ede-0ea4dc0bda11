import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/arylic_controller.dart';
import 'package:gocontrol/theme/theme.dart';

class ArylicRadioFlexList extends StatelessWidget {
  const ArylicRadioFlexList({super.key});

  @override
  Widget build(BuildContext context) {
    String title = 'Radio';
    if(Get.parameters['name']!=null) title = Get.parameters['name']!;

    return PagesBody(
      scroll: false,
      topbar: Top2tion(
        title: title,
      ), 
      body: BodyBox(
        showTopBar: true,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: Obx(()=> _getBox())
        )
      ),
    );
  }
  
  // 判断是否等于空
  Widget _getBox(){
    if(arylicRadioMusic.flexList.isEmpty){
      return SizedBox(
        width: Get.width,
        height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value * 2),
        child: Loading(
          color: themeBase.primaryColor.value,
          size: 28.w,
        ),
      );
    }else{
      return ListView.builder(
        padding: EdgeInsets.only(top: 0.sp,bottom: 68.sp),
        itemBuilder: (context, index) {
          Map item = arylicRadioMusic.flexList[index];
          return RadioBox(
            name: item['title'], 
            imgUrl: item['image_url'].replaceAll(RegExp(r'\s+'), ''),
            song: true,
            onTap: () async {
              
              if (homCon.selectDevice is AbstractNET) {
                AbstractNET device = homCon.selectDevice as AbstractNET;
                String info = await arylicRadioMusic.getinfo(item['id']);
                if(info != '' ){
                  Map imp = json.decode(info);
                  arylicRadioMusic.playRadio(device,imp);
                }
              }
              
              if (homCon.selectDevice is Libre) {
                Libre libre = homCon.selectDevice as Libre;
                final url = '${item['play_url']}::::${item['title']}::::Radio::::Music${item['id']}::::${item['image_url']}';
                libre.playRadio(url);
                await Future.delayed(const Duration(milliseconds: 200),(){});
                Get.toNamed('/player_page');
              }
            },
          );
        },
        itemCount: arylicRadioMusic.flexList.length, 
      );
    }
  }
}
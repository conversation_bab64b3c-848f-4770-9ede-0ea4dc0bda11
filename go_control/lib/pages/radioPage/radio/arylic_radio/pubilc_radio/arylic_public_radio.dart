import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/arylic_controller.dart';

class ArylicPublicRadio extends StatelessWidget {
  const ArylicPublicRadio({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: 'Public Radio',
      ), 
      body: BodyBox(
        showTopBar: true,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: <PERSON><PERSON>n(
            children: [
              RadioBox(
                name: 'All Station',
                imgUrl: '',
                onTap: () async {
                  arylicRadioMusic.getAllStation();
                  Get.toNamed('/arylicRadio/public/allStation');
                }
              ),
              RadioBox(
                name: 'Genre',
                imgUrl: '',
                onTap: (){
                  Get.toNamed('/arylicRadio/public/genre');
                }
              ),
              RadioBox(
                name: 'Language',
                imgUrl: '',
                onTap: (){
                  Get.toNamed('/arylicRadio/public/language');
                }
              ),
              RadioBox(
                name: 'Country',
                imgUrl: '',
                onTap: (){
                  Get.toNamed('/arylicRadio/public/country');
                }
              ),
            ],
          ),
        )
      )
    );
  }
}

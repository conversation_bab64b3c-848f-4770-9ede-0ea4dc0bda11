import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/radioPage/radio/spotify_page/get.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:url_launcher/url_launcher.dart';


class TidalGetPage extends StatelessWidget {
  const TidalGetPage({super.key});

  @override
  Widget build(BuildContext context) {
    final spotifyGetCon = Get.put(SpotifyGetCon());

    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: '',
      ), 
      body: BodyBox(
        showTopBar: true,
        scrollBox: false,
        child: Container(
          height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: Column(
            children: [
              SizedBox(height: 88.sp,),
              SizedBox(
                width: Get.width,
                child: GoIcon(
                  name: GoIcons.tidal,
                  size: 75.sp,
                )
              ),
              SizedBox(height: 66.sp,),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20.sp),
                child: AText(
                  text: 'rad1'.tr,
                  family: TextFamily.bold,
                  color: themeBase.primaryColor.value,
                  size: 18.sp,
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(vertical: 16.sp),
                padding: EdgeInsets.symmetric(horizontal: 20.sp),
                child: AText(
                  text: 'rad2'.tr,
                  softWrap: true,
                  color: themeBase.primaryColor.value.withOpacity(.7),
                  size: 17.sp,
                  textAlign: TextAlign.center,
                ),
              ),
              OnTapScaleToSmallBox(
                onTap: ()async {
                  await launchUrl(Uri.parse('https://tidal.com/connect'));
                },
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: 12.sp),
                  child: AText(
                    text: 'rad3'.tr,
                    color: const Color.fromARGB(255, 5, 48, 30),
                    size: 16.sp,
                    family: TextFamily.bold,
                  ),
                )
              ),
              OnTapScaleToSmallBox(
                onTap: () async {
                  if (spotifyGetCon.tidalIhave.value) {
                    await launchUrl(Uri.parse('tidal://'));
                  } else {
                    await launchUrl(Uri.parse('https://apps.apple.com/us/app/tidal-music-hifi-sound/id913943275'));
                  }
                },
                child: Container(
                  margin: EdgeInsets.only(top: 24.sp),
                  width: Get.width * 0.75,
                  height: 48.sp,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: themeBase.primaryColor.value,
                    borderRadius: BorderRadius.circular(48.sp)
                  ),
                  child: Obx(()=> AText(
                    text: spotifyGetCon.tidalIhave.value? 'rad4'.tr : 'rad5'.tr, 
                    color: themeBase.blockColor.value,
                  )),
                ),
              ),
            ],
          )
        )
      )
    );
  }
}
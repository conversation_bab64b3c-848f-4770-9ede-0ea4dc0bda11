import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:url_launcher/url_launcher.dart';

// GMT utc/ETC
class SpotifyGetPage extends StatelessWidget {
  const SpotifyGetPage({super.key});

  @override
  Widget build(BuildContext context) {
    final spotifyGetCon = Get.put(SpotifyGetCon());

    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: '',
      ), 
      body: BodyBox(
        showTopBar: true,
        scrollBox: false,
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: SingleChildScrollView(
            physics: NeverScrollableScrollPhysics(),
            child: Scrollbar(
              thumbVisibility: false,
              child: SizedBox(
                height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Transform.scale(
                      scale: .65,
                      child: Image.asset(
                        'images/spotify2.png',
                      ),
                    ),
                    SizedBox(height: 58.sp,),
                    Column(
                      children: [
                        SizedBox(
                          child: AText(
                            text: 'rads1'.tr,
                            family: TextFamily.bold,
                            color: themeBase.primaryColor.value,
                            size: 21.sp,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(vertical: 16.sp),
                          child: Column(
                            children: [
                              SizedBox(
                                child: AText(
                                  text: 'rads2'.tr,
                                  color: themeBase.primaryColor.value.withOpacity(.7),
                                  size: 17.sp,
                                ),
                              ),
                              SizedBox(
                                child: AText(
                                  text: 'rads3'.tr,
                                  family: TextFamily.bold,
                                  color: themeBase.primaryColor.value.withOpacity(.5),
                                  size: 16.sp,
                                ),
                              )
                            ],
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            if (await canLaunchUrl(Uri.parse('https://connect.spotify.com/'))) {
                              await launchUrl(Uri.parse('https://connect.spotify.com/'));
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            child: AText(
                              text: 'rads4'.tr,
                              color: const Color.fromRGBO(21, 201, 127, 1),
                              size: 17.sp,
                              family: TextFamily.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    OnTapScaleToSmallBox(
                      onTap: () async {
                        if (spotifyGetCon.ihave.value) {
                          await launchUrl(Uri.parse('spotify://'));
                        } else {
                          if (GetPlatform.isIOS) {
                            await launchUrl(Uri.parse('https://itunes.apple.com/cn/app/id324684580'));
                          } else {
                            await launchUrl(Uri.parse('https://play.google.com/store/apps/details?id=com.spotify.music'));
                          }
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.only(
                          top: 50.sp,
                          bottom: 80.sp
                        ),
                        width: Get.width * 0.75,
                        height: 50.sp,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: const Color.fromRGBO(21, 201, 127, 1),
                          borderRadius: BorderRadius.circular(48.sp)
                        ),
                        child: Obx(()=> AText(
                          text: spotifyGetCon.ihave.value? 'rads5'.tr : 'rads6'.tr, 
                          color: themeBase.blockColor.value,
                          family: TextFamily.bold,
                          size: 18.sp,
                        )),
                      ),
                    ),
                  ],
                ),
              ),
            )
          ),
        )
      )
    );
  }
}


class SpotifyGetCon extends GetxController {
  final RxBool ihave = false.obs;
  final RxBool tidalIhave = false.obs;
}
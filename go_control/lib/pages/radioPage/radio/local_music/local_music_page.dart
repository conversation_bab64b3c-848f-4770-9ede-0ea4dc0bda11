import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/ip_tools.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/radioPage/components/radio_box.dart';
import 'package:gocontrol/pages/radioPage/controller/local_music_con.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:on_audio_query/on_audio_query.dart';

class LocalMusicPage extends StatelessWidget {
  const LocalMusicPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: true,
      topbar: const Top2tion(
        title: 'Local Music',
      ), 
      body: BodyBox(
        child: Container(
          height: Get.height - (themeBase.topBarHeight + themeBase.searHeight.value),
          padding: EdgeInsets.symmetric(
            horizontal: 16.sp,
            vertical: 12.sp
          ),
          child: Obx(()=> ListView.builder(
            padding: EdgeInsets.all(0.sp),
            itemBuilder: ((context, index) {
              // NASservice item = nasRadio.services[index];
              SongModel item = localMusicCon.audios[index];
              return RadioBox(
                song: true,
                imgUrl: '',
                name: '${index + 1}. ${item.title}',
                onTap: () async {
                  String thePlayUrl = item.data;
                  Uri playUrl;

                  final localIP = IpV4AndIpV6Tool.localIpv4;
                  assert(localIP is String);
                  // return;

                  if (Platform.isIOS) {
                    // final setting = GetSettings();
                    // thePlayUrl = await setting.ipodToPath(item.data);
                    Log.w('DATA ${item.data}');
                    final id = item.data.split('?').last;
                    playUrl = Uri.parse('http://$localIP:7788/ios?$id');
                  } else {
                    playUrl = Uri.parse('http://$localIP:7788/android?android=$thePlayUrl');
                  }
                  // item['title'];
                  // item['play_url'];
                  // item['user_name'];
                  // item['image_url'];
                  
                  if (homCon.selectDevice is AbstractNET) {
                     AbstractNET device = homCon.selectDevice as AbstractNET;
                     Map playData = {
                      'title': item.title,
                      'image_url': '',
                      'user_name': (item.artist == '' || item.artist == '<unknown>' || item.artist == null ? 'Artist' : item.artist),
                      'play_url': playUrl.toString()
                    };
                    localMusicCon.playRadio(device, playData);
                  }

                  if (homCon.selectDevice is Libre) {
                    Libre libre = homCon.selectDevice as Libre;
                    final url = '$playUrl::::${item.title}::::${(item.artist == '' || item.artist == '<unknown>' || item.artist == null ? 'Artist' : item.artist)}::::Music';
                    libre.playRadio(Uri.encodeFull(url));
                    await Future.delayed(const Duration(milliseconds: 100),(){});
                    Get.toNamed('/player_page');
                  }
                  // final result = await http.get();
                  // Log.d(result.contentLength);
                  // nasRadio.mllsA.value = await item.getContent();
                  // nasRadio.selectService = item;
                  // if (nasRadio.mllsA.isEmpty) {
                  //   AppToast.show('The directory is empty!');
                  //   return;
                  // }
                  // Get.toNamed('/upnpRadio/upnpRadioA', parameters: {'name': item.name});
                },
              );
            }), 
            itemCount: localMusicCon.audios.length,
          ))
        ),
      )
    );
  }
}
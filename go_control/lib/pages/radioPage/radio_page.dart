import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/radioPage/controller/aroha_controller.dart';
import 'package:gocontrol/pages/radioPage/controller/arylic_controller.dart';
import 'package:gocontrol/pages/radioPage/controller/local_music_con.dart';
import 'package:gocontrol/pages/radioPage/controller/upnp_radio_controller.dart';
import 'package:gocontrol/pages/radioPage/radio/spotify_page/get.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:url_launcher/url_launcher.dart';

import 'components/radio_box.dart';
import 'package:gocontrol/common/ip_tools.dart';


class RadioPage  extends StatelessWidget {
  const RadioPage ({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: false,
      topbar: const Top2tion(
        title: 'Music',
      ),
      body: BodyBox(
        showTopBar: true,
        child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: 16.sp,
              vertical: 12.sp,
            ),
            child: Column(
              children: [
                // RadioBox(
                //   name: 'Local music',
                //   assetImage: Container(
                //     decoration: BoxDecoration(
                //       gradient: LinearGradient(
                //         colors: [Colors.pinkAccent ,Colors.lightBlueAccent],
                //         begin: Alignment.topCenter,
                //         end: Alignment.bottomCenter,
                //       ),
                //     ),
                //   ),
                //   onTap: () {
                //     // nasRadio.discoverClients();
                //     // nasRadio.currentSort.value = StorageClass.getStorage('NAS:SORT') ?? 0;
                //     localMusicCon.init();
                //     IpV4AndIpV6Tool.localIpv4;
                //     Get.toNamed(Routes.localMusicPage);
                //   },
                // ),
                RadioBox(
                  name: 'NAS PLAY',
                  assetImage: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.amberAccent, Colors.greenAccent],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                  onTap: () {
                    nasRadio.discoverClients();
                    nasRadio.currentSort.value = StorageClass.getStorage('NAS:SORT') ?? 0;
                    Get.toNamed(Routes.upnpRadio);
                  },
                ),
                RadioBox(
                  name: 'ArylicRadio',
                  assetImage: SvgPicture.asset(
                    'images/arylic_icon.svg',
                    width: 58.sp,
                    height: 58.sp,
                    fit: BoxFit.cover,
                  ),
                  onTap: (){
                    final bool yes = StorageClass.getStorage('ArylicRadioPrivacy') ?? false;
                    if (yes) {
                      arylicRadioMusic.getSummaryCode();
                      Get.toNamed('/arylicRadio/public');
                    } else {
                      Get.toNamed(Routes.privacyPolicy);
                    }
                  },
                ),
                RadioBox(
                  name: 'YouRadio',
                  assetImage: Container(
                    width: 58.sp,
                    height: 58.sp,
                    color: Color.fromRGBO(243, 80, 1, 1),
                    padding: EdgeInsets.all(10.sp),
                    child: SvgPicture.asset(
                      'images/aroha_icon.svg',
                      fit: BoxFit.cover,
                    ),
                  ),
                  onTap: (){
                    arohaController.getAllBrand();
                    Get.toNamed('/arohaRadio');
                  },
                ),
                Visibility(
                  visible: homCon.selectDevice! is Libre,
                  child: RadioBox(
                    name: 'Spotify',
                    assetImage: Container(
                      width: double.maxFinite,
                      height: double.maxFinite,
                      color: Colors.black,
                      child: GoIcon(
                        name: GoIcons.spotify,
                        size: 36.sp,
                        color:const Color.fromRGBO(21, 201, 127, 1),
                      ),
                    ),
                    onTap: () async {
                      SpotifyGetCon spotifyGetCon = Get.put(SpotifyGetCon());
                      spotifyGetCon.ihave.value = await canLaunchUrl(Uri.parse('spotify://'));
                      Get.toNamed(Routes.spotifyGet);
                    },
                  ),
                ),
                Visibility(
                  visible: homCon.selectDevice! is Libre,
                  child: RadioBox(
                    name: 'TIDAL',
                    assetImage: Container(
                      width: double.maxFinite,
                      height: double.maxFinite,
                      color: Colors.black,
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 11.sp
                        ),
                        child: Transform.translate(
                          offset: Offset(0, 2.sp),
                          child: Image.asset(
                            'images/tidal_logo.png',
                            fit: BoxFit.fitWidth,
                          ),
                        ),
                      ),
                    ),
                    onTap: () async {
                      SpotifyGetCon spotifyGetCon = Get.put(SpotifyGetCon());
                      spotifyGetCon.tidalIhave.value = await canLaunchUrl(Uri.parse('tidal://'));
                      Get.toNamed(Routes.tidalGet);
                    },
                  ),
                ),
              ],
          ),
        ),
      )
    );
  }
}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/ble_api.dart';

import '../components/atext.dart';

final TSc tsc = Get.put(TSc());

class TestPage extends StatelessWidget {
  const TestPage({super.key});

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        title: AText(text: 'TestPage'),
      ),
      floatingActionButton: ElevatedButton(
        child: Icon(Icons.import_contacts),
        onPressed: () {
          BleApi.scan(light: true);
        },
      ),
      body: SizedBox(
        child: Column(
          children: [
            
          ],
        ),
      ),
    );
  }
}

class TSc extends GetxController {
  BleApi bleApi = Get.put(BleApi());


}
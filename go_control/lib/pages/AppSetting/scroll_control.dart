import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gocontrol/models/model_class/a4_usblist.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

class ScrollViewControl extends GetxController {
  final ScrollController scrollController = ScrollController();
  final RxBool fetching = false.obs;
  final RxDouble lastScrollPosition = 0.0.obs;
  
  @override
  void onInit() {
    scrollController.addListener(onScroll);
    super.onInit();
  }

  @override
  void dispose() {
    scrollController.removeListener(onScroll);
    scrollController.dispose();
    super.dispose();
  }

  void onScroll() {
    // 获取当前滚动位置
    double maxScroll = scrollController.position.maxScrollExtent;
    double currentScroll = scrollController.position.pixels;
    // 获取前一次滚动位置
    double lastScroll = lastScrollPosition.value;
    // 更新前一次滚动位置
    lastScrollPosition.value = currentScroll;
    // 90%的位置作为滑动到底部的事件触发点
    double triggerPoint = maxScroll * 0.97; 
    // ListView快滑动到底部
    if (currentScroll > triggerPoint && !fetching.value && lastScroll < currentScroll) fetchData();
  }

  void fetchData() async {
    fetching.value = true;
    final device = homCon.selectDevice as A4Usblist;
    if (device.dataTot.value != 0) {
      if (device.dataList.length != device.dataTot.value) {
        await device.getSongListForUSB();
        await Future.delayed(const Duration(milliseconds: 500),(){});
        fetching.value = false;
      }
    }
  }
}
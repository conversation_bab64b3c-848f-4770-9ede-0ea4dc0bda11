import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/AppSetting/setc.dart';
import 'package:gocontrol/pages/LoginPage/controller/login_control.dart';
import 'package:gocontrol/pages/SettingPage/components/setting_box.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:http/http.dart' as http;
import 'package:package_info_plus/package_info_plus.dart';

class AppSetting extends StatelessWidget  {
  const AppSetting({super.key});

  @override
  Widget build(BuildContext context) {

    return FractionallySizedBox(  
      widthFactor: .85,
      child: Drawer(
        child: Container(
          margin: EdgeInsets.only(top: themeBase.searHeight.value),
          padding: EdgeInsets.only(
            left: 18.sp,
            right: 18.sp,
            top: 18.sp
          ),
          child: Column(
            children: [
              SettingBox(
                children: [
                  Obx(() => SetBox2(
                    title: loginCon.userInfo['name'] == ''? 'login'.tr : 'user_name'.tr,
                    info: loginCon.userInfo['name'] == ''? '' : loginCon.userInfo['name'],
                    iconInfo: loginCon.userInfo['name'] == ''?
                      Transform.rotate(
                        angle: pi / 1,
                        child: GoIcon(
                          name: GoIcons.back,
                          size: 13.sp,
                          color: themeBase.primaryColor.value.withOpacity(.7),
                        ),
                      ) : null,
                    onTap: loginCon.userInfo['name'] == ''? () async {
                      // 关闭侧边栏
                      Get.back();
                      await Future.delayed(const Duration(milliseconds: 100));
                      if (loginCon.userInfo['name'] == '') Get.toNamed('/login', parameters: {'back':'1'});
                    } : null,
                  )),
                  Obx(() => Visibility(
                    visible: loginCon.userInfo['name'] != '',
                    child: SetBox2(
                      title: 'user_login_out'.tr,
                      info: '',
                      iconInfo: GoIcon(
                        name: GoIcons.out,
                        size: 18.sp,
                        color: themeBase.primaryColor.value.withOpacity(.7),
                      ),
                      onTap: (){
                        showMyBottomSheet(
                          height: 180.sp, 
                          title: 'user_login_out'.tr, 
                          content: AText(
                            text: 'user_login_out_text'.tr,
                            size: themeBase.subBodyFont.value,
                            color: themeBase.primaryColor.value,
                          ), 
                          showCancel: true.obs,
                          cancel: ()=> Get.back(),
                          confirm: (){
                            if(loginCon.timerResetlogin != null){
                              StorageClass.setStorage('autologin', false);
                              loginCon.timerResetlogin!.cancel();
                              loginCon.timerResetlogin = null;
                              loginCon.userInfo.value = {
                                'name': '',
                                'id': '',
                                'token': '',
                              };
                            }
                            Get.back();
                          }
                        );
                      },
                    )
                  )),
                  Obx(()=> Visibility(
                    visible: loginCon.userInfo['name'] != '',
                    child: SetBox2(
                      title: 'feedback'.tr,
                      info: '',
                      iconInfo: GoIcon(
                        name: GoIcons.feedback,
                        size: 18.sp,
                        color: themeBase.primaryColor.value.withOpacity(.7),
                      ),
                      onTap: () async {
                        final feedVal = ''.obs;
                        final sedFeed = false.obs;
                        final isDone = false.obs;
                        final showCan = true.obs;
                        showMyBottomSheet(
                          height: 300.sp, 
                          title: 'feedback'.tr, 
                          content: Obx(() => !isDone.value? Container(
                            constraints: const BoxConstraints.expand(),
                            margin: EdgeInsets.only(
                              left: 14.sp,
                              right: 14.sp,
                              bottom: 14.sp
                            ),
                            // padding: EdgeInsets.all(6.sp),
                            decoration: BoxDecoration(
                              color: themeBase.inputColor.value,
                              borderRadius: BorderRadius.circular(12.sp),
                            ),
                            child: TextField(
                              keyboardType: TextInputType.multiline,
                              maxLines: null,
                              onChanged: (value) => feedVal.value = value,
                              autofocus: false,
                              decoration:  InputDecoration(
                                // contentPadding: EdgeInsets.symmetric(vertical: 6.sp, horizontal: 8.sp),
                                focusedBorder: OutlineInputBorder( 
                                  borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
                                  borderSide: BorderSide.none, // 取消边框
                                ),
                                enabledBorder: OutlineInputBorder( 
                                  borderRadius: BorderRadius.circular(10.r), // 设置圆角半径
                                  borderSide: BorderSide.none, // 取消边框
                                ),
                                hintText: 'fedback_hit'.tr,
                                hintStyle: TextStyle(
                                  color: themeBase.primaryColor.value.withOpacity(0.4),
                                  fontSize: 15.sp,
                                  fontFamily: 'Medium'
                                )
                              ),
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: themeBase.primaryColor.value,
                                fontFamily: 'Medium'
                              )
                            ),
                          ) : Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(horizontal: 16.sp),
                            child: AText(
                              text: 'feedback_done'.tr,
                              color: themeBase.primaryColor.value,
                              softWrap: true,
                              size: themeBase.bodyFont.value - 1.sp,
                            ),
                          )), 
                          showCancel: showCan,
                          cancel: ()=> Get.back(),
                          confirmFuture: sedFeed,
                          confirm: () async {
                            if (feedVal.value.length < 5) return;
                            PackageInfo packageInfo = await PackageInfo.fromPlatform();
                            var appVersion = '<div style="color:blue;">App 版本: ${packageInfo.version} + (${packageInfo.buildNumber})</div>';
                            var phone = '<div style="color:blue;">运行系统: ${Platform.operatingSystem}</div>';
                            var systemVersion = '<div style="color:green;">系统版本: ${Platform.version}</div>';
                            var feedback = '<div style="color:red;">反馈内容: ${feedVal.value}</div>';
                            var errText = '<div style="color:red;">相关报错信息: ${Log.errText.replaceAll('\n', '<br>')}</div>';
                            var msg = '$appVersion<br> $phone <br> $systemVersion <br> $feedback <br> $errText';
                            var url = 'https://radio.arylic.com/user/feedback';
                            final response = await http.post(
                              Uri.parse(url),
                              body: {
                                "token": loginCon.userInfo['token'],
                                "msg": msg
                              }
                            );
                            if (response.statusCode == 200) {
                              print('FeedBack Success');
                              Get.back();
                              AppToast.show('FeedBack Success');
                            }
                          }
                        );
                      },
                    )
                  )),
                  Obx(() => SetBox2(
                    title: 'language'.tr,
                    info: '${homCon.appLanguageCode.value}_${homCon.appCountryCode.value}'.tr,
                    onTap: (){
                      showMyBottomSheet(
                        height: 304.sp, 
                        title: 'language'.tr, 
                        content: ListView.builder(
                          padding: const EdgeInsets.all(0),
                          itemBuilder: (context, index) {
                            return OnTapScaleToSmallBox(
                              child: Obx(()=> Container(
                                height: 40.sp,
                                alignment: Alignment.center,
                                margin: EdgeInsets.only(
                                  left: 12.sp,
                                  right: 12.sp,
                                  top: 4.sp,
                                  bottom: (index + 1) == homCon.languages.length? 12.sp : 4.sp
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.sp),
                                  color: themeBase.primaryColor.value.withOpacity(
                                    '${homCon.appLanguageCode.value}_${homCon.appCountryCode.value}' == homCon.languages[index]?
                                    1:0.4
                                  )
                                ),
                                child: AText(
                                  text: '${homCon.languages[index]}'.tr,
                                  color: themeBase.textColor1.value,
                                  size: themeBase.bodyFont.value,
                                ),
                              )),
                              onTap: (){
                                String code = homCon.languages[index];
                                String languageCode = code.substring(0,code.indexOf('_'));
                                String countryCode = code.substring(code.indexOf('_') + 1,code.length);
                                homCon.appLanguageCode.value = languageCode;
                                homCon.appCountryCode.value = countryCode;
                                Log.d(homCon.appLanguageCode.value);
                                Log.d(homCon.appCountryCode.value);
                                StorageClass.setStorage('appLanguage', languageCode);
                                StorageClass.setStorage('appCountryCode', countryCode);
                                Get.updateLocale(Locale(languageCode,countryCode));
                                Get.back();
                              },
                            );
                          },
                          itemCount: homCon.languages.length,
                        ), 
                        showCancel: false.obs,
                      );
                    },
                  )),
                  Obx(() {
                    return SetBox2(
                      title: 'app_version'.tr,
                      info: Get.find<AppController>().version,
                      last: true,
                    );
                  }),
                ],
              ),
            ],
          )
        )
      ),
    );
  }
}

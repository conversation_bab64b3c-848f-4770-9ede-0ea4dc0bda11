
import 'dart:convert';
import 'package:get/get.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/PlayerPage/components/fm_search.dart';
import 'package:gocontrol/routes/routes.dart';

class NetMsg {
  static getMsg(String data, A31 a31) async {
    
    // 初始化信息
    if (data.contains('AXX+INF+INF')) {
      int start = 11;
      int end = data.lastIndexOf('&');
      String str;
      if (end == -1) {
        int newEnd = data.lastIndexOf(',');
        str = data.substring(start, newEnd);
        // 数据里面可能会少 '}' 所以匹配一下有几个 '{'  给予相对应的括号,否则无法转json
        RegExp rexK = RegExp(r'{');
        RegExp rexE = RegExp(r'}');
        int kCount = rexK.allMatches(str).length;
        int eCount = rexE.allMatches(str).length;
        while (eCount < kCount) {
          str = str += ' }';
          eCount++;
        }
      } else {
        str = data.substring(start, end);
      }
      
      try {
        Map info = json.decode(str);
        Log.d(info);
        a31.name.value = info['DeviceName'] ?? info['ssid'];
        a31.getDeviceName();
        a31.uuid = info['uuid'];
        if (!a31.uuid.contains('FF31')) {
          a31.httpStr = 'https';
          if (a31.uuid.contains('FF970023')) a31.deviceMode.value = 'A97';
          if (a31.uuid.contains('FF97F35D')) a31.deviceMode.value = 'H50';
          if (a31.uuid.contains('FF97001B')) a31.deviceMode.value = 'A97L';
          if (a31.uuid.contains('FF98F066')) a31.deviceMode.value = 'A98';
        }
        a31.mac = info['MAC'];
        a31.project = info['project'];
        String ver = info['firmware'];
        
        if (ver.contains('Linkplay')) ver = ver.substring(9, ver.length);
        a31.version.value = ver;
        a31.mcuVersion = info['mcu_ver'];
        if (info.containsKey('master_uuid')) {
          a31.isRoomChild.value = true;
        }
        a31.initNetInfo();
      } catch(e) {
        Log.e(e);
      }
      return;
    }

    Log.h('[${a31.name}] --- $data');

    if (data.contains('MCU+PAS+RAKOIT')) {
      String tag = data.substring(15,data.indexOf('&'));
      String str = tag.substring(tag.indexOf(':') + 1 ,tag.length);
      // 是否可以使用Rakoit API
      if(tag.contains('VER')){
        a31.rakoitAPI.value = true;
      }
      // 音量
      if(tag.contains('VOL')){
        try{
          a31.volume.value = double.parse(str);
          if (a31.uuid.contains('FF97F35D')) {
            Future.delayed(const Duration(milliseconds: 100),()=> a31.getSourceInput());
          }
        }catch(_){}
      }
      if (tag.contains('NAM')) {
        try{
          if (a31.name.value == '') Future.delayed(const Duration(milliseconds: 300),()=> a31.initNetInfo());
          String neName = Comm.hexStrToString(str);
          if (neName != '') a31.name.value = neName;
        }catch(_){}
      }
      if (tag.contains('LED')) {
        if(str == '1'){
          a31.led.value = true;
        }else{
          a31.led.value = false;
        }
        if (!a31.showLED.value) {
          if(a31.project.contains('A100')) return;
          a31.showLED.value = true;
        }
      }
      if (tag.contains('VST')) {
        try {
          a31.vst.value = int.parse(str);
        } catch(_) {}
      }
      // 音源列表
      if (tag.contains('LST') && !(data.contains('CEQ:LST')) && !(data.contains('DSK:LST'))) {
        a31.sourceInputList.clear();
        List lt = str.split(',');
        List<String> order = ['NET', 'BT', 'FM', 'LINE-IN', 'LINE-IN2', 'AURA', 'PHONO','OPT', 'HDMI', 'USB', 'USBPLAY', 'USBDAC', 'COAX', 'DAB'];
        // location 
        lt.sort((a, b) {
          int indexA = order.indexOf(a);
          int indexB = order.indexOf(b);
          if (indexA == -1 && indexB == -1) {
            return 0;
          } else if (indexA == -1) {
            return 1;
          } else if (indexB == -1) {
            return -1;
          } else {
            return indexA.compareTo(indexB);
          }
        });
        for (var el in lt) {
          Comm.sendSource(a31,el);
        }
      }
      if (data.contains('CEQ:LST')) {
        str = str.substring(4, str.length);
        BaseAudio audioDevice = a31 as BaseAudio;
        if (str != '') {
          List ls = str.split(',');
          audioDevice.customEQList.clear();
          for (var el in ls) {
            String id = el.substring(0,el.indexOf('@'));
            String name = el.substring(el.indexOf('@') + 1,el.length);
            audioDevice.customEQList.add({
              'id': id,
              'name': name
            });
          }
        } else {
          // audioDevice.setEqIndex(0);
          audioDevice.customEQList.clear();
          audioDevice.clearCustomEQval();
        }
      }
      if(data.contains('CEQ:FLT')){
        String newStr = str.substring(4,str.length);
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          int id = int.parse(newStr.substring(0,newStr.indexOf(':')));
          int flt = int.parse(newStr.substring(newStr.indexOf(':') + 1,newStr.length).split(',')[2],radix: 16).toSigned(16);
          double fltDouble = flt / 256;
          audioDevice.customEQValueList[id]['gain'].value = fltDouble;
        }catch(_){}
      }
      if(tag.contains('SRC') && !tag.contains('MST:SRC')){
        if (a31.deviceMode.value == 'H50') {
          a31.btm1.value = {
            'sink': '',
            'name': '',
            'codec': '',
            'state': ''
          };
          a31.btm2.value = {
            'sink': '',
            'name': '',
            'codec': '',
            'state': ''
          };
        }
        // 错误后重新发送
        if(str == 'INVALID'){
          if(a31.lastSendMsg.value == 'SRC'){
            a31.getSourceInput();
            a31.lastSendMsg.value == '';
          }
          return;
        }
        if (str == 'USBPLAY') {
          Future.delayed(const Duration(milliseconds: 666),(){
            if (a31.dataList.isEmpty) a31.sendMsgToSocket('MCU+PAS+RAKOIT:DSK:TOT&');
          });
        }
        if (str == 'USBDAC' && a31.project.contains('A100')) {
          // Log.w('切换！！！');
          Future.delayed(const Duration(milliseconds: 1500),()=> a31.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:TOT&'));
        } else if (str != 'USBDAC' && a31.project.contains('A100')) {
          a31.dataTot.value = 0;
          a31.dataList.clear();
        }
        if (str == 'FM') {
          a31.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMQ&');
        }
        if(str == a31.sourceInput.value) return;    
        a31.newSourceInput.value = '';
        a31.swingSRC.value = false;
        a31.sourceInput.value = str;
        // a31.getMedialInfo();
      }
      if (tag.contains('DSK:TOT:')) {
        int num = int.parse(str.substring(4, str.length));
        if (a31.dataList.length != num) {
          a31.dataList.clear();
          a31.dataKey.value = 0;
          a31.dataTot.value = num;
          Log.w('msg 数据总数 ${a31.dataTot.value}');
        }
      }
      if (tag.contains('DSK:LST:')) {
        String newStr = str.substring(4, str.length);
        List lt = newStr.split(':');
        int id = int.parse(lt[0]);
        String songName = lt[1];
        Log.d('id: $id');
        Log.d('songName: $songName');
        for (var el in a31.dataList) {
          if (el['id'] == id) el['name'].value = songName;
        }
        // 本地存储一下歌曲
        if ((id + 1) == a31.dataTot.value) {
          String uuid = a31.uuid;
          List songList = [];
          for (var el in a31.dataList) {
            songList.add({
              'id': el['id'],
              'name': el['name'].value,
            });
          }
          String storageKey = '$uuid:USB:SONGLIST';
          StorageClass.setStorage(storageKey, json.encode(songList));
        }
      }
      if (tag.contains('DSK:CUR:')) {
        String strx = str.substring(4, str.length);
        a31.usbPlayIndex.value = int.parse(strx);
      }
      if(tag.contains('TRE')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.treble.value = double.parse(str);
        }catch(_){}
      }
      if(tag.contains('MID')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.mid.value = double.parse(str);
        }catch(_){}
      }
      if(tag.contains('BAS')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.bass.value = double.parse(str);
        }catch(_){}
      }
      if(tag.contains('BAL')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.balance.value = double.parse(str);
        }catch(_){}
      }
      if(tag.contains('MXV')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.maxVolume.value = double.parse(str);
        }catch(_){}
      }
      if(tag.contains('CFE')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          if(str == '1'){
            audioDevice.crossover.value = true;
          }else if(str == '0'){
            audioDevice.crossover.value = false; 
          }
        }catch(_){}
      }
      if(tag.contains('CFF')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.frequency.value = double.parse(str);
        }catch(_){}
      }
      if(data.contains('VBS:')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          if(str == '0'){
            audioDevice.deepBass.value = false;
          }else if (str == '1'){
            audioDevice.deepBass.value = true;
          }
        }catch(_){}
      }
      if(tag.contains('VBI')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.intensity.value = double.parse(str);
        }catch(_){}
      }
      if(tag.contains('EQS')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.eqIndex.value = int.parse(str);
          // Log.d(audioDevice.eqIndex.value);
          if(audioDevice.eqIndex.value > 9){
            // Log.d('>>>');
            audioDevice.initCustomEQval();
          }else{
            audioDevice.clearCustomEQval();
          }
        }catch(_){}
      }
      if(tag.contains('PEQ')){
        try{
          BaseAudio audioDevice = a31 as BaseAudio;
          audioDevice.setEqList(str);
        }catch(_){}
      }
      if(tag.contains('BEP')){
        if (!a31.showBEP.value) a31.showBEP.value = true;
        if(str == '1'){
          a31.keySound.value = true;
        }else{
          a31.keySound.value = false;
        }
      }
      if(tag.contains('ARC')){
        if(str == '0'){
          a31.arcMode.value = true;
        }else{
          a31.arcMode.value = false;
        }
        if (!a31.showARC.value) {
          // if (a31.project.contains('A100')) return;
          for (var el in Comm.devList) {
            if (a31.project.contains(el)) return;
          }
          a31.showARC.value = true;
        }
      }
      if(tag.contains('TLM')){
        if(str == '1'){
          a31.tlmMode.value = true;
        }else{
          a31.tlmMode.value = false;
        }
        if (!a31.showTLM.value) {
          for (var el in Comm.devList) {
            if (a31.project.contains(el)) return;
          }
          a31.showTLM.value = true;
        }
      }
      if (tag.contains('BTM')) {
        if(str == 'INVALID') return;
        List btm = str.split(',');
        List code = [
          'unknown',
          'SBC',
          'AAC',
          'APTX',
          'APTXLL',
          'APTXHD',
          'APTXAD'
        ];
        if (btm[0] == '0') {
          a31.btm1['sink'] = btm[0];
          a31.btm1['state'] = btm[1];
          a31.btm1['codec'] = code[int.parse(btm[2])];
          a31.btm1['name'] = btm[3];
        } else if (btm[0] == '1') {
          a31.btm2['sink'] = btm[0];
          a31.btm2['state'] = btm[1];
          a31.btm2['codec'] = code[int.parse(btm[2])];
          a31.btm2['name'] = btm[3];
        }
      }
      if (tag.contains('MMC')) {
        Log.d(a31.project);
        if (tag.contains('MMC:0')) a31.phonoMode.value = false;
        if (tag.contains('MMC:1')) a31.phonoMode.value = true;
        if (!a31.showMMC.value) {
          for (var el in Comm.devList) {
            if (el == 'A100_MCU') continue;
            if (a31.project.contains(el)) return;
          }
          a31.showMMC.value = true;
        }
      }
    }else if(data.contains('AXX+PLY+PLA')){
      // 切换音源，本机
      a31.getSourceInput();
    }else if(data.contains('AXX+PLM+')){
      if(a31.sourceInputList.isNotEmpty) return a31.getSourceInput();
      a31.newSourceInput.value = '';
      a31.swingSRC.value = false;
      // 切换输入源，linkplay
      String src = data.split('+')[2];
      if(src == '041' || src == '053'){
        // 蓝牙模式
        if(a31.sourceInput.value == 'BT') return;
        a31.sourceInput.value = 'BT';
      }else if(src == '040'){
        // LINE-IN模式
        if(a31.sourceInput.value == 'LINE-IN') return;
        a31.sourceInput.value = 'LINE-IN';
      }else if(src == '042'){
        // 外部USB模式
        if(a31.sourceInput.value == 'USBPLAY') return;
        a31.sourceInput.value = 'USBPLAY';
      }else if(src == '043'){
        // OPT模式
        if(a31.sourceInput.value == 'OPT') return;
        a31.sourceInput.value = 'OPT';
      }else if(src == '045'){
        // COAX模式
        if(a31.sourceInput.value == 'COAX') return;
        a31.sourceInput.value = 'COAX';
      }else if(src == '046'){
        // FM模式
        if(a31.sourceInput.value == '046') return;
        a31.sourceInput.value = '046';
      }else if(src == '047'){
        // LINE2模式
        if(a31.sourceInput.value == 'LINE-IN2') return;
        a31.sourceInput.value = 'LINE-IN2';
      }else if(src == '049'){
        // HDMI模式
        if(a31.sourceInput.value == 'HDMI') return;
        a31.sourceInput.value = 'HDMI';
      }else if(src == '051'){
        // USBDAC模式
        if(a31.sourceInput.value == 'USBDAC') return;
        a31.sourceInput.value = 'USBDAC';
      }else if(src == '054'){
        // PHONO模式
        if(a31.sourceInput.value == 'PHONO') return;
        a31.sourceInput.value = 'PHONO';
      }else if(src == '011' || src == '016'){
        // 表示单方式播放模块端的u盘
        if(a31.sourceInput.value == 'USBPLAY') return;
        a31.sourceInput.value = 'USBPLAY';
      }else{
        if(a31.sourceInput.value == 'NET') return;
        a31.sourceInput.value = 'NET';
      }
      Log.d(a31.sourceInput);
    }else if(data.contains('AXX+VOL+')){
      // 调整音量
      try { 
        double vol = double.parse(data.substring(0,11).split('+')[2]);
        a31.volume.value = vol;
      } catch (e) {
        Log.e('$data $e');
      }
    }else if(data.contains('AXX+SNG+INF')){
      // Log.o('mos');
      String str = data.substring(11,data.indexOf('&'));
      Map item = json.decode(str);
      // double curpos = double.parse(item['curpos']);
      // double totlen = double.parse(item['totlen']);
      String status = item['status'];
      if (status == 'play') {
        a31.isPlaying.value = true;
        a31.timerLoops ??= a31.startLoopTask();
      } else {
        a31.isPlaying.value = false;
        if (a31.timerLoops != null) {
          a31.timerLoops!.cancel();
          a31.timerLoops = null;
        }
      }
      // String status = item['status'];
      // String loop = item['loop'];
      // if (totlen < 1) {
      //   a31.totTalVal.value = 0.1;
      //   a31.playLengths.value = 0.1;
      // } else {
      //   a31.totTalVal.value = totlen / 1000;
      //   a31.playLengths.value = curpos / 1000;
      // }
      // if (status == 'play') {
        
      // } else if (status == 'stop') {

      // }
      // Log.o(curpos);
      // Log.o(totlen);
      // Log.o(status);
      // Log.o(loop);
    } else if(data.contains('AXX+PLY')) {
      if(a31.playLoading.value) a31.playLoading.value = false;
      // 播放状态
      String playing = data.split('+')[2];
      if(playing.contains('000')){
        a31.isPlaying.value = false;
        if(a31.timerLoops != null){
          a31.timerLoops!.cancel();
          a31.timerLoops = null;
        }
      }else if(playing.contains('001')){

        a31.isPlaying.value = true;
        a31.timerLoops ??= a31.startLoopTask();
      }
    } else if(data.contains('AXX+MEA+RDY') || data.contains('AXX+MEA+DAT')) {
      a31.getPlayInfo();
      a31.getPlaySongList();
    } else if(data.contains('AXX+SLV+NOT')) {
      a31.isRoomChild.value = false;
    } else if(data.contains('AXX+SLV+YES')) {
      a31.isRoomChild.value = true;
    } else if(data.contains('AXX+SLV+0')) {
      a31.getSlaveList();
    } else if(data.contains('AXX+PLP')) {
      String loopMode = data.split('+')[2];
      if(loopMode == '000'){
        a31.loopRandom.value = false;
        a31.loopMode.value = 1;
      }else if(loopMode == '001'){
        a31.loopRandom.value = false;
        a31.loopMode.value = 2;
      }else if(loopMode == '002'){
        a31.loopRandom.value = true;
        a31.loopMode.value = 1;
      }else if(loopMode == '003'){
        a31.loopRandom.value = true;
        a31.loopMode.value = 0;
      }else if(loopMode == '004'){
        a31.loopRandom.value = false;
        a31.loopMode.value = 0;
      }else if(loopMode == '005'){
        a31.loopRandom.value = true;
        a31.loopMode.value = 2;
      }
    } else if (data.contains('MCU+PAS+FMQ')) {
      final SearchFMCon searchFMCon = Get.put(SearchFMCon());
      if (searchFMCon.searchState.value) {
        searchFMCon.searchState.value = false;
      }
      String mhz = data.substring(data.indexOf(':') + 1, data.lastIndexOf('&'));
      if (a31.lastMhz.value == mhz) return;
      a31.fmVal.value = double.parse(mhz) / 1000;
      a31.changeFMPage();
    } else if (data.contains('MCU+PAS+FMP')) {
      String dat = data.substring(data.indexOf(':') + 1, data.lastIndexOf('&'));
      List lt = dat.split(',');
      for (int i = 0; i < lt.length; i++) {
        a31.fmPresetList[i] = lt[i];
      }
    } else if (data.contains('MCU+PAS+FMT')) {
      if (Get.currentRoute == Routes.fmPage) {
        final SearchFMCon searchFMCon = Get.put(SearchFMCon());
        searchFMCon.showSearchFM();
        // SearchFM.showSearchFM(a31);
      }
    } else if (data.contains('MCU+PAS+DSK:TOT')) {
      String dat = data.substring(data.lastIndexOf(':') + 1, data.lastIndexOf('&'));
      int par = int.parse(dat);
      if (par != a31.dataTot.value) {
        a31.dataList.clear();
        a31.dataKey.value = 0;
        a31.dataTot.value = par;
        Log.b('dataTot的值 ${a31.dataTot.value}');
      }
    } else if (data.contains('MCU+PAS+DSK:LST')) {
      try{
        String str = data.substring(16, data.lastIndexOf('&'));
        String id = str.substring(0,str.indexOf(':'));
        String songName = str.substring(str.indexOf(':')+1,str.length);
        Log.d('id: $id');
        Log.d('songName: $songName');

        for (var el in a31.dataList) {
          if (el['id'].toString() == id) el['name'].value = songName;
        }
        // 本地存储一下歌曲
        if ((int.parse(id) + 1) == a31.dataTot.value) {
          String uuid = a31.uuid;
          List songList = [];
          for (var el in a31.dataList) {
            songList.add({
              'id': el['id'],
              'name': el['name'].value,
            });
          }
          String storageKey = '$uuid:USB:SONGLIST';
          if (StorageClass.getStorage(storageKey) == null) {
            StorageClass.setStorage(storageKey, json.encode(songList));
          }
        }
      }catch(_){}
    } else if (data.contains('MCU+PAS+DSK:CUR')) {
      String str = data.substring(16, data.lastIndexOf('&'));
      a31.usbPlayIndex.value = int.parse(str);
      // str = str.substring(4,str.length);
      // try{
      //   ble.usbPlayIndex.value = int.parse(str);
      // }catch(_){}
    }
  }
}
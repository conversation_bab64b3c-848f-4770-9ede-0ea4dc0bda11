
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/bottmsheet.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/AppSetting/scroll_control.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/theme/theme.dart';

class BleMsg {
  static getMsg(String data,AbstractBLE ble) async {
    // 截取内容
    String str;
    
    if(!data.contains(';')) {
      str = data.substring(data.indexOf(':') + 1,data.length);
    }else{
      str = data.substring(data.indexOf(':') + 1,data.length - 1);
    }
    
    Log.h('${ble.device.platformName}: [$data]');
    // 设备名字
    if(data.contains('NAM:')){
      // String name = Comm.hexStrToString(str);
      if (str != '' || str != 'UNKNOW') {
        // ble.name.value = name;
        ble.initBLEInfo();
        ble.name.value = ble.device.platformName.substring(0,ble.device.platformName.length - 4);
        // int index = homCon.adevices.indexWhere((device)=> device.origin == Origins.net && device.name.value == name);
        // if (index != -1) {
        //   Log.e('$name重复了');
        //   ble.disconnectBLE();
        //   homCon.removeDevice(ble);
        //   return;
        // }
        // homCon.addDeivce(ble);
        // if (homCon.selectDevice != ble) await ble.disconnectBLE();
      } else {
        Log.e('无法得到 ${ble.device.platformName}');
        homCon.adevices.remove(ble);
        // ble.name.value = ble.device.platformName;
      }
    }
    if (data.contains('AVL:USBPLAY')) {
      String nn = str.substring(8,str.length);
      if (nn == '0') ble.clearAllUSBinfo();
    }
    if (data.contains('VST')) {
      try{
        ble.vst.value = int.parse(str);
      }catch(_){}
    }
    if (data.contains('LED')) {
      if (str == '1') {
        ble.led.value = true;
      } else {
        ble.led.value = false;
      }
    }
    // 设备的音量
    if (data.contains('VOL')) {
      try {
        ble.volume.value = double.parse(str);
      } catch(_) {}
    }
    
    // 设备输入源
    if (data.contains('SRC:')) {
      ble.newSourceInput.value = '';
      ble.swingSRC.value = false;
      ble.sourceInput.value = str;
      if (str == 'USBPLAY') {
        Future.delayed(const Duration(milliseconds: 666),(){
          if (ble.dataList.isEmpty) ble.sendMsgToBLE(msg: 'DSK:TOT;');
        });
      }
      if (ble is B50) {
        // b50
        if (ble.deviceMode.value == 'B50' || ble.deviceMode.value == 'BP50') {
          ble.showBtm1.value = true;
          ble.showBtm2.value = true;
        }
        // bt10
        if (ble.deviceMode.value == 'BT10') {
          if (str != 'BT') {
            ble.showBtm1.value = true;
            ble.showBtm2.value = true;
          } else {
            ble.showBtm1.value = false;
            ble.showBtm2.value = false;
          }
        }
        if (ble.deviceMode.value == 'BR10') {
          if (str == 'BT') {
            ble.showBtm1.value = true;
            ble.showBtm2.value = true;
          } else {
            ble.showBtm1.value = false;
            ble.showBtm2.value = false;
          }
        }
      }
    }
    if (data.contains('DMO:')) {
      Future.delayed(const Duration(milliseconds: 666),()=> ble.sendMsgToBLE(msg: 'DSK:TOT;'));
    }
    // 设备可用输入源
    if(data.contains('LST:') && !data.contains('CEQ:LST') && !(data.contains('DSK:LST'))){
      try{
        ble.sourceInputList.clear();
        List lt = str.split(',');
        List<String> order = ['NET', 'BT', 'FM', 'LINE-IN', 'LINE-IN2', 'PHONO', 'OPT', 'HDMI', 'USB', 'USBPLAY', 'USBDAC', 'COAX', 'DAB'];
        lt.sort((a, b) {
          int indexA = order.indexOf(a);
          int indexB = order.indexOf(b);
          if (indexA == -1 && indexB == -1) {
            return 0; // 如果两个元素都不在指定顺序列表中，则保持原有顺序
          } else if (indexA == -1) {
            return 1; // 如果a不在指定顺序列表中，则将b排在a前面
          } else if (indexB == -1) {
            return -1; // 如果b不在指定顺序列表中，则将a排在b前面
          } else {
            return indexA.compareTo(indexB); // 按照指定顺序列表的顺序进行排序
          }
        });
        for (var el in lt) {
          Comm.sendSource(ble, el);
        }
      }catch(_){}
      // StorageClass.setStorage('${ble.device.remoteId}-SRC','$lt');
    }
    if (data.contains('CEQ:LST')) {
      str = str.substring(4, str.length);
      BaseAudio audio = ble as BaseAudio;
      if (str != '') {
        List ls = str.split(',');
        audio.customEQList.clear();
        for (var el in ls) {
          String id = el.substring(0, el.indexOf('@'));
          String name = el.substring(el.indexOf('@') + 1, el.length);
          audio.customEQList.add({'id': id, 'name': name});
        }
      } else {
        audio.customEQList.clear();
        // audio.setEqIndex(0);
        audio.clearCustomEQval();
      }
    }
    if(data.contains('DSK:LST')){
      try{
        str = str.substring(4,str.length);
        String id = str.substring(0,str.indexOf(':'));
        String songName = str.substring(str.indexOf(':')+1,str.length);
        for (var el in ble.dataList) {
          if (el['id'].toString() == id) el['name'].value = songName;
        }
        // 本地存储一下歌曲
        if ((int.parse(id) + 1) == ble.dataTot.value) {
          String remoteId = ble.device.remoteId.toString();
          List songList = [];
          for (var el in ble.dataList) {
            songList.add({
              'id': el['id'],
              'name': el['name'].value,
            });
          }
          String storageKey = '$remoteId:USB:SONGLIST';
          if (StorageClass.getStorage(storageKey) == null) {
            StorageClass.setStorage(storageKey, json.encode(songList));
          }
        }
      }catch(_){}
    }
    
    if(data.contains('CEQ:FLT')){
      String newStr = str.substring(4,str.length);
      try{
        BaseAudio audio = ble as BaseAudio;
        int id = int.parse(newStr.substring(0,newStr.indexOf(':')));
        int flt = int.parse(newStr.substring(newStr.indexOf(':') + 1,newStr.length).split(',')[2],radix: 16).toSigned(16);
        double fltDouble = flt / 256;
        audio.customEQValueList[id]['gain'].value = fltDouble;
      }catch(_){}
    }

    if(data.contains('DSK:TOT')){
      ble.dataList.clear();
      str = str.substring(4,str.length);
      ble.sendMsgToBLE(msg: 'DLM;');
      try{
        ble.dataTot.value = int.parse(str);
        if (Get.currentRoute == '/ble_page/usbplayPage') {
          ble.getSongListForUSB();
        }
      }catch(_){}
    }

    if (data.contains('DLM')) {
      final b50 = ble as B50;
      b50.usbPlayIndexForRandom.value = int.parse(str);
    }

    if (data.contains('DSK:CUR')) {
      str = str.substring(4,str.length);
      try{
        ble.usbPlayIndex.value = int.parse(str);
        if (Get.currentRoute == '/ble_page/usbplayPage') {
          final scc = Get.put(ScrollViewControl());
          if (ble.dataList.length >= ble.usbPlayIndex.value && ble.usbPlayIndex.value > 5) {
          final index = ble.usbPlayIndex.value - 1;
          if (index + 5 < ble.dataList.length) {
            scc.scrollController.animateTo(
              index * 46.sp, 
              duration: const Duration(milliseconds: 300), 
              curve: Curves.easeInOut,
            );
          } else {
            scc.scrollController.animateTo(
              (index - 5) * 44.sp, 
              duration: const Duration(milliseconds: 300), 
              curve: Curves.easeInOut,
            );
          }
        }
        }
      }catch(_){}
    }

    if(data.contains('BMO:') && ble is B50){
      final modes = ['B50', 'B50SE', 'BP50', 'BR10', 'BT10', 'Up2Cast'];
      if (modes.contains(str)) {
        ble.deviceMode.value = str;
        if (str == 'Up2Cast') ble.sendMsgToBLE(msg: 'AST;');
      } else {
        ble.deviceMode.value = 'B50';
      }
    }

    if (data.contains('AST:') && ble is B50) {
      int type = int.parse(str);
      ble.auracastType.value = type;
      if (ble.auracastChild.value && type != 1) {
        for (var el in homCon.adevices) {
          if (el is B50 && el.device.remoteId.toString() == homCon.masterId) {
            el.childForAuracast.remove(ble);
            homCon.auracastChildList.remove(ble.device.remoteId.toString());
            homCon.auracastChildListDevice.remove(ble);
          }
        }
        return;
      }
    }

    if (data.contains('AMN:') && ble is B50) {
      ble.auraCastMastName.value = str;
    }

    if (data.contains('BTS:') && ble is B50) {
      if (str == '4' || str == '5' || str == '6') {
        ble.btsPlay.value = true;
      } else {
        ble.btsPlay.value = false;
      }
    }

    if (data.contains('BTM') && ble is B50) {
      if(str == 'INVALID') return;
      List btm = str.split(',');
      List code = [
        'unknown',
        'SBC',
        'AAC',
        'APTX',
        'APTXLL',
        'APTXHD',
        'APTXAD'
      ];
      if(btm[0] == '0'){
        ble.btm1['sink'] = btm[0];
        ble.btm1['state'] = btm[1];
        ble.btm1['codec'] = code[int.parse(btm[2])];
        ble.btm1['name'] = btm[3];
      }else if(btm[0] == '1'){
        ble.btm2['sink'] = btm[0];
        ble.btm2['state'] = btm[1];
        ble.btm2['codec'] = code[int.parse(btm[2])];
        ble.btm2['name'] = btm[3];
      }
    }

    if(data.contains('TRE:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.treble.value = double.parse(str);
      }catch(_){}
    }
    if(data.contains('MID:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.mid.value = double.parse(str);
      }catch(_){}
    }
    if (data.contains('BAS:')) {
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.bass.value = double.parse(str);
      }catch(_){}
    }
    if (data.contains('BAL:')) {
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.balance.value = double.parse(str);
      }catch(_){}
    }
    if(data.contains('MXV:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.maxVolume.value = double.parse(str);
      }catch(_){}
    }
    if(data.contains('CFE:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        if(str == '1'){
          audio.crossover.value = true;
        }else{
          audio.crossover.value = false;
        }
      }catch(_){}
    }
    if(data.contains('CFF:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.frequency.value = double.parse(str);
      }catch(_){}
    }
    if(data.contains('VBS:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        if(str == '1'){
          audio.deepBass.value = true;
        }else{
          audio.deepBass.value = false;
        }
      }catch(_){}
    }
    if(data.contains('VBI:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.intensity.value = double.parse(str);
      }catch(_){}
    }
    if(data.contains('EQS:')){
      try{
        BaseAudio audio = ble as BaseAudio;
        audio.eqIndex.value = int.parse(str);
        if(audio.eqIndex.value > 9){
          audio.initCustomEQval();
        }else{
          audio.clearCustomEQval();
        }
      }catch(_){}
    }
    if(data.contains('PEQ:')){
      try{
        // Log.d(str);
        BaseAudio audio = ble as BaseAudio;
        audio.setEqList(str);
      }catch(_){}
    }
    if(data.contains('MMC:') && ble is B50){
      if(str == '1'){
        ble.phonoMode.value = true;
      }else{
        ble.phonoMode.value = false;
      }
    }
    if(data.contains('ARC:') && ble is B50){
      if(str == '0'){
        ble.arcMode.value = true;
      }else{
        ble.arcMode.value = false;
      }
    }
    if(data.contains('TLM:') && ble is B50){
      if(str == '1'){
        ble.tlmMode.value = true;
      }else{
        ble.tlmMode.value = false;
      }
    }
    if(data.contains('VER:') && ble is B50){
      ble.version.value = str;
    }
    if(data.contains('UPG:')){
      if(str != '0'){
        // String r = 'update_text'.tr.replaceAll('%s', str);
        // r = r.replaceAll('%r', ble.version.value.substring(0,ble.version.value.indexOf('-')));
        String r = 'update_text'.tr.replaceAll('%s', str);
        int dashIndex = ble.version.value.indexOf('-');
        if (dashIndex != -1) {
          r = r.replaceAll('%r', ble.version.value.substring(0, dashIndex));
        } else {
          return;
        }
        ble.updateStr.value = r;
        ble.updateVer.value = true;
        showMyBottomSheet(
          height: 260.sp,
          title: 'update'.tr,
          content: Container(
            padding: EdgeInsets.symmetric(horizontal: 18.sp),
            child: AText(
              text: r,
              color: themeBase.primaryColor.value,
              softWrap: true,
            ),
          ),
          showCancel: true.obs,
          cancel: ()=> Get.back(),
          confirm: (){
            ble.sendMsgToBLE(msg: 'UPG:CONFIRM;');
            Get.back();
          }
        );
      }else{

      }
    }
  }


  // 配网信息处理
  static listenSetupNetWork(List<int> data,A31 a31) {
    // Log.e('$data');
    if (data.isEmpty) return;
    final start = (data[0] == 80 && data[1] == 76);
    if (start) {
      Log.d('当前是100${data[6]}');
      // 截取长度和内容
      List<int> len = [data[10],data[11]];
      List<int> strList = List.from(data)..removeRange(0, 12);
      // 清楚上一次的结果
      a31.wifiSetMsg.clear();
      a31.wifiSetMsgLength.value = 0;
      // 设置数据长度和添加数据到wifi信息中
      a31.wifiSetMsg.addAll(strList);
      a31.wifiSetMsgLength.value = bytesToInteger(len);
      if(data[6] == 6) {
        netWorkResult(a31);
      }
    } else {
      a31.wifiSetMsg.addAll(data);
      if (a31.wifiSetMsgLength.value == a31.wifiSetMsg.length) {
        if (a31.wifiSetupIndex.value == 1) {
          String str = Comm.hexStrToString(decodeByteArray(a31.wifiSetMsg));
          Log.r(str);
          try {
            Map strMap = json.decode(str)['commonInfo'];
            String uuid = strMap['uuid'].toString();
            a31.typeModel.value = uuid.substring(0,4);
          } catch(_) {}
          Future.delayed(const Duration(milliseconds: 200),()=> a31.setupWifiThe1002());
        } else if (a31.wifiSetupIndex.value == 2) {
          try{
            Map wifiInfo = json.decode(Comm.hexStrToString(decodeByteArray(a31.wifiSetMsg)));
            a31.wifiScanList.value = wifiInfo['aplist'];
            Log.h('完整的wifi列表${a31.wifiScanList}');
            a31.wifiSetupIndex.value = 3;
          } catch(e) {
            Log.e('wifi信息获取失败 $e');
            Get.back();
            AppToast.show('error');
          }
        } else if(a31.wifiSetupIndex.value == 5) {
          final nem = Comm.hexStrToString(decodeByteArray(a31.wifiSetMsg));
          Log.h('1006信息是 --- $nem');
          netWorkResult(a31);
        }
      }
    }
  }
    
  //   if (data[0] == 80 && data[1] == 76) {
  //     Log.d('当前是100${data[6]}');
  //     /// 截取长度和内容
  //     List<int> len = [data[10],data[11]];
  //     List<int> strList = List.from(data)..removeRange(0, 12);
  //     /// 清楚上一次的结果
  //     device.setupWiFiMsgLen.value = 0;
  //     device.setupWiFiMsg.clear();
  //     /// 设置数据长度和添加数据到wifi信息中
  //     device.setupWiFiMsgLen.value = bytesToInteger(len);
  //     device.setupWiFiMsg.addAll(strList);
  //     if(data[6] == 6) netWorkResult(device);
  //   } else {
  //     device.setupWiFiMsg.addAll(data);

  //     if (device.setupWiFiMsgLen.value == device.setupWiFiMsg.length) {
        
  //       if (device.beginWiFiSetup.value == 1) {
  //         String str = Comm.hexStrToString(decodeByteArray(device.setupWiFiMsg));
  //         Log.e(str);
  //         try {
  //           Map strMap = json.decode(str)['commonInfo'];
  //           String uuid = strMap['uuid'].toString();
  //           device.typeModel.value = uuid.substring(0,4);
  //         } catch(e) {
  //           Log.e(e);
  //           Get.back();
  //           device.setupWiFiMsgLen.value = 0;
  //           device.setupWiFiMsg.clear();
  //         }
  //         Future.delayed(const Duration(milliseconds: 300),()=> device.sendSetup1002());
  //       } else if (device.beginWiFiSetup.value == 2) {
  //         try{
  //           Map wifiInfo = json.decode(Comm.hexStrToString(decodeByteArray(device.setupWiFiMsg)));
  //           device.wifiScanList.value = wifiInfo['aplist'];
  //           Log.h('完整的wifi列表${device.wifiScanList}');
  //         }catch(e){
  //           Log.e('wifi信息获取失败 $e');
  //           // Get.back();
  //         }
  //       } else if(device.beginWiFiSetup.value == 3) {
  //         Log.h('开始配置信息${Comm.hexStrToString(decodeByteArray(device.setupWiFiMsg))}');
  //       } else if(device.beginWiFiSetup.value == 4) {
  //         Log.h('1006信息是---${Comm.hexStrToString(decodeByteArray(device.setupWiFiMsg))}');
  //         netWorkResult(device);
  //       }
  //     }
  //   }
  // }
  
  static netWorkResult(A31 a31) async {
    if (a31.wifiSetMsg.isEmpty) return;
    try{
      Map result = json.decode(utf8.decode(a31.wifiSetMsg));
      if (result.containsKey('code') && result['code'] == '4') {
        a31.wifiSetupIndex.value = 3;
        AppToast.show('wrong_password'.tr);
      } else if(result.containsKey('code') && result['code'] == '0') {
        a31.wifiConnectResult.value = 1;
        a31.disconnectBLE();
        await Future.delayed(const Duration(seconds: 3),() async {
          AppToast.show('toast1'.tr);
          await Future.delayed(const Duration(milliseconds: 500));
          if (Get.currentRoute == '/home' || Get.currentRoute == '/') return;
          Get.back();
        });
      }
    } catch(e) {
      Log.e('err');
    }
  }
  
  /// 合并两个字节成一个整数
  static int bytesToInteger(List<int> byteList) {
    int combinedInt = (byteList[1] << 8) | byteList[0];
    return combinedInt;
  }
  /// 将字节转换为字符串
  static String decodeByteArray(List<int> byteList) {
    // 判断是否是十六进制字符串
    bool isHex = byteList.every((byte) => byte >= 0 && byte <= 255);
    if (isHex) {
      // 如果是十六进制字符串，将字节列表转换为十六进制字符串
      String hexString = byteList.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
      return hexString;
    } else {
      // 如果不是十六进制字符串，将字节列表解码为UTF-8字符串
      String utf8String = utf8.decode(byteList);
      return utf8String;
    }
  }
}
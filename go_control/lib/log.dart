// ignore_for_file: avoid_print
import 'dart:io';
import 'package:ansicolor/ansicolor.dart';

class Log {
  static String errText = '';
  static String _getStack({bool err = false}) {
    if (!Platform.isIOS) ansiColorDisabled = false;
    final trace = StackTrace.current;
    final location = trace.toString().split("\n")[2].split(":");
    final fileName = location[1].trim();
    final lineNumber = location[2].trim();
    if (err) {
      final e1 = trace.toString().split("\n")[2];
      final e2 = trace.toString().split("\n")[3];
      final e3 = trace.toString().split("\n")[4];
      final errMsg = ' \n $e1 \n $e2 \n $e3 \n 时间: ${DateTime.now()}';
      return errMsg;
    }
    return '($fileName...$lineNumber)';
  }

  // 成功打印信息(绿色)
  static void d(Object? msg){
    AnsiPen pen = AnsiPen()..xterm(121);
    final returnMsg = _getStack();
    print(pen('🟩 $msg $returnMsg'));
  }

  // 错误打印信息(红色)
  static void e(Object? msg){
    AnsiPen pen = AnsiPen()..xterm(203);
    final returnMsg = _getStack(err: true);
    final xc1 = '$msg'.contains('No host specified in URI');
    final xc2 = '$msg'.contains('remove = false');
    if (!xc1 && !xc2) {
      errText += '$msg $returnMsg';
    }
    print(pen('🟥 $msg $returnMsg'));
  }

  // 循环打印信息(紫色)
  static void r(Object? msg){
    AnsiPen pen = AnsiPen()..xterm(177);
    final returnMsg = _getStack();
    print(pen('🟪 $msg $returnMsg'));
  }

  /// 回调打印信息(粉色)
  static void h(Object? msg) {
    AnsiPen pen = AnsiPen()..xterm(218);
    final returnMsg = _getStack();
    print(pen('🌸 $msg $returnMsg'));
  }

  // 深蓝色
  static void b(Object? msg) {
    AnsiPen pen = AnsiPen()..xterm(12);
    final returnMsg = _getStack();
    print(pen('🟦 $msg $returnMsg'));
  }

  // 黄色
  static void y(Object? msg){
    AnsiPen pen = AnsiPen()..xterm(11);
    final returnMsg = _getStack();
    print(pen('🟨 $msg $returnMsg'));
  }

  // 白灰色
  static void w(Object? msg){
    AnsiPen pen = AnsiPen()..xterm(7);
    final returnMsg = _getStack();
    print(pen('⬜️ $msg $returnMsg'));
  }

  // 橙色
  static void o(Object? msg){
    AnsiPen pen = AnsiPen()..xterm(208);
    final returnMsg = _getStack();
    print(pen('🟧 $msg $returnMsg'));
  }

  // 紫红色
  static void p(Object? msg){
    AnsiPen pen = AnsiPen()..xterm(169);
    final returnMsg = _getStack();
    print(pen('🌺 $msg $returnMsg'));
  }

}

import 'dart:io';
import 'package:flutter/services.dart';
import 'package:gocontrol/log.dart';
import 'package:nsd/nsd.dart';

class NetWorkApi {
  NetWorkApi();

  // 发现的服务
  Discovery? discovery;

  // 存储已发现的 IP 列表
  final Set<String> _ips = {};

  // 创建并发现一个网络服务下面的设备
  void createDiscovery(Function callback, {
    String serviceType = '_linkplay._tcp',
    int serverPort = 8899
  }) async {
    // 如果 discovery 存在先关闭
    if (discovery != null) disposeDiscovery();
    // 开启服务并返回设备 ip
    _ips.clear();
    discovery = await startDiscovery(serviceType);
    discovery!.addServiceListener((service, status) async {
      if (status == ServiceStatus.found) {
        String? host = service.host;
        if (host != null) {
          String ip = await resolveDeviceIP(host);
          if (ip != '-1' && !_ips.contains(ip)) {
            _ips.add(ip);
            callback(ip);
          }
        }
      }
    });
  }
  // 停止搜索附近指定网络服务
  void disposeDiscovery() {
    if (discovery != null) {
      discovery!.dispose(); // 停止服务发现
      discovery = null; // 清空 discovery，以允许后续启动
    } 
  }

  // 处理得到的host并解析ip
  Future<String> resolveDeviceIP(String host) async {
    final addresses = await InternetAddress.lookup(host);
    String ip = '-1';
    for (var address in addresses) {
      if (address.type == InternetAddressType.IPv4) ip = address.address;
    }
    return ip;
  }

  // 解析从socket拿到的数据
  Map<String, dynamic> parseData(List<int> data) {
    // 转换字节高低位
    int bytesToIntLittleEndian(Uint8List bytes) {
      try{
        int result = 0;
        for (int i = 0; i < bytes.length; i++) {
          result += bytes[i] << (8 * i);
        }
        return result;
      }catch(e){
        return 0;
      }
    }
    // 解析数据包
    try{
      Map<String, dynamic> result = {};
      Uint8List header = Uint8List.fromList(data.sublist(0, 4));
      result['header'] = header;
      Uint8List lengthBytes = Uint8List.fromList(data.sublist(4, 8));
      int length = bytesToIntLittleEndian(lengthBytes);
      result['length'] = length;
      Uint8List checksumBytes = Uint8List.fromList(data.sublist(8, 12));
      int checksum = bytesToIntLittleEndian(checksumBytes);
      result['checksum'] = checksum;
      Uint8List reserved = Uint8List.fromList(data.sublist(12, 20));
      result['reserved'] = reserved;
      Uint8List payload = Uint8List.fromList(data.sublist(20));
      result['payload'] = payload;
      return result;
    }catch(err){
      Log.e('解析数据时出现错误，错误信息:$err,所以返回空Map对象{}');
      return {};
    }
  }

  // 转换需要发送的数据
  List<int> toPackage(String data) {
    // 固定 header
    List<int> header = [0x18, 0x96, 0x18, 0x20];
    // 计算 payload 内容的长度
    int dataLen = data.length;  
    // 计算 checksum，将 payload 中每个字节的 ASCII 值相加
    int checksum = 0;
    for (int i = 0; i < data.length; i++) {
      checksum += data.codeUnitAt(i);
    }
    // 构建 length 的 little endian 格式
    List<int> lengthBytes = [];
    for (int i = 0; i < 4; i++) {
      lengthBytes.add(dataLen & 0xFF);
      dataLen >>= 8;
    }
    // 构建 checksum 的 little endian 格式
    List<int> checksumBytes = [];
    for (int i = 0; i < 4; i++) {
      checksumBytes.add(checksum & 0xFF);
      checksum >>= 8;
    }
    // 固定 reserved
    List<int> reserved = [0, 0, 0, 0, 0, 0, 0, 0];  
    // 将所有部分合并成整数列表 并返回
    return [...header, ...lengthBytes, ...checksumBytes, ...reserved, ...data.codeUnits];
  }

}
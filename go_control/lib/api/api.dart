import 'package:gocontrol/api/http_api.dart';
import 'package:gocontrol/api/luci_api.dart';
import 'package:gocontrol/api/mcu_api.dart';
import 'package:gocontrol/api/network_api.dart';
import 'package:gocontrol/api/upnp_api.dart';

class ApiManage {
  
  // 功能: 播放控制 播放信息 输入源 多房间控制 网络设置 定时器和闹钟
  static HttpApi httpApi = HttpApi();
  
  // 
  static TcpApiAry tcpApi = TcpApiAry();

  // 功能：播放控制 播放信息 歌曲列表
  static UpnpApi upnpApi = UpnpApi();

  // Libre 设备模组  LP10等设备
  static LuciApi luciApi = LuciApi();
  
  // 网络设备相关
  static NetWorkApi netWorkApi = NetWorkApi();


}
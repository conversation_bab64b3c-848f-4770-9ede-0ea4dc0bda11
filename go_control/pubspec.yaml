name: gocontrol
description:
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.218+436
versionName: 436

environment:
  sdk: '^3.8.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  get: ^4.6.6
  win32: ^5.5.4
  archive: ^3.6.1
  lottie: ^3.1.2
  flutter_screenutil: ^5.9.0
  easy_refresh: ^3.4.0
  ansicolor: ^2.0.3
  get_storage: ^2.1.1
  nsd: ^4.0.3
  crypto: ^3.0.3
  http: ^1.2.1
  cupertino_icons: ^1.0.5
  url_launcher: ^6.2.4
  build_runner: ^2.1.4
  flutter_blue_plus: ^1.35.3
  flutter_colorpicker: ^1.0.3
  permission_handler:
  gbk_codec: ^0.4.0
  visibility_detector: ^0.4.0+2
  flutter_launcher_icons: ^0.14.2
  xml2json: ^6.2.3
  marquee: ^2.3.0
  tinycolor2: ^3.0.1
  loading_animation_widget: ^1.2.1
  reorderables: ^0.6.0
  flutter_svg: ^2.0.10+1
  hardware_buttons_find_flutter: ^0.0.4
  cached_network_image: ^3.4.1
  html: ^0.15.4
  upnp2: ^3.0.12
  path_provider: ^2.1.3
  infinite_scroll_pagination: ^4.0.0
  carousel_slider: ^5.0.0
  is_app_installed: ^0.0.4
  fluttertoast: ^8.2.8
  palette_generator: ^0.3.3+5
  network_info_plus: ^6.1.3
  android_intent_plus: ^5.3.0
  device_apps: ^2.1.1
  package_info_plus: ^8.2.0
  simple_circular_progress_bar: ^1.0.2
  open_settings_plus: ^0.4.0
  connectivity_plus: ^6.1.3
  window_manager: ^0.4.3
  on_audio_query: ^2.9.0
  intranet_ip: ^1.0.2
  get_settings: 1.0.6
  bwee_interface: ^1.0.0
  bwee_mod: ^1.0.1
  gocomponent: ^1.0.1

  base_common: ^1.0.0
  rk_get: ^1.0.0
  tl_getx_router_gen_annotations: ^1.0.0
  rk_package: ^1.0.0
  
  
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - images/volume0.svg
    - images/volume1.svg
    - images/volume2.svg
    - images/volume3.svg
    - images/google_cast.png
    - images/google_logo.png
    - images/tidal_logo.png
    - images/spotify2.png
    - images/google_works.png
    - images/google_works.webp
    - images/arylic_icon.svg
    - images/aroha_icon.svg
    - images/button.json
    - images/music.svg
    - images/light.svg
    - cert/client.pem
    - cert/client.key
    


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: IconFont
      fonts:
        - asset: fonts/iconfont.ttf
    - family: Bold
      fonts:
        - asset: fonts/D-DIN-Bold.otf
          style: italic
    - family: Medium
      fonts:
        - asset: fonts/D-DIN.otf
          style: italic
    - family: Regular
      fonts:
        - asset: fonts/D-DIN.otf
          style: italic
          weight: 100
    - family: Medium
      fonts:
        - asset: fonts/D-DIN.otf
          style: italic
    
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c329620c51892527db69ac984ef9321b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e986eaba3bbf34fffc52894406988f981b0", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9804db47a3ceef83edd118018eb43bf272", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb715f494f871512dadd78a4afa179e0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9aeba378b9d173e0c905869bfc24604", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980172d038fbc08dfe1d95191f996fc468", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/ios/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d4f7c5de2ea9200912a52ea688829734", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/ios/connectivity_plus/Sources/connectivity_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98747e3720e51631183a30d500305a0e79", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef166e8ae90983c0c462f9f5700bf900", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eae6138effd27f6a27ecb2bfff926c7c", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b552f22c8fca3a0e8ff5d53ce8b314ee", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a59a0d527be3143bbe6004e286b1022d", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812d3e07417c9fea71aee9d8a619c5d17", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c73afc9cfc53e351bc43d1447feb56c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d2ac7c2fa1dda360c630ce21de937a7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982782611cead43fc8f755bfe03c47fb11", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c89bf7ac795212498b72def4411e692", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d99359bf98957d38c9ca55d637140148", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860872fe19c00de4d779254d47a6b4704", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986240070f33a28719a33b78dfefea3c24", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98084bc0491638a0d29e348747634447d3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d21d7a4cf1f4fbd4e3361689f5c35dd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981355d73aaa60197a0c98b4aef2ab5c6c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98456a1455decc96455291d979527d2cb6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980142663a5b3e17f594cb1c4ec3322770", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98431c2d2771e33408deba0624c0aa28b6", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/ios/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c88360c4808c7fc617f797820f0eb2f2", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/ios/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ee3a08b3ab2c2ef1c782794a857d977e", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ea7b6eb3019da571f09cf2b6caccc94e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984afb483e14e740f69f732b8ca34c8e17", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f19ae371c778420a7c9321e3d84a27ed", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cfbad4db1a3b8d588ac4d3402837d663", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985efb0ec75e79354d7c208f6e42831238", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875633ef66cb2ad84e7df5cf6817c2d02", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98593f00bfd0479b56ab0e1bc27799dfac", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986f9abe552945fcbcd3be1e84a8dd9013", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b57a4815c1b6b440d4cd2b744574a65c", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f408943fc3f4c60f96c84c511a060081", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ad7980d6f6bcd21a4bbfe3959f756ad", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b1772b0d7c2eb21ddaeedfcd5c97d4b", "path": "../../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Classes/FFmpegKitFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b58936cba5cd9118abc15b07b254dfe2", "path": "../../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Classes/FFmpegKitFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980c29b9079691bb481b6c2a48a3f18db1", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899bf4a33826fcc39caeb8183573d7887", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c071045ec4fa2b6646afd1325793d18f", "name": "ffmpeg_kit_flutter_full_gpl", "path": "ffmpeg_kit_flutter_full_gpl", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e2afa4ae97150f843a2015f52fa1c9d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98123edaaa959eaba6acdc10e1decde4db", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f174db649921554c025dfa76801f57a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab78180239ea56cf3185481e7512469d", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98026eb0f44860836fb372d324b1049f14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98458be0e66b3b8e0513548c2895623c48", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985947a3bfbcd8a52081fb6845f19e1859", "name": "..", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98663b56dd3b5bff79556fdbdb0f5546a6", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/ffmpegkit.framework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e987207117a90f6c239884fad73f221eb6d", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/libavcodec.framework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e983049b1e95140d81f9a0c9ab8cdd73a20", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/libavdevice.framework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e985858e16f68d7115d86bea264531b0114", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/libavfilter.framework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9807e3aa194d4601186f4f7056b460ef1b", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/libavformat.framework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98ab3772a881e0e5b50296e17c3e028d8c", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/libavutil.framework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9845035e317f77101ed3da67a8e6eadd8b", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/libswresample.framework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98bd4c37faad342b737a2caf29042e3854", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/Frameworks/libswscale.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986901dea93413845b2ad31a376beea2de", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf69764b56b0bbe1108d0437cbbbd1b7", "name": "full-gpl-lts", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f971cf7a26804ac54b7e2691b7bacd06", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/ios/ffmpeg_kit_flutter_full_gpl.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b4da3116ac7423541c98f5efead34458", "path": "../../../../../../ffmpeg_kit_flutter_full_gpl/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983199085a90a42552a15b37fe493fcf8c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a218d06908b916daf67afa4c272502e3", "path": "ffmpeg_kit_flutter_full_gpl.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9892c6c3652a8cace2ffb07977c984f189", "path": "ffmpeg_kit_flutter_full_gpl-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ee27caec2693354141273f8af4ccc71b", "path": "ffmpeg_kit_flutter_full_gpl-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2c37fc16c32bfc4b1eea3d738b01326", "path": "ffmpeg_kit_flutter_full_gpl-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec2a459749b81a9d3e61cefef4e726ea", "path": "ffmpeg_kit_flutter_full_gpl-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983ca26119cd639e8f576bfe9dbf7a71e9", "path": "ffmpeg_kit_flutter_full_gpl.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9859b8bf1057928d5768616db360bd2331", "path": "ffmpeg_kit_flutter_full_gpl.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9881efff1c732ab9970221eeebfaa4adfa", "name": "Support Files", "path": "../../../../Pods/Target Support Files/ffmpeg_kit_flutter_full_gpl", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f721fd335ab2e3ca4bff67bca2a78a15", "name": "ffmpeg_kit_flutter_full_gpl", "path": "../.symlinks/plugins/ffmpeg_kit_flutter_full_gpl/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9899caffa8e8c268b9002dd9cee1011f16", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981b9394150fb9fb37b7c18042cd51828d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986170a06d2e27ece657a708296caa2221", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bbf7c7cca1dce80076c07e22ddbde35a", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9811a95355926da5aaa33cabf26fd12d1f", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873ab2cbd50e2f4bbb5d11d80a58f5656", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98265312f091e4cd6e0d6e5468e9f95631", "path": "../../../../../../../flutter_avasset_reader/ios/Classes/FlutterAvassetReaderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fb76117eac96fd2cf4b1017749366298", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808509db997c7aaaf2ee80be1320c31c7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ee083d708346651dcc7c679a5e66117", "name": "flutter_avasset_reader", "path": "flutter_avasset_reader", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98246ce72587692ead87d4ea90e4d3cd08", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1517099c22cdabafc298e6d90a3d5d8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6d39f12cd02c4fe2fa441fa5a41a38f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983db0a56e9d5412b843c81978b16bc567", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981891dc1211087f8bf8552595ec18afd0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816f50dab9a94e80e46a15ef24ad827f8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f9aefdf83bbf43e0cbe6fe7b4bcd948", "name": "..", "path": "../../../../../../flutter_avasset_reader/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9878480f5a143ee2c489c0ee55454229c6", "path": "../../../../../../flutter_avasset_reader/ios/flutter_avasset_reader.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fedbd53944afaf6ac3e5309177098645", "path": "../../../../../../flutter_avasset_reader/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981bced75a24a6f943e7d3ef0ecc3ad06a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986b65f289f4b256cbce8de5c270802919", "path": "flutter_avasset_reader.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987bb0353d067bd32c9a80129b905e66d8", "path": "flutter_avasset_reader-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9878c6d4c0dcf30e0e3edd52d29a82a2a3", "path": "flutter_avasset_reader-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b85d68facdaaf108a71c1d73b6df9f87", "path": "flutter_avasset_reader-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872a5ba9476aad25f25865097a75e2238", "path": "flutter_avasset_reader-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989678dc30d1d0a1e84aba12693b7eb015", "path": "flutter_avasset_reader.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9851b8e67bd839d332c643a0d5fb648bf1", "path": "flutter_avasset_reader.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98949f864760b74d8f88392689ed02b9ba", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_avasset_reader", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98419432d8bc227918179630eb8c6a40e1", "name": "flutter_avasset_reader", "path": "../.symlinks/plugins/flutter_avasset_reader/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9806510285e324bbc26c83df4e840fc900", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_blue_plus_darwin-4.0.0/darwin/flutter_blue_plus_darwin/Sources/flutter_blue_plus_darwin/FlutterBluePlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846ccf0d1755790b86999a88c3bf9c7c9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_blue_plus_darwin-4.0.0/darwin/flutter_blue_plus_darwin/Sources/flutter_blue_plus_darwin/include/flutter_blue_plus_darwin/FlutterBluePlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983f46a5fff695ac7331ba2eaf70276923", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e24a20bd6af25c2bd9fd0b80954b95d4", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98075d8c90ee51055a0a2436fb316e15ff", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b153f7a404db810a17daa6c51c00c02", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df4609d7904abed52469a14d855c79e1", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981054badc67fc31c3c8afbf2b446473cf", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831682047726686eb82096257cb69abc8", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0afe9945190879def91ab70b29f0ba4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9fc5ef4ad9bd686855c9618f11d6cc7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d05e735beef99ab3f0532c980f85f86", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838843fc4da51ce3086f15b3cfd099d7b", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828df479f3e3c0e597032311687623492", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980077d52811b82026ad1c6cb6ed6627c2", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4da06373e5940d4c345cd248157bd47", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5f0332a02ffb16b61fccf13a106b4b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982482933ccd6de91bd5b5aa4fcd10e579", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98661fe88bf66eacf84b56f9f03fa53fd0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e777effed3bad822516a569d0899a84b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c074cc757defc6a2c3f3008cb375d544", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c6cd94e53d2d9ee165d5110c3a7328", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884f0d8f948cd0ea85ab82ffe95e5d010", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_blue_plus_darwin-4.0.0/darwin/flutter_blue_plus_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98290d9410edcd28f47f3967f2d72ff242", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_blue_plus_darwin-4.0.0/darwin/flutter_blue_plus_darwin.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ea73e2889415e4a19f7ada5cb63443e0", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_blue_plus_darwin-4.0.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987d84d629653afdbd81076aaf1ebabae0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98413ca5b7e894c111d1561611468a246f", "path": "flutter_blue_plus_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985fa2f48e75bf451a5e24935f100eaec2", "path": "flutter_blue_plus_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b46320caca3673e2f36da34a1c30aa20", "path": "flutter_blue_plus_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cb05e01473a83a610684ce51008b65f", "path": "flutter_blue_plus_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce34a333d8e70c0cabea82f2d54ab943", "path": "flutter_blue_plus_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bbdbbf58614569e5204cc89cf5dc061a", "path": "flutter_blue_plus_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a426370e966e2a09ead9c499ca1365c7", "path": "flutter_blue_plus_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d9b9e0badbc137728f0034b38d2c432", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c121b3df127334ab357936406a83e2b", "name": "flutter_blue_plus_darwin", "path": "../.symlinks/plugins/flutter_blue_plus_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9cff04fa523bfd695d507cd7051b518", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc70c64a1f22bc86d179ce4a4cb2b89c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981018d517e24417997034f15b541e14e5", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/ios/Classes/UIView+Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c943b4a7d33be3165333c5a79a42867e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/ios/Classes/UIView+Toast.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d27722d556e4e0e68c3613203133fda", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ece05448db21e8e78991db665731f95c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9835aadcae233e14eea1d1182917d98639", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98111b0f4e43a1b0d33fa45559ff3c4d42", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838ff1d749bc4359c7231467981228dd3", "name": "fluttertoast", "path": "fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986be75e5d63c4482fb90b962fc8dab8bb", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845a2794f9046f297cc7b6c8e7c3f6ed8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988edde3ba04b31461fb93e2cdc3370045", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800643bf2d0a25fe6bbc69875b99d6c43", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b44987c952c55e3c71a9ff3d1e9c320e", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6341a7b7f8d8d4758dbb87dd0947738", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa8468b2fbed2db67326f8b70b6349c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a65c2c5eb6471e09154b5b271e11d63b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980487ed568358f47e337b9cf352ac4381", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6e3f5dafe221ab56d277c595edeb68a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895799fe46e6a4e1edc50a6b7b703023a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc0b2834c909a9e179616a110c659d8b", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980186e691659faa747c920311cfa96619", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/ios/fluttertoast.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9866b80fa23666d61f439734aa84a62f50", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.12/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a77940bd5c28eff7b8f3b6f66cb927ad", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9887bcae11f637592725f604ca6bba1fc0", "path": "fluttertoast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d232a226b6083134c43d8aa9f4d0cf46", "path": "fluttertoast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98217a8c6642edc66a9f9c4fec19bb365e", "path": "fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987575ac1bf94651f414eb9885f0638814", "path": "fluttertoast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f990444ecfc2dec6dd243dc8a71376b3", "path": "fluttertoast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989075077cd171ef84b206800dbc577751", "path": "fluttertoast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9830872efdadb897556b728c35a06c6dab", "path": "fluttertoast.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98123bdb72532ea7c524d598c61d4cc7d2", "path": "ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987e979efae9eed0b5c09985422f8d606f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c50ff48b88c5a55b4dab3e151a6ea01e", "name": "fluttertoast", "path": "../.symlinks/plugins/fluttertoast/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a176a3f21b5a4fa94fed56663fdc985", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/Classes/GetsettingsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885b8534024cbab0914a7b9b6cd06e181", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/Classes/GetsettingsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98768543761e684bd3d344c3e2dc00513d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/Classes/IpodToPath.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a68a3507ba1fbe8f0a336696821e4656", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/Classes/SettingsValueChangeContentObserver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fa078be3804f2be189eae23854f0e2e4", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/Classes/SwiftGetsettingsPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a99b510b9880d834660efac816be8126", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/Classes/handler/EventStateStreamHandler.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f737850514a44a1471df1836c4fa0aa2", "name": "handler", "path": "handler", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98299085d2737f019ca376c595fb7be138", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f915dfc398ae3d2605a7f4512853ed99", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9888d969ac76c8396d6955a899e9b284b3", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98575ef8d8582ed5a3db8aa8c519712f83", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98599e24c3db06c81928fc0d7b3cadfae3", "name": "get_settings", "path": "get_settings", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b721a55f46c347d514b4bf43689d1b6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2b968a237cce7e9887f76fb797e9dcd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896b770be4688c3e463710b85a0901ea0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b1afd179b4013b983d609820362b356", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccda0400be46bcc75c2d86aef2d50761", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872a0ec6c5af0dd6365c8e6b3fd0eee79", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd145c87fd8ff6b2c8642a428d23ebdf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3c1b05fdb9da1e9a59b97d704cbd990", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98871ebcd5fbef417a3c00d1e3ed844e58", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3ca33ae1e5365d4733d7a78c0288a8a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871b4370a03e3a4655b89da5ce9143ff1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d461103efb7786863244fe26ba6de968", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982fb4d35546523ac1e40bf2e756112313", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/ios/get_settings.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9899a2cfb6ab3e363664fa1f7e5ec1986d", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/get_settings-1.0.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98307475f2e847490ebb4300fc1f423439", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980ee0dbdd877efe360790787f59577b57", "path": "get_settings.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982254e6a591648ef1d231fdbd55e695c0", "path": "get_settings-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e06a7d32f4e287df6a1bcfada6d6cfaa", "path": "get_settings-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d7525ff5db81d2897a8a4312e69002e", "path": "get_settings-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886140c586493020c58ddfd8e7b914d4d", "path": "get_settings-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986d3e5c4bae528c188b4fc2a6118595b3", "path": "get_settings.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f43d08017760cd31d8783137794a661f", "path": "get_settings.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98446fc413531fca568d3ae72a79765d21", "path": "ResourceBundle-get_settings_privacy-get_settings-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bcaf379e36bb42682168970bdda5deee", "name": "Support Files", "path": "../../../../Pods/Target Support Files/get_settings", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870389fd1c9191af98243ed30d39d1863", "name": "get_settings", "path": "../.symlinks/plugins/get_settings/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98548eee0aab8dbe7c0bec78941896869a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/is_app_installed-0.0.4/ios/Classes/IsAppInstalledPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986911d1549e6d5e19d6a576acdf9b2462", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/is_app_installed-0.0.4/ios/Classes/IsAppInstalledPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98436be6fc3fff9936185f08f83bb1486a", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d22aae5d309c31743919d54fa0d88e37", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d5217e396fa993478e29254259a0e87", "name": "is_app_installed", "path": "is_app_installed", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e1a2d352c3dd89ef5894cac81337269", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f6081cef4d9a96b98cdd71f8f191ac8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e19a85c41d6b9c6a053bec92351ef376", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f653676dfd0e8474935d3b2d87c44948", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c51ce947b61f08e5887e221c6f35670", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986514260ff043c8e08a18723b812bc6a8", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bd41ffd5b9741d478cc7de106331483", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d8674e63c3dd0d8cf48be63f3c518f3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edf2770e26818ebdb58df5ed299a40aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6d9e45e541e19cf1fee5fd0aa21dfcd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829e92706218d2fb381cb4c6deda9d0f0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983113a960b79e399da19784f4f6e76a7d", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/is_app_installed-0.0.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c9079ae5f4bef4a5dbc33485ef259a0f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/is_app_installed-0.0.4/ios/is_app_installed.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9830d2a718f7572eb0cff8a7ed9bda520a", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/is_app_installed-0.0.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c1e800f03784cc09dcca44325830b923", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981f91e6d4ff92886fcee7c6cd5725ee4f", "path": "is_app_installed.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870efe3a103dcc657a47ea525b2080a12", "path": "is_app_installed-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d67ae56dfd616d5f445a0eb2ee9e238b", "path": "is_app_installed-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800f7eb9732f140d12afb51868b2654b2", "path": "is_app_installed-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829859c1760826d3a6fc805fda28dfb3e", "path": "is_app_installed-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b574b0b30c99fc813c37e5b02cac9609", "path": "is_app_installed.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ad0b8a2a99350b79c1d7ca104366abe4", "path": "is_app_installed.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f359282fdbe6998c751de6ba496c8e1c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/is_app_installed", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982556225caa3f8ccf2cadabcad8908d8e", "name": "is_app_installed", "path": "../.symlinks/plugins/is_app_installed/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854ef66392e2fe9f1386c8967cbd66ba9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/FPPCaptiveNetworkInfoProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3d233035cc21cefd4a4156c6a6a3824", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/FPPHotspotNetworkInfoProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98079733de3f695e6ba5612c842d4039ae", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/FPPNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98095b06be87db26ccd168d1605381574a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/FPPNetworkInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987731f6bac2d74931615b63382215fb75", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/getgateway.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981a5a48630166b8a803f2787f99a910da", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984261348a7bf98ab552dee55090ac94bb", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/include/network_info_plus/FPPCaptiveNetworkInfoProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855fa24ca3f42fb49b074de5c805b5ee5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/include/network_info_plus/FPPHotspotNetworkInfoProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8cdeb06cb606d11bea6443d2ebcbae4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/include/network_info_plus/FPPNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982512b96fbf1614e0f2389244a0f06dd7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/include/network_info_plus/FPPNetworkInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c12013fcbaa22f6f8149a4aed2db1383", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/include/network_info_plus/FPPNetworkInfoProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98248ceaaa422a67e54a3071e9bc4f101b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/include/network_info_plus/getgateway.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d44df4c0336b9e3b2cf45532fb4e164d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources/network_info_plus/include/network_info_plus/route.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981080b1dbe02de384bac358f0706cc756", "name": "network_info_plus", "path": "network_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ebff74445b6d82d5fef4f6caaf29b34", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824cedecdc6c3d03a3f4f31f82b1c077c", "name": "network_info_plus", "path": "network_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837a44981e213c6f93ef705b2818b6f4e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f15832e1cb42efa79dde5ac04426b027", "name": "network_info_plus", "path": "network_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca01a2f97bff82c237105da15db53b95", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b33e523075dcf8f2104c227e22f8857", "name": "network_info_plus", "path": "network_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3ec110269153956da0ef5d15296fade", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98625f28edceb1f717646a518f900bd91c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dea3220fcff2567254c747c161a60509", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b307891ff680fbd1653784972ef214c1", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817561ccdeeb86cb1ce34303fa49ad576", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8b9120739b3d85dcc8470b28d7b9d6a", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e5f92c0fc323acdf31b0732c36aba23", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b32729c7baed1b6c654121f0670096d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987175e9317d6dbb3b1e3e4fa9de62d04e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db49e51a30dec5415fb73ce62ecdbc88", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856cfca710766f970aa339f3a90a0d709", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986092a48914fc1c72dd995ac957b8dae3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d510e337e91c37d169321aa00e0d5e1a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d62c4f2e15b7de8fd3ba8dcfe3908d3b", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98cbcafb98482478e87b2204fab4731ea1", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98166a4a5085ecac0857aed68ac433a177", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/network_info_plus-6.1.3/ios/network_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98140c096e473f9b01c126fb0d420d7c40", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98043c103fb70bc679ab7d1234af5f2f2a", "path": "network_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98247f2ed54a2715222bc2608fb79b6ca4", "path": "network_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983e0f01f80c026c687c56def3844b8fcb", "path": "network_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985794af2f36e369df560319b0ecd79a46", "path": "network_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2ae9b4e59dc9932a1d79935b1173911", "path": "network_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9868c5016110a398edae7e13ec2b8fb736", "path": "network_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b0f5366541691b18c8109adfd41e79ac", "path": "network_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985ef9f79229aa8f68d95f17b3279ae800", "path": "ResourceBundle-network_info_plus_privacy-network_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b5bef976b4175935514c03ad827bdddb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/network_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98457739b846c71e8db88e17987ab527f9", "name": "network_info_plus", "path": "../.symlinks/plugins/network_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983a365fdb06fb1f19ce48985dd9cd2a60", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/ios/Classes/NsdError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf7164ba44460d82e34d51d3327045a3", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/ios/Classes/NsdIosPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804d25a3105ce870f21f099d027465c66", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/ios/Classes/NsdIosPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983bd75c6ad0d04d383ceec1064d90e16c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/ios/Classes/Serialization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98263e88c352dfaf3a584f5efe2ea655dc", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/ios/Classes/SwiftNsdIosPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fcee082559b9b6167bd2fd29e138ecef", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f78e8ebd27f4fa944fef75e5a0c30327", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0f6cc67943bbb57e44e5252dbce5f5b", "name": "nsd_ios", "path": "nsd_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e39b06fda74f68b2cc9bdcd8f0d124e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b03a00ffb07f3cb9bab3abd41bbab9d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803b0c08d8a4a5a43374e2309cab5fbeb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e51450e73d989304e33027ab24aef0a8", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892b73b9e50043cb333640a8dc91378bc", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b27bdb33f5922812ff559c4bd792cb9e", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983952467d36cc54f13b90c399634c2692", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb8deb859d8c1fbcda6a213e7120b13a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98179057516477e75c36b3accf9604648d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856d65bddb441a688c21afd053a1d510b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9e0b22fb6ad520d1091590f7250eb70", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980788655568ef4e51de56b1250e0a06a6", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986767e9e9ce1cded472299b63327a9156", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98caf87078163bd3d8c9b3d359ce37125f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/nsd_ios-2.0.1/ios/nsd_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981cd8f68e779dad994b636a3062db6be0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980978065b2848369565897f0c01da35f5", "path": "nsd_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6c83976edcb64a7f38f1902cfe1ece6", "path": "nsd_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988ef0931caf5b11da41d2887811085351", "path": "nsd_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b193565762f75a364632be5c29e2fee5", "path": "nsd_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f60be2be43c37265ac8028410a60789", "path": "nsd_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984082200d8a5d5018f4fc88af785bc7ea", "path": "nsd_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980bc8ab46480ccea2e6f6b510e6a4b69f", "path": "nsd_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9839a309825fc70f59bf37fc7259da897e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/nsd_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b88cb90f24a01552bf012a5ea19bfbf", "name": "nsd_ios", "path": "../.symlinks/plugins/nsd_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b23edb02c924c83d80f1fff96ddf92b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/OnAudioQueryPluginIos.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a24cfd7b07479164abc5a582745c8091", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/OnAudioQueryPluginIos.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98090816b0dac8be0bf884a1f224455631", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/PluginProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9837588a512fb1c2206d2f4c40e30edcdd", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/SwiftOnAudioQueryPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985179a350692ad65544f2a0a418b74ffe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/consts/Method.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f183cc90e1e2790ac48844a2e75f203", "name": "consts", "path": "consts", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db1f7e2bdfa114439a348f53b7e6c9b2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/controllers/MethodController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98059994e66f4c33991347a3158661baab", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/controllers/PermissionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bddb22937d7cb2d0bf847e16ead772e2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/controllers/PlaylistController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e8266bf037ddb67f8906fb86f20243dd", "name": "controllers", "path": "controllers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a41647aa1b6a848ba0a5360870936ca1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/AlbumQuery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec495e767edab7bc285756fee2fe919d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/ArtistQuery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985db12815d8e03e0a149401d4452697fc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/ArtworkQuery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819b78d047384e35bab32fd3537fd276d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/AudioFromQuery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98747e2d2975cfe6d7502fb983b75f053e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/AudioQuery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9855834480359f162871790a98577019f4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/GenreQuery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9873abc4d2ade1a8d1691dbb8ed35156b4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/PlaylistQuery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9893c3ef9712507a0337c988b23a4452f8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/WithFiltersQuery.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e51b1e1c544f1fcf8c1c84c09fc907b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/queries/helper/OnAudioHelper.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9836db686a674711f5680c19045e2bc5ee", "name": "helper", "path": "helper", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857226a1caddae146c071155605876b10", "name": "queries", "path": "queries", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e10388045b54df13614201e37d7c867", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/types/AudiosFromType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a6b50d6a70c08397401f73780aea9716", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/types/WithFiltersType.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988db6f1073a7601ba55f9f03c748efc52", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/types/sorttypes/AlbumSortType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989ef54b8b22fbfb31d86de3ca8fb19f40", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/types/sorttypes/SongSortType.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c972d69b39f10f6ceb127e788fa50c3e", "name": "sorttypes", "path": "sorttypes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98205a516ad2fb90bab64c54db95b298eb", "name": "types", "path": "types", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9895b12b75aabd50e9b5d24e7c4eb30df8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/Classes/utils/Log.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9865fb5e3f1f1aa4ac37f79fc5652154b9", "name": "utils", "path": "utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a64d7bf367ea4a3593bd1bbba0c6ed8", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edf264e61cedf8b35150b569cf0ad5b0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98faa06352863272597659aac39f636b79", "name": "on_audio_query_ios", "path": "on_audio_query_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c7333fd971b0e1e7abb6eefbf2a6faf", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809cb91f695d977bd40b79fbda327e8cd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec917d54da3e197cfd9f4caf473e53cd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a55486281878d4a85b03a3291f88d9c", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ae7decb7f33200ef21aca3150020fbc", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bebc55fe6b7db5abbfaf446dcbb0508f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98611d893e323bae24ad43a5d9ed1e40f0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858f1b647f82f3ace938775633ffcda10", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98585f6286ec0724aa55a49aab60267ade", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839a19745a0ba79e3895aabc4779ec974", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872ef795073c5068c9a9ecff53fba2bb3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98959bc87a36804bee5e46fe4f341aea23", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b0f0a7f632168baee59ffc460b15ee49", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9896ff462aeff00a4a71f24e352ebc66b5", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/on_audio_query_ios-1.1.0/ios/on_audio_query_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980a68c31d5a10dfc6df4ff02facc3661c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988c343a9b8bfb8268a7bd0d25f330baf7", "path": "on_audio_query_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891720439efce98d109b3b14b4b60ec89", "path": "on_audio_query_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a9a03938e8769ce4f2d0b977b9ce38e0", "path": "on_audio_query_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a90a97455e8df547c4c64ecd44dcbe2", "path": "on_audio_query_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e659a05b1ca0e99dfb59174bead2de51", "path": "on_audio_query_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985fccabda30431a15a10ba4ac4397f4af", "path": "on_audio_query_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988981e539a5e7e5418f1aa04471485366", "path": "on_audio_query_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a7fa4b7fdfde00689febec028a62877e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/on_audio_query_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842b0cd6aaac7134a6913834dc85e546b", "name": "on_audio_query_ios", "path": "../.symlinks/plugins/on_audio_query_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988b48cbdb2ca373d0f28405c28a10dbbf", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/open_settings_plus-0.4.0/ios/Classes/OpenSettingsPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848dfbc84a8a296f60bc4f608214c1952", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5be4df20626888d85776e616babeba4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804efba90aedd368c8987a06334cc5824", "name": "open_settings_plus", "path": "open_settings_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98806ace54f81115badddb6f42d1720e29", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98266f009bb153acb035ca5cd0cf849393", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98921d80995f15e566f0ac2005d7f1477f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981084210e719e9d63f4a9fb993f910714", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98609f7a9dbb4011d08bdab768b234e9df", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afc1fe272a6f4d7735530f0a3f46512a", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e619060b720b4b395453936758ad02c3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ad8653ffd3f17eef2be0ebb8ba97bf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982247740a0365c19872371f666db604da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb15fbcb2fbccc96bff50252200fc508", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989baf42107596fc9dd45ec93ca76fb603", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c99b0ebf005d17473cf2c0e8694af6a6", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/open_settings_plus-0.4.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ba339caee5776b71ddfe0d8e057067e5", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/open_settings_plus-0.4.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989c3c294027a0ceb6e92a2a25c20ed98e", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/open_settings_plus-0.4.0/ios/open_settings_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cdf0504a3cf9e5c74f630f0171173aca", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9889874398d13cb017f45c7cbbc8647874", "path": "open_settings_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c86d0a463513dfbbd3e0290e86582d04", "path": "open_settings_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98649960e43c57c31ab79f60513c84c25f", "path": "open_settings_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e76fc9c7cf742a2441f1313a2ed9a7c1", "path": "open_settings_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984dc4375beebdef5ebd283d7e095f1c02", "path": "open_settings_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9840aba581cd7364082f869a9724e9d740", "path": "open_settings_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9845d2afb7a40e6a81ee453202a00ca058", "path": "open_settings_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e5cbd9e58dcfc586cc5ccf1475c1cb1f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/open_settings_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838b2f8a6851da3b3ac8909518b68c0ad", "name": "open_settings_plus", "path": "../.symlinks/plugins/open_settings_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841b9558ba9f2f341ca6c227fbcef858b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9883155afadee918abb14d703d74908bbc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1a6bf3ba284c473055493dc3a05f739", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/include/package_info_plus/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9863ae9bd19bb651df3c30ae57fe596240", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980096320bd731bb53d6be9afe2f00eafe", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886d28c72ecf3a44255b2671443f430ad", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837c2362f690b1a7519d2b1b02ab8f017", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae9adbfe987c611120c31477fe6a9525", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edc89e98d228db700d74172a846b9d97", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983689c1197d9c35858dbc481cedc21788", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875185c3bef6915084bcc43cee62c9a9b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98021e205a1a6412f649272f1bcb67399a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982de4361582563d6d0f96af0eccd3b5ef", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cfd586fe40ebd0bdd938ca5160dcb7f", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1e78b6cce1ac85372be5c0e1342a0f8", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860a979cb24e528e06a557e32e28e7259", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f3be74d753eadc7812fc0d0ab0f92b5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8e37180f0da3915510bc9d1a640ea3e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988440a9ab6b774cf43f0c22e60b2e5ca3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f36a9f4aa87dd1069b8a349ccc727484", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98676818e831c09d7585531da62e631714", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836dd1f428e1b742c24c9e768c114fd9b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98608d96888af52d7288d0ffc2307881c6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98036c28dea891c2d0334af2a26341c08d", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98355ad4762ea2f521796bbd2cf2a8fe7c", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c9c0296424467da6e76f3171323def5f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9831030fdbc21e5eedb2eb9e0821c96f38", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ea92db103ed62d7fa103a51a9ada95eb", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98247314e7eb02292f5ec476f3b4ed89f8", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9878784fdcf8ebcfe07a431e48d87554f4", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9a3889b1115f66228ccfb74e880f5ef", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888bbda5584dd1ec83f7a3a2d5082f0c6", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ea7cadda172307abeff6525e3261ff81", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984cefc5bbfd7f1b3dee259be0c182669d", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98173d4a46c611e1f5a37142e0d88ed7bb", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98451a31afad3920a1cbf219a7b40515f7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983903fb5a09a68e96b822b6104239a796", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c519303509a0c7da5722f0d7b6103ea7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9815056689fa2e86848507a716cd2978fb", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8f446fac62d8df76a133deca2cb2ea4", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0031af712216a0c3960a09eab60f853", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4fa966999981b3fd25311f791fb37a6", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bdb62cf74988eb4cc8becefcb8862f60", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98305f2f81c7381bda6893989344d468ce", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987065deb8a6296f3940f2d98421d90596", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f80b1463fb4803560487560cd9cb3041", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dba70b5f7cc65108eb8ff5e9eba9464a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b72e8c186eba30843c8097e34014d2a7", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c70411789ef0dbf32d6fc098992715fd", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98326ae19cc1378d09d36db58295d13ef9", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98748d19f78ffcf0b87155605ccb72df95", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988632f4917e065d5712a57bb9c5dc559d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98955234cdb79f31a7b6234cd673768038", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9899ae52384c8f47605a055a29a1b1de0d", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98905a9617a599236daa8d64e0449e3702", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f1ec31c1635fd69f381734acdd48683", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894504b12caffe9c2c077865af650a317", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b0c83da092a8adbfe0041f10dba586b", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862bb62d7e6cd9342279d29e8f90e8c6e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2c910cb91707ccfa3341d16d8218c57", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e582e325929cbff7640be25616bccbd9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98481d52c16e003e6a712602b980ff35d2", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b341243d7dd2ef804b0c7ad2a592cef4", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c41f481abedbd57e385027d769acb3d8", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bfb0e6fb18bbc836d574d38c83c3602", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868040c3172d1291a059d8dde1f610ac4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a35e7dd632fdfdaf3e9425ba7d0986e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd77be18872d94419c7cbe84bae2e025", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3af244375ca684efd879a1dae156bc7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878341ce0c78bcf2ee33924fd68183283", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a09639ad71b03742ff7d3095b7dd75a6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987736b9c38eacfcc68c893200f296ae6f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989d739eab6c0cd2e68144a1afc56b851c", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e985a12226e770b3603478e18840a8f9443", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9836f4b0a70945cd5d05039a6b9dcc5c5f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b556b7ac170c0838610c3ea2b3c93505", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854fcba7644754a83ab716be14031a08e", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9832f37d28a58143fd57b07b1b27e25389", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859085d7bd091f89fb09ae50917a785be", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f61ba9de2e04a3809b8c9eff4d222fc3", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f90d41d97581260bbc6c8be210c9de74", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981a2f6fe92afb6e057935e4d7552cf1b2", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98151373376c5253b89fbc93b97c7738eb", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b7a11d7c7a570b294631a6901476899", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b170f96af01ea34b01f4246d34902ce", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b945226a03fbad483c94b1cc74908dc6", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e82a7862685e22fce8593529e0e56ea8", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982becf868210880294e3041f231106c17", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869d92a3774e5b6ba6991a5161c31426c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afd2106c4021bf6f0bb2136f881c3d5e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b2480a899cc446eb4f8e5a12c35d95d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986843420e5a8508c1ed451d11f3662616", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fa581810439c5bdbb6f5c907ae73495", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e5de012f6c4b9075cd3968f4cbc2ac6d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984595b80a4ca064108b526c271681d593", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984289ecd430e852dc0b5113cc165ea27e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b39e198afbac6283f1b8c6b7c46485c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805cc7922dc2543db4fff6d0beaa84efa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981274eef8e5d9d1088265d50683541ad1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b2600a0e6eb826de0f6b8952e304d2b5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c58b5dd54af9f9abde9ef53faf7ffd87", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a2f410168c3381a8d91554c70a57909e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983297c4955c670049b5ef1babf7038f44", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c621b1aa8953876827f5a67cb8c8e58e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1fa9d1c0bb5697d29244b2e347cc838", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989cb6064bbf096c4a37201f6480b87dc0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f7c79aa2c24e41edb97a5fd006482d7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984123725395763aa3a5669489aa40f67e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a846082caebb4c46057891b9f1b86118", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ab80e98ffd3111398894f3ca65e1db79", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879a3b48c447194364ec3e8ac7cd6e091", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bcc4677906ec2e1fcb49ec3bf1f233a9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98427d58e3667cbb4393acfc7688b3b855", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870a276c19bfa997d6abc6e1cf70a7215", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850348df4fc2d5b435cf65b33b0d75699", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edea6c189abdac44b30df78826b1f195", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b3b0d5d1dbc177dd46bc641e22198fd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a0f9cb8d2ac182a5da2ee302bed18a2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa7f2006da6fcfab448fe35841e97a27", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894f3ac009b5deed09160bb7ce1bc7d21", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f690e9a17f887d5d21cf5d261479e23c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984119c45f760dc1908aabb78c4ec9874f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a1379418d7af6d3baab639d86d97dbd9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e411fff84085f00a564c50663ed0f30a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989767a240c1f28dec584f5f343ab9cf92", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad70b8144322751771aa518072c3b551", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b7c29d868fff6efa9117c585438c494", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c863354b7943032d715c74b919133af7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985df9f73d0eabd7cd09766ee88ebef70d", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881cb3b3c7ec94d3b958ea0569d4d5498", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987685b8b75abc83d3ca3fe9d001bf48cd", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9834b0aad879ce00a467fda8f779ca7552", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98328d54deb4fd43025b0f800ca927b61a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b250cbf30c7c24de834fd0c5f1170b3b", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980163a57c66facacea7a8b6b2070b9765", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866e0768ed576ab172fe40a060e3e9a05", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98387f1a86de2ae308cd4fca8543cc4c2e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a12ea97a0229f2aa9827e075f629433", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98486c38771863a360826407a06616fc67", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f47c641b5df88f96173ca52da0cb384f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dab220bb80a4a6d99d6c4b41fbde8bb7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cbfeb69cb02834ba6688a9ce63b074f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be7ab0e839ee04ded8f499c6d7c8b68e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832560c5e2b3759225e0dab31ed03e098", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eeb3f654596d488ae99c677d8aa96fc2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3d991694cc03547d467f428437d2dfb", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e7fa39ea0723274117eb97b586f3f32d", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9826ae5156c5f1a2d6d29fbed88b02852b", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98726ba9d391ddd8872f0788f9bc880d09", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986377a8dcb8ef31e50aef2ecb7e6ef265", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a499e940b6973fe0940e5ba944693f8e", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b5d5e1c49b3689a7ad15bdaa4f9a05d8", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbcdca475bb5d4bf3b377f3d8e7a57e2", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bb4867988445ab4d46150d6ebc37253", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b3d6dcfc2189fec8cff571716ecb9fc5", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982d52d05c8ab546191552faa7c067f895", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ab26077531c9e5fa702e5a076cc587c1", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988ae137215b90cb5dfa42340ac341be93", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c30a7944413bebc669f90d7afc84b3c3", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98427073ec17db68a72a3bdb4cb14ec795", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98419c7e25e9b3d56cf2709f020e152746", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d79e13bef82bd638243015a6316cd737", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854bd4e7f7e75faa315521e6941a3b4b8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9531c30d6d874446beebfb60829585a", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98265a2a90872c38efff9f89ffc49ca0bb", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c278cc3dc3ea2ddf07d6f9c681c90f6e", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98295bc9b55f19c51e6a23bae1a1c043ff", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e52c94875f684bc0ace0feb9f94f391", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f39b03bf39ddec9ad9cda5489e38486c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c6031f16cbf01f513178931bf7a1df1", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcc28bdfe95fd96a6fac6b543ff978c0", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989091527fc2f96b06f206d225bb979b25", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893d1d1da662a4f9833db102c8e2c6f7f", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc1663c50cb1b6f1ba4d586811887410", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98009d1fe20a6327908de76488787b8a20", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b3aa0c8e8f94149e4e3b53181bbe2e0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988a4b85c6c0fd04d6c10eaf4f8c69c967", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805803bc8dba454d38f1ca2d8caebfced", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9810b890100ea1eb848e61b3e6ffc845fc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab7f6591fde713196bd4053b4be87e0b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bfce590e66d4b12ad9537fb2b09f796e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871cc978747c816e6cb87963e1f871573", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dba4654a017b93652ab38dde5c0b8e40", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ba7c0d5d179a694e42e3e6297b709ea", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983dc38d5b28015a8ac67ae14c782232e6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986eb55be4619ed869e38b592f751c1cb8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832d50528570d23378640418d97a5acde", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6936a4edc986a2aba6fb038758a4772", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981fb0228242316bcff807a93397e40e09", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d3261cf976ade8dfb8ad9467c70d54cb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858b10a758ee456cace3588563b7d0681", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad973d5554895a85efa503cc8b63f44e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9488389292f44a22694ea6f34b63816", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8ed56e46eccddad911e7ea969ca161b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98478ee93ef804f9746a69a1dbeb20f848", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e2c0086799862d94e77aa099eadb529", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98598ff62ef5fcfa511342ceb954391d5a", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7cd96ffe5346edf9549b73a6ab62893", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7ed9ce47d9041a5275073c6627de700", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98376576e8dc7da04ad1dbb102224d1887", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98505d394cda7291c8c59a89c5bf6d7cf2", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f08316ccb5210d6b5be4683065c6c3c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df0389e8e60343d90d741082b5456719", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fc3f0e49d2a7d68314e38f5a2a916ee", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ee370fe8ea56010cec5a47dcb95390f", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd7270998172aecf7e90ce912b937d9a", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98307656f180fac47460f0c3dc39d7b393", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873c60c31227ab2972024ca00019b8981", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddab78c34d4591ae75b5b0e3c69ece16", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d6db6074c7743a57db3be2385bf00e1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a770d4d240e52276097d23563bc5663", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980259417ae8f209cc9b6834c71e06d8cc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b55166688448c1a80be45d2bda3904f9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812a82e6daf3737221e13fc5c1689b21a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98593c5990d0c48fc6b2b0d2043ed479d6", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9836cce9b895bcc5cbea09a9a0eda9c083", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e9824e26ffef55c72b033868ecae40ef946", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98847e12380357e282d856b50fbda253a4", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98569f0c1ebc238e8574f0c19ad1b752d9", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989af7b8cb38ea99f287f68234790edd7d", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988456d5a1058b6172311e06837a336ea2", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fadfbda42ce517e5dc36e9130c1ecaa9", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d0ec1b83e1a1dd1698c14746088ac60e", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe98249a6593b74bd7c805a17543d820", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc062578f1be2e05ee972505f21b36a2", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9822b3c6f21bbb5914e4ed6e5535614e03", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9866068aadd32d93dae223922f3bbea3c0", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a12211d18d47f68ac365217f11259144", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc19322e9f8f54e562d2976439cc951a", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983e1918659ce284191d2b531d4df95f11", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9858266192e2f41b600ab3ae8111d97d56", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed1089e77a34b60591ce6e3609b16ca6", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cbce88f58871ce066c569afee0ae4e5", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984234d612769d7c5dd8eb60cab460a7cc", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4b3453f24847db25510ee23a7fb995f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d66964cd83ee4d33138e19b3b7ae42f3", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d682fbc7a2a4276ead88a68dca4b6915", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822afeccd18f8cac7612e9de48c96d562", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846702bc6b4f1f67a6bd3444794c16222", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988641d882782ac79b0613716f63a3281e", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c767c77ae48ab4a48704ae3d452652a4", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e8e751c56353561be79bd1ad59f0499", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d2277e8294fd80694de3d94ba2580ac", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e250792b13af1adaa64ef7cf69360da", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dfe7bb5ddba0e7e56531ca1917e3c81d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d208f01ee8a130a819e18a0b6a84a50", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98284fb00ffd08753130c41798932421cc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd5af4fca817016b6388fb640785328d", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3d0dd78af20585bac40a1617e21f563", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ed756d863efa5fe171813d4b5498185", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98220b425d62c5f4f6d54f48b45d533549", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d16ac643c7d0fb564bf04f7025536aec", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822ad28b43aa28e1a7e0cbf13d98f9c9f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988eeeafcfb9c8e5c50d28970fbeec48af", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8fcef449dfa2993dc996e41567a2c44", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbb7fc37c291be85884bdd19776ee51c", "name": "go_control", "path": "go_control", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98041c23b4193d082d7646c4543602c95b", "name": "MyProject", "path": "MyProject", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842067fd7809e61d64f5b4c216e82670b", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bebd5e29bbe5892f7f51fd3adab37ae0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98184131dbee49d9ffe4b3dbfb43a10095", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817e2baf824c29094b5c78b55e0f42226", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e06abd9c97a9268f597fb89f2e20c62", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2426dde41982e88e3f8b61e0fda2886", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869c55f0d1e07fd7429ca8065b73951f1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835decfbdf980d486adb4e466cecdee21", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989029a5ae773928969e05a7561cfdcaa2", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc70cd7fbccde7c96de5bc6fdfe492f7", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f49c86148470b42c4c06f2f48fb85cfc", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9822b2e86e97de5578b0d228de5a937a46", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d98206c9d0fbd7e1e0cee538124de7ba", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98247371236ff2f13e876e882c27b66b47", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811b42f5865c0668d8105bcd5cb74b4ba", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987e1facd066d2bd5a260e94c46debaac8", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a113cec1bc8c63f283e53b91cd70f8a3", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5966b5ba444abacc4624ab44877652d", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9873c43bf0bafc8d69ea36f1ba2ad694a6", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985053bebd8411172a4a4ef917270af8f4", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988b15ebf7e4820350826a12b03983b0c0", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e12eb60cae2c7fb52f76256bb2bd0345", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c4e2028f70dc447d9cd8c9e7a469b12", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e982dfe584b0af643e83495c96cac808c62", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/CoreBluetooth.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e5846d5481396a0e80acb6050a58602a", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986773171dc892ee411717d66a0dbacc7f", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98093e0d274405de132c9336fa40762070", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b67ff6870ece79e6bb82f2c5617ebc7a", "path": "Sources/Base64.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980afc1f044a45dc9c720f672946ba63b0", "path": "Sources/BaseDestination.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9894eb1e26e5390d6a8c7856d10f62f6b6", "path": "Sources/ConsoleDestination.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c1d285e72b2f62dbb7177200a583e27f", "path": "Sources/Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ac6f926d8740829e5ecedcbf44b8e46f", "path": "Sources/FileDestination.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884ddea8fa027b41b68cef74a1177ccb0", "path": "Sources/Filter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982441be7cb7f5286e8ab7e242a9636afc", "path": "Sources/FilterValidator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980cb80fd04cf8c4764b751af8a6560654", "path": "Sources/GoogleCloudDestination.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981ef123eca24347789e562ab8059557b0", "path": "Sources/SwiftyBeaver.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987e34cabcbb5259d81d1b4a7e73725f56", "path": "PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f18896cc47db038c96c63e39c1265d2", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987ff34325a3c9b9101b8c9727ae27c91d", "path": "ResourceBundle-SwiftyBeaver-SwiftyBeaver-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98516cd8d6854384e36ed0c324eb0bee2e", "path": "SwiftyBeaver.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abf63913eb0cfcbb9335d8fde5cb1bb0", "path": "SwiftyBeaver-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980b5ef3898d7c70d04be5172c2945412f", "path": "SwiftyBeaver-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ccdcff6b5e3757a5aa119195902cbea", "path": "SwiftyBeaver-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98053470e11ca776bad21f8efa405b3e91", "path": "SwiftyBeaver-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98add7228b85ff615243d752026d28c633", "path": "SwiftyBeaver.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985cec5f90735e9bbdd21c8c3b18900666", "path": "SwiftyBeaver.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9830f0e91c432ff179bb77e0402c229fc6", "name": "Support Files", "path": "../Target Support Files/SwiftyBeaver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980560286f5db7f57f973ea4de0a4c079e", "name": "SwiftyBeaver", "path": "SwiftyBeaver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987696229b0bdcc2ccba003eb6e4cb3a6f", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98906b6837336a802aa1e983f8032ba3d0", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Desktop/MyProject/go_control/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/MyProject/go_control/ios/Pods", "targets": ["TARGET@v11_hash=361a080d6335aa7aba5033c36f95b3a2", "TARGET@v11_hash=9bbc941e9cee6f00176615e669cb4495", "TARGET@v11_hash=3a4028cdcc238f3aebb70e7a6f4347e9", "TARGET@v11_hash=4e90d07de6e92e39e495a3e8f511c844", "TARGET@v11_hash=624218ef55f026e1ac4bc52545ec972e", "TARGET@v11_hash=18feaec92cd34df2aa2738a7f060a7b6", "TARGET@v11_hash=d3e01fd526b2545e17f2e2695f5ec10f", "TARGET@v11_hash=a8d67b46b565bfd33e35856c18c78df2", "TARGET@v11_hash=ebea84d3fce5021fadd29e93094d07ad", "TARGET@v11_hash=c61d3d6243a9cdee30f1f44a4b685b86", "TARGET@v11_hash=a1c2609c53b11daef7b025c0fe7b4113", "TARGET@v11_hash=153c552cd0376be99cdc6a85473d2ba0", "TARGET@v11_hash=85fc10bb8b1b872be68a6795e498452e", "TARGET@v11_hash=1aaa4b3e3a0965d8b3c2d65631454eb6", "TARGET@v11_hash=b01a6217a964d025fde73b753d7fc560", "TARGET@v11_hash=9afc61235fb9c697f6d924f979c2b63b", "TARGET@v11_hash=d3cb25893d64dd6d0448219734583aff", "TARGET@v11_hash=564d8177f4055969843e629e56539907", "TARGET@v11_hash=5da8d1de3a27e56d6bf71a98aac45790", "TARGET@v11_hash=6b1e68aa59da121a2a4983393fd0c02e", "TARGET@v11_hash=bdcefa26a8a2bd6fbe8deb716342a643", "TARGET@v11_hash=7f4a6b86f7c8aa17c8ef3c7d773977b6", "TARGET@v11_hash=5eb4b33751a0df3bf1bed3e20485766f", "TARGET@v11_hash=d1d9ec497d8c046ec71e910ab514fca4", "TARGET@v11_hash=a37587561db7e52be07779a9a58453ce", "TARGET@v11_hash=a674de0b3886e470f8a17af66852bb27", "TARGET@v11_hash=c0ee72933a68f60bf71b95c9755544cb", "TARGET@v11_hash=bc075dd360fff9376fdd6dc8f885c8b8", "TARGET@v11_hash=60304b011548d52bc8488a483d609079", "TARGET@v11_hash=38cae8c0b7fa2d4b1607e3a035394a3a"]}
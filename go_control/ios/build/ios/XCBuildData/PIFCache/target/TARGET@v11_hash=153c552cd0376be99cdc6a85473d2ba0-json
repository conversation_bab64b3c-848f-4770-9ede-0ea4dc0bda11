{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868c5016110a398edae7e13ec2b8fb736", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888c0620367fa1fbdac32b1f08d8f7c27", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0f5366541691b18c8109adfd41e79ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98efd4531fcaa3285d72a1323d4b87ddd8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b0f5366541691b18c8109adfd41e79ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9811fadaba47d9e993ef2217fdc8cb60c0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984261348a7bf98ab552dee55090ac94bb", "guid": "bfdfe7dc352907fc980b868725387e98109d62cacdc33c4fb02d690ee5531760", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855fa24ca3f42fb49b074de5c805b5ee5", "guid": "bfdfe7dc352907fc980b868725387e98abac55aca2c47079b2de14130d0d0b78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8cdeb06cb606d11bea6443d2ebcbae4", "guid": "bfdfe7dc352907fc980b868725387e984a2717594eb6bad317f8ed42a2bd34ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982512b96fbf1614e0f2389244a0f06dd7", "guid": "bfdfe7dc352907fc980b868725387e98b22bad02de391f0c0522fbf629f69afe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c12013fcbaa22f6f8149a4aed2db1383", "guid": "bfdfe7dc352907fc980b868725387e98d63b9e27b877ee589ce199acfa0868f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98248ceaaa422a67e54a3071e9bc4f101b", "guid": "bfdfe7dc352907fc980b868725387e983101d62404c5e39f3402702568e69bd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ae9b4e59dc9932a1d79935b1173911", "guid": "bfdfe7dc352907fc980b868725387e9837a850b0b320d790906886ca6c8058e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d44df4c0336b9e3b2cf45532fb4e164d", "guid": "bfdfe7dc352907fc980b868725387e98d8474005a088d845f840522eec1db555", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bb7d4e1d4de770555830caba6c229bb2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9854ef66392e2fe9f1386c8967cbd66ba9", "guid": "bfdfe7dc352907fc980b868725387e9838267d0eee28640eca267ea7fb6e3f39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d233035cc21cefd4a4156c6a6a3824", "guid": "bfdfe7dc352907fc980b868725387e98e5c2180c36431c625ac8ae2f832d0626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98079733de3f695e6ba5612c842d4039ae", "guid": "bfdfe7dc352907fc980b868725387e98cc67b40db4e376e77de49785c75326d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98095b06be87db26ccd168d1605381574a", "guid": "bfdfe7dc352907fc980b868725387e980b6f8ce02e0a5a7e37ae8a2980b7c972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987731f6bac2d74931615b63382215fb75", "guid": "bfdfe7dc352907fc980b868725387e98b3f3fc9edfc0e86a6b9bb2a72f4c9c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247f2ed54a2715222bc2608fb79b6ca4", "guid": "bfdfe7dc352907fc980b868725387e9827b95ff5257611987f8c065dc116ac3d"}], "guid": "bfdfe7dc352907fc980b868725387e98025d18499b46e41e978db64b642d963c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5846d5481396a0e80acb6050a58602a", "guid": "bfdfe7dc352907fc980b868725387e98f5cd08c65e447969061e57acf0eb9b47"}], "guid": "bfdfe7dc352907fc980b868725387e9879aebb9d87130aceb6ddff231159042a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ffdb43189a3365cbf943a8c22758356d", "targetReference": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b"}], "guid": "bfdfe7dc352907fc980b868725387e98f5411fdfa9ab737a6b2c400b79917e75", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b", "name": "network_info_plus-network_info_plus_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cbf57ad863a4d83e4bcb3860e61a5af3", "name": "network_info_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ebbb202873b59db98b5eabd407a20857", "name": "network_info_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
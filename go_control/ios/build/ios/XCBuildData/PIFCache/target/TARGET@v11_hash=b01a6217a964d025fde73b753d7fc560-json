{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985fccabda30431a15a10ba4ac4397f4af", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/on_audio_query_ios/on_audio_query_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/on_audio_query_ios/on_audio_query_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/on_audio_query_ios/on_audio_query_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "on_audio_query_ios", "PRODUCT_NAME": "on_audio_query_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9830c433a25312a21a950ed1e3f484bb9c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988981e539a5e7e5418f1aa04471485366", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/on_audio_query_ios/on_audio_query_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/on_audio_query_ios/on_audio_query_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/on_audio_query_ios/on_audio_query_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "on_audio_query_ios", "PRODUCT_NAME": "on_audio_query_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e42896825f4c5a93ea4ebba9786c13e3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988981e539a5e7e5418f1aa04471485366", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/on_audio_query_ios/on_audio_query_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/on_audio_query_ios/on_audio_query_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/on_audio_query_ios/on_audio_query_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "on_audio_query_ios", "PRODUCT_NAME": "on_audio_query_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98161216337981c25780443201a35ddf82", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e659a05b1ca0e99dfb59174bead2de51", "guid": "bfdfe7dc352907fc980b868725387e98e15691b49d7c0ee8c232de48efc31983", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b23edb02c924c83d80f1fff96ddf92b", "guid": "bfdfe7dc352907fc980b868725387e9809ae556aada01609b80cab2d8ac60e09", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e500daeac165619c8eb230a690850eca", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a41647aa1b6a848ba0a5360870936ca1", "guid": "bfdfe7dc352907fc980b868725387e985a2c212cd5c9588272697f5d5f7c23f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988db6f1073a7601ba55f9f03c748efc52", "guid": "bfdfe7dc352907fc980b868725387e98640085e5ffb1514434e34c36cd38ad66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec495e767edab7bc285756fee2fe919d", "guid": "bfdfe7dc352907fc980b868725387e98475dd9e26b6b19d802c5d5b69343a954"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985db12815d8e03e0a149401d4452697fc", "guid": "bfdfe7dc352907fc980b868725387e98a0b49df275b3fd229a22eb635208e8c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b78d047384e35bab32fd3537fd276d", "guid": "bfdfe7dc352907fc980b868725387e9824c5c215e0f82aebee80a042b7fc438a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98747e2d2975cfe6d7502fb983b75f053e", "guid": "bfdfe7dc352907fc980b868725387e985d8da726ca55d0a1a74de429840665f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e10388045b54df13614201e37d7c867", "guid": "bfdfe7dc352907fc980b868725387e98dfc4d4992528a7df6478f3ff045a8662"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855834480359f162871790a98577019f4", "guid": "bfdfe7dc352907fc980b868725387e98b559b9252384b81068bbf7928658995a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895b12b75aabd50e9b5d24e7c4eb30df8", "guid": "bfdfe7dc352907fc980b868725387e98039721780e340f118f105d0835425546"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985179a350692ad65544f2a0a418b74ffe", "guid": "bfdfe7dc352907fc980b868725387e98dfaef3914c16d14b0ef57b2d46be64e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1f7e2bdfa114439a348f53b7e6c9b2", "guid": "bfdfe7dc352907fc980b868725387e98a9580498771ae06ed2fcea4abc9437aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891720439efce98d109b3b14b4b60ec89", "guid": "bfdfe7dc352907fc980b868725387e985097c913132e19d6777e3a0e102a6189"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e51b1e1c544f1fcf8c1c84c09fc907b", "guid": "bfdfe7dc352907fc980b868725387e98085cb42387f7ca92c559cf522173ce22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a24cfd7b07479164abc5a582745c8091", "guid": "bfdfe7dc352907fc980b868725387e9828f45f7a962892b46a8883b2a23dc3ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98059994e66f4c33991347a3158661baab", "guid": "bfdfe7dc352907fc980b868725387e98c908b0df55845d9d532bdefaf29b3955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bddb22937d7cb2d0bf847e16ead772e2", "guid": "bfdfe7dc352907fc980b868725387e9899bfd8352bad009d11369b9ab339379c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873abc4d2ade1a8d1691dbb8ed35156b4", "guid": "bfdfe7dc352907fc980b868725387e98c42e9a2c9966729d276531b04b7b7dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98090816b0dac8be0bf884a1f224455631", "guid": "bfdfe7dc352907fc980b868725387e98f44c1ec93e7af81ecce6189f9840ae58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ef54b8b22fbfb31d86de3ca8fb19f40", "guid": "bfdfe7dc352907fc980b868725387e98f828e22d04da66045704da4fa26ead17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837588a512fb1c2206d2f4c40e30edcdd", "guid": "bfdfe7dc352907fc980b868725387e98354a575d92f113f6f47c9b375faeb5ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c3ef9712507a0337c988b23a4452f8", "guid": "bfdfe7dc352907fc980b868725387e98f1704a4db2e4387beb33afe8796b1fca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b50d6a70c08397401f73780aea9716", "guid": "bfdfe7dc352907fc980b868725387e98cc82d7e17de7d02d857bdb4922e7ec34"}], "guid": "bfdfe7dc352907fc980b868725387e98d01715f28201545a316f5f3ca64ac5a0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5846d5481396a0e80acb6050a58602a", "guid": "bfdfe7dc352907fc980b868725387e988f18b67a8069d4eaeafd3644b7992fa6"}], "guid": "bfdfe7dc352907fc980b868725387e98901da132d848fe0ca5af2a3cfe4d5944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98963dbc90399b6b23a85ede537666e2bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9848227e53a3e646c7fe32cd408b63bb07", "name": "SwiftyBeaver"}], "guid": "bfdfe7dc352907fc980b868725387e9851a6f561fd2fa227bec9806b49e7ce09", "name": "on_audio_query_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98c789b8b38d9a81819cb993d4698f7874", "name": "on_audio_query_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
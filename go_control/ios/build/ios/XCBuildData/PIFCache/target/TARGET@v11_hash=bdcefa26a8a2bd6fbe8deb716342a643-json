{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3d6dcfc2189fec8cff571716ecb9fc5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d52d05c8ab546191552faa7c067f895", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d52d05c8ab546191552faa7c067f895", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983b2480a899cc446eb4f8e5a12c35d95d", "guid": "bfdfe7dc352907fc980b868725387e9808262771755b823e0ed7c90f08236720", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa581810439c5bdbb6f5c907ae73495", "guid": "bfdfe7dc352907fc980b868725387e98693baabf44f5daebb43e1eb177280bd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984595b80a4ca064108b526c271681d593", "guid": "bfdfe7dc352907fc980b868725387e983084a854f7be3734ca6863bc52a36aa7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b39e198afbac6283f1b8c6b7c46485c", "guid": "bfdfe7dc352907fc980b868725387e98ab7a8f0f498c79ed9b8e6b44c25e1186", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981274eef8e5d9d1088265d50683541ad1", "guid": "bfdfe7dc352907fc980b868725387e980e5a12ec23e10f55aee7cd9c28abbf03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b7c29d868fff6efa9117c585438c494", "guid": "bfdfe7dc352907fc980b868725387e987de9d514ec7b2a1d9fbfb40fb02a9228", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c58b5dd54af9f9abde9ef53faf7ffd87", "guid": "bfdfe7dc352907fc980b868725387e98fa59716780c488812053494aff1e7351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983297c4955c670049b5ef1babf7038f44", "guid": "bfdfe7dc352907fc980b868725387e98c94f215dd7797c4ef544f903b707b6ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1fa9d1c0bb5697d29244b2e347cc838", "guid": "bfdfe7dc352907fc980b868725387e98b6ec3df47c88203bf5855b73b06db8d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f7c79aa2c24e41edb97a5fd006482d7", "guid": "bfdfe7dc352907fc980b868725387e98ff0e0db77547bc8599d877ac6904f6b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a846082caebb4c46057891b9f1b86118", "guid": "bfdfe7dc352907fc980b868725387e9800adf978e67d11902be7afe9106c0959", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879a3b48c447194364ec3e8ac7cd6e091", "guid": "bfdfe7dc352907fc980b868725387e98f833f530ac3bd0edf14701a75989a11b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bb4867988445ab4d46150d6ebc37253", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b945226a03fbad483c94b1cc74908dc6", "guid": "bfdfe7dc352907fc980b868725387e98c9b2594bb6eb797e687403260c964937", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e82a7862685e22fce8593529e0e56ea8", "guid": "bfdfe7dc352907fc980b868725387e988f2e08f63f92d6f1ee28d46c198acaa8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d92a3774e5b6ba6991a5161c31426c", "guid": "bfdfe7dc352907fc980b868725387e980ea3709b8effd5d0be006669cc3007fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98427d58e3667cbb4393acfc7688b3b855", "guid": "bfdfe7dc352907fc980b868725387e986df7bbde9663806a4ed22d461e2636f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a276c19bfa997d6abc6e1cf70a7215", "guid": "bfdfe7dc352907fc980b868725387e98a6e7126dda9be654138baf1fd0742f4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edea6c189abdac44b30df78826b1f195", "guid": "bfdfe7dc352907fc980b868725387e98f4ce07ab424823750f5731e0debd189d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0f9cb8d2ac182a5da2ee302bed18a2", "guid": "bfdfe7dc352907fc980b868725387e9820b3deeea953010c12488ad02883c998", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f3ac009b5deed09160bb7ce1bc7d21", "guid": "bfdfe7dc352907fc980b868725387e98288312080e2624a7c9efad1741d3a19f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984119c45f760dc1908aabb78c4ec9874f", "guid": "bfdfe7dc352907fc980b868725387e9833d27a149a97a8deafb9a66ed6088339", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e411fff84085f00a564c50663ed0f30a", "guid": "bfdfe7dc352907fc980b868725387e98e66c0d41b78e7f199328e87836a58c70", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986843420e5a8508c1ed451d11f3662616", "guid": "bfdfe7dc352907fc980b868725387e98187ffddaad0f7a0f77acca32b467e97a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5de012f6c4b9075cd3968f4cbc2ac6d", "guid": "bfdfe7dc352907fc980b868725387e984970c1ec1e1f4238a522798f8e725d32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984289ecd430e852dc0b5113cc165ea27e", "guid": "bfdfe7dc352907fc980b868725387e98eb1d80a47c8f2e8b48a34803b9f872b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805cc7922dc2543db4fff6d0beaa84efa", "guid": "bfdfe7dc352907fc980b868725387e98e9014520ac9834cd241e1b61520ebaab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2600a0e6eb826de0f6b8952e304d2b5", "guid": "bfdfe7dc352907fc980b868725387e98302787c9a8b923ae949e4326d02b91f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c863354b7943032d715c74b919133af7", "guid": "bfdfe7dc352907fc980b868725387e98966053c62beed543476b77b9810214be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f410168c3381a8d91554c70a57909e", "guid": "bfdfe7dc352907fc980b868725387e98de831bd0256699b73131967074b797b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c621b1aa8953876827f5a67cb8c8e58e", "guid": "bfdfe7dc352907fc980b868725387e9870b15d15155d2d1d4dd729615097aebc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb6064bbf096c4a37201f6480b87dc0", "guid": "bfdfe7dc352907fc980b868725387e98922ccd8b833e84026883713aa61a1239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984123725395763aa3a5669489aa40f67e", "guid": "bfdfe7dc352907fc980b868725387e982506e5079203242aa2e0bcb59ab6e4ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab80e98ffd3111398894f3ca65e1db79", "guid": "bfdfe7dc352907fc980b868725387e98fa7a42138970679d041be58859d3f512"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc4677906ec2e1fcb49ec3bf1f233a9", "guid": "bfdfe7dc352907fc980b868725387e984921b3859be2f0ba9a95505673f07826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a499e940b6973fe0940e5ba944693f8e", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982becf868210880294e3041f231106c17", "guid": "bfdfe7dc352907fc980b868725387e98b7ca358534a7fc7c2f4f16e38e0e1f3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd2106c4021bf6f0bb2136f881c3d5e", "guid": "bfdfe7dc352907fc980b868725387e9852a6a36e2c886055dcd58b80543e11f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850348df4fc2d5b435cf65b33b0d75699", "guid": "bfdfe7dc352907fc980b868725387e98ed1d465084a1bdcca255fbf13d363757"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b3b0d5d1dbc177dd46bc641e22198fd", "guid": "bfdfe7dc352907fc980b868725387e98fb239a7730832f2dee9c108c72b626f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa7f2006da6fcfab448fe35841e97a27", "guid": "bfdfe7dc352907fc980b868725387e9893755cecea3f6adad4221f9513a00a86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f690e9a17f887d5d21cf5d261479e23c", "guid": "bfdfe7dc352907fc980b868725387e98827b61841681afd41c732d61f19a0416"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1379418d7af6d3baab639d86d97dbd9", "guid": "bfdfe7dc352907fc980b868725387e98455c3af829105196b83b1ec549a53fb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989767a240c1f28dec584f5f343ab9cf92", "guid": "bfdfe7dc352907fc980b868725387e986eb6b40864e7b3f0fe1bcd29aa30748a"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5846d5481396a0e80acb6050a58602a", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98add7228b85ff615243d752026d28c633", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyBeaver/SwiftyBeaver-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/SwiftyBeaver/SwiftyBeaver-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.4", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyBeaver/SwiftyBeaver.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftyBeaver", "PRODUCT_NAME": "SwiftyBeaver", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.1", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983b4bf3d76b7a6e7fa9df4a582bf87baa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985cec5f90735e9bbdd21c8c3b18900666", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyBeaver/SwiftyBeaver-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/SwiftyBeaver/SwiftyBeaver-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.4", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyBeaver/SwiftyBeaver.modulemap", "PRODUCT_MODULE_NAME": "SwiftyBeaver", "PRODUCT_NAME": "SwiftyBeaver", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.1", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982b6629aabdf51c9ca0f0206801327159", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985cec5f90735e9bbdd21c8c3b18900666", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyBeaver/SwiftyBeaver-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/SwiftyBeaver/SwiftyBeaver-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.4", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyBeaver/SwiftyBeaver.modulemap", "PRODUCT_MODULE_NAME": "SwiftyBeaver", "PRODUCT_NAME": "SwiftyBeaver", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.1", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9862e5ba781ec711dd1bc1ce12a66249af", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98053470e11ca776bad21f8efa405b3e91", "guid": "bfdfe7dc352907fc980b868725387e98def6a76c3bf3d0be4cb5a245e80164d3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983e880c62bf5b65bf15b1b39d54abf24e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b67ff6870ece79e6bb82f2c5617ebc7a", "guid": "bfdfe7dc352907fc980b868725387e987245087f9ad7aea9f17b33d70e2758ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afc1f044a45dc9c720f672946ba63b0", "guid": "bfdfe7dc352907fc980b868725387e9859c1842a0fda52a9cd950d66a9aa0cdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894eb1e26e5390d6a8c7856d10f62f6b6", "guid": "bfdfe7dc352907fc980b868725387e98b40d8ce705fc424b2bf8b044619ebb4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d285e72b2f62dbb7177200a583e27f", "guid": "bfdfe7dc352907fc980b868725387e9808723ad224740156da2a1f046f88b44f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac6f926d8740829e5ecedcbf44b8e46f", "guid": "bfdfe7dc352907fc980b868725387e9877626a873af03b516ae971f23b9f2353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ddea8fa027b41b68cef74a1177ccb0", "guid": "bfdfe7dc352907fc980b868725387e9857fd26cc9105efbef35680e1e2d6e0f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982441be7cb7f5286e8ab7e242a9636afc", "guid": "bfdfe7dc352907fc980b868725387e981d957016723cb55f2556a638ec3d2103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cb80fd04cf8c4764b751af8a6560654", "guid": "bfdfe7dc352907fc980b868725387e98f5755b35c03b87e62954383295b6e4ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef123eca24347789e562ab8059557b0", "guid": "bfdfe7dc352907fc980b868725387e988e9a67cf22fc8b7c42d4864c662e14d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf63913eb0cfcbb9335d8fde5cb1bb0", "guid": "bfdfe7dc352907fc980b868725387e986b4ac0039759d3cb296bd3c51abc1a9b"}], "guid": "bfdfe7dc352907fc980b868725387e98903008fbf2f7884a32302e41b7839dcd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5846d5481396a0e80acb6050a58602a", "guid": "bfdfe7dc352907fc980b868725387e986e031846fd2d2f3d8e7fe7d2de1fa238"}], "guid": "bfdfe7dc352907fc980b868725387e98d5f70ae6ce1ec76bb5f1782b9faddf39", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989b3e392ceaf1fc1f63c68915d5b14a39", "targetReference": "bfdfe7dc352907fc980b868725387e989ef56b0808e8d339e3227087886f9779"}], "guid": "bfdfe7dc352907fc980b868725387e989aaf4cc9137da7847b3e60e07f8a5db3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989ef56b0808e8d339e3227087886f9779", "name": "SwiftyBeaver-SwiftyBeaver"}], "guid": "bfdfe7dc352907fc980b868725387e9848227e53a3e646c7fe32cd408b63bb07", "name": "SwiftyBeaver", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98221e8b2598e5ecfc737ec080e56b234c", "name": "SwiftyBeaver.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
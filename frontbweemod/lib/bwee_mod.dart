export 'src/common/lamp_color_picker.dart';
export 'src/ui/add_light/view.dart';
export 'src/ui/add_light/logic.dart';

export 'src/routes/route_config.dart';
export 'src/routes/route_config.get_x_router_config.dart';
export 'src/configs/local.dart' show localizationsDelegates, supportedLocales;
export 'src/components/bwee_control.dart';

import 'package:gocomponent/gocomponet.dart';
import 'package:rk_kvstore_mmkv/rk_kvstore_mmkv.dart' as mmkv;
import 'package:rk_kvstore_shared_preferences/rk_kvstore_shared_preferences.dart'
    as shared_preferences;
import 'package:rk_package/rk_package.dart';

void init() {
  if (GetPlatform.isAndroid || GetPlatform.isIOS) {
    mmkv.init();
  } else {
    shared_preferences.init();
  }

  if (Get.isRegistered<ThemeManager>()) {
    Get.lazyPut(() => ThemeManager());
  }
}

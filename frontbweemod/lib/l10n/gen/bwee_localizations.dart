import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'bwee_localizations_en.dart';
import 'bwee_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of BweeLocalizations
/// returned by `BweeLocalizations.of(context)`.
///
/// Applications need to include `BweeLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen/bwee_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: BweeLocalizations.localizationsDelegates,
///   supportedLocales: BweeLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the BweeLocalizations.supportedLocales
/// property.
abstract class BweeLocalizations {
  BweeLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static BweeLocalizations? of(BuildContext context) {
    return Localizations.of<BweeLocalizations>(context, BweeLocalizations);
  }

  static const LocalizationsDelegate<BweeLocalizations> delegate = _BweeLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @light_setting.
  ///
  /// In zh, this message translates to:
  /// **'灯光设置'**
  String get light_setting;

  /// No description provided for @customize_color.
  ///
  /// In zh, this message translates to:
  /// **'自定义颜色'**
  String get customize_color;

  /// No description provided for @customize.
  ///
  /// In zh, this message translates to:
  /// **'自定义'**
  String get customize;

  /// No description provided for @light_mode.
  ///
  /// In zh, this message translates to:
  /// **'灯光模式'**
  String get light_mode;

  /// No description provided for @solid_color.
  ///
  /// In zh, this message translates to:
  /// **'纯色模式'**
  String get solid_color;

  /// No description provided for @rhythm_for_color.
  ///
  /// In zh, this message translates to:
  /// **'颜色律动'**
  String get rhythm_for_color;

  /// No description provided for @rhythm_for_brightness.
  ///
  /// In zh, this message translates to:
  /// **'亮度律动'**
  String get rhythm_for_brightness;

  /// No description provided for @rhythm_for_color_and_brightness.
  ///
  /// In zh, this message translates to:
  /// **'颜色和亮度律动'**
  String get rhythm_for_color_and_brightness;

  /// No description provided for @intensity.
  ///
  /// In zh, this message translates to:
  /// **'强度'**
  String get intensity;

  /// No description provided for @low.
  ///
  /// In zh, this message translates to:
  /// **'低'**
  String get low;

  /// No description provided for @medium.
  ///
  /// In zh, this message translates to:
  /// **'中'**
  String get medium;

  /// No description provided for @high.
  ///
  /// In zh, this message translates to:
  /// **'高'**
  String get high;

  /// No description provided for @maximum.
  ///
  /// In zh, this message translates to:
  /// **'最大'**
  String get maximum;

  /// No description provided for @brightness.
  ///
  /// In zh, this message translates to:
  /// **'亮度'**
  String get brightness;

  /// No description provided for @color_temperature.
  ///
  /// In zh, this message translates to:
  /// **'色温'**
  String get color_temperature;

  /// No description provided for @color_customization.
  ///
  /// In zh, this message translates to:
  /// **'颜色自定义'**
  String get color_customization;

  /// No description provided for @color_combo.
  ///
  /// In zh, this message translates to:
  /// **'颜色组合'**
  String get color_combo;

  /// No description provided for @color_palette.
  ///
  /// In zh, this message translates to:
  /// **'颜色调色板'**
  String get color_palette;

  /// No description provided for @confirm.
  ///
  /// In zh, this message translates to:
  /// **'确认'**
  String get confirm;

  /// No description provided for @add_your_light_fixture.
  ///
  /// In zh, this message translates to:
  /// **'添加您的灯具'**
  String get add_your_light_fixture;

  /// No description provided for @add_tip.
  ///
  /// In zh, this message translates to:
  /// **'请点击下方的 \' + \' 开始。'**
  String get add_tip;

  /// No description provided for @add_light.
  ///
  /// In zh, this message translates to:
  /// **'添加灯具'**
  String get add_light;

  /// No description provided for @discovering.
  ///
  /// In zh, this message translates to:
  /// **'正在发现...'**
  String get discovering;

  /// No description provided for @power.
  ///
  /// In zh, this message translates to:
  /// **'电源'**
  String get power;

  /// No description provided for @mode.
  ///
  /// In zh, this message translates to:
  /// **'模式'**
  String get mode;

  /// No description provided for @color.
  ///
  /// In zh, this message translates to:
  /// **'颜色'**
  String get color;

  /// No description provided for @setting.
  ///
  /// In zh, this message translates to:
  /// **'设置'**
  String get setting;
}

class _BweeLocalizationsDelegate extends LocalizationsDelegate<BweeLocalizations> {
  const _BweeLocalizationsDelegate();

  @override
  Future<BweeLocalizations> load(Locale locale) {
    return SynchronousFuture<BweeLocalizations>(lookupBweeLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_BweeLocalizationsDelegate old) => false;
}

BweeLocalizations lookupBweeLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return BweeLocalizationsEn();
    case 'zh': return BweeLocalizationsZh();
  }

  throw FlutterError(
    'BweeLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

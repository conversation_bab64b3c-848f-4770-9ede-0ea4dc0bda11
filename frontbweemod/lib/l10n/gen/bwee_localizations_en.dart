import 'bwee_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class BweeLocalizationsEn extends BweeLocalizations {
  BweeLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get light_setting => 'Light Settings';

  @override
  String get customize_color => 'Customize Color';

  @override
  String get customize => 'Customize';

  @override
  String get light_mode => 'Light Mode';

  @override
  String get solid_color => 'Solid Color';

  @override
  String get rhythm_for_color => 'Color Rhythm';

  @override
  String get rhythm_for_brightness => 'Brightness Rhythm';

  @override
  String get rhythm_for_color_and_brightness => 'Color and Brightness Rhythm';

  @override
  String get intensity => 'Intensity';

  @override
  String get low => 'Low';

  @override
  String get medium => 'Medium';

  @override
  String get high => 'High';

  @override
  String get maximum => 'Maximum';

  @override
  String get brightness => 'Brightness';

  @override
  String get color_temperature => 'Color Temperature';

  @override
  String get color_customization => 'Color Customization';

  @override
  String get color_combo => 'Color Combo';

  @override
  String get color_palette => 'Color Palette';

  @override
  String get confirm => 'Confirm';

  @override
  String get add_your_light_fixture => 'Add Your Light Fixture';

  @override
  String get add_tip => 'Please click the \'+\' button below to start.';

  @override
  String get add_light => 'Add Light';

  @override
  String get discovering => 'Discovering...';

  @override
  String get power => 'Power';

  @override
  String get mode => 'Mode';

  @override
  String get color => 'Color';

  @override
  String get setting => 'Setting';
}

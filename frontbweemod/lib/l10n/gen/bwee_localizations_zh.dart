import 'bwee_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class BweeLocalizationsZh extends BweeLocalizations {
  BweeLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get light_setting => '灯光设置';

  @override
  String get customize_color => '自定义颜色';

  @override
  String get customize => '自定义';

  @override
  String get light_mode => '灯光模式';

  @override
  String get solid_color => '纯色模式';

  @override
  String get rhythm_for_color => '颜色律动';

  @override
  String get rhythm_for_brightness => '亮度律动';

  @override
  String get rhythm_for_color_and_brightness => '颜色和亮度律动';

  @override
  String get intensity => '强度';

  @override
  String get low => '低';

  @override
  String get medium => '中';

  @override
  String get high => '高';

  @override
  String get maximum => '最大';

  @override
  String get brightness => '亮度';

  @override
  String get color_temperature => '色温';

  @override
  String get color_customization => '颜色自定义';

  @override
  String get color_combo => '颜色组合';

  @override
  String get color_palette => '颜色调色板';

  @override
  String get confirm => '确认';

  @override
  String get add_your_light_fixture => '添加您的灯具';

  @override
  String get add_tip => '请点击下方的 \' + \' 开始。';

  @override
  String get add_light => '添加灯具';

  @override
  String get discovering => '正在发现...';

  @override
  String get power => '电源';

  @override
  String get mode => '模式';

  @override
  String get color => '颜色';

  @override
  String get setting => '设置';
}

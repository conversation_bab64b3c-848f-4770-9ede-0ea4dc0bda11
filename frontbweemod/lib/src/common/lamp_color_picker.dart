import 'dart:math' as math;

import 'package:bwee_mod/src/configs/assets_gen.dart';
import 'package:flutter/material.dart';

class LampColorPicker extends StatefulWidget {
  const LampColorPicker({
    super.key,
    required this.onChange,
    required this.colorValue,
    required this.onChangeEnd,
    this.width = 300,
    this.thumbSize = 30,
    this.thumbMoveDuration = const Duration(milliseconds: 80),
    this.showThumb = true,
    this.thumbBorderWidth = 3.5,
    this.thumbBorderColor = Colors.white,
  });

  // 用于通知父组件颜色变化
  final ValueChanged<Color> onChange;
  final ValueChanged<Color> onChangeEnd;

  // 用于设置初始颜色
  final Color colorValue;
  // 组件的宽度
  final double width;
  // 触摸圆的大小
  final double thumbSize;
  // 触摸圆移动的动画时间
  final Duration thumbMoveDuration;
  // 触摸圆是否显示
  final bool showThumb;
  // 触摸圆的边框宽度
  final double thumbBorderWidth;
  // 触摸圆的边框颜色
  final Color thumbBorderColor;

  @override
  State<LampColorPicker> createState() => _LampColorPickerState();
}

class _LampColorPickerState extends State<LampColorPicker> {
  Offset thumbOffset = Offset.zero;
  Color pickerColor = Colors.white;
  bool colorInit = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 180), () {
        _updateThumbPosition(widget.colorValue);
      });
      Future.delayed(const Duration(milliseconds: 220), () {
        setState(() {
          colorInit = true;
        });
      });
    });
  }

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.colorValue != oldWidget.colorValue) {
      if (widget.colorValue != pickerColor) {
        _updateThumbPosition(widget.colorValue);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      child: Stack(
        children: [
          GestureDetector(
            onPanUpdate: (details) =>
                _handleInteraction(details.localPosition, onPanUpdate: true),
            onTapDown: (details) => _handleInteraction(
              details.localPosition,
            ),
            onTapUp: (details) {
              widget.onChangeEnd(pickerColor);
            },
            onPanEnd: (details) {
              widget.onChangeEnd(pickerColor);
            },
            child: Stack(
              children: [
                Image.asset(Assets.ASSETS_COLOR_COLOR_WHEEL_PNG,
                    fit: BoxFit.contain, package: 'bwee_mod'),
                Visibility(
                  visible: widget.showThumb,
                  child: _buildColorIndicator(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorIndicator() {
    return AnimatedPositioned(
      duration: switch (colorInit) {
        true => widget.thumbMoveDuration,
        false => Duration.zero,
      },
      left: thumbOffset.dx - (widget.thumbSize / 2),
      top: thumbOffset.dy - (widget.thumbSize / 2),
      child: Container(
        width: widget.thumbSize,
        height: widget.thumbSize,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.thumbBorderColor,
            width: widget.thumbBorderWidth,
          ),
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  void _handleInteraction(
    Offset localPosition, {
    bool onPanUpdate = false,
  }) {
    final renderBox = context.findRenderObject() as RenderBox;
    final center = Offset(renderBox.size.width / 2, renderBox.size.height / 2);
    final radius = renderBox.size.shortestSide / 2;

    // 计算触摸点相对于圆心的偏移量
    final Offset relativeOffset = localPosition - center;

    // 计算触摸点到圆心的距离
    final double distance = relativeOffset.distance;

    // 环形的半径
    double indicatorRadius = widget.thumbSize / 2;

    // 可触摸的有效半径，减去指示器的半径
    final double effectiveRadius = radius - indicatorRadius;

    if (onPanUpdate) {
      if (distance > effectiveRadius) {
        final double scaleFactor = effectiveRadius / distance;
        final newOffset = center + relativeOffset * scaleFactor;
        setState(() {
          thumbOffset = newOffset;
        });
      } else {
        setState(() {
          thumbOffset = localPosition;
        });
      }
    } else {
      if (distance > effectiveRadius) {
        final double scaleFactor = effectiveRadius / distance;
        setState(() {
          thumbOffset = center + relativeOffset * scaleFactor;
        });
      } else {
        setState(() {
          thumbOffset = localPosition;
        });
      }
    }

    // 实现获取颜色的逻辑
    final double angle =
        math.atan2(thumbOffset.dy - center.dy, thumbOffset.dx - center.dx);
    final double hue = (angle / (2 * math.pi) + 0.5) % 1;

    // 计算饱和度和亮度
    final double saturation = distance / radius;

    // 固定亮度值
    final double value = 1;

    // 计算色相
    double adjustedHue = (hue * 360 - 90);
    if (adjustedHue < 0) {
      adjustedHue += 360;
    }

    // 确保色相、饱和度和亮度值在有效范围内
    final double clampedHue = adjustedHue.clamp(0.0, 360.0);
    final double clampedSaturation = saturation.clamp(0.0, 1.0);
    final double clampedValue = value.clamp(0.0, 1.0);

    final Color color =
        HSVColor.fromAHSV(1.0, clampedHue, clampedSaturation, clampedValue)
            .toColor();

    pickerColor = color;
    widget.onChange(color);
  }

  void _updateThumbPosition(Color color) {
    final hsvColor = HSVColor.fromColor(color);
    final renderBox = context.findRenderObject() as RenderBox;
    final center = Offset(renderBox.size.width / 2, renderBox.size.height / 2);
    final radius = renderBox.size.shortestSide / 2 * 0.9;

    double hueAngle = (hsvColor.hue - 90) % 360 * (math.pi / 180);
    double saturationDistance = hsvColor.saturation * (radius * 1.1);

    double dx = saturationDistance * math.cos(hueAngle);
    double dy = saturationDistance * math.sin(hueAngle);

    setState(() {
      thumbOffset = center + Offset(dx, dy);
    });
  }
}

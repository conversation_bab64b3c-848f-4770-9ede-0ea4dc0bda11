// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// TLGetXRouterConfigGenerator
// **************************************************************************

// ignore_for_file: depend_on_referenced_packages, prefer_const_constructors
// ignore_for_file: prefer_const_literals_to_create_immutables
// ignore_for_file: unnecessary_parenthesis

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:bwee_mod/src/ui/add_light/binding.dart' as _i1;
import 'package:bwee_mod/src/ui/color_customization/binding.dart' as _i4;
import 'package:bwee_mod/src/ui/light_settings/binding.dart' as _i6;
import 'package:flutter/material.dart' as _i3;
import 'package:get/get.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart'
    as _i8;

import '../ui/add_light/view.dart' as _i2;
import '../ui/color_customization/view.dart' as _i5;
import '../ui/light_settings/view.dart' as _i7;

mixin BweeRouteConfigMixin {
  static final pages = [
    GetPage<dynamic>(
      name: RouteNames.addLightView,
      bindings: [_i1.AddLightBinding()],
      page: () => _i2.AddLightView(
        deviceIP: (Get.arguments?['deviceIP'] as String),
        key: (Get.arguments?['key'] as _i3.Key?),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.colorCustomizationView,
      bindings: [_i4.ColorCustomizationBinding()],
      page: () => _i5.ColorCustomizationView(
        deviceIP: (Get.arguments?['deviceIP'] as String),
        key: (Get.arguments?['key'] as _i3.Key?),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.lightSettingsView,
      bindings: [_i6.LightSettingsBinding()],
      page: () => _i7.LightSettingsView(
        key: (Get.arguments?['key'] as _i3.Key?),
        deviceIP: (Get.arguments?['deviceIP'] as String),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
  ];
}

class RouteNames {
  static const addLightView = '/bwee/add_light_view';

  static const colorCustomizationView = '/bwee/color_customization_view';

  static const lightSettingsView = '/bwee/light_settings_view';
}

class AddLightViewTypedRoute extends _i8.TypedTlGetRouter {
  AddLightViewTypedRoute({
    required String deviceIP,
    _i3.Key? key,
  }) {
    super.arguments.addAll({'deviceIP': deviceIP});
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.addLightView;
}

class ColorCustomizationViewTypedRoute extends _i8.TypedTlGetRouter {
  ColorCustomizationViewTypedRoute({
    required String deviceIP,
    _i3.Key? key,
  }) {
    super.arguments.addAll({'deviceIP': deviceIP});
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.colorCustomizationView;
}

class LightSettingsViewTypedRoute extends _i8.TypedTlGetRouter {
  LightSettingsViewTypedRoute({
    _i3.Key? key,
    required String deviceIP,
  }) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'deviceIP': deviceIP});
  }

  @override
  final routeName = RouteNames.lightSettingsView;
}

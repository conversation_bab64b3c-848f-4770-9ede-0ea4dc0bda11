import 'dart:typed_data';

import 'package:flutter/widgets.dart';
import 'package:rk_kvstore/rk_kvstore.dart';

IKvStore get kvStore => KvStoreHelper.createOrGetInstance('bwee_mod', id: '_');

enum KvKey {
  customColors,
}

//用拓展的方式封装存取方法
extension KvStoreExt on IKvStore {
  Future<List<Color>?> getCustomColors() async {
    final colorHex = await getUint8List(KvKey.customColors.name);
    if (colorHex == null || colorHex.isEmpty) {
      return null;
    }
    final colors = <Color>[];
    assert(
        colorHex.length % 4 == 0, 'Color hex length must be a multiple of 4');
    for (int i = 0; i < colorHex.length; i += 4) {
      if (i + 4 <= colorHex.length) {
        final r = colorHex[i];
        final g = colorHex[i + 1];
        final b = colorHex[i + 2];
        final a = colorHex[i + 3];
        colors.add(Color.fromARGB(a, r, g, b));
      }
    }
    return colors;
  }

  Future<void> setCustomColors(List<Color> colors) async {
    final colorHex = <int>[];
    for (final color in colors) {
      colorHex.add((color.r * 255 ).ceil());
      colorHex.add((color.g * 255 ).ceil());
      colorHex.add((color.b * 255 ).ceil());
      colorHex.add((color.a * 255 ).ceil());
    }
    print ('设置自定义颜色: $colorHex');
    await setUint8List(KvKey.customColors.name, Uint8List.fromList(colorHex));
  }
}

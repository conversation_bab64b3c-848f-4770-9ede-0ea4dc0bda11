import 'package:bwee_interface/bwee_interface.dart';

import 'assets_gen.dart';
import 'local.dart';

extension SyncModeNameExt on BweeSyncMode {
  String get name {
    switch (this) {
      case BweeSyncMode.monochrome:
        return Local.bwee.solid_color;
      case BweeSyncMode.video:
        return Local.bwee.rhythm_for_color;
      case BweeSyncMode.audio:
        return Local.bwee.rhythm_for_brightness;
      case BweeSyncMode.game:
        return Local.bwee.rhythm_for_color_and_brightness;
    }
  }
}

extension SyncModeIconExt on BweeSyncMode {
  String get icon {
    switch (this) {
      case BweeSyncMode.monochrome:
        return Assets.ASSETS_ICON_LIGHT_SVG;
      case BweeSyncMode.video:
        return Assets.ASSETS_ICON_LIGHT_SVG;
      case BweeSyncMode.audio:
        return Assets.ASSETS_ICON_LIGHT_SVG;
      case BweeSyncMode.game:
        return Assets.ASSETS_ICON_LIGHT_SVG;
    }
  }
}

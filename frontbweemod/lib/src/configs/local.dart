
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:rk_get/rk_get.dart';

import '../../l10n/gen/bwee_localizations.dart';


abstract class Local {
  static BweeLocalizations get bwee =>
      BweeLocalizations.of(Get.context!)!;

}

List<LocalizationsDelegate> get localizationsDelegates => [
      GlobalMaterialLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
      BweeLocalizations.delegate,
    ];

Iterable<Locale> get supportedLocales =>
    [...BweeLocalizations.supportedLocales];



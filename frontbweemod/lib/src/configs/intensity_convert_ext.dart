import 'package:bwee_interface/bwee_interface.dart';
import 'package:bwee_mod/src/configs/assets_gen.dart';

import 'local.dart';

extension IntensityConvertExt on SyncStrength {
  String get toName {
    switch (this) {
      case SyncStrength.weak:
        return Local.bwee.low;
      case SyncStrength.medium:
        return Local.bwee.medium;
      case SyncStrength.strong:
        return Local.bwee.high;
      case SyncStrength.veryStrong:
        return Local.bwee.maximum;
    }
  }

  String get toIcon {
    switch (this) {
      case SyncStrength.weak:
        return Assets.ASSETS_ICON_INTENSITY_LOW_SVG;
      case SyncStrength.medium:
        return Assets.ASSETS_ICON_INTENSITY_NORMAL_SVG;
      case SyncStrength.strong:
        return Assets.ASSETS_ICON_INTENSITY_HIGH_SVG;
      case SyncStrength.veryStrong:
        return Assets.ASSETS_ICON_INTENSITY_MAXIMUN_SVG;
    }
  }
}

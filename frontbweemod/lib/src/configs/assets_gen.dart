/// Generate by [asset_generator](https://github.com/fluttercandies/flutter_asset_generator) library.
/// PLEASE DO NOT EDIT MANUALLY.
// ignore_for_file: constant_identifier_names
class Assets {
  const Assets._();

  static const String ASSETS_COLOR_COLOR_WHEEL_PNG =
      'assets/color/color_wheel.png';

  static const String ASSETS_ICON_ADD_SVG = 'assets/icon/add.svg';

  static const String ASSETS_ICON_BACK_SVG = 'assets/icon/back.svg';

  static const String ASSETS_ICON_COLOR_SVG = 'assets/icon/color.svg';

  static const String ASSETS_ICON_CORRECT_SVG = 'assets/icon/correct.svg';

  static const String ASSETS_ICON_INTENSITY_HIGH_SVG =
      'assets/icon/intensity_high.svg';

  static const String ASSETS_ICON_INTENSITY_LOW_SVG =
      'assets/icon/intensity_low.svg';

  static const String ASSETS_ICON_INTENSITY_MAXIMUN_SVG =
      'assets/icon/intensity_maximun.svg';

  static const String ASSETS_ICON_INTENSITY_NORMAL_SVG =
      'assets/icon/intensity_normal.svg';

  static const String ASSETS_ICON_LIGHT_SVG = 'assets/icon/light.svg';

  static const String ASSETS_ICON_MODE_SVG = 'assets/icon/mode.svg';

  static const String ASSETS_ICON_POWER_SVG = 'assets/icon/power.svg';

  static const String ASSETS_ICON_REFRESH_SVG = 'assets/icon/refresh.svg';

  static const String ASSETS_ICON_REMOVE_SVG = 'assets/icon/remove.svg';

  static const String ASSETS_ICON_SETTING_SVG = 'assets/icon/setting.svg';
}

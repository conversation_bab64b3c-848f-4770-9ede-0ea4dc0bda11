import 'package:bwee_interface/bwee_interface.dart';
import 'package:bwee_mod/src/configs/kv_store.dart';
import 'package:flutter/material.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:rk_package/rk_package.dart';

import 'state.dart';

class LightSettingsLogic extends GetxController with LightSettingsState {
  LightSettingsLogic();
  LightSettingsLogic.fromComponent({required this.deviceIP});

  String deviceIP = '';

  @override
  void onInit() {
    super.onInit();
    if (deviceIP.isEmpty) {
      deviceIP = Get.arguments['deviceIP'] ?? '';
    }
  }

  BweeLogic get bweeLogic => Get.find<BweeLogic>(tag: deviceIP);

  @override
  void onReady() {
    super.onReady();
    //获取自定义颜色
    Future.sync(() async {
      try {
        final colors = await kvStore.getCustomColors();
        if (colors != null && colors.isNotEmpty) {
          customColors.assignAll(colors);
        } else {
          //默认3颜色
          customColors.assignAll([
            Colors.yellow,
            Colors.blue,
            Colors.green,
          ]);
        }
      } catch(e) {
        customColors.assignAll([
          Colors.yellow,
          Colors.blue,
          Colors.green,
        ]);
      }
      
    });
    if (!bweeLogic.initData.value) {
      Future.sync(() async {
        final power = await bweeLogic.controller.power;
        bweeLogic.powerOn.value = power;
        final sync = await bweeLogic.controller.sync;
        bweeLogic.syncOn.value = sync;
        final deviceOn = await bweeLogic.controller.coeDevice;
        bweeLogic.deviceOn.value = deviceOn;

        final brightness = await bweeLogic.controller.brightness;
        bweeLogic.brightness.value = brightness;
        final color = await bweeLogic.controller.colorHex;
        bweeLogic.color.value = Color.fromARGB(
          255,
          color[0],
          color[1],
          color[2],
        );
        final syncMode = await bweeLogic.controller.syncMode;
        bweeLogic.syncMode.value = syncMode;
        final syncStrength = await bweeLogic.controller.strength;
        bweeLogic.syncStrength.value = syncStrength;
      });
    }
  }

  //设置单色
  Future<void> setColor(Color color) async {
    bweeLogic.color.value = color;
    //先切换到单色模式
    if (bweeLogic.syncMode.value != BweeSyncMode.monochrome) {
      await bweeLogic.controller.setSyncMode(BweeSyncMode.monochrome);
    }
    await bweeLogic.controller.setColorHex(color.toHex());
  }

  //设置模式
  Future<void> setSyncMode(BweeSyncMode mode) async {
    bweeLogic.syncMode.value = mode;
    await bweeLogic.controller.setSyncMode(mode);
  }

  //设置强度
  Future<void> setSyncStrength(SyncStrength strength) async {
    bweeLogic.syncStrength.value = strength;
    await bweeLogic.controller.setStrength(strength);
  }

  //设置亮度
  Future<void> setBrightness(int brightness) async {
    bweeLogic.brightness.value = brightness;
    await bweeLogic.controller.setBrightness(brightness);
  }

  //开关灯光
  Future<void> togglePower() async {
    //实际是设置是否同步
    bweeLogic.deviceOn.value = !bweeLogic.deviceOn.value;
    await bweeLogic.controller.setDevice(bweeLogic.deviceOn.value);
  }

  ///切换模式
  Future<void> toggleSyncMode() async {
    final mode = bweeLogic.syncMode.value;
    final modeIndex = BweeSyncMode.values.indexOf(mode);
    final nextModeIndex = (modeIndex + 1) % BweeSyncMode.values.length;
    bweeLogic.syncMode.value = BweeSyncMode.values[nextModeIndex];
    await bweeLogic.controller.setSyncMode(bweeLogic.syncMode.value);
  }

  ///切换颜色
  Future<void> toggleColor() async {
    final color = bweeLogic.color.value;
    //获取当前颜色的下一个颜色
    final nextColor = customColors[(customColors.indexOf(color) + 1) % customColors.length];
    bweeLogic.color.value = nextColor;
    if (bweeLogic.syncMode.value != BweeSyncMode.monochrome) {
      //如果不是单色模式，切换到单色模式
      await bweeLogic.controller.setSyncMode(BweeSyncMode.monochrome);
      bweeLogic.syncMode.value = BweeSyncMode.monochrome;
    }
    await bweeLogic.controller.setColorHex(nextColor.toHex());
  }

  ///切换强度
  Future<void> toggleIntensity() async {
    final strength = bweeLogic.syncStrength.value;
    final strengthIndex = SyncStrength.values.indexOf(strength);
    final nextStrengthIndex = (strengthIndex + 1) % SyncStrength.values.length;
    bweeLogic.syncStrength.value = SyncStrength.values[nextStrengthIndex];
    await bweeLogic.controller.setStrength(bweeLogic.syncStrength.value);
  }

  Rx<bool> get powerOn => bweeLogic.powerOn;
  Rx<bool> get sync => bweeLogic.syncOn;
  Rx<bool> get deviceOn => bweeLogic.deviceOn;

  Rx<BweeSyncMode> get syncMode => bweeLogic.syncMode;
  Rx<SyncStrength> get syncStrength => bweeLogic.syncStrength;
  Rx<int> get brightness => bweeLogic.brightness;
  Rx<Color> get color => bweeLogic.color;
}

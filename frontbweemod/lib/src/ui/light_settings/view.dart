import 'package:bwee_interface/bwee_interface.dart';
import 'package:bwee_mod/bwee_mod.dart';
import 'package:bwee_mod/src/configs/intensity_convert_ext.dart';
import 'package:bwee_mod/src/configs/sync_mode_convert_ext.dart';

import 'package:flutter/material.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:rk_package/rk_package.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../components/add_button.dart';
import '../../configs/local.dart';
import 'logic.dart';

@getXRoute
class LightSettingsView extends GetView<LightSettingsLogic> {
  const LightSettingsView({super.key, required this.deviceIP});

  final String deviceIP;

  static const _unselectColor = Color.fromRGBO(242, 242, 242, 1);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GoAppBar(
        title: AxText(
          text: Local.bwee.light_setting,
          fontSize: 22.sp,
          color: Color.fromRGBO(18, 18, 18, 1),
          fontFamily: AxTextType.bold,
        ),
      ),
      floatingActionButton: OnTapAnimation(
        type: OnTapAnimationType.toOpacity,
        onTap: () {
          Get.toTyped(AddLightViewTypedRoute(deviceIP: deviceIP));
        },
        child: AddLightButton(),
      ),
      body: Container(
        margin: EdgeInsets.symmetric(
          horizontal: 14.w,
          vertical: 16.w
        ),
        width: Get.width,
        // color: Colors.red,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(
              Local.bwee.customize_color,
            ),
            _buildColorSection().marginOnly(bottom: 20.w),
            _buildSectionTitle(
              Local.bwee.light_mode,
            ),
            _buildLightModeSection().marginOnly(bottom: 20.w),
            _buildSectionTitle(
              Local.bwee.intensity,
            ),
            _buildIntensitySection().marginOnly(bottom: 20.w),
            _buildSectionTitle(
              Local.bwee.brightness,
            ),
            _buildBrightnessSection(context),
          ],
        )
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return AxText(
      text: title,
      fontSize: 18.sp,
      color: Colors.black,
      fontFamily: AxTextType.bold,
    ).marginOnly(bottom: 12.w);
  }

  Widget _buildColorSection() {
    return SizedBox(
      height: 110.w,
      child: Obx(() => ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: controller.customColors.length + 1,
        separatorBuilder: (context, index) {
          return SizedBox(width: 12.w);
        },
        itemBuilder: (context, index) {
          if (index == controller.customColors.length) {
            return _buildCustomizeButton();
          }

          final color = controller.customColors[index];
          final colorName = 'Color${index + 1}';

          return _buildColorItem(color, colorName);
        },
      ).addMacScroll()),
    );
  }

  Widget _buildCustomizeButton() {
    return GestureDetector(
      onTap: () {
        Get.toTyped(ColorCustomizationViewTypedRoute(deviceIP: deviceIP)).then((value) {
          if (value != null && value is List<Color>) {
            controller.customColors.assignAll(value);
          }
        });
      },
      child: Container(
        width: 80.w,
        decoration: BoxDecoration(
          color: Color.fromRGBO(242, 242, 242, 1),
          borderRadius: BorderRadius.circular(15.w),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 54.w,
              height: 54.w,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.add, size: 30.w, color: Colors.grey[600]),
            ),
            SizedBox(height: 8.h),
            AxText(
              text: Local.bwee.customize,
              fontSize: 12.sp,
              color: Colors.black54,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorItem(Color color, String name) {
    return Obx(() {
      final isSelected = controller.color.value == color;
      return GestureDetector(
        onTap: () => controller.setColor(color),
        child: Container(
          width: 80.w,
          decoration: BoxDecoration(
            color: isSelected ? Colors.black.toOpacity(0.8) : _unselectColor,
            borderRadius: BorderRadius.circular(15.w),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                children: [
                  Container(
                    width: 54.w,
                    height: 54.w,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Positioned.fill(
                    child: Center(
                      child: Container(
                        width: 48.w,
                        height: 48.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2.w),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              AxText(
                text: name,
                fontSize: 12.sp,
                fontFamily: isSelected? AxTextType.bold : AxTextType.medium,
                color: isSelected ? Colors.white : Colors.black54,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildLightModeSection() {
    return SizedBox(
      height: 110.w,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: BweeSyncMode.values.length,
        separatorBuilder: (BuildContext context, int index) {
          return SizedBox(width: 12.w);
        },
        itemBuilder: (context, index) {
          final mode = BweeSyncMode.values[index];
          return _buildModeItem(mode);
        },
      ),
    );
  }

  Widget _buildModeItem(BweeSyncMode mode) {
    return Obx(() {
      final isSelected = controller.syncMode.value == mode;
      return GestureDetector(
        onTap: () => controller.setSyncMode(mode),
        child: Container(
          width: 80.w,
          // margin: EdgeInsets.only(right: 15.w),
          decoration: BoxDecoration(
            color: isSelected ? Colors.black.toOpacity(0.8) : _unselectColor,
            borderRadius: BorderRadius.circular(15.w),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgAssetIcon(
                size: 42.w,
                path: mode.icon,
                package: 'bwee_mod',
                color: isSelected
                    ? Colors.white
                    : Colors.black54,
              ),
              SizedBox(height: 8.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                child: AxText(
                  text: mode.name,
                  fontSize: 12.sp,
                  color: isSelected ? Colors.white : Colors.black54,
                  softWrap: false,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildIntensitySection() {
    return SizedBox(
      height: 100.w,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: SyncStrength.values.length,
        separatorBuilder: (context, index) {
          return SizedBox(width: 12.w);
        },
        itemBuilder: (context, index) {
          final strength = SyncStrength.values[index];
          return _buildIntensityItem(
            strength,
          );
        },
      ),
    );
  }

  Widget _buildIntensityItem(SyncStrength intensity) {
    return Obx(() {
      final isSelected = controller.syncStrength.value == intensity;
      return GestureDetector(
        onTap: () => controller.setSyncStrength(intensity),
        child: Container(
          width: 80.w,
          decoration: BoxDecoration(
            color: isSelected ? Colors.black.toOpacity(0.8) : _unselectColor,
            borderRadius: BorderRadius.circular(15.w),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgAssetIcon(
                size: 44.w,
                path: intensity.toIcon,
                package: 'bwee_mod',
                color: isSelected
                    ? Colors.white
                    : Colors.black54,
              ),
              SizedBox(height: 8.w),
              AxText(
                text: intensity.toName,
                fontSize: 12.sp,
                color: isSelected ? Colors.white : Colors.black54,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildBrightnessSection(BuildContext context) {
    return Obx(() => Column(
          children: [
            Container(
              height: 32.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.w),
              ),
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  trackHeight: 32.w,
                  thumbShape: SliderComponentShape.noThumb,
                  overlayShape: SliderComponentShape.noOverlay,
                  activeTrackColor: Colors.grey[400],
                  inactiveTrackColor: Colors.grey[300],
                  // thumbColor: SliderComponentShape.noThumb,
                ),
                child: Slider(
                  value: controller.brightness.value.toDouble(),
                  min: 0,
                  max: 254,
                  onChanged: (value) {
                    controller.brightness.value = value.toInt();
                  },
                  onChangeEnd: (value) {
                    controller.setBrightness(value.toInt());
                  },
                ),
              ),
            ),
            SizedBox(height: 10.w),
            AxText(
              text: "${((controller.brightness.value / 254) * 100).round()}%",
              fontSize: 14.sp,
              color: Colors.black54,
            ),
          ],
        ));
  }
}

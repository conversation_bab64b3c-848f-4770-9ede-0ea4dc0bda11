import 'package:bwee_mod/bwee_mod.dart';
import 'package:bwee_mod/src/configs/package_string.dart';
import 'package:bwee_mod/src/ui/add_light/text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gocomponent/components/appbar/appbar.dart';
import 'package:gocomponent/components/axtext/axtext.dart';
import 'package:gocomponent/components/ontap/ontap_animation.dart';
import 'package:gocomponent/components/svgicon/svgicon.dart';
import 'package:gocomponent/theme/theme_manager.dart';
import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../components/add_light_bottomsheet.dart';
import '../../configs/assets_gen.dart';
import '../../components/add_button.dart';

@GetXRoute()
class AddLightView extends GetView<AddLightLogic> {
  const AddLightView({required this.deviceIP, super.key});

  final String deviceIP;

  @override
  Widget build(BuildContext context) {
    
    return Scaffold(
      appBar: GoAppBar(
        title: LightText.toAppBarTitle(),
        action: AppBarActionIcon(
          actionIcon: Icon(
            Icons.light,
            size: 20.w,
            color: Colors.black,
          ),
          actionOnTap: () {
            Get.toTyped(LightSettingsViewTypedRoute(deviceIP: deviceIP));
          }
        )
      ),
      floatingActionButton: OnTapAnimation(
        type: OnTapAnimationType.toOpacity,
        onTap: () {
          Get.bottomSheet(
            AddLightBottomsheet(
              controller: controller,
            ),
            isScrollControlled: true,
            enableDrag: false,
            useRootNavigator: true,
            // backgroundColor: Colors.black.withAlpha((255 * 0.4).toInt()),
            // barrierColor: Colors.transparent,
          ).then((_) {
            // 清空选择的灯
            controller.changedList.clear();
          });
        },
        child: AddLightButton(),
      ),
      body: Container(
        alignment: Alignment.center,
        color: Colors.white,
        child: Obx(()=> switch(controller.connectedLightList.isNotEmpty) {
          true => Container(
            padding: EdgeInsets.symmetric(
              horizontal: 22.w,
              vertical: 22.w
            ),
            child: Column(
              children: [
                for (var item in controller.connectedLightList)
                Container(
                  width: Get.width,
                  height: 80.w,
                  padding: EdgeInsets.symmetric(horizontal: 22.w),
                  decoration: BoxDecoration(
                    color: ThemeManager.to.colorTo.surface,
                    borderRadius: BorderRadius.circular(15.r)
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SvgAssetIcon(
                        path: Assets.ASSETS_ICON_LIGHT_SVG,
                        size: 38.w,
                        package: BWEEMOD_PACKAGE,
                        color: ThemeManager.to.colorTo.primary,
                      ).marginOnly(right: 22.w),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AxText(
                              text: item.device.platformName,
                              fontFamily: AxTextType.bold,
                              fontSize: 18.w,
                            ),
                            AxText(
                              text: 'SN: ${item.device.remoteId.str.substring(0, 8)}',
                              fontFamily: AxTextType.medium,
                              fontSize: 16.w,
                              overflow: TextOverflow.clip,
                              softWrap: false,
                            ),
                          ],
                        ),
                      ),
                      OnTapAnimation(
                        onTap: () {
                          loadingCallback(() => controller.removeDevice(item));
                        },
                        child: SvgAssetIcon(
                          path: Assets.ASSETS_ICON_REMOVE_SVG,
                          size: 38.w,
                          package: BWEEMOD_PACKAGE,
                          color: ThemeManager.to.colorTo.primary,
                        ),
                      )
                    ],
                  ),
                ).marginOnly(bottom: 22.w),
              ],
            ),
          ),
          false => Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                Assets.ASSETS_ICON_LIGHT_SVG,
                package: 'bwee_mod',
                width: 75.w,
                height: 75.w,
              ),
              LightText.toText().marginOnly(top: 50.w),
              LightText.toText2().marginOnly(top: 12.w),
              SizedBox(height: 40.w),
              
            ],
          ),
        })
      ),
    );
  }
}

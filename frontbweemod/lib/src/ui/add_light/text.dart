import 'package:flutter/widgets.dart';
import 'package:gocomponent/components/axtext/axtext.dart';
import 'package:rk_package/rk_package.dart';

import '../../configs/local.dart';

class LightText {
  // 添加灯的信息
  static Widget toAppBarTitle() {
    return AxText(
      text: Local.bwee.add_light,
      fontSize: 22.sp,
      color: Color.fromRGBO(18, 18, 18, 1),
      fontFamily: AxTextType.bold,
    );
  }

  // 添加灯的信息
  static Widget toText() {
    return AxText(
      text: Local.bwee.add_your_light_fixture,
      fontSize: 20.sp,
      color: Color.fromRGBO(18, 18, 18, 1),
    );
  }

  // 添加灯的信息2
  static Widget toText2() {
    return AxText(
      text: Local.bwee.add_tip,
      fontSize: 16.sp,
      color: Color.fromRGBO(128, 128, 128, 1),
    );
  }
}

import 'package:rk_get/rk_get.dart';
import 'package:bwee_interface/bwee_interface.dart';
import 'package:rk_toast/rk_toast.dart';

import '../../configs/log.dart';
import 'state.dart';

class AddLightLogic extends GetxController
    with AddLightState, ControllerMountedMixin {
  late final String _deviceIP;
  // todo 现在获取灯的方式是通过名字
  static const lightName = 'Bweetech';
  @override
  void onInit() {
    super.onInit();
    _deviceIP = Get.arguments['deviceIP'] ?? '';
  }

  BweeLogic get bweeLogic => Get.find<BweeLogic>(tag: _deviceIP);

  Future<void> onRefreshLights() async {
    // 禁止重复调用
    print('禁止重复调用');
    if (discovered.value == true) return;

    discovered.value = true;
    final devices = await bweeLogic.controller.getBleDevices(true);
    if (devices.isNotEmpty) {
      //如果有蓝牙设备，获取设备列表
      //对比新扫描出来的灯,去查询是否连接上
      final newLights = devices
          .where((e) => e.device.platformName == lightName)
          .map((e) => BweeLightDevice(e.device))
          .where((e) => !lightList.any(
            (element) => element.device.remoteId == e.device.remoteId,
          )).toList();
      lightList.assignAll(devices
          .where((e) => e.device.platformName == lightName)
          .map((e) => BweeLightDevice(e.device)));
      for (var device in newLights) {
        if (!mounted) return;
        final bweeLightDevice = BweeLightDevice(
          device.device,
        );
        //获取灯的绑定状态
        final readMsg = await bweeLightDevice.read;
        if (readMsg.networkStatus) {
          //如果灯组网成功,判断panId与channel是否匹配
          if (readMsg.panId == bweeLogic.syncBoxReadMsg.value!.panId &&
              readMsg.channel == bweeLogic.syncBoxReadMsg.value!.channel) {
            //如果匹配,添加到灯列表
            connectedLightList.add(bweeLightDevice);
          }
        }
        bweeLightDevice.disconnect();
      }
    }
    discovered.value = false;
  }

  @override
  void onReady() {
    super.onReady();
    discovered.value = true;
    Future.sync(() async {
      await bweeLogic.init();
      assert(bweeLogic.syncBox.value != null, 'syncBox should not be null');
      //获取蓝牙列表
      final bleDevices = await bweeLogic.controller.getBleDevices(false);
      if (bleDevices.isNotEmpty) {
        //如果有蓝牙设备，获取设备列表
        //循环获取灯
        lightList.assignAll(bleDevices
            .where((e) => e.device.platformName == lightName)
            .map((e) => BweeLightDevice(e.device)));

        for (var device
            in bleDevices.where((e) => e.device.platformName == (lightName))) {
          if (!mounted) return;
          final bweeLightDevice = BweeLightDevice(
            device.device,
          );
          //获取灯的绑定状态
          try {
            final readMsg = await bweeLightDevice.read;
            if (readMsg.networkStatus) {
              //如果灯组网成功,判断panId与channel是否匹配
              if (readMsg.panId == bweeLogic.syncBoxReadMsg.value!.panId &&
                  readMsg.channel == bweeLogic.syncBoxReadMsg.value!.channel) {
                //如果匹配,添加到灯列表
                connectedLightList.add(bweeLightDevice);
              }
            }
          } finally {
            bweeLightDevice.disconnect();
          }
        }
      }
    }).whenComplete(() {
      print('完成');
      discovered.value = false;
    });
  }

  Future<void> removeDevice(BweeLightDevice light) async {
    await light.unbind();
    changedList.remove(light);
    connectedLightList.remove(light);
  }

  Future<void> bindLights() async {
    if (bweeLogic.syncBoxReadMsg.value == null) {
      final msg = await bweeLogic.syncBox.value?.read;
      if (msg != null) {
        bweeLogic.syncBoxReadMsg.value = msg;
      } else {
        return;
      }
    }
    //备份一个已连接的灯列表
    final connectedLights = connectedLightList.toList();
    //循环处理需要处理的灯
    for (var light in changedList.toList()) {
      if (!mounted) return;
      //判断这个灯需要做的处理
      if (connectedLights.any((e) => e == light)) {
        //如果这个灯已经连接上了,则需要解绑
        await light.unbind();
        changedList.remove(light);
        connectedLightList.remove(light);
      } else {
        //如果这个灯没有连接上,则需要绑定
        final bweeLightDevice = BweeLightDevice(light.device);
        final bindResult = await bweeLightDevice.bind(
          bweeLogic.syncBoxReadMsg.value!.panId,
          bweeLogic.syncBoxReadMsg.value!.channel,
        );
        if (bindResult) {
          //如果绑定成功,则添加到已连接的灯列表
          connectedLightList.add(bweeLightDevice);
          changedList.remove(light);
        } else {
          //如果绑定失败,则提示用户
          log.logError(
            'Failed to bind light: ${light.device.platformName}',
          );
          ToastUtils.showToast(
            msg: 'Failed to bind light: ${light.device.platformName}',
          );
          return;
        }
      }
    }
    ToastUtils.showToast(
      msg: '操作完成',
    );
  }

  @override
  void onClose() {
    super.onClose();
    //清理资源
    for (var light in lightList) {
      if (light.device.isConnected) {
        light.disconnect();
      }
    }
  }
}

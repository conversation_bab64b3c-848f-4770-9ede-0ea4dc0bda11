import 'package:flutter/material.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:rk_package/rk_package.dart';

import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart';

import '../../common/lamp_color_picker.dart';
import '../../configs/local.dart';
import 'logic.dart';

@getXRoute
class ColorCustomizationView extends GetView<ColorCustomizationLogic> {
  const ColorCustomizationView({required this.deviceIP, super.key});

  final String deviceIP;
  static const _unselectColor = Color.fromRGBO(244, 244, 244, 1);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GoAppBar(
        title: AxText(
          text: Local.bwee.color_customization,
          fontSize: 22.sp,
          color: Color.fromRGBO(18, 18, 18, 1),
          fontFamily: AxTextType.bold,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 14.w,
          vertical: 16.w
        ),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: AxText(
                text: Local.bwee.color_combo,
                fontSize: 18.sp,
                color: Colors.black,
                fontFamily: AxTextType.bold,
              ),
            ).marginOnly(bottom: 10.w),
            Obx(()=> Container(
              height: 32.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(32.w),
                gradient: LinearGradient(
                  colors: [
                    Color.fromRGBO(244, 244, 244, 1),
                    controller.customColors[controller.selectedColorIndex.value]
                  ]
                )
              ),
            )).marginOnly(bottom: 20.w),
            Container(
              alignment: Alignment.centerLeft,
              child: AxText(
                text: Local.bwee.color_palette,
                fontSize: 18.sp,
                color: Colors.black,
                fontFamily: AxTextType.bold,
              ),
            ).marginOnly(bottom: 10.w),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Obx(() {
                return LampColorPicker(
                  width: 320.w,
                  colorValue: controller.customColors[controller.selectedColorIndex.value],
                  onChange: (color) {
                    controller.onColorChange(color);
                    controller.throttledSetColor(color);
                  },
                  onChangeEnd: (value) {
                    controller.throttledSetColor(value);
                  },
                );
              }),
            ).marginOnly(bottom: 20.w),
            _buildColorSection(),
            Expanded(child: SizedBox.shrink()),
            SafeArea(
              top: false,
              bottom: true,
              child: AxButton(
              onTap: () {
                controller.onConfirm();
                Get.back(result: controller.customColors);
              },
              text: Local.bwee.confirm,
            ))
          ],
        ),
      ),
    );
  }

  Widget _buildColorSection() {
    return SizedBox(
      height: 110.w,
      child: Obx(() => ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: controller.customColors.length + 1,
        separatorBuilder: (context, index) {
          return SizedBox(width: 12.w);
        },
        itemBuilder: (context, index) {
          if (index == controller.customColors.length) {
            return _buildCustomizeButton(controller);
          }

          final color = controller.customColors[index];
          final colorName = index == controller.customColors.length
              ? 'New Color'
              : 'Color${index + 1}';

          return _buildColorItem(color, colorName, index); // Blue is selected
        },
      ).addMacScroll()),
    );
  }

  Widget _buildCustomizeButton(ColorCustomizationLogic controller) {
    return GestureDetector(
      onTap: () {
        controller.customColors.add(Colors.white);
        controller.selectedColorIndex.value = controller.customColors.length - 1;
        // Get.toTyped(ColorCustomizationViewTypedRoute(deviceIP: deviceIP)).then((value) {
        //   if (value != null && value is List<Color>) {
        //     controller.customColors.assignAll(value);
        //   }
        // });
      },
      child: Container(
        width: 80.w,
        decoration: BoxDecoration(
          color: Color.fromRGBO(242, 242, 242, 1),
          borderRadius: BorderRadius.circular(15.w),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 54.w,
              height: 54.w,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.add, size: 30.w, color: Colors.grey[600]),
            ),
            SizedBox(height: 8.h),
            AxText(
              text: Local.bwee.customize,
              fontSize: 12.sp,
              color: Colors.black54,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorItem(Color color, String name, int index) {
    return Obx(() {
      final isSelected = controller.selectedColorIndex.value == index;
      return GestureDetector(
        onTap: () {
          controller.selectedColorIndex.value = index;
        },
        child: Container(
          width: 80.w,
          decoration: BoxDecoration(
            color: _unselectColor,
            border: isSelected
                ? Border.all(
                  color: Color.fromRGBO(222, 222, 222, 1), 
                  width: 2.w
                )
                : null,
            borderRadius: BorderRadius.circular(15.w),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                children: [
                  Container(
                    width: 54.w,
                    height: 54.w,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Positioned.fill(
                    child: Center(
                      child: Container(
                        width: 48.w,
                        height: 48.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2.w),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              AxText(
                text: name,
                fontSize: 12.sp,
                color: Colors.black54,
              ),
            ],
          ),
        ),
      );
    });
  }
}

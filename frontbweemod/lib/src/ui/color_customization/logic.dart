import 'package:bwee_interface/bwee_interface.dart';
import 'package:bwee_mod/src/configs/kv_store.dart';
import 'package:flutter/material.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:rk_get/rk_get.dart';

import 'state.dart';

class ColorCustomizationLogic extends GetxController
    with ColorCustomizationState {
  late final String deviceIP;
  @override
  void onInit() {
    super.onInit();
    deviceIP = Get.arguments['deviceIP'] ?? '';
    Future.sync(() async {
      try {
        final colors = await kvStore.getCustomColors();
        if (colors != null && colors.isNotEmpty) {
          customColors.assignAll(colors);
        } else {
          //默认3颜色
          customColors.assignAll([
            Colors.yellow,
            Colors.blue,
            Colors.green,
          ]);
        }
      } catch (e) {
        customColors.assignAll([
          Colors.yellow,
          Colors.blue,
          Colors.green,
        ]);
      }
      selectedColorIndex.value = customColors.indexWhere((color) => color == bweeLogic.color.value).clamp( 0, customColors.length - 1);
    });

    // 在初始化时创建节流函数
    throttledSetColor = Throttle.createWithParam<Color>(
      (color) => bweeLogic.controller.setColorHex(color.toHex()),
      delay: 400,
      leading: false, // 使用 trailing 模式，确保使用最后一次的颜色值
    );
  }

  BweeLogic get bweeLogic => Get.find<BweeLogic>(tag: deviceIP);

  late final void Function(Color) throttledSetColor;

  void onColorChange(Color color) {
    customColors[selectedColorIndex.value] = color;
  }

  void onColorSelect(int index) {
    if (index < 0 || index >= customColors.length) return;
    selectedColorIndex.value = index;
  }

  void onConfirm() {
    final colors = customColors.toList();
    kvStore.setCustomColors(colors);
  }
}

import 'package:bwee_mod/src/configs/package_string.dart';
import 'package:flutter/material.dart';
import 'package:gocomponent/components/svgicon/svgicon.dart';
import 'package:rk_package/rk_package.dart';

import '../configs/assets_gen.dart';

class AddLightButton extends StatelessWidget {
  const AddLightButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 49.w,
      height: 49.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.25),
            blurRadius: 10,
            offset: Offset(0, 4),
          )
        ]
      ),
      child: SvgAssetIcon(
        path: Assets.ASSETS_ICON_ADD_SVG,
        size: 48.w,
        package: BWEEMOD_PACKAGE,
      ),
    );
  }
}
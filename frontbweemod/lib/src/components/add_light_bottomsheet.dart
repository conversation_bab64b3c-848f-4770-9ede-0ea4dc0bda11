import 'package:bwee_mod/src/configs/assets_gen.dart';
import 'package:bwee_mod/src/configs/package_string.dart';
import 'package:bwee_mod/src/ui/add_light/logic.dart';
import 'package:flutter/material.dart';
import 'package:gocomponent/components/axtext/axtext.dart';
import 'package:gocomponent/components/button/axbutton.dart';
import 'package:gocomponent/components/ontap/ontap_animation.dart';
import 'package:gocomponent/components/svgicon/svgicon.dart';
import 'package:gocomponent/theme/theme_manager.dart';
import 'package:rk_package/rk_package.dart';
import 'package:rk_toast/rk_toast.dart';

class AddLightBottomsheet extends StatefulWidget {
  const AddLightBottomsheet({
    super.key,
    required this.controller
  });

  final AddLightLogic controller;

  @override
  State<AddLightBottomsheet> createState() => _AddLightBottomsheetState();
}

class _AddLightBottomsheetState extends State<AddLightBottomsheet> with SingleTickerProviderStateMixin {

  late final AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2)
    )..repeat();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      height: 568.w,
      // alignment: Alignment.center,
      padding: EdgeInsets.symmetric(
        horizontal: 15.w
      ),
      decoration: BoxDecoration(
        color: ThemeManager.to.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(20.w)
      ),
      child: Column(
        children: [
          Flexible(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 25.w,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Obx(()=> AxText(
                        text: widget.controller.discovered.value? 'Discovering ...' : 'Discovered',
                        fontSize: 20.sp,
                        fontFamily: AxTextType.bold,
                        color: ThemeManager.to.colorTo.secondary,
                      )),
                      OnTapAnimation(
                        onTap: () async {
                          print('重置');
                          widget.controller.onRefreshLights();
                        },
                        duration: const Duration(seconds: 5),
                        child: Obx(() {
                          widget.controller.discovered.value 
                            ? _animationController.repeat() 
                            : _animationController.stop();
                          return RotationTransition(
                            turns: _animationController,
                            child: SvgAssetIcon(
                              path: Assets.ASSETS_ICON_REFRESH_SVG,
                              size: 32.w,
                              color: ThemeManager.to.colorTo.secondary,
                              package: BWEEMOD_PACKAGE,
                            ),
                          );
                        }),
                      )
                    ],
                  ),
                ).marginOnly(
                  top: 40.w,
                  bottom: 40.w
                ),
                Flexible(
                  child: SingleChildScrollView(
                    child: Obx(()=> Column(
                      children: [
                        for (var item in widget.controller.lightList)
                          Visibility(
                            visible: !widget.controller.connectedLightList.contains(item),
                            child: OnTapAnimation(
                              onTap: () {
                                print('${widget.controller.changedList}');
                                if (widget.controller.changedList.contains(item)) {
                                  widget.controller.changedList.remove(item);
                                } else {
                                  widget.controller.changedList.add(item);
                                }
                              },
                              child: AnimatedOpacity(
                                duration: const Duration(milliseconds: 150),
                                opacity: widget.controller.changedList.contains(item)? 1 : 0.4,
                                child: Container(
                                  width: Get.width,
                                  height: 80.w,
                                  padding: EdgeInsets.symmetric(horizontal: 22.w),
                                  decoration: BoxDecoration(
                                    color: ThemeManager.to.colorTo.surface,
                                    borderRadius: BorderRadius.circular(15.r)
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      SvgAssetIcon(
                                        path: Assets.ASSETS_ICON_LIGHT_SVG,
                                        size: 38.w,
                                        package: BWEEMOD_PACKAGE,
                                        color: ThemeManager.to.colorTo.primary,
                                      ).marginOnly(right: 22.w),
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            AxText(
                                              text: item.device.platformName,
                                              fontFamily: AxTextType.bold,
                                              fontSize: 18.w,
                                            ),
                                            AxText(
                                              text: 'SN: ${item.device.remoteId.str.substring(0, 8)}',
                                              fontFamily: AxTextType.medium,
                                              fontSize: 16.w,
                                              overflow: TextOverflow.clip,
                                              softWrap: false,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Opacity(
                                        opacity: widget.controller.changedList.contains(item)? 1 : 0.4,
                                        child: SvgAssetIcon(
                                          path: Assets.ASSETS_ICON_CORRECT_SVG,
                                          size: 22.w,
                                          package: BWEEMOD_PACKAGE,
                                          color: ThemeManager.to.colorTo.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ).marginOnly(bottom: 10.w),
                              )
                            ),
                          )
                      ],
                    )),
                  ),
                ),
              ],
            ),
          ),
          
          SafeArea(
            top: false,
            bottom: true,
            child: Obx(() {
              final confirmEnabled = widget.controller.changedList.isNotEmpty;

              return Opacity(
                opacity: confirmEnabled? 1 : 0.4,
                child: AxButton(
                  text: 'Confirm',
                  onTap: () {
                    if (!confirmEnabled) {
                      return;
                    }
                    loadingCallback(() => widget.controller.bindLights()).then((_) => Get.back());
                  }
                ),
              );
            }),
          ).marginOnly(bottom: 50.w)
        ],
      )
    );
  }
}
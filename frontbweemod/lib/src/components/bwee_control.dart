import 'package:bwee_interface/bwee_interface.dart';
import 'package:bwee_mod/src/configs/assets_gen.dart';
import 'package:bwee_mod/src/configs/intensity_convert_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:rk_package/rk_package.dart';
import 'package:tl_getx_router_gen_annotations/navigator.dart';

import '../configs/local.dart';
import '../routes/route_config.get_x_router_config.dart';
import '../ui/light_settings/light_settings.dart';

class BweeControl extends StatefulWidget {
  const BweeControl({required this.deviceIP, super.key});

  final String deviceIP;

  @override
  State<BweeControl> createState() => _BweeControlState();
}

class _BweeControlState extends State<BweeControl> {

  late final LightSettingsLogic controller;

  @override
  void initState() {
    super.initState();

    // 初始化逻辑
    controller = Get.put(
      LightSettingsLogic.fromComponent(
        deviceIP: widget.deviceIP,
      ),
      tag: 'component_${widget.deviceIP}',
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.onReady();
    });
  }

  @override
  void dispose() {
    // 清理逻辑
    Get.delete<LightSettingsLogic>(tag: 'component_${widget.deviceIP}');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BweeLogic>(
        init: BweeLogic(deviceIP: widget.deviceIP),
        tag: widget.deviceIP,
        builder: (_) => ListView(
          scrollDirection: Axis.horizontal, 
          children: [
              _buildItem(
                Assets.ASSETS_ICON_POWER_SVG,
                Local.bwee.power,
                () {
                  controller.togglePower();
                },
                iconColor: controller.powerOn.value? 
                    Colors.white
                    : const Color.fromRGBO(15, 15, 15, 0.5),
              ),
              SizedBox(
                width: 21.w,
              ),
              _buildItem(
                Assets.ASSETS_ICON_MODE_SVG,
                Local.bwee.mode,
                () {
                  controller.toggleSyncMode();
                },
                iconColor: Colors.white
              ),
              SizedBox(
                width: 21.w,
              ),
              _buildItem(
                Assets.ASSETS_ICON_COLOR_SVG,
                Local.bwee.color,
                () async {
                  Get.toTyped(
                    ColorCustomizationViewTypedRoute(deviceIP: widget.deviceIP)
                  );
                },
                iconColor: Colors.white,
              ),
              SizedBox(
                width: 21.w,
              ),
              Obx(()=> _buildItem(
                controller.syncStrength.value.toIcon,
                Local.bwee.intensity,
                () {
                  controller.toggleIntensity();
                },
                fontSize: 10,
                iconColor:  Colors.white
              )),
              SizedBox(
                width: 21.w,
              ),
              _buildItem(
                Assets.ASSETS_ICON_SETTING_SVG,
                Local.bwee.setting,
                () {
                  Get.toTyped(
                    LightSettingsViewTypedRoute(deviceIP: widget.deviceIP),
                  );
                },
              ),
            ]));
  }

  Widget _buildItem(
    String iconPath, 
    String name,
    VoidCallback onTap, {
      Color? iconColor,
      int fontSize = 11
  }) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: 48.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            //icon
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: Colors.white.toOpacity(0.3),
                borderRadius: BorderRadius.circular(12.r),
              ),
              alignment: Alignment.center,
              child: SvgPicture.asset(
                iconPath,
                package: 'bwee_mod',
                colorFilter: iconColor != null
                    ? ColorFilter.mode(iconColor, BlendMode.srcIn)
                    : null,
                width: 32.w,
                height: 32.w,
              ),
            ),
            AxText(
              text: name,
              fontSize: fontSize.sp,
              color: Colors.white,
              fontFamily: AxTextType.bold,
            ).marginOnly(top: 5.w),
          ],
        ),
      ),
    );
  }
}

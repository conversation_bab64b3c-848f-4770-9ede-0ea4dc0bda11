
import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart' as rk_package;

/// 颜色温度滑块组件
///
/// 提供颜色温度调节功能，支持2700K-6500K范围调节
/// 具有渐变背景和实时数值显示
class BweeSlider extends StatelessWidget {
  /// 当前温度值（2700K-6500K）
  final double value;

  /// 最小温度值
  final double minValue;

  /// 最大温度值
  final double maxValue;

  /// 值变化回调（实时回调）
  final ValueChanged<double>? onChange;

  /// 拖拽结束回调
  final ValueChanged<double>? onChangeEnd;

  /// 滑块宽度
  final double width;

  /// 滑块轨道高度
  final double? trackHeight;

  const BweeSlider({
    super.key,
    required this.value,
    this.minValue = 2700,
    this.maxValue = 6500,
    this.onChange,
    this.onChangeEnd,
    this.width = 300,
    this.trackHeight,
  });

  @override
  Widget build(BuildContext context) {
    final double thumbRadius = switch(trackHeight) {
      double() => trackHeight! > 3.w ? (trackHeight! / 2) - 3.w : 12.w,
      null => (32.w / 2) - 3.w,
    };

    return Container(
      width: width,
      height: 32.w,
      padding: EdgeInsets.symmetric(horizontal: thumbRadius), // 关键：添加padding
      child: SliderTheme(
        data: SliderThemeData(
          trackHeight: trackHeight ?? 32.w,
          thumbShape: _BweeSliderThumbShape(
            radius: thumbRadius,
            borderWidth: 3.w,
          ),
          overlayShape: SliderComponentShape.noOverlay,
          trackShape: _BweeSliderTrackShape(),
          activeTrackColor: Colors.transparent, // 激活轨道透明，使用自定义trackShape
          inactiveTrackColor: Colors.transparent, // 未激活轨道透明，使用自定义trackShape
          thumbColor: Colors.transparent,
        ),
        child: Slider(
          value: value.clamp(minValue, maxValue),
          min: minValue,
          max: maxValue,
          onChanged: onChange,
          onChangeEnd: onChangeEnd,
        ),
      ),
    );
  }
}

/// 自定义滑块轨道形状 - 渐变背景
class _BweeSliderTrackShape extends SliderTrackShape {
  const _BweeSliderTrackShape();

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight!;
    final double trackLeft = offset.dx;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    // 创建渐变画笔 - 激活和未激活都使用相同的渐变色
    final Paint paint = Paint()
      ..shader = LinearGradient(
        colors: [
          Color.fromRGBO(255, 169, 30, 1), // 橙色
          Color.fromRGBO(255, 221, 89, 1), // 黄色
          Color.fromRGBO(255, 255, 200, 1), // 浅黄色
        ],
        stops: [0.0, 0.25, 1.0],
      ).createShader(trackRect);

    // 绘制圆角矩形轨道
    final RRect trackRRect = RRect.fromRectAndRadius(
      trackRect,
      Radius.circular(trackRect.height / 2),
    );

    context.canvas.drawRRect(trackRRect, paint);
  }
}

/// 自定义滑块指示器形状 - 环形小圆
class _BweeSliderThumbShape extends SliderComponentShape {
  final double radius;
  final double borderWidth;

  const _BweeSliderThumbShape({
    required this.radius,
    required this.borderWidth,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(radius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    // 绘制白色边框（环形小圆）
    final Paint borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    canvas.drawCircle(center, radius, borderPaint);
  }
}

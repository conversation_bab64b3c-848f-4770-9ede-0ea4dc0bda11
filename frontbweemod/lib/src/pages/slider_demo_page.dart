import 'package:flutter/material.dart';
import '../components/slider.dart';

/// 颜色温度滑块演示页面
class SliderDemoPage extends StatefulWidget {
  const SliderDemoPage({super.key});

  @override
  State<SliderDemoPage> createState() => _SliderDemoPageState();
}

class _SliderDemoPageState extends State<SliderDemoPage> {
  double _colorTemperature = 3500.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text('Color Temperature Slider'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 40),
            
            // 主要的颜色温度滑块
            BweeSlider(
              value: _colorTemperature,
              minValue: 2700,
              maxValue: 6500,
              width: MediaQuery.of(context).size.width - 48,
              onChange: (value) {
                setState(() {
                  _colorTemperature = value;
                });
              },
              onChangeEnd: (value) {
                print('Color temperature changed to: ${value.round()}K');
              },
            ),
            
            SizedBox(height: 60),
            
            // 显示当前颜色效果的预览区域
            _buildColorPreview(),
            
            SizedBox(height: 40),
            
            // 其他配置选项
            _buildConfigOptions(),
          ],
        ),
      ),
    );
  }

  /// 构建颜色预览区域
  Widget _buildColorPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Color Preview',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 12),
        Container(
          width: double.infinity,
          height: 100,
          decoration: BoxDecoration(
            color: _getColorFromTemperature(_colorTemperature),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Center(
            child: Text(
              '${_colorTemperature.round()}K',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: _colorTemperature > 4000 ? Colors.black : Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建配置选项
  Widget _buildConfigOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Temperature Info',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 12),
        _buildInfoCard('Current', '${_colorTemperature.round()}K'),
        _buildInfoCard('Range', '2700K - 6500K'),
        _buildInfoCard('Type', _getTemperatureType(_colorTemperature)),
      ],
    );
  }

  /// 构建信息卡片
  Widget _buildInfoCard(String label, String value) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  /// 根据色温获取对应颜色
  Color _getColorFromTemperature(double temperature) {
    // 简化的色温到颜色映射
    if (temperature <= 3000) {
      return Color(0xFFFF8C42); // 暖橙色
    } else if (temperature <= 3500) {
      return Color(0xFFFFB366); // 橙黄色
    } else if (temperature <= 4500) {
      return Color(0xFFFFD700); // 金黄色
    } else if (temperature <= 5500) {
      return Color(0xFFFFF8DC); // 米白色
    } else {
      return Color(0xFFFFFFFF); // 纯白色
    }
  }

  /// 获取色温类型描述
  String _getTemperatureType(double temperature) {
    if (temperature <= 3000) {
      return 'Warm Light';
    } else if (temperature <= 4000) {
      return 'Soft White';
    } else if (temperature <= 5000) {
      return 'Neutral White';
    } else {
      return 'Cool White';
    }
  }
}

# 智能家居3D控制中心

一个基于Vue3 + Three.js的智能家居3D控制演示项目，支持在3D场景中可视化控制各种智能设备。

## 🚀 功能特性

- **3D场景展示**: 使用Three.js渲染3D房间场景
- **设备可视化**: 空调、风扇、灯光、窗帘等设备的3D模型展示
- **交互控制**: 点击3D设备进行控制操作
- **实时状态**: 设备状态实时同步到3D场景
- **控制面板**: 详细的设备控制界面
- **设备列表**: 分类显示所有设备及其状态
- **响应式设计**: 支持桌面和移动端

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **状态管理**: Pinia
- **3D渲染**: Three.js
- **UI组件**: Element Plus
- **图标**: Element Plus Icons

## 📦 项目结构

```
src/
├── components/          # 组件目录
│   ├── Scene3D.vue     # 3D场景组件
│   ├── DeviceList.vue  # 设备列表组件
│   └── DeviceControlPanel.vue # 设备控制面板
├── stores/             # Pinia状态管理
│   └── devices.js      # 设备状态管理
├── App.vue            # 主应用组件
├── main.js            # 应用入口
└── style.css          # 全局样式
```

## 🎮 支持的设备类型

### 空调 (Air Conditioner)
- 开关控制
- 温度调节 (16-30°C)
- 模式切换 (制冷/制热/送风/除湿)

### 电风扇 (Fan)
- 开关控制
- 风速调节 (1-3档)
- 旋转动画效果

### 灯光 (Light)
- 开关控制
- 亮度调节 (0-100%)
- 发光效果

### 窗帘 (Curtain)
- 开关控制
- 开合程度调节 (0-100%)
- 快捷操作按钮

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🎯 使用说明

1. **3D场景交互**: 在3D场景中点击任意设备即可打开控制面板
2. **设备列表**: 左侧设备列表显示所有设备，可快速开关或点击进入详细控制
3. **控制面板**: 右侧抽屉式控制面板提供详细的设备控制选项
4. **快捷操作**: 设备列表底部提供批量控制功能

## 🔧 自定义配置

### 添加新设备
在 `src/stores/devices.js` 中的 `devices` 数组添加新设备：

```javascript
{
  id: 'device-id',
  name: '设备名称',
  type: 'device-type',
  status: 'off',
  position: { x: 0, y: 0, z: 0 },
  color: '#409EFF'
  // 其他设备特定属性...
}
```

### 修改3D场景
在 `src/components/Scene3D.vue` 中的 `createDeviceMesh` 方法添加新设备类型的3D模型。

## 📱 响应式支持

项目支持移动端访问，在小屏幕设备上会自动调整布局：
- 侧边栏变为顶部布局
- 3D场景高度自适应
- 隐藏部分统计信息

## 🎨 主题定制

可以通过修改 `src/style.css` 中的CSS变量来自定义主题颜色和样式。

## 📄 许可证

MIT License

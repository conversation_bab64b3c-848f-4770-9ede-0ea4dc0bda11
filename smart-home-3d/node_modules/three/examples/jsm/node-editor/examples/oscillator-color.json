{"objects": {"247": {"x": 1602, "y": -3, "width": 300, "elements": [248, 250, 251, 252, 253], "id": 247, "type": "StandardMaterialEditor"}, "248": {"outputLength": 1, "style": "blue", "title": "Standard Material", "id": 248, "type": "TitleElement"}, "250": {"inputLength": 3, "inputs": [254], "links": [293], "label": "Color", "id": 250, "type": "LabelElement"}, "251": {"inputLength": 1, "inputs": [255], "label": "Opacity", "icon": "ti ti-layers-subtract", "id": 251, "type": "LabelElement"}, "252": {"inputLength": 1, "inputs": [257], "label": "Metalness", "id": 252, "type": "LabelElement"}, "253": {"inputLength": 1, "inputs": [259], "label": "Roughness", "id": 253, "type": "LabelElement"}, "254": {"value": 16777215, "id": 254, "type": "ColorInput"}, "255": {"min": 0, "max": 1, "value": 1, "id": 255, "type": "SliderInput"}, "257": {"min": 0, "max": 1, "value": 0, "id": 257, "type": "SliderInput"}, "259": {"min": 0, "max": 1, "value": 1, "id": 259, "type": "SliderInput"}, "266": {"x": 123, "y": 207, "width": 250, "elements": [267, 273, 274, 271], "id": 266, "type": "Timer<PERSON><PERSON>or"}, "267": {"outputLength": 1, "title": "Timer", "icon": "ti ti-clock", "id": 267, "type": "TitleElement"}, "269": {"value": 1.985, "id": 269, "type": "NumberInput"}, "270": {"value": 0.37, "id": 270, "type": "NumberInput"}, "271": {"inputs": [272], "id": 271, "type": "Element"}, "272": {"value": "Reset", "id": 272, "type": "ButtonInput"}, "273": {"inputs": [269], "id": 273, "type": "Element"}, "274": {"inputs": [270], "label": "Scale", "id": 274, "type": "LabelElement"}, "282": {"x": 485, "y": 94, "width": 250, "elements": [283, 287, 286], "id": 282, "type": "OscillatorEditor"}, "283": {"outputLength": 1, "title": "Oscillator", "id": 283, "type": "TitleElement"}, "285": {"options": [{"name": "<PERSON><PERSON>", "value": "sine"}, {"name": "Square", "value": "square"}, {"name": "Triangle", "value": "triangle"}, {"name": "Sawtooth", "value": "sawtooth"}], "value": "sine", "id": 285, "type": "SelectInput"}, "286": {"inputLength": 1, "links": [267], "label": "Time", "id": 286, "type": "LabelElement"}, "287": {"inputs": [285], "id": 287, "type": "Element"}, "292": {"x": 1208, "y": 66, "width": 200, "elements": [293, 295, 296, 297], "id": 292, "type": "BlendEditor"}, "293": {"outputLength": 3, "title": "Blend", "id": 293, "type": "TitleElement"}, "295": {"inputLength": 3, "links": [303], "label": "Base", "id": 295, "type": "LabelElement"}, "296": {"inputLength": 3, "links": [323], "label": "Blend", "id": 296, "type": "LabelElement"}, "297": {"inputLength": 1, "links": [283], "label": "Opacity", "id": 297, "type": "LabelElement"}, "302": {"x": 797, "y": -55, "width": 300, "elements": [303, 310, 311, 312], "id": 302, "type": "ColorEditor"}, "303": {"outputLength": 1, "title": "Color", "icon": "ti ti-palette", "id": 303, "type": "TitleElement"}, "305": {"value": 16580865, "id": 305, "type": "ColorInput"}, "306": {"value": "#FD0101", "id": 306, "type": "StringInput"}, "307": {"min": 0, "max": 1, "step": 0.01, "value": 0.996, "id": 307, "type": "NumberInput"}, "308": {"min": 0, "max": 1, "step": 0.01, "value": 0.004, "id": 308, "type": "NumberInput"}, "309": {"min": 0, "max": 1, "step": 0.01, "value": 0.004, "id": 309, "type": "NumberInput"}, "310": {"inputs": [305], "id": 310, "type": "Element"}, "311": {"inputs": [306], "label": "Hex", "id": 311, "type": "LabelElement"}, "312": {"inputs": [307, 308, 309], "label": "RGB", "id": 312, "type": "LabelElement"}, "322": {"x": 810, "y": 220, "width": 300, "elements": [323, 330, 331, 332], "id": 322, "type": "ColorEditor"}, "323": {"outputLength": 1, "title": "Color", "icon": "ti ti-palette", "id": 323, "type": "TitleElement"}, "325": {"value": 19455, "id": 325, "type": "ColorInput"}, "326": {"value": "#004BFF", "id": 326, "type": "StringInput"}, "327": {"min": 0, "max": 1, "step": 0.01, "value": 0, "id": 327, "type": "NumberInput"}, "328": {"min": 0, "max": 1, "step": 0.01, "value": 0.298, "id": 328, "type": "NumberInput"}, "329": {"min": 0, "max": 1, "step": 0.01, "value": 1, "id": 329, "type": "NumberInput"}, "330": {"inputs": [325], "id": 330, "type": "Element"}, "331": {"inputs": [326], "label": "Hex", "id": 331, "type": "LabelElement"}, "332": {"inputs": [327, 328, 329], "label": "RGB", "id": 332, "type": "LabelElement"}, "371": {"x": 2064, "y": -17, "width": 300, "elements": [372, 375, 385, 386, 387, 388], "id": 371, "type": "MeshEditor"}, "372": {"outputLength": 1, "title": "<PERSON><PERSON>", "id": 372, "type": "TitleElement"}, "374": {"value": "<PERSON><PERSON><PERSON>", "id": 374, "type": "StringInput"}, "375": {"inputs": [374], "label": "Name", "id": 375, "type": "LabelElement"}, "376": {"value": 0, "id": 376, "type": "NumberInput"}, "377": {"value": 0, "id": 377, "type": "NumberInput"}, "378": {"value": 10, "id": 378, "type": "NumberInput"}, "379": {"value": 0, "id": 379, "type": "NumberInput"}, "380": {"value": 0, "id": 380, "type": "NumberInput"}, "381": {"value": 0, "id": 381, "type": "NumberInput"}, "382": {"value": 100, "id": 382, "type": "NumberInput"}, "383": {"value": 100, "id": 383, "type": "NumberInput"}, "384": {"value": 100, "id": 384, "type": "NumberInput"}, "385": {"inputs": [376, 377, 378], "label": "Position", "id": 385, "type": "LabelElement"}, "386": {"inputs": [379, 380, 381], "label": "Rotation", "id": 386, "type": "LabelElement"}, "387": {"inputs": [382, 383, 384], "label": "Scale", "id": 387, "type": "LabelElement"}, "388": {"inputLength": 1, "links": [248], "label": "Material", "id": 388, "type": "LabelElement"}}, "nodes": [247, 266, 282, 292, 302, 322, 371], "id": 2, "type": "<PERSON><PERSON>"}
import CondNode from '../math/CondNode.js';
import { expression } from '../core/ExpressionNode.js';
import { addNodeClass } from '../core/Node.js';
import { addNodeElement, nodeProxy } from '../shadernode/ShaderNode.js';

let discardExpression;

class DiscardNode extends CondNode {

	constructor( condNode ) {

		discardExpression = discardExpression || expression( 'discard' );

		super( condNode, discardExpression );

	}

}

export default DiscardNode;

export const discard = nodeProxy( DiscardNode );

addNodeElement( 'discard', discard );

addNodeClass( DiscardNode );

{"hash": "ae647f89", "configHash": "e76c0f5b", "lockfileHash": "46d9ea8a", "browserHash": "6062e3b7", "optimized": {"@element-plus/icons-vue": {"src": "../../@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "ba2f699d", "needsInterop": false}, "element-plus": {"src": "../../element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "6f8d3d11", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "a239d29a", "needsInterop": false}, "three": {"src": "../../three/build/three.module.js", "file": "three.js", "fileHash": "3498de01", "needsInterop": false}, "three/examples/jsm/controls/OrbitControls.js": {"src": "../../three/examples/jsm/controls/OrbitControls.js", "file": "three_examples_jsm_controls_OrbitControls__js.js", "fileHash": "a8f598ee", "needsInterop": false}, "three/examples/jsm/loaders/GLTFLoader.js": {"src": "../../three/examples/jsm/loaders/GLTFLoader.js", "file": "three_examples_jsm_loaders_GLTFLoader__js.js", "fileHash": "21809275", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "84f81a94", "needsInterop": false}}, "chunks": {"chunk-M7SMPNQH": {"file": "chunk-M7SMPNQH.js"}, "chunk-HX5C5AN6": {"file": "chunk-HX5C5AN6.js"}, "chunk-QNYFAHYS": {"file": "chunk-QNYFAHYS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}
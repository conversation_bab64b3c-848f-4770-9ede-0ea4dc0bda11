{"hash": "5fe441e1", "configHash": "e76c0f5b", "lockfileHash": "a905892c", "browserHash": "f1d7db86", "optimized": {"@element-plus/icons-vue": {"src": "../../@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "c002ce2f", "needsInterop": false}, "element-plus": {"src": "../../element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "4c437ca5", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "567753cb", "needsInterop": false}, "three": {"src": "../../three/build/three.module.js", "file": "three.js", "fileHash": "3df4354a", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "3c124d6d", "needsInterop": false}, "three/examples/jsm/loaders/GLTFLoader.js": {"src": "../../three/examples/jsm/loaders/GLTFLoader.js", "file": "three_examples_jsm_loaders_GLTFLoader__js.js", "fileHash": "01a1e5d5", "needsInterop": false}, "three/examples/jsm/controls/OrbitControls.js": {"src": "../../three/examples/jsm/controls/OrbitControls.js", "file": "three_examples_jsm_controls_OrbitControls__js.js", "fileHash": "e3a03924", "needsInterop": false}}, "chunks": {"chunk-M7SMPNQH": {"file": "chunk-M7SMPNQH.js"}, "chunk-HX5C5AN6": {"file": "chunk-HX5C5AN6.js"}, "chunk-QUPN4MTR": {"file": "chunk-QUPN4MTR.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}
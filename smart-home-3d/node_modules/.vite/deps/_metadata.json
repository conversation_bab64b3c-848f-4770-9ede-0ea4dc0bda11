{"hash": "2e056cae", "configHash": "e76c0f5b", "lockfileHash": "635f18d2", "browserHash": "c6538e9d", "optimized": {"@element-plus/icons-vue": {"src": "../../@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "d5966549", "needsInterop": false}, "element-plus": {"src": "../../element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "dbab04d8", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "1b72bb9d", "needsInterop": false}, "three": {"src": "../../three/build/three.module.js", "file": "three.js", "fileHash": "7111a09d", "needsInterop": false}, "three/examples/jsm/controls/OrbitControls.js": {"src": "../../three/examples/jsm/controls/OrbitControls.js", "file": "three_examples_jsm_controls_OrbitControls__js.js", "fileHash": "898595d2", "needsInterop": false}, "three/examples/jsm/loaders/GLTFLoader.js": {"src": "../../three/examples/jsm/loaders/GLTFLoader.js", "file": "three_examples_jsm_loaders_GLTFLoader__js.js", "fileHash": "a06d6872", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "61fe45ae", "needsInterop": false}}, "chunks": {"chunk-M7SMPNQH": {"file": "chunk-M7SMPNQH.js"}, "chunk-HX5C5AN6": {"file": "chunk-HX5C5AN6.js"}, "chunk-BDPJJYMX": {"file": "chunk-BDPJJYMX.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}
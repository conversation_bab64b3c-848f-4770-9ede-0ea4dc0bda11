{"version": 3, "file": "ReflectorRTT.js", "sources": ["../../src/objects/ReflectorRTT.ts"], "sourcesContent": ["import { BufferGeometry } from 'three'\nimport { Reflector, ReflectorOptions } from '../objects/Reflector'\n\nclass ReflectorRTT extends Reflector {\n  constructor(geometry?: BufferGeometry, options?: ReflectorOptions) {\n    super(geometry, options)\n    this.geometry.setDrawRange(0, 0)\n  }\n}\n\nexport { ReflectorRTT }\n"], "names": [], "mappings": ";AAGA,MAAM,qBAAqB,UAAU;AAAA,EACnC,YAAY,UAA2B,SAA4B;AACjE,UAAM,UAAU,OAAO;AAClB,SAAA,SAAS,aAAa,GAAG,CAAC;AAAA,EACjC;AACF;"}
{"version": 3, "file": "AfterimageShader.cjs", "sources": ["../../src/shaders/AfterimageShader.ts"], "sourcesContent": ["/**\n * Afterimage shader\n * I created this effect inspired by a demo on codepen:\n * https://codepen.io/brunoimbrizi/pen/MoRJaN?page=1&\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type AfterimageShaderUniforms = {\n  damp: IUniform<number>\n  tNew: IUniform<Texture | null>\n  tOld: IUniform<Texture | null>\n}\n\nexport interface IAfterimageShader extends IShader<AfterimageShaderUniforms> {}\n\nexport const AfterimageShader: IAfterimageShader = {\n  uniforms: {\n    damp: { value: 0.96 },\n    tOld: { value: null },\n    tNew: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float damp;\n\n    uniform sampler2D tOld;\n    uniform sampler2D tNew;\n\n    varying vec2 vUv;\n\n    vec4 when_gt( vec4 x, float y ) {\n\n    \treturn max( sign( x - y ), 0.0 );\n\n    }\n\n    void main() {\n\n    \tvec4 texelOld = texture2D( tOld, vUv );\n    \tvec4 texelNew = texture2D( tNew, vUv );\n\n    \ttexelOld *= damp * when_gt( texelOld, 0.1 );\n\n    \tgl_FragColor = max(texelNew, texelOld);\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAiBO,MAAM,mBAAsC;AAAA,EACjD,UAAU;AAAA,IACR,MAAM,EAAE,OAAO,KAAK;AAAA,IACpB,MAAM,EAAE,OAAO,KAAK;AAAA,IACpB,MAAM,EAAE,OAAO,KAAK;AAAA,EACtB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyB7B;;"}
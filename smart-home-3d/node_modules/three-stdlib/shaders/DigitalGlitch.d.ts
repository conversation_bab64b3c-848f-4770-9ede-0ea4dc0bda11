export declare const DigitalGlitch: {
    uniforms: {
        tDiffuse: {
            value: null;
        };
        tDisp: {
            value: null;
        };
        byp: {
            value: number;
        };
        amount: {
            value: number;
        };
        angle: {
            value: number;
        };
        seed: {
            value: number;
        };
        seed_x: {
            value: number;
        };
        seed_y: {
            value: number;
        };
        distortion_x: {
            value: number;
        };
        distortion_y: {
            value: number;
        };
        col_s: {
            value: number;
        };
    };
    vertexShader: string;
    fragmentShader: string;
};

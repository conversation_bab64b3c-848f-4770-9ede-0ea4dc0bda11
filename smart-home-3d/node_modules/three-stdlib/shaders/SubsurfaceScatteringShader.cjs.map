{"version": 3, "file": "SubsurfaceScatteringShader.cjs", "sources": ["../../src/shaders/SubsurfaceScatteringShader.ts"], "sourcesContent": ["import { Color, ShaderChunk, ShaderLib, UniformsUtils } from 'three'\n\n/**\n * ------------------------------------------------------------------------------------------\n * Subsurface Scattering shader\n * Based on GDC 2011 – Approximating Translucency for a Fast, Cheap and Convincing Subsurface Scattering Look\n * https://colinbarrebrisebois.com/2011/03/07/gdc-2011-approximating-translucency-for-a-fast-cheap-and-convincing-subsurface-scattering-look/\n *------------------------------------------------------------------------------------------\n */\n\nlet _SubsurfaceScatteringShader: any\n\nfunction get() {\n  if (_SubsurfaceScatteringShader) return _SubsurfaceScatteringShader\n\n  const meshphong_frag_head = ShaderChunk['meshphong_frag'].slice(\n    0,\n    ShaderChunk['meshphong_frag'].indexOf('void main() {'),\n  )\n  const meshphong_frag_body = ShaderChunk['meshphong_frag'].slice(\n    ShaderChunk['meshphong_frag'].indexOf('void main() {'),\n  )\n\n  _SubsurfaceScatteringShader = {\n    uniforms: UniformsUtils.merge([\n      ShaderLib['phong'].uniforms,\n      {\n        thicknessMap: { value: null },\n        thicknessColor: { value: new Color(0xffffff) },\n        thicknessDistortion: { value: 0.1 },\n        thicknessAmbient: { value: 0.0 },\n        thicknessAttenuation: { value: 0.1 },\n        thicknessPower: { value: 2.0 },\n        thicknessScale: { value: 10.0 },\n      },\n    ]),\n\n    vertexShader: /* glsl */ `\n    #define USE_UV\n    ${ShaderChunk['meshphong_vert']}\n  `,\n    fragmentShader: /* glsl */ `\n    #define USE_UV',\n    #define SUBSURFACE',\n\n    ${meshphong_frag_head}\n\n    uniform sampler2D thicknessMap;\n    uniform float thicknessPower;\n    uniform float thicknessScale;\n    uniform float thicknessDistortion;\n    uniform float thicknessAmbient;\n    uniform float thicknessAttenuation;\n    uniform vec3 thicknessColor;\n\n    void RE_Direct_Scattering(const in IncidentLight directLight, const in vec2 uv, const in GeometricContext geometry, inout ReflectedLight reflectedLight) {\n    \tvec3 thickness = thicknessColor * texture2D(thicknessMap, uv).r;\n    \tvec3 scatteringHalf = normalize(directLight.direction + (geometry.normal * thicknessDistortion));\n    \tfloat scatteringDot = pow(saturate(dot(geometry.viewDir, -scatteringHalf)), thicknessPower) * thicknessScale;\n    \tvec3 scatteringIllu = (scatteringDot + thicknessAmbient) * thickness;\n    \treflectedLight.directDiffuse += scatteringIllu * thicknessAttenuation * directLight.color;\n    }\n\n    ${meshphong_frag_body.replace(\n      '#include <lights_fragment_begin>',\n      ShaderChunk['lights_fragment_begin'].replace(\n        /RE_Direct\\( directLight, geometry, material, reflectedLight \\);/g,\n        /* glsl */ `\n        RE_Direct( directLight, geometry, material, reflectedLight );\n\n        #if defined( SUBSURFACE ) && defined( USE_UV )\n          RE_Direct_Scattering(directLight, vUv, geometry, reflectedLight);\n        #endif\n      `,\n      ),\n    )}\n  `,\n  }\n\n  return _SubsurfaceScatteringShader\n}\n\nexport const SubsurfaceScatteringShader = {\n  get uniforms() {\n    return get().uniforms\n  },\n  set uniforms(value) {\n    get().uniforms = value\n  },\n  get vertexShader() {\n    return get().vertexShader\n  },\n  set vertexShader(value) {\n    get().vertexShader = value\n  },\n  get fragmentShader() {\n    return get().vertexShader\n  },\n  set fragmentShader(value) {\n    get().vertexShader = value\n  },\n}\n"], "names": ["ShaderChunk", "UniformsUtils", "ShaderLib", "Color"], "mappings": ";;;AAUA,IAAI;AAEJ,SAAS,MAAM;AACT,MAAA;AAAoC,WAAA;AAElC,QAAA,sBAAsBA,MAAAA,YAAY,gBAAgB,EAAE;AAAA,IACxD;AAAA,IACAA,MAAAA,YAAY,gBAAgB,EAAE,QAAQ,eAAe;AAAA,EAAA;AAEjD,QAAA,sBAAsBA,MAAAA,YAAY,gBAAgB,EAAE;AAAA,IACxDA,MAAAA,YAAY,gBAAgB,EAAE,QAAQ,eAAe;AAAA,EAAA;AAGzB,gCAAA;AAAA,IAC5B,UAAUC,oBAAc,MAAM;AAAA,MAC5BC,MAAA,UAAU,OAAO,EAAE;AAAA,MACnB;AAAA,QACE,cAAc,EAAE,OAAO,KAAK;AAAA,QAC5B,gBAAgB,EAAE,OAAO,IAAIC,MAAA,MAAM,QAAQ,EAAE;AAAA,QAC7C,qBAAqB,EAAE,OAAO,IAAI;AAAA,QAClC,kBAAkB,EAAE,OAAO,EAAI;AAAA,QAC/B,sBAAsB,EAAE,OAAO,IAAI;AAAA,QACnC,gBAAgB,EAAE,OAAO,EAAI;AAAA,QAC7B,gBAAgB,EAAE,OAAO,GAAK;AAAA,MAChC;AAAA,IAAA,CACD;AAAA,IAED;AAAA;AAAA,MAAyB;AAAA;AAAA,MAEvBH,MAAAA,YAAY,gBAAgB;AAAA;AAAA;AAAA,IAE9B;AAAA;AAAA,MAA2B;AAAA;AAAA;AAAA;AAAA,MAIzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBA,oBAAoB;AAAA,QACpB;AAAA,QACAA,MAAA,YAAY,uBAAuB,EAAE;AAAA,UACnC;AAAA;AAAA,UACW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOb;AAAA,MAAA;AAAA;AAAA;AAAA,EACF;AAIK,SAAA;AACT;AAEO,MAAM,6BAA6B;AAAA,EACxC,IAAI,WAAW;AACb,WAAO,IAAM,EAAA;AAAA,EACf;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,UAAM,WAAW;AAAA,EACnB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,IAAM,EAAA;AAAA,EACf;AAAA,EACA,IAAI,aAAa,OAAO;AACtB,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,IAAM,EAAA;AAAA,EACf;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,UAAM,eAAe;AAAA,EACvB;AACF;;"}
/**
 * Technicolor Shader
 * Simulates the look of the two-strip technicolor process popular in early 20th century films.
 * More historical info here: http://www.widescreenmuseum.com/oldcolor/technicolor1.htm
 * Demo here: http://charliehoey.com/technicolor_shader/shader_test.html
 */
export declare const TechnicolorShader: {
    uniforms: {
        tDiffuse: {
            value: null;
        };
    };
    vertexShader: string;
    fragmentShader: string;
};

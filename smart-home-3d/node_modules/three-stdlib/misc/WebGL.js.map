{"version": 3, "file": "WebGL.js", "sources": ["../../src/misc/WebGL.ts"], "sourcesContent": ["let webGLAvailable: boolean, webGL2Available: boolean\n\nexport function isWebGLAvailable(): boolean {\n  if (webGLAvailable !== undefined) return webGLAvailable\n  try {\n    let gl\n    const canvas = document.createElement('canvas')\n    webGLAvailable = !!(window.WebGLRenderingContext && (gl = canvas.getContext('webgl')))\n    if (gl) gl.getExtension('WEBGL_lose_context')?.loseContext()\n    return webGLAvailable\n  } catch (e) {\n    return (webGLAvailable = false)\n  }\n}\n\nexport function isWebGL2Available(): boolean {\n  if (webGL2Available !== undefined) return webGL2Available\n  try {\n    let gl\n    const canvas = document.createElement('canvas')\n    webGL2Available = !!(window.WebGL2RenderingContext && (gl = canvas.getContext('webgl2')))\n    if (gl) gl.getExtension('WEBGL_lose_context')?.loseContext()\n    return webGL2Available\n  } catch (e) {\n    return (webGL2Available = false)\n  }\n}\n\nexport function getWebGLErrorMessage(): HTMLDivElement {\n  return getErrorMessage(1)\n}\n\nexport function getWebGL2ErrorMessage(): HTMLDivElement {\n  return getErrorMessage(2)\n}\n\nexport function getErrorMessage(version: 1 | 2): HTMLDivElement {\n  const names = {\n    1: 'WebGL',\n    2: 'WebGL 2',\n  }\n\n  const contexts = {\n    1: window.WebGLRenderingContext,\n    2: window.WebGL2RenderingContext,\n  }\n\n  const element = document.createElement('div')\n  element.id = 'webglmessage'\n  element.style.fontFamily = 'monospace'\n  element.style.fontSize = '13px'\n  element.style.fontWeight = 'normal'\n  element.style.textAlign = 'center'\n  element.style.background = '#fff'\n  element.style.color = '#000'\n  element.style.padding = '1.5em'\n  element.style.width = '400px'\n  element.style.margin = '5em auto 0'\n\n  let message =\n    'Your $0 does not seem to support <a href=\"http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation\" style=\"color:#000\">$1</a>'\n\n  if (contexts[version]) {\n    message = message.replace('$0', 'graphics card')\n  } else {\n    message = message.replace('$0', 'browser')\n  }\n\n  message = message.replace('$1', names[version])\n  element.innerHTML = message\n  return element\n}\n"], "names": [], "mappings": "AAAA,IAAI,gBAAyB;AAEtB,SAAS,mBAA4B;AAF5C;AAGE,MAAI,mBAAmB;AAAkB,WAAA;AACrC,MAAA;AACE,QAAA;AACE,UAAA,SAAS,SAAS,cAAc,QAAQ;AAC9C,qBAAiB,CAAC,EAAE,OAAO,0BAA0B,KAAK,OAAO,WAAW,OAAO;AAC/E,QAAA;AAAO,eAAA,aAAa,oBAAoB,MAAjC,mBAAoC;AACxC,WAAA;AAAA,WACA;AACP,WAAQ,iBAAiB;AAAA,EAC3B;AACF;AAEO,SAAS,oBAA6B;AAf7C;AAgBE,MAAI,oBAAoB;AAAkB,WAAA;AACtC,MAAA;AACE,QAAA;AACE,UAAA,SAAS,SAAS,cAAc,QAAQ;AAC9C,sBAAkB,CAAC,EAAE,OAAO,2BAA2B,KAAK,OAAO,WAAW,QAAQ;AAClF,QAAA;AAAO,eAAA,aAAa,oBAAoB,MAAjC,mBAAoC;AACxC,WAAA;AAAA,WACA;AACP,WAAQ,kBAAkB;AAAA,EAC5B;AACF;AAEO,SAAS,uBAAuC;AACrD,SAAO,gBAAgB,CAAC;AAC1B;AAEO,SAAS,wBAAwC;AACtD,SAAO,gBAAgB,CAAC;AAC1B;AAEO,SAAS,gBAAgB,SAAgC;AAC9D,QAAM,QAAQ;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EAAA;AAGL,QAAM,WAAW;AAAA,IACf,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,EAAA;AAGN,QAAA,UAAU,SAAS,cAAc,KAAK;AAC5C,UAAQ,KAAK;AACb,UAAQ,MAAM,aAAa;AAC3B,UAAQ,MAAM,WAAW;AACzB,UAAQ,MAAM,aAAa;AAC3B,UAAQ,MAAM,YAAY;AAC1B,UAAQ,MAAM,aAAa;AAC3B,UAAQ,MAAM,QAAQ;AACtB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,QAAQ;AACtB,UAAQ,MAAM,SAAS;AAEvB,MAAI,UACF;AAEE,MAAA,SAAS,OAAO,GAAG;AACX,cAAA,QAAQ,QAAQ,MAAM,eAAe;AAAA,EAAA,OAC1C;AACK,cAAA,QAAQ,QAAQ,MAAM,SAAS;AAAA,EAC3C;AAEA,YAAU,QAAQ,QAAQ,MAAM,MAAM,OAAO,CAAC;AAC9C,UAAQ,YAAY;AACb,SAAA;AACT;"}
{"version": 3, "file": "RollerCoaster.js", "sources": ["../../src/misc/RollerCoaster.js"], "sourcesContent": ["import { <PERSON><PERSON>erAttribute, BufferGeometry, Color, Quaternion, Raycaster, Vector3 } from 'three'\n\nclass RollerCoasterGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super()\n\n    const vertices = []\n    const normals = []\n    const colors = []\n\n    const color1 = [1, 1, 1]\n    const color2 = [1, 1, 0]\n\n    const up = new Vector3(0, 1, 0)\n    const forward = new Vector3()\n    const right = new Vector3()\n\n    const quaternion = new Quaternion()\n    const prevQuaternion = new Quaternion()\n    prevQuaternion.setFromAxisAngle(up, Math.PI / 2)\n\n    const point = new Vector3()\n    const prevPoint = new Vector3()\n    prevPoint.copy(curve.getPointAt(0))\n\n    // shapes\n\n    const step = [\n      new Vector3(-0.225, 0, 0),\n      new Vector3(0, -0.05, 0),\n      new Vector3(0, -0.175, 0),\n\n      new Vector3(0, -0.05, 0),\n      new Vector3(0.225, 0, 0),\n      new Vector3(0, -0.175, 0),\n    ]\n\n    const PI2 = Math.PI * 2\n\n    let sides = 5\n    const tube1 = []\n\n    for (let i = 0; i < sides; i++) {\n      const angle = (i / sides) * PI2\n      tube1.push(new Vector3(Math.sin(angle) * 0.06, Math.cos(angle) * 0.06, 0))\n    }\n\n    sides = 6\n    const tube2 = []\n\n    for (let i = 0; i < sides; i++) {\n      const angle = (i / sides) * PI2\n      tube2.push(new Vector3(Math.sin(angle) * 0.025, Math.cos(angle) * 0.025, 0))\n    }\n\n    const vector = new Vector3()\n    const normal = new Vector3()\n\n    function drawShape(shape, color) {\n      normal.set(0, 0, -1).applyQuaternion(quaternion)\n\n      for (let j = 0; j < shape.length; j++) {\n        vector.copy(shape[j])\n        vector.applyQuaternion(quaternion)\n        vector.add(point)\n\n        vertices.push(vector.x, vector.y, vector.z)\n        normals.push(normal.x, normal.y, normal.z)\n        colors.push(color[0], color[1], color[2])\n      }\n\n      normal.set(0, 0, 1).applyQuaternion(quaternion)\n\n      for (let j = shape.length - 1; j >= 0; j--) {\n        vector.copy(shape[j])\n        vector.applyQuaternion(quaternion)\n        vector.add(point)\n\n        vertices.push(vector.x, vector.y, vector.z)\n        normals.push(normal.x, normal.y, normal.z)\n        colors.push(color[0], color[1], color[2])\n      }\n    }\n\n    const vector1 = new Vector3()\n    const vector2 = new Vector3()\n    const vector3 = new Vector3()\n    const vector4 = new Vector3()\n\n    const normal1 = new Vector3()\n    const normal2 = new Vector3()\n    const normal3 = new Vector3()\n    const normal4 = new Vector3()\n\n    function extrudeShape(shape, offset, color) {\n      for (let j = 0, jl = shape.length; j < jl; j++) {\n        const point1 = shape[j]\n        const point2 = shape[(j + 1) % jl]\n\n        vector1.copy(point1).add(offset)\n        vector1.applyQuaternion(quaternion)\n        vector1.add(point)\n\n        vector2.copy(point2).add(offset)\n        vector2.applyQuaternion(quaternion)\n        vector2.add(point)\n\n        vector3.copy(point2).add(offset)\n        vector3.applyQuaternion(prevQuaternion)\n        vector3.add(prevPoint)\n\n        vector4.copy(point1).add(offset)\n        vector4.applyQuaternion(prevQuaternion)\n        vector4.add(prevPoint)\n\n        vertices.push(vector1.x, vector1.y, vector1.z)\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector3.x, vector3.y, vector3.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        //\n\n        normal1.copy(point1)\n        normal1.applyQuaternion(quaternion)\n        normal1.normalize()\n\n        normal2.copy(point2)\n        normal2.applyQuaternion(quaternion)\n        normal2.normalize()\n\n        normal3.copy(point2)\n        normal3.applyQuaternion(prevQuaternion)\n        normal3.normalize()\n\n        normal4.copy(point1)\n        normal4.applyQuaternion(prevQuaternion)\n        normal4.normalize()\n\n        normals.push(normal1.x, normal1.y, normal1.z)\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal3.x, normal3.y, normal3.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n        colors.push(color[0], color[1], color[2])\n      }\n    }\n\n    const offset = new Vector3()\n\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions))\n\n      up.set(0, 1, 0)\n\n      forward.subVectors(point, prevPoint).normalize()\n      right.crossVectors(up, forward).normalize()\n      up.crossVectors(forward, right)\n\n      const angle = Math.atan2(forward.x, forward.z)\n\n      quaternion.setFromAxisAngle(up, angle)\n\n      if (i % 2 === 0) {\n        drawShape(step, color2)\n      }\n\n      extrudeShape(tube1, offset.set(0, -0.125, 0), color2)\n      extrudeShape(tube2, offset.set(0.2, 0, 0), color1)\n      extrudeShape(tube2, offset.set(-0.2, 0, 0), color1)\n\n      prevPoint.copy(point)\n      prevQuaternion.copy(quaternion)\n    }\n\n    // console.log( vertices.length );\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n    this.setAttribute('normal', new BufferAttribute(new Float32Array(normals), 3))\n    this.setAttribute('color', new BufferAttribute(new Float32Array(colors), 3))\n  }\n}\n\nclass RollerCoasterLiftersGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super()\n\n    const vertices = []\n    const normals = []\n\n    const quaternion = new Quaternion()\n\n    const up = new Vector3(0, 1, 0)\n\n    const point = new Vector3()\n    const tangent = new Vector3()\n\n    // shapes\n\n    const tube1 = [new Vector3(0, 0.05, -0.05), new Vector3(0, 0.05, 0.05), new Vector3(0, -0.05, 0)]\n\n    const tube2 = [new Vector3(-0.05, 0, 0.05), new Vector3(-0.05, 0, -0.05), new Vector3(0.05, 0, 0)]\n\n    const tube3 = [new Vector3(0.05, 0, -0.05), new Vector3(0.05, 0, 0.05), new Vector3(-0.05, 0, 0)]\n\n    const vector1 = new Vector3()\n    const vector2 = new Vector3()\n    const vector3 = new Vector3()\n    const vector4 = new Vector3()\n\n    const normal1 = new Vector3()\n    const normal2 = new Vector3()\n    const normal3 = new Vector3()\n    const normal4 = new Vector3()\n\n    function extrudeShape(shape, fromPoint, toPoint) {\n      for (let j = 0, jl = shape.length; j < jl; j++) {\n        const point1 = shape[j]\n        const point2 = shape[(j + 1) % jl]\n\n        vector1.copy(point1)\n        vector1.applyQuaternion(quaternion)\n        vector1.add(fromPoint)\n\n        vector2.copy(point2)\n        vector2.applyQuaternion(quaternion)\n        vector2.add(fromPoint)\n\n        vector3.copy(point2)\n        vector3.applyQuaternion(quaternion)\n        vector3.add(toPoint)\n\n        vector4.copy(point1)\n        vector4.applyQuaternion(quaternion)\n        vector4.add(toPoint)\n\n        vertices.push(vector1.x, vector1.y, vector1.z)\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        vertices.push(vector2.x, vector2.y, vector2.z)\n        vertices.push(vector3.x, vector3.y, vector3.z)\n        vertices.push(vector4.x, vector4.y, vector4.z)\n\n        //\n\n        normal1.copy(point1)\n        normal1.applyQuaternion(quaternion)\n        normal1.normalize()\n\n        normal2.copy(point2)\n        normal2.applyQuaternion(quaternion)\n        normal2.normalize()\n\n        normal3.copy(point2)\n        normal3.applyQuaternion(quaternion)\n        normal3.normalize()\n\n        normal4.copy(point1)\n        normal4.applyQuaternion(quaternion)\n        normal4.normalize()\n\n        normals.push(normal1.x, normal1.y, normal1.z)\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n\n        normals.push(normal2.x, normal2.y, normal2.z)\n        normals.push(normal3.x, normal3.y, normal3.z)\n        normals.push(normal4.x, normal4.y, normal4.z)\n      }\n    }\n\n    const fromPoint = new Vector3()\n    const toPoint = new Vector3()\n\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions))\n      tangent.copy(curve.getTangentAt(i / divisions))\n\n      const angle = Math.atan2(tangent.x, tangent.z)\n\n      quaternion.setFromAxisAngle(up, angle)\n\n      //\n\n      if (point.y > 10) {\n        fromPoint.set(-0.75, -0.35, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(0.75, -0.35, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube1, fromPoint, toPoint)\n\n        fromPoint.set(-0.7, -0.3, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(-0.7, -point.y, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube2, fromPoint, toPoint)\n\n        fromPoint.set(0.7, -0.3, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(0.7, -point.y, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube3, fromPoint, toPoint)\n      } else {\n        fromPoint.set(0, -0.2, 0)\n        fromPoint.applyQuaternion(quaternion)\n        fromPoint.add(point)\n\n        toPoint.set(0, -point.y, 0)\n        toPoint.applyQuaternion(quaternion)\n        toPoint.add(point)\n\n        extrudeShape(tube3, fromPoint, toPoint)\n      }\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n    this.setAttribute('normal', new BufferAttribute(new Float32Array(normals), 3))\n  }\n}\n\nclass RollerCoasterShadowGeometry extends BufferGeometry {\n  constructor(curve, divisions) {\n    super()\n\n    const vertices = []\n\n    const up = new Vector3(0, 1, 0)\n    const forward = new Vector3()\n\n    const quaternion = new Quaternion()\n    const prevQuaternion = new Quaternion()\n    prevQuaternion.setFromAxisAngle(up, Math.PI / 2)\n\n    const point = new Vector3()\n\n    const prevPoint = new Vector3()\n    prevPoint.copy(curve.getPointAt(0))\n    prevPoint.y = 0\n\n    const vector1 = new Vector3()\n    const vector2 = new Vector3()\n    const vector3 = new Vector3()\n    const vector4 = new Vector3()\n\n    for (let i = 1; i <= divisions; i++) {\n      point.copy(curve.getPointAt(i / divisions))\n      point.y = 0\n\n      forward.subVectors(point, prevPoint)\n\n      const angle = Math.atan2(forward.x, forward.z)\n\n      quaternion.setFromAxisAngle(up, angle)\n\n      vector1.set(-0.3, 0, 0)\n      vector1.applyQuaternion(quaternion)\n      vector1.add(point)\n\n      vector2.set(0.3, 0, 0)\n      vector2.applyQuaternion(quaternion)\n      vector2.add(point)\n\n      vector3.set(0.3, 0, 0)\n      vector3.applyQuaternion(prevQuaternion)\n      vector3.add(prevPoint)\n\n      vector4.set(-0.3, 0, 0)\n      vector4.applyQuaternion(prevQuaternion)\n      vector4.add(prevPoint)\n\n      vertices.push(vector1.x, vector1.y, vector1.z)\n      vertices.push(vector2.x, vector2.y, vector2.z)\n      vertices.push(vector4.x, vector4.y, vector4.z)\n\n      vertices.push(vector2.x, vector2.y, vector2.z)\n      vertices.push(vector3.x, vector3.y, vector3.z)\n      vertices.push(vector4.x, vector4.y, vector4.z)\n\n      prevPoint.copy(point)\n      prevQuaternion.copy(quaternion)\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n  }\n}\n\nclass SkyGeometry extends BufferGeometry {\n  constructor() {\n    super()\n\n    const vertices = []\n\n    for (let i = 0; i < 100; i++) {\n      const x = Math.random() * 800 - 400\n      const y = Math.random() * 50 + 50\n      const z = Math.random() * 800 - 400\n\n      const size = Math.random() * 40 + 20\n\n      vertices.push(x - size, y, z - size)\n      vertices.push(x + size, y, z - size)\n      vertices.push(x - size, y, z + size)\n\n      vertices.push(x + size, y, z - size)\n      vertices.push(x + size, y, z + size)\n      vertices.push(x - size, y, z + size)\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n  }\n}\n\nclass TreesGeometry extends BufferGeometry {\n  constructor(landscape) {\n    super()\n\n    const vertices = []\n    const colors = []\n\n    const raycaster = new Raycaster()\n    raycaster.ray.direction.set(0, -1, 0)\n\n    const _color = new Color()\n\n    for (let i = 0; i < 2000; i++) {\n      const x = Math.random() * 500 - 250\n      const z = Math.random() * 500 - 250\n\n      raycaster.ray.origin.set(x, 50, z)\n\n      const intersections = raycaster.intersectObject(landscape)\n\n      if (intersections.length === 0) continue\n\n      const y = intersections[0].point.y\n\n      const height = Math.random() * 5 + 0.5\n\n      let angle = Math.random() * Math.PI * 2\n\n      vertices.push(x + Math.sin(angle), y, z + Math.cos(angle))\n      vertices.push(x, y + height, z)\n      vertices.push(x + Math.sin(angle + Math.PI), y, z + Math.cos(angle + Math.PI))\n\n      angle += Math.PI / 2\n\n      vertices.push(x + Math.sin(angle), y, z + Math.cos(angle))\n      vertices.push(x, y + height, z)\n      vertices.push(x + Math.sin(angle + Math.PI), y, z + Math.cos(angle + Math.PI))\n\n      const random = Math.random() * 0.1\n\n      for (let j = 0; j < 6; j++) {\n        _color.setRGB(0.2 + random, 0.4 + random, 0, 'srgb')\n\n        colors.push(_color.r, _color.g, _color.b)\n      }\n    }\n\n    this.setAttribute('position', new BufferAttribute(new Float32Array(vertices), 3))\n    this.setAttribute('color', new BufferAttribute(new Float32Array(colors), 3))\n  }\n}\n\nexport { RollerCoasterGeometry, RollerCoasterLiftersGeometry, RollerCoasterShadowGeometry, SkyGeometry, TreesGeometry }\n"], "names": ["offset", "fromPoint", "toPoint"], "mappings": ";AAEA,MAAM,8BAA8B,eAAe;AAAA,EACjD,YAAY,OAAO,WAAW;AAC5B,UAAO;AAEP,UAAM,WAAW,CAAE;AACnB,UAAM,UAAU,CAAE;AAClB,UAAM,SAAS,CAAE;AAEjB,UAAM,SAAS,CAAC,GAAG,GAAG,CAAC;AACvB,UAAM,SAAS,CAAC,GAAG,GAAG,CAAC;AAEvB,UAAM,KAAK,IAAI,QAAQ,GAAG,GAAG,CAAC;AAC9B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,QAAQ,IAAI,QAAS;AAE3B,UAAM,aAAa,IAAI,WAAY;AACnC,UAAM,iBAAiB,IAAI,WAAY;AACvC,mBAAe,iBAAiB,IAAI,KAAK,KAAK,CAAC;AAE/C,UAAM,QAAQ,IAAI,QAAS;AAC3B,UAAM,YAAY,IAAI,QAAS;AAC/B,cAAU,KAAK,MAAM,WAAW,CAAC,CAAC;AAIlC,UAAM,OAAO;AAAA,MACX,IAAI,QAAQ,QAAQ,GAAG,CAAC;AAAA,MACxB,IAAI,QAAQ,GAAG,OAAO,CAAC;AAAA,MACvB,IAAI,QAAQ,GAAG,QAAQ,CAAC;AAAA,MAExB,IAAI,QAAQ,GAAG,OAAO,CAAC;AAAA,MACvB,IAAI,QAAQ,OAAO,GAAG,CAAC;AAAA,MACvB,IAAI,QAAQ,GAAG,QAAQ,CAAC;AAAA,IACzB;AAED,UAAM,MAAM,KAAK,KAAK;AAEtB,QAAI,QAAQ;AACZ,UAAM,QAAQ,CAAE;AAEhB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,QAAS,IAAI,QAAS;AAC5B,YAAM,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,IAC1E;AAED,YAAQ;AACR,UAAM,QAAQ,CAAE;AAEhB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,QAAS,IAAI,QAAS;AAC5B,YAAM,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,CAAC;AAAA,IAC5E;AAED,UAAM,SAAS,IAAI,QAAS;AAC5B,UAAM,SAAS,IAAI,QAAS;AAE5B,aAAS,UAAU,OAAO,OAAO;AAC/B,aAAO,IAAI,GAAG,GAAG,EAAE,EAAE,gBAAgB,UAAU;AAE/C,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,eAAO,KAAK,MAAM,CAAC,CAAC;AACpB,eAAO,gBAAgB,UAAU;AACjC,eAAO,IAAI,KAAK;AAEhB,iBAAS,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAC1C,gBAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AACzC,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MACzC;AAED,aAAO,IAAI,GAAG,GAAG,CAAC,EAAE,gBAAgB,UAAU;AAE9C,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,eAAO,KAAK,MAAM,CAAC,CAAC;AACpB,eAAO,gBAAgB,UAAU;AACjC,eAAO,IAAI,KAAK;AAEhB,iBAAS,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAC1C,gBAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AACzC,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MACzC;AAAA,IACF;AAED,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAE7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAE7B,aAAS,aAAa,OAAOA,SAAQ,OAAO;AAC1C,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,cAAM,SAAS,MAAM,CAAC;AACtB,cAAM,SAAS,OAAO,IAAI,KAAK,EAAE;AAEjC,gBAAQ,KAAK,MAAM,EAAE,IAAIA,OAAM;AAC/B,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAI,KAAK;AAEjB,gBAAQ,KAAK,MAAM,EAAE,IAAIA,OAAM;AAC/B,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAI,KAAK;AAEjB,gBAAQ,KAAK,MAAM,EAAE,IAAIA,OAAM;AAC/B,gBAAQ,gBAAgB,cAAc;AACtC,gBAAQ,IAAI,SAAS;AAErB,gBAAQ,KAAK,MAAM,EAAE,IAAIA,OAAM;AAC/B,gBAAQ,gBAAgB,cAAc;AACtC,gBAAQ,IAAI,SAAS;AAErB,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAI7C,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,cAAc;AACtC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,cAAc;AACtC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE5C,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACxC,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACxC,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAExC,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACxC,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACxC,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MACzC;AAAA,IACF;AAED,UAAM,SAAS,IAAI,QAAS;AAE5B,aAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,YAAM,KAAK,MAAM,WAAW,IAAI,SAAS,CAAC;AAE1C,SAAG,IAAI,GAAG,GAAG,CAAC;AAEd,cAAQ,WAAW,OAAO,SAAS,EAAE,UAAW;AAChD,YAAM,aAAa,IAAI,OAAO,EAAE,UAAW;AAC3C,SAAG,aAAa,SAAS,KAAK;AAE9B,YAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAE7C,iBAAW,iBAAiB,IAAI,KAAK;AAErC,UAAI,IAAI,MAAM,GAAG;AACf,kBAAU,MAAM,MAAM;AAAA,MACvB;AAED,mBAAa,OAAO,OAAO,IAAI,GAAG,QAAQ,CAAC,GAAG,MAAM;AACpD,mBAAa,OAAO,OAAO,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM;AACjD,mBAAa,OAAO,OAAO,IAAI,MAAM,GAAG,CAAC,GAAG,MAAM;AAElD,gBAAU,KAAK,KAAK;AACpB,qBAAe,KAAK,UAAU;AAAA,IAC/B;AAID,SAAK,aAAa,YAAY,IAAI,gBAAgB,IAAI,aAAa,QAAQ,GAAG,CAAC,CAAC;AAChF,SAAK,aAAa,UAAU,IAAI,gBAAgB,IAAI,aAAa,OAAO,GAAG,CAAC,CAAC;AAC7E,SAAK,aAAa,SAAS,IAAI,gBAAgB,IAAI,aAAa,MAAM,GAAG,CAAC,CAAC;AAAA,EAC5E;AACH;AAEA,MAAM,qCAAqC,eAAe;AAAA,EACxD,YAAY,OAAO,WAAW;AAC5B,UAAO;AAEP,UAAM,WAAW,CAAE;AACnB,UAAM,UAAU,CAAE;AAElB,UAAM,aAAa,IAAI,WAAY;AAEnC,UAAM,KAAK,IAAI,QAAQ,GAAG,GAAG,CAAC;AAE9B,UAAM,QAAQ,IAAI,QAAS;AAC3B,UAAM,UAAU,IAAI,QAAS;AAI7B,UAAM,QAAQ,CAAC,IAAI,QAAQ,GAAG,MAAM,KAAK,GAAG,IAAI,QAAQ,GAAG,MAAM,IAAI,GAAG,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC;AAEhG,UAAM,QAAQ,CAAC,IAAI,QAAQ,OAAO,GAAG,IAAI,GAAG,IAAI,QAAQ,OAAO,GAAG,KAAK,GAAG,IAAI,QAAQ,MAAM,GAAG,CAAC,CAAC;AAEjG,UAAM,QAAQ,CAAC,IAAI,QAAQ,MAAM,GAAG,KAAK,GAAG,IAAI,QAAQ,MAAM,GAAG,IAAI,GAAG,IAAI,QAAQ,OAAO,GAAG,CAAC,CAAC;AAEhG,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAE7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAE7B,aAAS,aAAa,OAAOC,YAAWC,UAAS;AAC/C,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,cAAM,SAAS,MAAM,CAAC;AACtB,cAAM,SAAS,OAAO,IAAI,KAAK,EAAE;AAEjC,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAID,UAAS;AAErB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAIA,UAAS;AAErB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAIC,QAAO;AAEnB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAIA,QAAO;AAEnB,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,iBAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAI7C,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,MAAM;AACnB,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,UAAW;AAEnB,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC5C,gBAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACF;AAED,UAAM,YAAY,IAAI,QAAS;AAC/B,UAAM,UAAU,IAAI,QAAS;AAE7B,aAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,YAAM,KAAK,MAAM,WAAW,IAAI,SAAS,CAAC;AAC1C,cAAQ,KAAK,MAAM,aAAa,IAAI,SAAS,CAAC;AAE9C,YAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAE7C,iBAAW,iBAAiB,IAAI,KAAK;AAIrC,UAAI,MAAM,IAAI,IAAI;AAChB,kBAAU,IAAI,OAAO,OAAO,CAAC;AAC7B,kBAAU,gBAAgB,UAAU;AACpC,kBAAU,IAAI,KAAK;AAEnB,gBAAQ,IAAI,MAAM,OAAO,CAAC;AAC1B,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAI,KAAK;AAEjB,qBAAa,OAAO,WAAW,OAAO;AAEtC,kBAAU,IAAI,MAAM,MAAM,CAAC;AAC3B,kBAAU,gBAAgB,UAAU;AACpC,kBAAU,IAAI,KAAK;AAEnB,gBAAQ,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;AAC7B,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAI,KAAK;AAEjB,qBAAa,OAAO,WAAW,OAAO;AAEtC,kBAAU,IAAI,KAAK,MAAM,CAAC;AAC1B,kBAAU,gBAAgB,UAAU;AACpC,kBAAU,IAAI,KAAK;AAEnB,gBAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;AAC5B,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAI,KAAK;AAEjB,qBAAa,OAAO,WAAW,OAAO;AAAA,MAC9C,OAAa;AACL,kBAAU,IAAI,GAAG,MAAM,CAAC;AACxB,kBAAU,gBAAgB,UAAU;AACpC,kBAAU,IAAI,KAAK;AAEnB,gBAAQ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC;AAC1B,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,IAAI,KAAK;AAEjB,qBAAa,OAAO,WAAW,OAAO;AAAA,MACvC;AAAA,IACF;AAED,SAAK,aAAa,YAAY,IAAI,gBAAgB,IAAI,aAAa,QAAQ,GAAG,CAAC,CAAC;AAChF,SAAK,aAAa,UAAU,IAAI,gBAAgB,IAAI,aAAa,OAAO,GAAG,CAAC,CAAC;AAAA,EAC9E;AACH;AAEA,MAAM,oCAAoC,eAAe;AAAA,EACvD,YAAY,OAAO,WAAW;AAC5B,UAAO;AAEP,UAAM,WAAW,CAAE;AAEnB,UAAM,KAAK,IAAI,QAAQ,GAAG,GAAG,CAAC;AAC9B,UAAM,UAAU,IAAI,QAAS;AAE7B,UAAM,aAAa,IAAI,WAAY;AACnC,UAAM,iBAAiB,IAAI,WAAY;AACvC,mBAAe,iBAAiB,IAAI,KAAK,KAAK,CAAC;AAE/C,UAAM,QAAQ,IAAI,QAAS;AAE3B,UAAM,YAAY,IAAI,QAAS;AAC/B,cAAU,KAAK,MAAM,WAAW,CAAC,CAAC;AAClC,cAAU,IAAI;AAEd,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAC7B,UAAM,UAAU,IAAI,QAAS;AAE7B,aAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,YAAM,KAAK,MAAM,WAAW,IAAI,SAAS,CAAC;AAC1C,YAAM,IAAI;AAEV,cAAQ,WAAW,OAAO,SAAS;AAEnC,YAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAE7C,iBAAW,iBAAiB,IAAI,KAAK;AAErC,cAAQ,IAAI,MAAM,GAAG,CAAC;AACtB,cAAQ,gBAAgB,UAAU;AAClC,cAAQ,IAAI,KAAK;AAEjB,cAAQ,IAAI,KAAK,GAAG,CAAC;AACrB,cAAQ,gBAAgB,UAAU;AAClC,cAAQ,IAAI,KAAK;AAEjB,cAAQ,IAAI,KAAK,GAAG,CAAC;AACrB,cAAQ,gBAAgB,cAAc;AACtC,cAAQ,IAAI,SAAS;AAErB,cAAQ,IAAI,MAAM,GAAG,CAAC;AACtB,cAAQ,gBAAgB,cAAc;AACtC,cAAQ,IAAI,SAAS;AAErB,eAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,eAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,eAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE7C,eAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,eAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC7C,eAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE7C,gBAAU,KAAK,KAAK;AACpB,qBAAe,KAAK,UAAU;AAAA,IAC/B;AAED,SAAK,aAAa,YAAY,IAAI,gBAAgB,IAAI,aAAa,QAAQ,GAAG,CAAC,CAAC;AAAA,EACjF;AACH;AAEA,MAAM,oBAAoB,eAAe;AAAA,EACvC,cAAc;AACZ,UAAO;AAEP,UAAM,WAAW,CAAE;AAEnB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,IAAI,KAAK,OAAQ,IAAG,MAAM;AAChC,YAAM,IAAI,KAAK,OAAQ,IAAG,KAAK;AAC/B,YAAM,IAAI,KAAK,OAAQ,IAAG,MAAM;AAEhC,YAAM,OAAO,KAAK,OAAQ,IAAG,KAAK;AAElC,eAAS,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AACnC,eAAS,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AACnC,eAAS,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AAEnC,eAAS,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AACnC,eAAS,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AACnC,eAAS,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AAAA,IACpC;AAED,SAAK,aAAa,YAAY,IAAI,gBAAgB,IAAI,aAAa,QAAQ,GAAG,CAAC,CAAC;AAAA,EACjF;AACH;AAEA,MAAM,sBAAsB,eAAe;AAAA,EACzC,YAAY,WAAW;AACrB,UAAO;AAEP,UAAM,WAAW,CAAE;AACnB,UAAM,SAAS,CAAE;AAEjB,UAAM,YAAY,IAAI,UAAW;AACjC,cAAU,IAAI,UAAU,IAAI,GAAG,IAAI,CAAC;AAEpC,UAAM,SAAS,IAAI,MAAO;AAE1B,aAAS,IAAI,GAAG,IAAI,KAAM,KAAK;AAC7B,YAAM,IAAI,KAAK,OAAQ,IAAG,MAAM;AAChC,YAAM,IAAI,KAAK,OAAQ,IAAG,MAAM;AAEhC,gBAAU,IAAI,OAAO,IAAI,GAAG,IAAI,CAAC;AAEjC,YAAM,gBAAgB,UAAU,gBAAgB,SAAS;AAEzD,UAAI,cAAc,WAAW;AAAG;AAEhC,YAAM,IAAI,cAAc,CAAC,EAAE,MAAM;AAEjC,YAAM,SAAS,KAAK,OAAQ,IAAG,IAAI;AAEnC,UAAI,QAAQ,KAAK,OAAQ,IAAG,KAAK,KAAK;AAEtC,eAAS,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC;AACzD,eAAS,KAAK,GAAG,IAAI,QAAQ,CAAC;AAC9B,eAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,GAAG,GAAG,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,CAAC;AAE7E,eAAS,KAAK,KAAK;AAEnB,eAAS,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC;AACzD,eAAS,KAAK,GAAG,IAAI,QAAQ,CAAC;AAC9B,eAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,GAAG,GAAG,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,CAAC;AAE7E,YAAM,SAAS,KAAK,OAAM,IAAK;AAE/B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAO,OAAO,MAAM,QAAQ,MAAM,QAAQ,GAAG,MAAM;AAEnD,eAAO,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,MACzC;AAAA,IACF;AAED,SAAK,aAAa,YAAY,IAAI,gBAAgB,IAAI,aAAa,QAAQ,GAAG,CAAC,CAAC;AAChF,SAAK,aAAa,SAAS,IAAI,gBAAgB,IAAI,aAAa,MAAM,GAAG,CAAC,CAAC;AAAA,EAC5E;AACH;"}
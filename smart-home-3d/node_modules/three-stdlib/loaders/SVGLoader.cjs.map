{"version": 3, "file": "SVGLoader.cjs", "sources": ["../../src/loaders/SVGLoader.js"], "sourcesContent": ["import {\n  Box2,\n  <PERSON>ufferG<PERSON><PERSON>,\n  FileLoader,\n  Float32BufferAttribute,\n  Loader,\n  Matrix3,\n  Path,\n  Shape,\n  <PERSON>hapePath,\n  <PERSON>hape<PERSON><PERSON>s,\n  Vector2,\n  Vector3,\n} from 'three'\n\nconst COLOR_SPACE_SVG = 'srgb'\n\nconst SVGLoader = /* @__PURE__ */ (() => {\n  class SVGLoader extends Loader {\n    constructor(manager) {\n      super(manager)\n\n      // Default dots per inch\n      this.defaultDPI = 90\n\n      // Accepted units: 'mm', 'cm', 'in', 'pt', 'pc', 'px'\n      this.defaultUnit = 'px'\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      const scope = this\n\n      const loader = new FileLoader(scope.manager)\n      loader.setPath(scope.path)\n      loader.setRequestHeader(scope.requestHeader)\n      loader.setWithCredentials(scope.withCredentials)\n      loader.load(\n        url,\n        function (text) {\n          try {\n            onLoad(scope.parse(text))\n          } catch (e) {\n            if (onError) {\n              onError(e)\n            } else {\n              console.error(e)\n            }\n\n            scope.manager.itemError(url)\n          }\n        },\n        onProgress,\n        onError,\n      )\n    }\n\n    parse(text) {\n      const scope = this\n\n      function parseNode(node, style) {\n        if (node.nodeType !== 1) return\n\n        const transform = getNodeTransform(node)\n\n        let isDefsNode = false\n\n        let path = null\n\n        switch (node.nodeName) {\n          case 'svg':\n            style = parseStyle(node, style)\n            break\n\n          case 'style':\n            parseCSSStylesheet(node)\n            break\n\n          case 'g':\n            style = parseStyle(node, style)\n            break\n\n          case 'path':\n            style = parseStyle(node, style)\n            if (node.hasAttribute('d')) path = parsePathNode(node)\n            break\n\n          case 'rect':\n            style = parseStyle(node, style)\n            path = parseRectNode(node)\n            break\n\n          case 'polygon':\n            style = parseStyle(node, style)\n            path = parsePolygonNode(node)\n            break\n\n          case 'polyline':\n            style = parseStyle(node, style)\n            path = parsePolylineNode(node)\n            break\n\n          case 'circle':\n            style = parseStyle(node, style)\n            path = parseCircleNode(node)\n            break\n\n          case 'ellipse':\n            style = parseStyle(node, style)\n            path = parseEllipseNode(node)\n            break\n\n          case 'line':\n            style = parseStyle(node, style)\n            path = parseLineNode(node)\n            break\n\n          case 'defs':\n            isDefsNode = true\n            break\n\n          case 'use':\n            style = parseStyle(node, style)\n\n            const href = node.getAttributeNS('http://www.w3.org/1999/xlink', 'href') || ''\n            const usedNodeId = href.substring(1)\n            const usedNode = node.viewportElement.getElementById(usedNodeId)\n            if (usedNode) {\n              parseNode(usedNode, style)\n            } else {\n              console.warn(\"SVGLoader: 'use node' references non-existent node id: \" + usedNodeId)\n            }\n\n            break\n\n          default:\n          // console.log( node );\n        }\n\n        if (path) {\n          if (style.fill !== undefined && style.fill !== 'none') {\n            path.color.setStyle(style.fill, COLOR_SPACE_SVG)\n          }\n\n          transformPath(path, currentTransform)\n\n          paths.push(path)\n\n          path.userData = { node: node, style: style }\n        }\n\n        const childNodes = node.childNodes\n\n        for (let i = 0; i < childNodes.length; i++) {\n          const node = childNodes[i]\n\n          if (isDefsNode && node.nodeName !== 'style' && node.nodeName !== 'defs') {\n            // Ignore everything in defs except CSS style definitions\n            // and nested defs, because it is OK by the standard to have\n            // <style/> there.\n            continue\n          }\n\n          parseNode(node, style)\n        }\n\n        if (transform) {\n          transformStack.pop()\n\n          if (transformStack.length > 0) {\n            currentTransform.copy(transformStack[transformStack.length - 1])\n          } else {\n            currentTransform.identity()\n          }\n        }\n      }\n\n      function parsePathNode(node) {\n        const path = new ShapePath()\n\n        const point = new Vector2()\n        const control = new Vector2()\n\n        const firstPoint = new Vector2()\n        let isFirstPoint = true\n        let doSetFirstPoint = false\n\n        const d = node.getAttribute('d')\n\n        if (d === '' || d === 'none') return null\n\n        // console.log( d );\n\n        const commands = d.match(/[a-df-z][^a-df-z]*/gi)\n\n        for (let i = 0, l = commands.length; i < l; i++) {\n          const command = commands[i]\n\n          const type = command.charAt(0)\n          const data = command.slice(1).trim()\n\n          if (isFirstPoint === true) {\n            doSetFirstPoint = true\n            isFirstPoint = false\n          }\n\n          let numbers\n\n          switch (type) {\n            case 'M':\n              numbers = parseFloats(data)\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x = numbers[j + 0]\n                point.y = numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n\n                if (j === 0) {\n                  path.moveTo(point.x, point.y)\n                } else {\n                  path.lineTo(point.x, point.y)\n                }\n\n                if (j === 0) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'H':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.x = numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'V':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.y = numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'L':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x = numbers[j + 0]\n                point.y = numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'C':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 6) {\n                path.bezierCurveTo(\n                  numbers[j + 0],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                  numbers[j + 4],\n                  numbers[j + 5],\n                )\n                control.x = numbers[j + 2]\n                control.y = numbers[j + 3]\n                point.x = numbers[j + 4]\n                point.y = numbers[j + 5]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'S':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.bezierCurveTo(\n                  getReflection(point.x, control.x),\n                  getReflection(point.y, control.y),\n                  numbers[j + 0],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                )\n                control.x = numbers[j + 0]\n                control.y = numbers[j + 1]\n                point.x = numbers[j + 2]\n                point.y = numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'Q':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.quadraticCurveTo(numbers[j + 0], numbers[j + 1], numbers[j + 2], numbers[j + 3])\n                control.x = numbers[j + 0]\n                control.y = numbers[j + 1]\n                point.x = numbers[j + 2]\n                point.y = numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'T':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                const rx = getReflection(point.x, control.x)\n                const ry = getReflection(point.y, control.y)\n                path.quadraticCurveTo(rx, ry, numbers[j + 0], numbers[j + 1])\n                control.x = rx\n                control.y = ry\n                point.x = numbers[j + 0]\n                point.y = numbers[j + 1]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'A':\n              numbers = parseFloats(data, [3, 4], 7)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 7) {\n                // skip command if start point == end point\n                if (numbers[j + 5] == point.x && numbers[j + 6] == point.y) continue\n\n                const start = point.clone()\n                point.x = numbers[j + 5]\n                point.y = numbers[j + 6]\n                control.x = point.x\n                control.y = point.y\n                parseArcCommand(\n                  path,\n                  numbers[j],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                  numbers[j + 4],\n                  start,\n                  point,\n                )\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'm':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x += numbers[j + 0]\n                point.y += numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n\n                if (j === 0) {\n                  path.moveTo(point.x, point.y)\n                } else {\n                  path.lineTo(point.x, point.y)\n                }\n\n                if (j === 0) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'h':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.x += numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'v':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.y += numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'l':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x += numbers[j + 0]\n                point.y += numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'c':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 6) {\n                path.bezierCurveTo(\n                  point.x + numbers[j + 0],\n                  point.y + numbers[j + 1],\n                  point.x + numbers[j + 2],\n                  point.y + numbers[j + 3],\n                  point.x + numbers[j + 4],\n                  point.y + numbers[j + 5],\n                )\n                control.x = point.x + numbers[j + 2]\n                control.y = point.y + numbers[j + 3]\n                point.x += numbers[j + 4]\n                point.y += numbers[j + 5]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 's':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.bezierCurveTo(\n                  getReflection(point.x, control.x),\n                  getReflection(point.y, control.y),\n                  point.x + numbers[j + 0],\n                  point.y + numbers[j + 1],\n                  point.x + numbers[j + 2],\n                  point.y + numbers[j + 3],\n                )\n                control.x = point.x + numbers[j + 0]\n                control.y = point.y + numbers[j + 1]\n                point.x += numbers[j + 2]\n                point.y += numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'q':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.quadraticCurveTo(\n                  point.x + numbers[j + 0],\n                  point.y + numbers[j + 1],\n                  point.x + numbers[j + 2],\n                  point.y + numbers[j + 3],\n                )\n                control.x = point.x + numbers[j + 0]\n                control.y = point.y + numbers[j + 1]\n                point.x += numbers[j + 2]\n                point.y += numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 't':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                const rx = getReflection(point.x, control.x)\n                const ry = getReflection(point.y, control.y)\n                path.quadraticCurveTo(rx, ry, point.x + numbers[j + 0], point.y + numbers[j + 1])\n                control.x = rx\n                control.y = ry\n                point.x = point.x + numbers[j + 0]\n                point.y = point.y + numbers[j + 1]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'a':\n              numbers = parseFloats(data, [3, 4], 7)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 7) {\n                // skip command if no displacement\n                if (numbers[j + 5] == 0 && numbers[j + 6] == 0) continue\n\n                const start = point.clone()\n                point.x += numbers[j + 5]\n                point.y += numbers[j + 6]\n                control.x = point.x\n                control.y = point.y\n                parseArcCommand(\n                  path,\n                  numbers[j],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                  numbers[j + 4],\n                  start,\n                  point,\n                )\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'Z':\n            case 'z':\n              path.currentPath.autoClose = true\n\n              if (path.currentPath.curves.length > 0) {\n                // Reset point to beginning of Path\n                point.copy(firstPoint)\n                path.currentPath.currentPoint.copy(point)\n                isFirstPoint = true\n              }\n\n              break\n\n            default:\n              console.warn(command)\n          }\n\n          // console.log( type, parseFloats( data ), parseFloats( data ).length  )\n\n          doSetFirstPoint = false\n        }\n\n        return path\n      }\n\n      function parseCSSStylesheet(node) {\n        if (!node.sheet || !node.sheet.cssRules || !node.sheet.cssRules.length) return\n\n        for (let i = 0; i < node.sheet.cssRules.length; i++) {\n          const stylesheet = node.sheet.cssRules[i]\n\n          if (stylesheet.type !== 1) continue\n\n          const selectorList = stylesheet.selectorText\n            .split(/,/gm)\n            .filter(Boolean)\n            .map((i) => i.trim())\n\n          for (let j = 0; j < selectorList.length; j++) {\n            // Remove empty rules\n            const definitions = Object.fromEntries(Object.entries(stylesheet.style).filter(([, v]) => v !== ''))\n\n            stylesheets[selectorList[j]] = Object.assign(stylesheets[selectorList[j]] || {}, definitions)\n          }\n        }\n      }\n\n      /**\n       * https://www.w3.org/TR/SVG/implnote.html#ArcImplementationNotes\n       * https://mortoray.com/2017/02/16/rendering-an-svg-elliptical-arc-as-bezier-curves/ Appendix: Endpoint to center arc conversion\n       * From\n       * rx ry x-axis-rotation large-arc-flag sweep-flag x y\n       * To\n       * aX, aY, xRadius, yRadius, aStartAngle, aEndAngle, aClockwise, aRotation\n       */\n\n      function parseArcCommand(path, rx, ry, x_axis_rotation, large_arc_flag, sweep_flag, start, end) {\n        if (rx == 0 || ry == 0) {\n          // draw a line if either of the radii == 0\n          path.lineTo(end.x, end.y)\n          return\n        }\n\n        x_axis_rotation = (x_axis_rotation * Math.PI) / 180\n\n        // Ensure radii are positive\n        rx = Math.abs(rx)\n        ry = Math.abs(ry)\n\n        // Compute (x1', y1')\n        const dx2 = (start.x - end.x) / 2.0\n        const dy2 = (start.y - end.y) / 2.0\n        const x1p = Math.cos(x_axis_rotation) * dx2 + Math.sin(x_axis_rotation) * dy2\n        const y1p = -Math.sin(x_axis_rotation) * dx2 + Math.cos(x_axis_rotation) * dy2\n\n        // Compute (cx', cy')\n        let rxs = rx * rx\n        let rys = ry * ry\n        const x1ps = x1p * x1p\n        const y1ps = y1p * y1p\n\n        // Ensure radii are large enough\n        const cr = x1ps / rxs + y1ps / rys\n\n        if (cr > 1) {\n          // scale up rx,ry equally so cr == 1\n          const s = Math.sqrt(cr)\n          rx = s * rx\n          ry = s * ry\n          rxs = rx * rx\n          rys = ry * ry\n        }\n\n        const dq = rxs * y1ps + rys * x1ps\n        const pq = (rxs * rys - dq) / dq\n        let q = Math.sqrt(Math.max(0, pq))\n        if (large_arc_flag === sweep_flag) q = -q\n        const cxp = (q * rx * y1p) / ry\n        const cyp = (-q * ry * x1p) / rx\n\n        // Step 3: Compute (cx, cy) from (cx', cy')\n        const cx = Math.cos(x_axis_rotation) * cxp - Math.sin(x_axis_rotation) * cyp + (start.x + end.x) / 2\n        const cy = Math.sin(x_axis_rotation) * cxp + Math.cos(x_axis_rotation) * cyp + (start.y + end.y) / 2\n\n        // Step 4: Compute θ1 and Δθ\n        const theta = svgAngle(1, 0, (x1p - cxp) / rx, (y1p - cyp) / ry)\n        const delta = svgAngle((x1p - cxp) / rx, (y1p - cyp) / ry, (-x1p - cxp) / rx, (-y1p - cyp) / ry) % (Math.PI * 2)\n\n        path.currentPath.absellipse(cx, cy, rx, ry, theta, theta + delta, sweep_flag === 0, x_axis_rotation)\n      }\n\n      function svgAngle(ux, uy, vx, vy) {\n        const dot = ux * vx + uy * vy\n        const len = Math.sqrt(ux * ux + uy * uy) * Math.sqrt(vx * vx + vy * vy)\n        let ang = Math.acos(Math.max(-1, Math.min(1, dot / len))) // floating point precision, slightly over values appear\n        if (ux * vy - uy * vx < 0) ang = -ang\n        return ang\n      }\n\n      /*\n       * According to https://www.w3.org/TR/SVG/shapes.html#RectElementRXAttribute\n       * rounded corner should be rendered to elliptical arc, but bezier curve does the job well enough\n       */\n      function parseRectNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute('x') || 0)\n        const y = parseFloatWithUnits(node.getAttribute('y') || 0)\n        const rx = parseFloatWithUnits(node.getAttribute('rx') || node.getAttribute('ry') || 0)\n        const ry = parseFloatWithUnits(node.getAttribute('ry') || node.getAttribute('rx') || 0)\n        const w = parseFloatWithUnits(node.getAttribute('width'))\n        const h = parseFloatWithUnits(node.getAttribute('height'))\n\n        // Ellipse arc to Bezier approximation Coefficient (Inversed). See:\n        // https://spencermortensen.com/articles/bezier-circle/\n        const bci = 1 - 0.551915024494\n\n        const path = new ShapePath()\n\n        // top left\n        path.moveTo(x + rx, y)\n\n        // top right\n        path.lineTo(x + w - rx, y)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + w - rx * bci, y, x + w, y + ry * bci, x + w, y + ry)\n        }\n\n        // bottom right\n        path.lineTo(x + w, y + h - ry)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + w, y + h - ry * bci, x + w - rx * bci, y + h, x + w - rx, y + h)\n        }\n\n        // bottom left\n        path.lineTo(x + rx, y + h)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + rx * bci, y + h, x, y + h - ry * bci, x, y + h - ry)\n        }\n\n        // back to top left\n        path.lineTo(x, y + ry)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x, y + ry * bci, x + rx * bci, y, x + rx, y)\n        }\n\n        return path\n      }\n\n      function parsePolygonNode(node) {\n        function iterator(match, a, b) {\n          const x = parseFloatWithUnits(a)\n          const y = parseFloatWithUnits(b)\n\n          if (index === 0) {\n            path.moveTo(x, y)\n          } else {\n            path.lineTo(x, y)\n          }\n\n          index++\n        }\n\n        const regex = /([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)(?:,|\\s)([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)/g\n\n        const path = new ShapePath()\n\n        let index = 0\n\n        node.getAttribute('points').replace(regex, iterator)\n\n        path.currentPath.autoClose = true\n\n        return path\n      }\n\n      function parsePolylineNode(node) {\n        function iterator(match, a, b) {\n          const x = parseFloatWithUnits(a)\n          const y = parseFloatWithUnits(b)\n\n          if (index === 0) {\n            path.moveTo(x, y)\n          } else {\n            path.lineTo(x, y)\n          }\n\n          index++\n        }\n\n        const regex = /([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)(?:,|\\s)([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)/g\n\n        const path = new ShapePath()\n\n        let index = 0\n\n        node.getAttribute('points').replace(regex, iterator)\n\n        path.currentPath.autoClose = false\n\n        return path\n      }\n\n      function parseCircleNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute('cx') || 0)\n        const y = parseFloatWithUnits(node.getAttribute('cy') || 0)\n        const r = parseFloatWithUnits(node.getAttribute('r') || 0)\n\n        const subpath = new Path()\n        subpath.absarc(x, y, r, 0, Math.PI * 2)\n\n        const path = new ShapePath()\n        path.subPaths.push(subpath)\n\n        return path\n      }\n\n      function parseEllipseNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute('cx') || 0)\n        const y = parseFloatWithUnits(node.getAttribute('cy') || 0)\n        const rx = parseFloatWithUnits(node.getAttribute('rx') || 0)\n        const ry = parseFloatWithUnits(node.getAttribute('ry') || 0)\n\n        const subpath = new Path()\n        subpath.absellipse(x, y, rx, ry, 0, Math.PI * 2)\n\n        const path = new ShapePath()\n        path.subPaths.push(subpath)\n\n        return path\n      }\n\n      function parseLineNode(node) {\n        const x1 = parseFloatWithUnits(node.getAttribute('x1') || 0)\n        const y1 = parseFloatWithUnits(node.getAttribute('y1') || 0)\n        const x2 = parseFloatWithUnits(node.getAttribute('x2') || 0)\n        const y2 = parseFloatWithUnits(node.getAttribute('y2') || 0)\n\n        const path = new ShapePath()\n        path.moveTo(x1, y1)\n        path.lineTo(x2, y2)\n        path.currentPath.autoClose = false\n\n        return path\n      }\n\n      //\n\n      function parseStyle(node, style) {\n        style = Object.assign({}, style) // clone style\n\n        let stylesheetStyles = {}\n\n        if (node.hasAttribute('class')) {\n          const classSelectors = node\n            .getAttribute('class')\n            .split(/\\s/)\n            .filter(Boolean)\n            .map((i) => i.trim())\n\n          for (let i = 0; i < classSelectors.length; i++) {\n            stylesheetStyles = Object.assign(stylesheetStyles, stylesheets['.' + classSelectors[i]])\n          }\n        }\n\n        if (node.hasAttribute('id')) {\n          stylesheetStyles = Object.assign(stylesheetStyles, stylesheets['#' + node.getAttribute('id')])\n        }\n\n        function addStyle(svgName, jsName, adjustFunction) {\n          if (adjustFunction === undefined)\n            adjustFunction = function copy(v) {\n              if (v.startsWith('url')) console.warn('SVGLoader: url access in attributes is not implemented.')\n\n              return v\n            }\n\n          if (node.hasAttribute(svgName)) style[jsName] = adjustFunction(node.getAttribute(svgName))\n          if (stylesheetStyles[svgName]) style[jsName] = adjustFunction(stylesheetStyles[svgName])\n          if (node.style && node.style[svgName] !== '') style[jsName] = adjustFunction(node.style[svgName])\n        }\n\n        function clamp(v) {\n          return Math.max(0, Math.min(1, parseFloatWithUnits(v)))\n        }\n\n        function positive(v) {\n          return Math.max(0, parseFloatWithUnits(v))\n        }\n\n        addStyle('fill', 'fill')\n        addStyle('fill-opacity', 'fillOpacity', clamp)\n        addStyle('fill-rule', 'fillRule')\n        addStyle('opacity', 'opacity', clamp)\n        addStyle('stroke', 'stroke')\n        addStyle('stroke-opacity', 'strokeOpacity', clamp)\n        addStyle('stroke-width', 'strokeWidth', positive)\n        addStyle('stroke-linejoin', 'strokeLineJoin')\n        addStyle('stroke-linecap', 'strokeLineCap')\n        addStyle('stroke-miterlimit', 'strokeMiterLimit', positive)\n        addStyle('visibility', 'visibility')\n\n        return style\n      }\n\n      // http://www.w3.org/TR/SVG11/implnote.html#PathElementImplementationNotes\n\n      function getReflection(a, b) {\n        return a - (b - a)\n      }\n\n      // from https://github.com/ppvg/svg-numbers (MIT License)\n\n      function parseFloats(input, flags, stride) {\n        if (typeof input !== 'string') {\n          throw new TypeError('Invalid input: ' + typeof input)\n        }\n\n        // Character groups\n        const RE = {\n          SEPARATOR: /[ \\t\\r\\n\\,.\\-+]/,\n          WHITESPACE: /[ \\t\\r\\n]/,\n          DIGIT: /[\\d]/,\n          SIGN: /[-+]/,\n          POINT: /\\./,\n          COMMA: /,/,\n          EXP: /e/i,\n          FLAGS: /[01]/,\n        }\n\n        // States\n        const SEP = 0\n        const INT = 1\n        const FLOAT = 2\n        const EXP = 3\n\n        let state = SEP\n        let seenComma = true\n        let number = '',\n          exponent = ''\n        const result = []\n\n        function throwSyntaxError(current, i, partial) {\n          const error = new SyntaxError('Unexpected character \"' + current + '\" at index ' + i + '.')\n          error.partial = partial\n          throw error\n        }\n\n        function newNumber() {\n          if (number !== '') {\n            if (exponent === '') result.push(Number(number))\n            else result.push(Number(number) * Math.pow(10, Number(exponent)))\n          }\n\n          number = ''\n          exponent = ''\n        }\n\n        let current\n        const length = input.length\n\n        for (let i = 0; i < length; i++) {\n          current = input[i]\n\n          // check for flags\n          if (Array.isArray(flags) && flags.includes(result.length % stride) && RE.FLAGS.test(current)) {\n            state = INT\n            number = current\n            newNumber()\n            continue\n          }\n\n          // parse until next number\n          if (state === SEP) {\n            // eat whitespace\n            if (RE.WHITESPACE.test(current)) {\n              continue\n            }\n\n            // start new number\n            if (RE.DIGIT.test(current) || RE.SIGN.test(current)) {\n              state = INT\n              number = current\n              continue\n            }\n\n            if (RE.POINT.test(current)) {\n              state = FLOAT\n              number = current\n              continue\n            }\n\n            // throw on double commas (e.g. \"1, , 2\")\n            if (RE.COMMA.test(current)) {\n              if (seenComma) {\n                throwSyntaxError(current, i, result)\n              }\n\n              seenComma = true\n            }\n          }\n\n          // parse integer part\n          if (state === INT) {\n            if (RE.DIGIT.test(current)) {\n              number += current\n              continue\n            }\n\n            if (RE.POINT.test(current)) {\n              number += current\n              state = FLOAT\n              continue\n            }\n\n            if (RE.EXP.test(current)) {\n              state = EXP\n              continue\n            }\n\n            // throw on double signs (\"-+1\"), but not on sign as separator (\"-1-2\")\n            if (RE.SIGN.test(current) && number.length === 1 && RE.SIGN.test(number[0])) {\n              throwSyntaxError(current, i, result)\n            }\n          }\n\n          // parse decimal part\n          if (state === FLOAT) {\n            if (RE.DIGIT.test(current)) {\n              number += current\n              continue\n            }\n\n            if (RE.EXP.test(current)) {\n              state = EXP\n              continue\n            }\n\n            // throw on double decimal points (e.g. \"1..2\")\n            if (RE.POINT.test(current) && number[number.length - 1] === '.') {\n              throwSyntaxError(current, i, result)\n            }\n          }\n\n          // parse exponent part\n          if (state === EXP) {\n            if (RE.DIGIT.test(current)) {\n              exponent += current\n              continue\n            }\n\n            if (RE.SIGN.test(current)) {\n              if (exponent === '') {\n                exponent += current\n                continue\n              }\n\n              if (exponent.length === 1 && RE.SIGN.test(exponent)) {\n                throwSyntaxError(current, i, result)\n              }\n            }\n          }\n\n          // end of number\n          if (RE.WHITESPACE.test(current)) {\n            newNumber()\n            state = SEP\n            seenComma = false\n          } else if (RE.COMMA.test(current)) {\n            newNumber()\n            state = SEP\n            seenComma = true\n          } else if (RE.SIGN.test(current)) {\n            newNumber()\n            state = INT\n            number = current\n          } else if (RE.POINT.test(current)) {\n            newNumber()\n            state = FLOAT\n            number = current\n          } else {\n            throwSyntaxError(current, i, result)\n          }\n        }\n\n        // add the last number found (if any)\n        newNumber()\n\n        return result\n      }\n\n      // Units\n\n      const units = ['mm', 'cm', 'in', 'pt', 'pc', 'px']\n\n      // Conversion: [ fromUnit ][ toUnit ] (-1 means dpi dependent)\n      const unitConversion = {\n        mm: {\n          mm: 1,\n          cm: 0.1,\n          in: 1 / 25.4,\n          pt: 72 / 25.4,\n          pc: 6 / 25.4,\n          px: -1,\n        },\n        cm: {\n          mm: 10,\n          cm: 1,\n          in: 1 / 2.54,\n          pt: 72 / 2.54,\n          pc: 6 / 2.54,\n          px: -1,\n        },\n        in: {\n          mm: 25.4,\n          cm: 2.54,\n          in: 1,\n          pt: 72,\n          pc: 6,\n          px: -1,\n        },\n        pt: {\n          mm: 25.4 / 72,\n          cm: 2.54 / 72,\n          in: 1 / 72,\n          pt: 1,\n          pc: 6 / 72,\n          px: -1,\n        },\n        pc: {\n          mm: 25.4 / 6,\n          cm: 2.54 / 6,\n          in: 1 / 6,\n          pt: 72 / 6,\n          pc: 1,\n          px: -1,\n        },\n        px: {\n          px: 1,\n        },\n      }\n\n      function parseFloatWithUnits(string) {\n        let theUnit = 'px'\n\n        if (typeof string === 'string' || string instanceof String) {\n          for (let i = 0, n = units.length; i < n; i++) {\n            const u = units[i]\n\n            if (string.endsWith(u)) {\n              theUnit = u\n              string = string.substring(0, string.length - u.length)\n              break\n            }\n          }\n        }\n\n        let scale = undefined\n\n        if (theUnit === 'px' && scope.defaultUnit !== 'px') {\n          // Conversion scale from  pixels to inches, then to default units\n\n          scale = unitConversion['in'][scope.defaultUnit] / scope.defaultDPI\n        } else {\n          scale = unitConversion[theUnit][scope.defaultUnit]\n\n          if (scale < 0) {\n            // Conversion scale to pixels\n\n            scale = unitConversion[theUnit]['in'] * scope.defaultDPI\n          }\n        }\n\n        return scale * parseFloat(string)\n      }\n\n      // Transforms\n\n      function getNodeTransform(node) {\n        if (\n          !(\n            node.hasAttribute('transform') ||\n            (node.nodeName === 'use' && (node.hasAttribute('x') || node.hasAttribute('y')))\n          )\n        ) {\n          return null\n        }\n\n        const transform = parseNodeTransform(node)\n\n        if (transformStack.length > 0) {\n          transform.premultiply(transformStack[transformStack.length - 1])\n        }\n\n        currentTransform.copy(transform)\n        transformStack.push(transform)\n\n        return transform\n      }\n\n      function parseNodeTransform(node) {\n        const transform = new Matrix3()\n        const currentTransform = tempTransform0\n\n        if (node.nodeName === 'use' && (node.hasAttribute('x') || node.hasAttribute('y'))) {\n          const tx = parseFloatWithUnits(node.getAttribute('x'))\n          const ty = parseFloatWithUnits(node.getAttribute('y'))\n\n          transform.translate(tx, ty)\n        }\n\n        if (node.hasAttribute('transform')) {\n          const transformsTexts = node.getAttribute('transform').split(')')\n\n          for (let tIndex = transformsTexts.length - 1; tIndex >= 0; tIndex--) {\n            const transformText = transformsTexts[tIndex].trim()\n\n            if (transformText === '') continue\n\n            const openParPos = transformText.indexOf('(')\n            const closeParPos = transformText.length\n\n            if (openParPos > 0 && openParPos < closeParPos) {\n              const transformType = transformText.slice(0, openParPos)\n\n              const array = parseFloats(transformText.slice(openParPos + 1))\n\n              currentTransform.identity()\n\n              switch (transformType) {\n                case 'translate':\n                  if (array.length >= 1) {\n                    const tx = array[0]\n                    let ty = 0\n\n                    if (array.length >= 2) {\n                      ty = array[1]\n                    }\n\n                    currentTransform.translate(tx, ty)\n                  }\n\n                  break\n\n                case 'rotate':\n                  if (array.length >= 1) {\n                    let angle = 0\n                    let cx = 0\n                    let cy = 0\n\n                    // Angle\n                    angle = (array[0] * Math.PI) / 180\n\n                    if (array.length >= 3) {\n                      // Center x, y\n                      cx = array[1]\n                      cy = array[2]\n                    }\n\n                    // Rotate around center (cx, cy)\n                    tempTransform1.makeTranslation(-cx, -cy)\n                    tempTransform2.makeRotation(angle)\n                    tempTransform3.multiplyMatrices(tempTransform2, tempTransform1)\n                    tempTransform1.makeTranslation(cx, cy)\n                    currentTransform.multiplyMatrices(tempTransform1, tempTransform3)\n                  }\n\n                  break\n\n                case 'scale':\n                  if (array.length >= 1) {\n                    const scaleX = array[0]\n                    let scaleY = scaleX\n\n                    if (array.length >= 2) {\n                      scaleY = array[1]\n                    }\n\n                    currentTransform.scale(scaleX, scaleY)\n                  }\n\n                  break\n\n                case 'skewX':\n                  if (array.length === 1) {\n                    currentTransform.set(1, Math.tan((array[0] * Math.PI) / 180), 0, 0, 1, 0, 0, 0, 1)\n                  }\n\n                  break\n\n                case 'skewY':\n                  if (array.length === 1) {\n                    currentTransform.set(1, 0, 0, Math.tan((array[0] * Math.PI) / 180), 1, 0, 0, 0, 1)\n                  }\n\n                  break\n\n                case 'matrix':\n                  if (array.length === 6) {\n                    currentTransform.set(array[0], array[2], array[4], array[1], array[3], array[5], 0, 0, 1)\n                  }\n\n                  break\n              }\n            }\n\n            transform.premultiply(currentTransform)\n          }\n        }\n\n        return transform\n      }\n\n      function transformPath(path, m) {\n        function transfVec2(v2) {\n          tempV3.set(v2.x, v2.y, 1).applyMatrix3(m)\n\n          v2.set(tempV3.x, tempV3.y)\n        }\n\n        function transfEllipseGeneric(curve) {\n          // For math description see:\n          // https://math.stackexchange.com/questions/4544164\n\n          const a = curve.xRadius\n          const b = curve.yRadius\n\n          const cosTheta = Math.cos(curve.aRotation)\n          const sinTheta = Math.sin(curve.aRotation)\n\n          const v1 = new Vector3(a * cosTheta, a * sinTheta, 0)\n          const v2 = new Vector3(-b * sinTheta, b * cosTheta, 0)\n\n          const f1 = v1.applyMatrix3(m)\n          const f2 = v2.applyMatrix3(m)\n\n          const mF = tempTransform0.set(f1.x, f2.x, 0, f1.y, f2.y, 0, 0, 0, 1)\n\n          const mFInv = tempTransform1.copy(mF).invert()\n          const mFInvT = tempTransform2.copy(mFInv).transpose()\n          const mQ = mFInvT.multiply(mFInv)\n          const mQe = mQ.elements\n\n          const ed = eigenDecomposition(mQe[0], mQe[1], mQe[4])\n          const rt1sqrt = Math.sqrt(ed.rt1)\n          const rt2sqrt = Math.sqrt(ed.rt2)\n\n          curve.xRadius = 1 / rt1sqrt\n          curve.yRadius = 1 / rt2sqrt\n          curve.aRotation = Math.atan2(ed.sn, ed.cs)\n\n          const isFullEllipse = (curve.aEndAngle - curve.aStartAngle) % (2 * Math.PI) < Number.EPSILON\n\n          // Do not touch angles of a full ellipse because after transformation they\n          // would converge to a sinle value effectively removing the whole curve\n\n          if (!isFullEllipse) {\n            const mDsqrt = tempTransform1.set(rt1sqrt, 0, 0, 0, rt2sqrt, 0, 0, 0, 1)\n\n            const mRT = tempTransform2.set(ed.cs, ed.sn, 0, -ed.sn, ed.cs, 0, 0, 0, 1)\n\n            const mDRF = mDsqrt.multiply(mRT).multiply(mF)\n\n            const transformAngle = (phi) => {\n              const { x: cosR, y: sinR } = new Vector3(Math.cos(phi), Math.sin(phi), 0).applyMatrix3(mDRF)\n\n              return Math.atan2(sinR, cosR)\n            }\n\n            curve.aStartAngle = transformAngle(curve.aStartAngle)\n            curve.aEndAngle = transformAngle(curve.aEndAngle)\n\n            if (isTransformFlipped(m)) {\n              curve.aClockwise = !curve.aClockwise\n            }\n          }\n        }\n\n        function transfEllipseNoSkew(curve) {\n          // Faster shortcut if no skew is applied\n          // (e.g, a euclidean transform of a group containing the ellipse)\n\n          const sx = getTransformScaleX(m)\n          const sy = getTransformScaleY(m)\n\n          curve.xRadius *= sx\n          curve.yRadius *= sy\n\n          // Extract rotation angle from the matrix of form:\n          //\n          //  | cosθ sx   -sinθ sy |\n          //  | sinθ sx    cosθ sy |\n          //\n          // Remembering that tanθ = sinθ / cosθ; and that\n          // `sx`, `sy`, or both might be zero.\n          const theta =\n            sx > Number.EPSILON ? Math.atan2(m.elements[1], m.elements[0]) : Math.atan2(-m.elements[3], m.elements[4])\n\n          curve.aRotation += theta\n\n          if (isTransformFlipped(m)) {\n            curve.aStartAngle *= -1\n            curve.aEndAngle *= -1\n            curve.aClockwise = !curve.aClockwise\n          }\n        }\n\n        const subPaths = path.subPaths\n\n        for (let i = 0, n = subPaths.length; i < n; i++) {\n          const subPath = subPaths[i]\n          const curves = subPath.curves\n\n          for (let j = 0; j < curves.length; j++) {\n            const curve = curves[j]\n\n            if (curve.isLineCurve) {\n              transfVec2(curve.v1)\n              transfVec2(curve.v2)\n            } else if (curve.isCubicBezierCurve) {\n              transfVec2(curve.v0)\n              transfVec2(curve.v1)\n              transfVec2(curve.v2)\n              transfVec2(curve.v3)\n            } else if (curve.isQuadraticBezierCurve) {\n              transfVec2(curve.v0)\n              transfVec2(curve.v1)\n              transfVec2(curve.v2)\n            } else if (curve.isEllipseCurve) {\n              // Transform ellipse center point\n\n              tempV2.set(curve.aX, curve.aY)\n              transfVec2(tempV2)\n              curve.aX = tempV2.x\n              curve.aY = tempV2.y\n\n              // Transform ellipse shape parameters\n\n              if (isTransformSkewed(m)) {\n                transfEllipseGeneric(curve)\n              } else {\n                transfEllipseNoSkew(curve)\n              }\n            }\n          }\n        }\n      }\n\n      function isTransformFlipped(m) {\n        const te = m.elements\n        return te[0] * te[4] - te[1] * te[3] < 0\n      }\n\n      function isTransformSkewed(m) {\n        const te = m.elements\n        const basisDot = te[0] * te[3] + te[1] * te[4]\n\n        // Shortcut for trivial rotations and transformations\n        if (basisDot === 0) return false\n\n        const sx = getTransformScaleX(m)\n        const sy = getTransformScaleY(m)\n\n        return Math.abs(basisDot / (sx * sy)) > Number.EPSILON\n      }\n\n      function getTransformScaleX(m) {\n        const te = m.elements\n        return Math.sqrt(te[0] * te[0] + te[1] * te[1])\n      }\n\n      function getTransformScaleY(m) {\n        const te = m.elements\n        return Math.sqrt(te[3] * te[3] + te[4] * te[4])\n      }\n\n      // Calculates the eigensystem of a real symmetric 2x2 matrix\n      //    [ A  B ]\n      //    [ B  C ]\n      // in the form\n      //    [ A  B ]  =  [ cs  -sn ] [ rt1   0  ] [  cs  sn ]\n      //    [ B  C ]     [ sn   cs ] [  0   rt2 ] [ -sn  cs ]\n      // where rt1 >= rt2.\n      //\n      // Adapted from: https://www.mpi-hd.mpg.de/personalhomes/globes/3x3/index.html\n      // -> Algorithms for real symmetric matrices -> Analytical (2x2 symmetric)\n      function eigenDecomposition(A, B, C) {\n        let rt1, rt2, cs, sn, t\n        const sm = A + C\n        const df = A - C\n        const rt = Math.sqrt(df * df + 4 * B * B)\n\n        if (sm > 0) {\n          rt1 = 0.5 * (sm + rt)\n          t = 1 / rt1\n          rt2 = A * t * C - B * t * B\n        } else if (sm < 0) {\n          rt2 = 0.5 * (sm - rt)\n        } else {\n          // This case needs to be treated separately to avoid div by 0\n\n          rt1 = 0.5 * rt\n          rt2 = -0.5 * rt\n        }\n\n        // Calculate eigenvectors\n\n        if (df > 0) {\n          cs = df + rt\n        } else {\n          cs = df - rt\n        }\n\n        if (Math.abs(cs) > 2 * Math.abs(B)) {\n          t = (-2 * B) / cs\n          sn = 1 / Math.sqrt(1 + t * t)\n          cs = t * sn\n        } else if (Math.abs(B) === 0) {\n          cs = 1\n          sn = 0\n        } else {\n          t = (-0.5 * cs) / B\n          cs = 1 / Math.sqrt(1 + t * t)\n          sn = t * cs\n        }\n\n        if (df > 0) {\n          t = cs\n          cs = -sn\n          sn = t\n        }\n\n        return { rt1, rt2, cs, sn }\n      }\n\n      //\n\n      const paths = []\n      const stylesheets = {}\n\n      const transformStack = []\n\n      const tempTransform0 = new Matrix3()\n      const tempTransform1 = new Matrix3()\n      const tempTransform2 = new Matrix3()\n      const tempTransform3 = new Matrix3()\n      const tempV2 = new Vector2()\n      const tempV3 = new Vector3()\n\n      const currentTransform = new Matrix3()\n\n      const xml = new DOMParser().parseFromString(text, 'image/svg+xml') // application/xml\n\n      parseNode(xml.documentElement, {\n        fill: '#000',\n        fillOpacity: 1,\n        strokeOpacity: 1,\n        strokeWidth: 1,\n        strokeLineJoin: 'miter',\n        strokeLineCap: 'butt',\n        strokeMiterLimit: 4,\n      })\n\n      const data = { paths: paths, xml: xml.documentElement }\n\n      // console.log( paths );\n      return data\n    }\n\n    static createShapes(shapePath) {\n      // Param shapePath: a shapepath as returned by the parse function of this class\n      // Returns Shape object\n\n      const BIGNUMBER = 999999999\n\n      const IntersectionLocationType = {\n        ORIGIN: 0,\n        DESTINATION: 1,\n        BETWEEN: 2,\n        LEFT: 3,\n        RIGHT: 4,\n        BEHIND: 5,\n        BEYOND: 6,\n      }\n\n      const classifyResult = {\n        loc: IntersectionLocationType.ORIGIN,\n        t: 0,\n      }\n\n      function findEdgeIntersection(a0, a1, b0, b1) {\n        const x1 = a0.x\n        const x2 = a1.x\n        const x3 = b0.x\n        const x4 = b1.x\n        const y1 = a0.y\n        const y2 = a1.y\n        const y3 = b0.y\n        const y4 = b1.y\n        const nom1 = (x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)\n        const nom2 = (x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)\n        const denom = (y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1)\n        const t1 = nom1 / denom\n        const t2 = nom2 / denom\n\n        if ((denom === 0 && nom1 !== 0) || t1 <= 0 || t1 >= 1 || t2 < 0 || t2 > 1) {\n          //1. lines are parallel or edges don't intersect\n\n          return null\n        } else if (nom1 === 0 && denom === 0) {\n          //2. lines are colinear\n\n          //check if endpoints of edge2 (b0-b1) lies on edge1 (a0-a1)\n          for (let i = 0; i < 2; i++) {\n            classifyPoint(i === 0 ? b0 : b1, a0, a1)\n            //find position of this endpoints relatively to edge1\n            if (classifyResult.loc == IntersectionLocationType.ORIGIN) {\n              const point = i === 0 ? b0 : b1\n              return { x: point.x, y: point.y, t: classifyResult.t }\n            } else if (classifyResult.loc == IntersectionLocationType.BETWEEN) {\n              const x = +(x1 + classifyResult.t * (x2 - x1)).toPrecision(10)\n              const y = +(y1 + classifyResult.t * (y2 - y1)).toPrecision(10)\n              return { x: x, y: y, t: classifyResult.t }\n            }\n          }\n\n          return null\n        } else {\n          //3. edges intersect\n\n          for (let i = 0; i < 2; i++) {\n            classifyPoint(i === 0 ? b0 : b1, a0, a1)\n\n            if (classifyResult.loc == IntersectionLocationType.ORIGIN) {\n              const point = i === 0 ? b0 : b1\n              return { x: point.x, y: point.y, t: classifyResult.t }\n            }\n          }\n\n          const x = +(x1 + t1 * (x2 - x1)).toPrecision(10)\n          const y = +(y1 + t1 * (y2 - y1)).toPrecision(10)\n          return { x: x, y: y, t: t1 }\n        }\n      }\n\n      function classifyPoint(p, edgeStart, edgeEnd) {\n        const ax = edgeEnd.x - edgeStart.x\n        const ay = edgeEnd.y - edgeStart.y\n        const bx = p.x - edgeStart.x\n        const by = p.y - edgeStart.y\n        const sa = ax * by - bx * ay\n\n        if (p.x === edgeStart.x && p.y === edgeStart.y) {\n          classifyResult.loc = IntersectionLocationType.ORIGIN\n          classifyResult.t = 0\n          return\n        }\n\n        if (p.x === edgeEnd.x && p.y === edgeEnd.y) {\n          classifyResult.loc = IntersectionLocationType.DESTINATION\n          classifyResult.t = 1\n          return\n        }\n\n        if (sa < -Number.EPSILON) {\n          classifyResult.loc = IntersectionLocationType.LEFT\n          return\n        }\n\n        if (sa > Number.EPSILON) {\n          classifyResult.loc = IntersectionLocationType.RIGHT\n          return\n        }\n\n        if (ax * bx < 0 || ay * by < 0) {\n          classifyResult.loc = IntersectionLocationType.BEHIND\n          return\n        }\n\n        if (Math.sqrt(ax * ax + ay * ay) < Math.sqrt(bx * bx + by * by)) {\n          classifyResult.loc = IntersectionLocationType.BEYOND\n          return\n        }\n\n        let t\n\n        if (ax !== 0) {\n          t = bx / ax\n        } else {\n          t = by / ay\n        }\n\n        classifyResult.loc = IntersectionLocationType.BETWEEN\n        classifyResult.t = t\n      }\n\n      function getIntersections(path1, path2) {\n        const intersectionsRaw = []\n        const intersections = []\n\n        for (let index = 1; index < path1.length; index++) {\n          const path1EdgeStart = path1[index - 1]\n          const path1EdgeEnd = path1[index]\n\n          for (let index2 = 1; index2 < path2.length; index2++) {\n            const path2EdgeStart = path2[index2 - 1]\n            const path2EdgeEnd = path2[index2]\n\n            const intersection = findEdgeIntersection(path1EdgeStart, path1EdgeEnd, path2EdgeStart, path2EdgeEnd)\n\n            if (\n              intersection !== null &&\n              intersectionsRaw.find(\n                (i) => i.t <= intersection.t + Number.EPSILON && i.t >= intersection.t - Number.EPSILON,\n              ) === undefined\n            ) {\n              intersectionsRaw.push(intersection)\n              intersections.push(new Vector2(intersection.x, intersection.y))\n            }\n          }\n        }\n\n        return intersections\n      }\n\n      function getScanlineIntersections(scanline, boundingBox, paths) {\n        const center = new Vector2()\n        boundingBox.getCenter(center)\n\n        const allIntersections = []\n\n        paths.forEach((path) => {\n          // check if the center of the bounding box is in the bounding box of the paths.\n          // this is a pruning method to limit the search of intersections in paths that can't envelop of the current path.\n          // if a path envelops another path. The center of that oter path, has to be inside the bounding box of the enveloping path.\n          if (path.boundingBox.containsPoint(center)) {\n            const intersections = getIntersections(scanline, path.points)\n\n            intersections.forEach((p) => {\n              allIntersections.push({ identifier: path.identifier, isCW: path.isCW, point: p })\n            })\n          }\n        })\n\n        allIntersections.sort((i1, i2) => {\n          return i1.point.x - i2.point.x\n        })\n\n        return allIntersections\n      }\n\n      function isHoleTo(simplePath, allPaths, scanlineMinX, scanlineMaxX, _fillRule) {\n        if (_fillRule === null || _fillRule === undefined || _fillRule === '') {\n          _fillRule = 'nonzero'\n        }\n\n        const centerBoundingBox = new Vector2()\n        simplePath.boundingBox.getCenter(centerBoundingBox)\n\n        const scanline = [\n          new Vector2(scanlineMinX, centerBoundingBox.y),\n          new Vector2(scanlineMaxX, centerBoundingBox.y),\n        ]\n\n        const scanlineIntersections = getScanlineIntersections(scanline, simplePath.boundingBox, allPaths)\n\n        scanlineIntersections.sort((i1, i2) => {\n          return i1.point.x - i2.point.x\n        })\n\n        const baseIntersections = []\n        const otherIntersections = []\n\n        scanlineIntersections.forEach((i) => {\n          if (i.identifier === simplePath.identifier) {\n            baseIntersections.push(i)\n          } else {\n            otherIntersections.push(i)\n          }\n        })\n\n        const firstXOfPath = baseIntersections[0].point.x\n\n        // build up the path hierarchy\n        const stack = []\n        let i = 0\n\n        while (i < otherIntersections.length && otherIntersections[i].point.x < firstXOfPath) {\n          if (stack.length > 0 && stack[stack.length - 1] === otherIntersections[i].identifier) {\n            stack.pop()\n          } else {\n            stack.push(otherIntersections[i].identifier)\n          }\n\n          i++\n        }\n\n        stack.push(simplePath.identifier)\n\n        if (_fillRule === 'evenodd') {\n          const isHole = stack.length % 2 === 0 ? true : false\n          const isHoleFor = stack[stack.length - 2]\n\n          return { identifier: simplePath.identifier, isHole: isHole, for: isHoleFor }\n        } else if (_fillRule === 'nonzero') {\n          // check if path is a hole by counting the amount of paths with alternating rotations it has to cross.\n          let isHole = true\n          let isHoleFor = null\n          let lastCWValue = null\n\n          for (let i = 0; i < stack.length; i++) {\n            const identifier = stack[i]\n            if (isHole) {\n              lastCWValue = allPaths[identifier].isCW\n              isHole = false\n              isHoleFor = identifier\n            } else if (lastCWValue !== allPaths[identifier].isCW) {\n              lastCWValue = allPaths[identifier].isCW\n              isHole = true\n            }\n          }\n\n          return { identifier: simplePath.identifier, isHole: isHole, for: isHoleFor }\n        } else {\n          console.warn('fill-rule: \"' + _fillRule + '\" is currently not implemented.')\n        }\n      }\n\n      // check for self intersecting paths\n      // TODO\n\n      // check intersecting paths\n      // TODO\n\n      // prepare paths for hole detection\n      let scanlineMinX = BIGNUMBER\n      let scanlineMaxX = -BIGNUMBER\n\n      let simplePaths = shapePath.subPaths.map((p) => {\n        const points = p.getPoints()\n        let maxY = -BIGNUMBER\n        let minY = BIGNUMBER\n        let maxX = -BIGNUMBER\n        let minX = BIGNUMBER\n\n        //points.forEach(p => p.y *= -1);\n\n        for (let i = 0; i < points.length; i++) {\n          const p = points[i]\n\n          if (p.y > maxY) {\n            maxY = p.y\n          }\n\n          if (p.y < minY) {\n            minY = p.y\n          }\n\n          if (p.x > maxX) {\n            maxX = p.x\n          }\n\n          if (p.x < minX) {\n            minX = p.x\n          }\n        }\n\n        //\n        if (scanlineMaxX <= maxX) {\n          scanlineMaxX = maxX + 1\n        }\n\n        if (scanlineMinX >= minX) {\n          scanlineMinX = minX - 1\n        }\n\n        return {\n          curves: p.curves,\n          points: points,\n          isCW: ShapeUtils.isClockWise(points),\n          identifier: -1,\n          boundingBox: new Box2(new Vector2(minX, minY), new Vector2(maxX, maxY)),\n        }\n      })\n\n      simplePaths = simplePaths.filter((sp) => sp.points.length > 1)\n\n      for (let identifier = 0; identifier < simplePaths.length; identifier++) {\n        simplePaths[identifier].identifier = identifier\n      }\n\n      // check if path is solid or a hole\n      const isAHole = simplePaths.map((p) =>\n        isHoleTo(\n          p,\n          simplePaths,\n          scanlineMinX,\n          scanlineMaxX,\n          shapePath.userData ? shapePath.userData.style.fillRule : undefined,\n        ),\n      )\n\n      const shapesToReturn = []\n      simplePaths.forEach((p) => {\n        const amIAHole = isAHole[p.identifier]\n\n        if (!amIAHole.isHole) {\n          const shape = new Shape()\n          shape.curves = p.curves\n          const holes = isAHole.filter((h) => h.isHole && h.for === p.identifier)\n          holes.forEach((h) => {\n            const hole = simplePaths[h.identifier]\n            const path = new Path()\n            path.curves = hole.curves\n            shape.holes.push(path)\n          })\n          shapesToReturn.push(shape)\n        }\n      })\n\n      return shapesToReturn\n    }\n\n    static getStrokeStyle(width, color, lineJoin, lineCap, miterLimit) {\n      // Param width: Stroke width\n      // Param color: As returned by THREE.Color.getStyle()\n      // Param lineJoin: One of \"round\", \"bevel\", \"miter\" or \"miter-limit\"\n      // Param lineCap: One of \"round\", \"square\" or \"butt\"\n      // Param miterLimit: Maximum join length, in multiples of the \"width\" parameter (join is truncated if it exceeds that distance)\n      // Returns style object\n\n      width = width !== undefined ? width : 1\n      color = color !== undefined ? color : '#000'\n      lineJoin = lineJoin !== undefined ? lineJoin : 'miter'\n      lineCap = lineCap !== undefined ? lineCap : 'butt'\n      miterLimit = miterLimit !== undefined ? miterLimit : 4\n\n      return {\n        strokeColor: color,\n        strokeWidth: width,\n        strokeLineJoin: lineJoin,\n        strokeLineCap: lineCap,\n        strokeMiterLimit: miterLimit,\n      }\n    }\n\n    static pointsToStroke(points, style, arcDivisions, minDistance) {\n      // Generates a stroke with some width around the given path.\n      // The path can be open or closed (last point equals to first point)\n      // Param points: Array of Vector2D (the path). Minimum 2 points.\n      // Param style: Object with SVG properties as returned by SVGLoader.getStrokeStyle(), or SVGLoader.parse() in the path.userData.style object\n      // Params arcDivisions: Arc divisions for round joins and endcaps. (Optional)\n      // Param minDistance: Points closer to this distance will be merged. (Optional)\n      // Returns BufferGeometry with stroke triangles (In plane z = 0). UV coordinates are generated ('u' along path. 'v' across it, from left to right)\n\n      const vertices = []\n      const normals = []\n      const uvs = []\n\n      if (SVGLoader.pointsToStrokeWithBuffers(points, style, arcDivisions, minDistance, vertices, normals, uvs) === 0) {\n        return null\n      }\n\n      const geometry = new BufferGeometry()\n      geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n      geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n      geometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n\n      return geometry\n    }\n\n    static pointsToStrokeWithBuffers(points, style, arcDivisions, minDistance, vertices, normals, uvs, vertexOffset) {\n      // This function can be called to update existing arrays or buffers.\n      // Accepts same parameters as pointsToStroke, plus the buffers and optional offset.\n      // Param vertexOffset: Offset vertices to start writing in the buffers (3 elements/vertex for vertices and normals, and 2 elements/vertex for uvs)\n      // Returns number of written vertices / normals / uvs pairs\n      // if 'vertices' parameter is undefined no triangles will be generated, but the returned vertices count will still be valid (useful to preallocate the buffers)\n      // 'normals' and 'uvs' buffers are optional\n\n      const tempV2_1 = new Vector2()\n      const tempV2_2 = new Vector2()\n      const tempV2_3 = new Vector2()\n      const tempV2_4 = new Vector2()\n      const tempV2_5 = new Vector2()\n      const tempV2_6 = new Vector2()\n      const tempV2_7 = new Vector2()\n      const lastPointL = new Vector2()\n      const lastPointR = new Vector2()\n      const point0L = new Vector2()\n      const point0R = new Vector2()\n      const currentPointL = new Vector2()\n      const currentPointR = new Vector2()\n      const nextPointL = new Vector2()\n      const nextPointR = new Vector2()\n      const innerPoint = new Vector2()\n      const outerPoint = new Vector2()\n\n      arcDivisions = arcDivisions !== undefined ? arcDivisions : 12\n      minDistance = minDistance !== undefined ? minDistance : 0.001\n      vertexOffset = vertexOffset !== undefined ? vertexOffset : 0\n\n      // First ensure there are no duplicated points\n      points = removeDuplicatedPoints(points)\n\n      const numPoints = points.length\n\n      if (numPoints < 2) return 0\n\n      const isClosed = points[0].equals(points[numPoints - 1])\n\n      let currentPoint\n      let previousPoint = points[0]\n      let nextPoint\n\n      const strokeWidth2 = style.strokeWidth / 2\n\n      const deltaU = 1 / (numPoints - 1)\n      let u0 = 0,\n        u1\n\n      let innerSideModified\n      let joinIsOnLeftSide\n      let isMiter\n      let initialJoinIsOnLeftSide = false\n\n      let numVertices = 0\n      let currentCoordinate = vertexOffset * 3\n      let currentCoordinateUV = vertexOffset * 2\n\n      // Get initial left and right stroke points\n      getNormal(points[0], points[1], tempV2_1).multiplyScalar(strokeWidth2)\n      lastPointL.copy(points[0]).sub(tempV2_1)\n      lastPointR.copy(points[0]).add(tempV2_1)\n      point0L.copy(lastPointL)\n      point0R.copy(lastPointR)\n\n      for (let iPoint = 1; iPoint < numPoints; iPoint++) {\n        currentPoint = points[iPoint]\n\n        // Get next point\n        if (iPoint === numPoints - 1) {\n          if (isClosed) {\n            // Skip duplicated initial point\n            nextPoint = points[1]\n          } else nextPoint = undefined\n        } else {\n          nextPoint = points[iPoint + 1]\n        }\n\n        // Normal of previous segment in tempV2_1\n        const normal1 = tempV2_1\n        getNormal(previousPoint, currentPoint, normal1)\n\n        tempV2_3.copy(normal1).multiplyScalar(strokeWidth2)\n        currentPointL.copy(currentPoint).sub(tempV2_3)\n        currentPointR.copy(currentPoint).add(tempV2_3)\n\n        u1 = u0 + deltaU\n\n        innerSideModified = false\n\n        if (nextPoint !== undefined) {\n          // Normal of next segment in tempV2_2\n          getNormal(currentPoint, nextPoint, tempV2_2)\n\n          tempV2_3.copy(tempV2_2).multiplyScalar(strokeWidth2)\n          nextPointL.copy(currentPoint).sub(tempV2_3)\n          nextPointR.copy(currentPoint).add(tempV2_3)\n\n          joinIsOnLeftSide = true\n          tempV2_3.subVectors(nextPoint, previousPoint)\n          if (normal1.dot(tempV2_3) < 0) {\n            joinIsOnLeftSide = false\n          }\n\n          if (iPoint === 1) initialJoinIsOnLeftSide = joinIsOnLeftSide\n\n          tempV2_3.subVectors(nextPoint, currentPoint)\n          tempV2_3.normalize()\n          const dot = Math.abs(normal1.dot(tempV2_3))\n\n          // If path is straight, don't create join\n          if (dot > Number.EPSILON) {\n            // Compute inner and outer segment intersections\n            const miterSide = strokeWidth2 / dot\n            tempV2_3.multiplyScalar(-miterSide)\n            tempV2_4.subVectors(currentPoint, previousPoint)\n            tempV2_5.copy(tempV2_4).setLength(miterSide).add(tempV2_3)\n            innerPoint.copy(tempV2_5).negate()\n            const miterLength2 = tempV2_5.length()\n            const segmentLengthPrev = tempV2_4.length()\n            tempV2_4.divideScalar(segmentLengthPrev)\n            tempV2_6.subVectors(nextPoint, currentPoint)\n            const segmentLengthNext = tempV2_6.length()\n            tempV2_6.divideScalar(segmentLengthNext)\n            // Check that previous and next segments doesn't overlap with the innerPoint of intersection\n            if (tempV2_4.dot(innerPoint) < segmentLengthPrev && tempV2_6.dot(innerPoint) < segmentLengthNext) {\n              innerSideModified = true\n            }\n\n            outerPoint.copy(tempV2_5).add(currentPoint)\n            innerPoint.add(currentPoint)\n\n            isMiter = false\n\n            if (innerSideModified) {\n              if (joinIsOnLeftSide) {\n                nextPointR.copy(innerPoint)\n                currentPointR.copy(innerPoint)\n              } else {\n                nextPointL.copy(innerPoint)\n                currentPointL.copy(innerPoint)\n              }\n            } else {\n              // The segment triangles are generated here if there was overlapping\n\n              makeSegmentTriangles()\n            }\n\n            switch (style.strokeLineJoin) {\n              case 'bevel':\n                makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u1)\n\n                break\n\n              case 'round':\n                // Segment triangles\n\n                createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified)\n\n                // Join triangles\n\n                if (joinIsOnLeftSide) {\n                  makeCircularSector(currentPoint, currentPointL, nextPointL, u1, 0)\n                } else {\n                  makeCircularSector(currentPoint, nextPointR, currentPointR, u1, 1)\n                }\n\n                break\n\n              case 'miter':\n              case 'miter-clip':\n              default:\n                const miterFraction = (strokeWidth2 * style.strokeMiterLimit) / miterLength2\n\n                if (miterFraction < 1) {\n                  // The join miter length exceeds the miter limit\n\n                  if (style.strokeLineJoin !== 'miter-clip') {\n                    makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u1)\n                    break\n                  } else {\n                    // Segment triangles\n\n                    createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified)\n\n                    // Miter-clip join triangles\n\n                    if (joinIsOnLeftSide) {\n                      tempV2_6.subVectors(outerPoint, currentPointL).multiplyScalar(miterFraction).add(currentPointL)\n                      tempV2_7.subVectors(outerPoint, nextPointL).multiplyScalar(miterFraction).add(nextPointL)\n\n                      addVertex(currentPointL, u1, 0)\n                      addVertex(tempV2_6, u1, 0)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_6, u1, 0)\n                      addVertex(tempV2_7, u1, 0)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_7, u1, 0)\n                      addVertex(nextPointL, u1, 0)\n                    } else {\n                      tempV2_6.subVectors(outerPoint, currentPointR).multiplyScalar(miterFraction).add(currentPointR)\n                      tempV2_7.subVectors(outerPoint, nextPointR).multiplyScalar(miterFraction).add(nextPointR)\n\n                      addVertex(currentPointR, u1, 1)\n                      addVertex(tempV2_6, u1, 1)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_6, u1, 1)\n                      addVertex(tempV2_7, u1, 1)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_7, u1, 1)\n                      addVertex(nextPointR, u1, 1)\n                    }\n                  }\n                } else {\n                  // Miter join segment triangles\n\n                  if (innerSideModified) {\n                    // Optimized segment + join triangles\n\n                    if (joinIsOnLeftSide) {\n                      addVertex(lastPointR, u0, 1)\n                      addVertex(lastPointL, u0, 0)\n                      addVertex(outerPoint, u1, 0)\n\n                      addVertex(lastPointR, u0, 1)\n                      addVertex(outerPoint, u1, 0)\n                      addVertex(innerPoint, u1, 1)\n                    } else {\n                      addVertex(lastPointR, u0, 1)\n                      addVertex(lastPointL, u0, 0)\n                      addVertex(outerPoint, u1, 1)\n\n                      addVertex(lastPointL, u0, 0)\n                      addVertex(innerPoint, u1, 0)\n                      addVertex(outerPoint, u1, 1)\n                    }\n\n                    if (joinIsOnLeftSide) {\n                      nextPointL.copy(outerPoint)\n                    } else {\n                      nextPointR.copy(outerPoint)\n                    }\n                  } else {\n                    // Add extra miter join triangles\n\n                    if (joinIsOnLeftSide) {\n                      addVertex(currentPointL, u1, 0)\n                      addVertex(outerPoint, u1, 0)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(outerPoint, u1, 0)\n                      addVertex(nextPointL, u1, 0)\n                    } else {\n                      addVertex(currentPointR, u1, 1)\n                      addVertex(outerPoint, u1, 1)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(outerPoint, u1, 1)\n                      addVertex(nextPointR, u1, 1)\n                    }\n                  }\n\n                  isMiter = true\n                }\n\n                break\n            }\n          } else {\n            // The segment triangles are generated here when two consecutive points are collinear\n\n            makeSegmentTriangles()\n          }\n        } else {\n          // The segment triangles are generated here if it is the ending segment\n\n          makeSegmentTriangles()\n        }\n\n        if (!isClosed && iPoint === numPoints - 1) {\n          // Start line endcap\n          addCapGeometry(points[0], point0L, point0R, joinIsOnLeftSide, true, u0)\n        }\n\n        // Increment loop variables\n\n        u0 = u1\n\n        previousPoint = currentPoint\n\n        lastPointL.copy(nextPointL)\n        lastPointR.copy(nextPointR)\n      }\n\n      if (!isClosed) {\n        // Ending line endcap\n        addCapGeometry(currentPoint, currentPointL, currentPointR, joinIsOnLeftSide, false, u1)\n      } else if (innerSideModified && vertices) {\n        // Modify path first segment vertices to adjust to the segments inner and outer intersections\n\n        let lastOuter = outerPoint\n        let lastInner = innerPoint\n\n        if (initialJoinIsOnLeftSide !== joinIsOnLeftSide) {\n          lastOuter = innerPoint\n          lastInner = outerPoint\n        }\n\n        if (joinIsOnLeftSide) {\n          if (isMiter || initialJoinIsOnLeftSide) {\n            lastInner.toArray(vertices, 0 * 3)\n            lastInner.toArray(vertices, 3 * 3)\n\n            if (isMiter) {\n              lastOuter.toArray(vertices, 1 * 3)\n            }\n          }\n        } else {\n          if (isMiter || !initialJoinIsOnLeftSide) {\n            lastInner.toArray(vertices, 1 * 3)\n            lastInner.toArray(vertices, 3 * 3)\n\n            if (isMiter) {\n              lastOuter.toArray(vertices, 0 * 3)\n            }\n          }\n        }\n      }\n\n      return numVertices\n\n      // -- End of algorithm\n\n      // -- Functions\n\n      function getNormal(p1, p2, result) {\n        result.subVectors(p2, p1)\n        return result.set(-result.y, result.x).normalize()\n      }\n\n      function addVertex(position, u, v) {\n        if (vertices) {\n          vertices[currentCoordinate] = position.x\n          vertices[currentCoordinate + 1] = position.y\n          vertices[currentCoordinate + 2] = 0\n\n          if (normals) {\n            normals[currentCoordinate] = 0\n            normals[currentCoordinate + 1] = 0\n            normals[currentCoordinate + 2] = 1\n          }\n\n          currentCoordinate += 3\n\n          if (uvs) {\n            uvs[currentCoordinateUV] = u\n            uvs[currentCoordinateUV + 1] = v\n\n            currentCoordinateUV += 2\n          }\n        }\n\n        numVertices += 3\n      }\n\n      function makeCircularSector(center, p1, p2, u, v) {\n        // param p1, p2: Points in the circle arc.\n        // p1 and p2 are in clockwise direction.\n\n        tempV2_1.copy(p1).sub(center).normalize()\n        tempV2_2.copy(p2).sub(center).normalize()\n\n        let angle = Math.PI\n        const dot = tempV2_1.dot(tempV2_2)\n        if (Math.abs(dot) < 1) angle = Math.abs(Math.acos(dot))\n\n        angle /= arcDivisions\n\n        tempV2_3.copy(p1)\n\n        for (let i = 0, il = arcDivisions - 1; i < il; i++) {\n          tempV2_4.copy(tempV2_3).rotateAround(center, angle)\n\n          addVertex(tempV2_3, u, v)\n          addVertex(tempV2_4, u, v)\n          addVertex(center, u, 0.5)\n\n          tempV2_3.copy(tempV2_4)\n        }\n\n        addVertex(tempV2_4, u, v)\n        addVertex(p2, u, v)\n        addVertex(center, u, 0.5)\n      }\n\n      function makeSegmentTriangles() {\n        addVertex(lastPointR, u0, 1)\n        addVertex(lastPointL, u0, 0)\n        addVertex(currentPointL, u1, 0)\n\n        addVertex(lastPointR, u0, 1)\n        addVertex(currentPointL, u1, 0)\n        addVertex(currentPointR, u1, 1)\n      }\n\n      function makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u) {\n        if (innerSideModified) {\n          // Optimized segment + bevel triangles\n\n          if (joinIsOnLeftSide) {\n            // Path segments triangles\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointL, u1, 0)\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(currentPointL, u1, 0)\n            addVertex(innerPoint, u1, 1)\n\n            // Bevel join triangle\n\n            addVertex(currentPointL, u, 0)\n            addVertex(nextPointL, u, 0)\n            addVertex(innerPoint, u, 0.5)\n          } else {\n            // Path segments triangles\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointR, u1, 1)\n\n            addVertex(lastPointL, u0, 0)\n            addVertex(innerPoint, u1, 0)\n            addVertex(currentPointR, u1, 1)\n\n            // Bevel join triangle\n\n            addVertex(currentPointR, u, 1)\n            addVertex(innerPoint, u, 0)\n            addVertex(nextPointR, u, 1)\n          }\n        } else {\n          // Bevel join triangle. The segment triangles are done in the main loop\n\n          if (joinIsOnLeftSide) {\n            addVertex(currentPointL, u, 0)\n            addVertex(nextPointL, u, 0)\n            addVertex(currentPoint, u, 0.5)\n          } else {\n            addVertex(currentPointR, u, 1)\n            addVertex(nextPointR, u, 0)\n            addVertex(currentPoint, u, 0.5)\n          }\n        }\n      }\n\n      function createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified) {\n        if (innerSideModified) {\n          if (joinIsOnLeftSide) {\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointL, u1, 0)\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(currentPointL, u1, 0)\n            addVertex(innerPoint, u1, 1)\n\n            addVertex(currentPointL, u0, 0)\n            addVertex(currentPoint, u1, 0.5)\n            addVertex(innerPoint, u1, 1)\n\n            addVertex(currentPoint, u1, 0.5)\n            addVertex(nextPointL, u0, 0)\n            addVertex(innerPoint, u1, 1)\n          } else {\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointR, u1, 1)\n\n            addVertex(lastPointL, u0, 0)\n            addVertex(innerPoint, u1, 0)\n            addVertex(currentPointR, u1, 1)\n\n            addVertex(currentPointR, u0, 1)\n            addVertex(innerPoint, u1, 0)\n            addVertex(currentPoint, u1, 0.5)\n\n            addVertex(currentPoint, u1, 0.5)\n            addVertex(innerPoint, u1, 0)\n            addVertex(nextPointR, u0, 1)\n          }\n        }\n      }\n\n      function addCapGeometry(center, p1, p2, joinIsOnLeftSide, start, u) {\n        // param center: End point of the path\n        // param p1, p2: Left and right cap points\n\n        switch (style.strokeLineCap) {\n          case 'round':\n            if (start) {\n              makeCircularSector(center, p2, p1, u, 0.5)\n            } else {\n              makeCircularSector(center, p1, p2, u, 0.5)\n            }\n\n            break\n\n          case 'square':\n            if (start) {\n              tempV2_1.subVectors(p1, center)\n              tempV2_2.set(tempV2_1.y, -tempV2_1.x)\n\n              tempV2_3.addVectors(tempV2_1, tempV2_2).add(center)\n              tempV2_4.subVectors(tempV2_2, tempV2_1).add(center)\n\n              // Modify already existing vertices\n              if (joinIsOnLeftSide) {\n                tempV2_3.toArray(vertices, 1 * 3)\n                tempV2_4.toArray(vertices, 0 * 3)\n                tempV2_4.toArray(vertices, 3 * 3)\n              } else {\n                tempV2_3.toArray(vertices, 1 * 3)\n                // using tempV2_4 to update 3rd vertex if the uv.y of 3rd vertex is 1\n                uvs[3 * 2 + 1] === 1 ? tempV2_4.toArray(vertices, 3 * 3) : tempV2_3.toArray(vertices, 3 * 3)\n                tempV2_4.toArray(vertices, 0 * 3)\n              }\n            } else {\n              tempV2_1.subVectors(p2, center)\n              tempV2_2.set(tempV2_1.y, -tempV2_1.x)\n\n              tempV2_3.addVectors(tempV2_1, tempV2_2).add(center)\n              tempV2_4.subVectors(tempV2_2, tempV2_1).add(center)\n\n              const vl = vertices.length\n\n              // Modify already existing vertices\n              if (joinIsOnLeftSide) {\n                tempV2_3.toArray(vertices, vl - 1 * 3)\n                tempV2_4.toArray(vertices, vl - 2 * 3)\n                tempV2_4.toArray(vertices, vl - 4 * 3)\n              } else {\n                tempV2_4.toArray(vertices, vl - 2 * 3)\n                tempV2_3.toArray(vertices, vl - 1 * 3)\n                tempV2_4.toArray(vertices, vl - 4 * 3)\n              }\n            }\n\n            break\n\n          case 'butt':\n          default:\n            // Nothing to do here\n            break\n        }\n      }\n\n      function removeDuplicatedPoints(points) {\n        // Creates a new array if necessary with duplicated points removed.\n        // This does not remove duplicated initial and ending points of a closed path.\n\n        let dupPoints = false\n        for (let i = 1, n = points.length - 1; i < n; i++) {\n          if (points[i].distanceTo(points[i + 1]) < minDistance) {\n            dupPoints = true\n            break\n          }\n        }\n\n        if (!dupPoints) return points\n\n        const newPoints = []\n        newPoints.push(points[0])\n\n        for (let i = 1, n = points.length - 1; i < n; i++) {\n          if (points[i].distanceTo(points[i + 1]) >= minDistance) {\n            newPoints.push(points[i])\n          }\n        }\n\n        newPoints.push(points[points.length - 1])\n\n        return newPoints\n      }\n    }\n  }\n\n  return SVGLoader\n})()\n\nexport { SVGLoader }\n"], "names": ["SVGLoader", "Loader", "<PERSON><PERSON><PERSON><PERSON>", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vector2", "data", "i", "Path", "current", "Matrix3", "currentTransform", "Vector3", "scanlineMinX", "scanlineMaxX", "p", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Box2", "<PERSON><PERSON><PERSON>", "BufferGeometry", "Float32BufferAttribute", "joinIsOnLeftSide", "innerSideModified", "points"], "mappings": ";;;AAeA,MAAM,kBAAkB;AAEnB,MAAC,YAA6B,uBAAM;AACvC,QAAMA,mBAAkBC,MAAAA,OAAO;AAAA,IAC7B,YAAY,SAAS;AACnB,YAAM,OAAO;AAGb,WAAK,aAAa;AAGlB,WAAK,cAAc;AAAA,IACpB;AAAA,IAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,YAAM,QAAQ;AAEd,YAAM,SAAS,IAAIC,iBAAW,MAAM,OAAO;AAC3C,aAAO,QAAQ,MAAM,IAAI;AACzB,aAAO,iBAAiB,MAAM,aAAa;AAC3C,aAAO,mBAAmB,MAAM,eAAe;AAC/C,aAAO;AAAA,QACL;AAAA,QACA,SAAU,MAAM;AACd,cAAI;AACF,mBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,UACzB,SAAQ,GAAP;AACA,gBAAI,SAAS;AACX,sBAAQ,CAAC;AAAA,YACvB,OAAmB;AACL,sBAAQ,MAAM,CAAC;AAAA,YAChB;AAED,kBAAM,QAAQ,UAAU,GAAG;AAAA,UAC5B;AAAA,QACF;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAAA,IAED,MAAM,MAAM;AACV,YAAM,QAAQ;AAEd,eAAS,UAAU,MAAM,OAAO;AAC9B,YAAI,KAAK,aAAa;AAAG;AAEzB,cAAM,YAAY,iBAAiB,IAAI;AAEvC,YAAI,aAAa;AAEjB,YAAI,OAAO;AAEX,gBAAQ,KAAK,UAAQ;AAAA,UACnB,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B;AAAA,UAEF,KAAK;AACH,+BAAmB,IAAI;AACvB;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B,gBAAI,KAAK,aAAa,GAAG;AAAG,qBAAO,cAAc,IAAI;AACrD;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B,mBAAO,cAAc,IAAI;AACzB;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B,mBAAO,iBAAiB,IAAI;AAC5B;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B,mBAAO,kBAAkB,IAAI;AAC7B;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B,mBAAO,gBAAgB,IAAI;AAC3B;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B,mBAAO,iBAAiB,IAAI;AAC5B;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAC9B,mBAAO,cAAc,IAAI;AACzB;AAAA,UAEF,KAAK;AACH,yBAAa;AACb;AAAA,UAEF,KAAK;AACH,oBAAQ,WAAW,MAAM,KAAK;AAE9B,kBAAM,OAAO,KAAK,eAAe,gCAAgC,MAAM,KAAK;AAC5E,kBAAM,aAAa,KAAK,UAAU,CAAC;AACnC,kBAAM,WAAW,KAAK,gBAAgB,eAAe,UAAU;AAC/D,gBAAI,UAAU;AACZ,wBAAU,UAAU,KAAK;AAAA,YACvC,OAAmB;AACL,sBAAQ,KAAK,4DAA4D,UAAU;AAAA,YACpF;AAED;AAAA,QAIH;AAED,YAAI,MAAM;AACR,cAAI,MAAM,SAAS,UAAa,MAAM,SAAS,QAAQ;AACrD,iBAAK,MAAM,SAAS,MAAM,MAAM,eAAe;AAAA,UAChD;AAED,wBAAc,MAAM,gBAAgB;AAEpC,gBAAM,KAAK,IAAI;AAEf,eAAK,WAAW,EAAE,MAAY,MAAc;AAAA,QAC7C;AAED,cAAM,aAAa,KAAK;AAExB,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,gBAAMC,QAAO,WAAW,CAAC;AAEzB,cAAI,cAAcA,MAAK,aAAa,WAAWA,MAAK,aAAa,QAAQ;AAIvE;AAAA,UACD;AAED,oBAAUA,OAAM,KAAK;AAAA,QACtB;AAED,YAAI,WAAW;AACb,yBAAe,IAAK;AAEpB,cAAI,eAAe,SAAS,GAAG;AAC7B,6BAAiB,KAAK,eAAe,eAAe,SAAS,CAAC,CAAC;AAAA,UAC3E,OAAiB;AACL,6BAAiB,SAAU;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAED,eAAS,cAAc,MAAM;AAC3B,cAAM,OAAO,IAAIC,gBAAW;AAE5B,cAAM,QAAQ,IAAIC,cAAS;AAC3B,cAAM,UAAU,IAAIA,cAAS;AAE7B,cAAM,aAAa,IAAIA,cAAS;AAChC,YAAI,eAAe;AACnB,YAAI,kBAAkB;AAEtB,cAAM,IAAI,KAAK,aAAa,GAAG;AAE/B,YAAI,MAAM,MAAM,MAAM;AAAQ,iBAAO;AAIrC,cAAM,WAAW,EAAE,MAAM,sBAAsB;AAE/C,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,gBAAM,UAAU,SAAS,CAAC;AAE1B,gBAAM,OAAO,QAAQ,OAAO,CAAC;AAC7B,gBAAMC,QAAO,QAAQ,MAAM,CAAC,EAAE,KAAM;AAEpC,cAAI,iBAAiB,MAAM;AACzB,8BAAkB;AAClB,2BAAe;AAAA,UAChB;AAED,cAAI;AAEJ,kBAAQ,MAAI;AAAA,YACV,KAAK;AACH,wBAAU,YAAYA,KAAI;AAC1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAElB,oBAAI,MAAM,GAAG;AACX,uBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAAA,gBAC9C,OAAuB;AACL,uBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAAA,gBAC7B;AAED,oBAAI,MAAM;AAAG,6BAAW,KAAK,KAAK;AAAA,cACnC;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,sBAAM,IAAI,QAAQ,CAAC;AACnB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB,qBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAE5B,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,sBAAM,IAAI,QAAQ,CAAC;AACnB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB,qBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAE5B,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB,qBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAE5B,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,qBAAK;AAAA,kBACH,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,gBACd;AACD,wBAAQ,IAAI,QAAQ,IAAI,CAAC;AACzB,wBAAQ,IAAI,QAAQ,IAAI,CAAC;AACzB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AAEvB,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,qBAAK;AAAA,kBACH,cAAc,MAAM,GAAG,QAAQ,CAAC;AAAA,kBAChC,cAAc,MAAM,GAAG,QAAQ,CAAC;AAAA,kBAChC,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,gBACd;AACD,wBAAQ,IAAI,QAAQ,IAAI,CAAC;AACzB,wBAAQ,IAAI,QAAQ,IAAI,CAAC;AACzB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AAEvB,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,qBAAK,iBAAiB,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC;AACpF,wBAAQ,IAAI,QAAQ,IAAI,CAAC;AACzB,wBAAQ,IAAI,QAAQ,IAAI,CAAC;AACzB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AAEvB,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,sBAAM,KAAK,cAAc,MAAM,GAAG,QAAQ,CAAC;AAC3C,sBAAM,KAAK,cAAc,MAAM,GAAG,QAAQ,CAAC;AAC3C,qBAAK,iBAAiB,IAAI,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC;AAC5D,wBAAQ,IAAI;AACZ,wBAAQ,IAAI;AACZ,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AAEvB,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,OAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AAErC,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AAEnD,oBAAI,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM;AAAG;AAE5D,sBAAM,QAAQ,MAAM,MAAO;AAC3B,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,sBAAM,IAAI,QAAQ,IAAI,CAAC;AACvB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB;AAAA,kBACE;AAAA,kBACA,QAAQ,CAAC;AAAA,kBACT,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb;AAAA,kBACA;AAAA,gBACD;AAED,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAElB,oBAAI,MAAM,GAAG;AACX,uBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAAA,gBAC9C,OAAuB;AACL,uBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAAA,gBAC7B;AAED,oBAAI,MAAM;AAAG,6BAAW,KAAK,KAAK;AAAA,cACnC;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,sBAAM,KAAK,QAAQ,CAAC;AACpB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB,qBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAE5B,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,sBAAM,KAAK,QAAQ,CAAC;AACpB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB,qBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAE5B,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB,qBAAK,OAAO,MAAM,GAAG,MAAM,CAAC;AAE5B,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,qBAAK;AAAA,kBACH,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,gBACxB;AACD,wBAAQ,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AACnC,wBAAQ,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AACnC,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,sBAAM,KAAK,QAAQ,IAAI,CAAC;AAExB,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,qBAAK;AAAA,kBACH,cAAc,MAAM,GAAG,QAAQ,CAAC;AAAA,kBAChC,cAAc,MAAM,GAAG,QAAQ,CAAC;AAAA,kBAChC,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,gBACxB;AACD,wBAAQ,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AACnC,wBAAQ,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AACnC,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,sBAAM,KAAK,QAAQ,IAAI,CAAC;AAExB,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,qBAAK;AAAA,kBACH,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,kBACvB,MAAM,IAAI,QAAQ,IAAI,CAAC;AAAA,gBACxB;AACD,wBAAQ,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AACnC,wBAAQ,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AACnC,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,sBAAM,KAAK,QAAQ,IAAI,CAAC;AAExB,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,KAAI;AAE1B,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AACnD,sBAAM,KAAK,cAAc,MAAM,GAAG,QAAQ,CAAC;AAC3C,sBAAM,KAAK,cAAc,MAAM,GAAG,QAAQ,CAAC;AAC3C,qBAAK,iBAAiB,IAAI,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC,GAAG,MAAM,IAAI,QAAQ,IAAI,CAAC,CAAC;AAChF,wBAAQ,IAAI;AACZ,wBAAQ,IAAI;AACZ,sBAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AACjC,sBAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,CAAC;AAEjC,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AACH,wBAAU,YAAYA,OAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AAErC,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK,GAAG;AAEnD,oBAAI,QAAQ,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK;AAAG;AAEhD,sBAAM,QAAQ,MAAM,MAAO;AAC3B,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,sBAAM,KAAK,QAAQ,IAAI,CAAC;AACxB,wBAAQ,IAAI,MAAM;AAClB,wBAAQ,IAAI,MAAM;AAClB;AAAA,kBACE;AAAA,kBACA,QAAQ,CAAC;AAAA,kBACT,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb,QAAQ,IAAI,CAAC;AAAA,kBACb;AAAA,kBACA;AAAA,gBACD;AAED,oBAAI,MAAM,KAAK,oBAAoB;AAAM,6BAAW,KAAK,KAAK;AAAA,cAC/D;AAED;AAAA,YAEF,KAAK;AAAA,YACL,KAAK;AACH,mBAAK,YAAY,YAAY;AAE7B,kBAAI,KAAK,YAAY,OAAO,SAAS,GAAG;AAEtC,sBAAM,KAAK,UAAU;AACrB,qBAAK,YAAY,aAAa,KAAK,KAAK;AACxC,+BAAe;AAAA,cAChB;AAED;AAAA,YAEF;AACE,sBAAQ,KAAK,OAAO;AAAA,UACvB;AAID,4BAAkB;AAAA,QACnB;AAED,eAAO;AAAA,MACR;AAED,eAAS,mBAAmB,MAAM;AAChC,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,YAAY,CAAC,KAAK,MAAM,SAAS;AAAQ;AAExE,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,SAAS,QAAQ,KAAK;AACnD,gBAAM,aAAa,KAAK,MAAM,SAAS,CAAC;AAExC,cAAI,WAAW,SAAS;AAAG;AAE3B,gBAAM,eAAe,WAAW,aAC7B,MAAM,KAAK,EACX,OAAO,OAAO,EACd,IAAI,CAACC,OAAMA,GAAE,KAAI,CAAE;AAEtB,mBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAE5C,kBAAM,cAAc,OAAO,YAAY,OAAO,QAAQ,WAAW,KAAK,EAAE,OAAO,CAAC,CAAG,EAAA,CAAC,MAAM,MAAM,EAAE,CAAC;AAEnG,wBAAY,aAAa,CAAC,CAAC,IAAI,OAAO,OAAO,YAAY,aAAa,CAAC,CAAC,KAAK,CAAA,GAAI,WAAW;AAAA,UAC7F;AAAA,QACF;AAAA,MACF;AAWD,eAAS,gBAAgB,MAAM,IAAI,IAAI,iBAAiB,gBAAgB,YAAY,OAAO,KAAK;AAC9F,YAAI,MAAM,KAAK,MAAM,GAAG;AAEtB,eAAK,OAAO,IAAI,GAAG,IAAI,CAAC;AACxB;AAAA,QACD;AAED,0BAAmB,kBAAkB,KAAK,KAAM;AAGhD,aAAK,KAAK,IAAI,EAAE;AAChB,aAAK,KAAK,IAAI,EAAE;AAGhB,cAAM,OAAO,MAAM,IAAI,IAAI,KAAK;AAChC,cAAM,OAAO,MAAM,IAAI,IAAI,KAAK;AAChC,cAAM,MAAM,KAAK,IAAI,eAAe,IAAI,MAAM,KAAK,IAAI,eAAe,IAAI;AAC1E,cAAM,MAAM,CAAC,KAAK,IAAI,eAAe,IAAI,MAAM,KAAK,IAAI,eAAe,IAAI;AAG3E,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,KAAK;AACf,cAAM,OAAO,MAAM;AACnB,cAAM,OAAO,MAAM;AAGnB,cAAM,KAAK,OAAO,MAAM,OAAO;AAE/B,YAAI,KAAK,GAAG;AAEV,gBAAM,IAAI,KAAK,KAAK,EAAE;AACtB,eAAK,IAAI;AACT,eAAK,IAAI;AACT,gBAAM,KAAK;AACX,gBAAM,KAAK;AAAA,QACZ;AAED,cAAM,KAAK,MAAM,OAAO,MAAM;AAC9B,cAAM,MAAM,MAAM,MAAM,MAAM;AAC9B,YAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,EAAE,CAAC;AACjC,YAAI,mBAAmB;AAAY,cAAI,CAAC;AACxC,cAAM,MAAO,IAAI,KAAK,MAAO;AAC7B,cAAM,MAAO,CAAC,IAAI,KAAK,MAAO;AAG9B,cAAM,KAAK,KAAK,IAAI,eAAe,IAAI,MAAM,KAAK,IAAI,eAAe,IAAI,OAAO,MAAM,IAAI,IAAI,KAAK;AACnG,cAAM,KAAK,KAAK,IAAI,eAAe,IAAI,MAAM,KAAK,IAAI,eAAe,IAAI,OAAO,MAAM,IAAI,IAAI,KAAK;AAGnG,cAAM,QAAQ,SAAS,GAAG,IAAI,MAAM,OAAO,KAAK,MAAM,OAAO,EAAE;AAC/D,cAAM,QAAQ,UAAU,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,OAAO,EAAE,KAAK,KAAK,KAAK;AAE9G,aAAK,YAAY,WAAW,IAAI,IAAI,IAAI,IAAI,OAAO,QAAQ,OAAO,eAAe,GAAG,eAAe;AAAA,MACpG;AAED,eAAS,SAAS,IAAI,IAAI,IAAI,IAAI;AAChC,cAAM,MAAM,KAAK,KAAK,KAAK;AAC3B,cAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACtE,YAAI,MAAM,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;AACxD,YAAI,KAAK,KAAK,KAAK,KAAK;AAAG,gBAAM,CAAC;AAClC,eAAO;AAAA,MACR;AAMD,eAAS,cAAc,MAAM;AAC3B,cAAM,IAAI,oBAAoB,KAAK,aAAa,GAAG,KAAK,CAAC;AACzD,cAAM,IAAI,oBAAoB,KAAK,aAAa,GAAG,KAAK,CAAC;AACzD,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,KAAK,aAAa,IAAI,KAAK,CAAC;AACtF,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,KAAK,aAAa,IAAI,KAAK,CAAC;AACtF,cAAM,IAAI,oBAAoB,KAAK,aAAa,OAAO,CAAC;AACxD,cAAM,IAAI,oBAAoB,KAAK,aAAa,QAAQ,CAAC;AAIzD,cAAM,MAAM,IAAI;AAEhB,cAAM,OAAO,IAAIH,gBAAW;AAG5B,aAAK,OAAO,IAAI,IAAI,CAAC;AAGrB,aAAK,OAAO,IAAI,IAAI,IAAI,CAAC;AACzB,YAAI,OAAO,KAAK,OAAO,GAAG;AACxB,eAAK,cAAc,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,EAAE;AAAA,QAC3E;AAGD,aAAK,OAAO,IAAI,GAAG,IAAI,IAAI,EAAE;AAC7B,YAAI,OAAO,KAAK,OAAO,GAAG;AACxB,eAAK,cAAc,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC;AAAA,QACvF;AAGD,aAAK,OAAO,IAAI,IAAI,IAAI,CAAC;AACzB,YAAI,OAAO,KAAK,OAAO,GAAG;AACxB,eAAK,cAAc,IAAI,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,EAAE;AAAA,QAC3E;AAGD,aAAK,OAAO,GAAG,IAAI,EAAE;AACrB,YAAI,OAAO,KAAK,OAAO,GAAG;AACxB,eAAK,cAAc,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC;AAAA,QAC/D;AAED,eAAO;AAAA,MACR;AAED,eAAS,iBAAiB,MAAM;AAC9B,iBAAS,SAAS,OAAO,GAAG,GAAG;AAC7B,gBAAM,IAAI,oBAAoB,CAAC;AAC/B,gBAAM,IAAI,oBAAoB,CAAC;AAE/B,cAAI,UAAU,GAAG;AACf,iBAAK,OAAO,GAAG,CAAC;AAAA,UAC5B,OAAiB;AACL,iBAAK,OAAO,GAAG,CAAC;AAAA,UACjB;AAED;AAAA,QACD;AAED,cAAM,QAAQ;AAEd,cAAM,OAAO,IAAIA,gBAAW;AAE5B,YAAI,QAAQ;AAEZ,aAAK,aAAa,QAAQ,EAAE,QAAQ,OAAO,QAAQ;AAEnD,aAAK,YAAY,YAAY;AAE7B,eAAO;AAAA,MACR;AAED,eAAS,kBAAkB,MAAM;AAC/B,iBAAS,SAAS,OAAO,GAAG,GAAG;AAC7B,gBAAM,IAAI,oBAAoB,CAAC;AAC/B,gBAAM,IAAI,oBAAoB,CAAC;AAE/B,cAAI,UAAU,GAAG;AACf,iBAAK,OAAO,GAAG,CAAC;AAAA,UAC5B,OAAiB;AACL,iBAAK,OAAO,GAAG,CAAC;AAAA,UACjB;AAED;AAAA,QACD;AAED,cAAM,QAAQ;AAEd,cAAM,OAAO,IAAIA,gBAAW;AAE5B,YAAI,QAAQ;AAEZ,aAAK,aAAa,QAAQ,EAAE,QAAQ,OAAO,QAAQ;AAEnD,aAAK,YAAY,YAAY;AAE7B,eAAO;AAAA,MACR;AAED,eAAS,gBAAgB,MAAM;AAC7B,cAAM,IAAI,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC1D,cAAM,IAAI,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC1D,cAAM,IAAI,oBAAoB,KAAK,aAAa,GAAG,KAAK,CAAC;AAEzD,cAAM,UAAU,IAAII,WAAM;AAC1B,gBAAQ,OAAO,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,CAAC;AAEtC,cAAM,OAAO,IAAIJ,gBAAW;AAC5B,aAAK,SAAS,KAAK,OAAO;AAE1B,eAAO;AAAA,MACR;AAED,eAAS,iBAAiB,MAAM;AAC9B,cAAM,IAAI,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC1D,cAAM,IAAI,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC1D,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC3D,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAE3D,cAAM,UAAU,IAAII,WAAM;AAC1B,gBAAQ,WAAW,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC;AAE/C,cAAM,OAAO,IAAIJ,gBAAW;AAC5B,aAAK,SAAS,KAAK,OAAO;AAE1B,eAAO;AAAA,MACR;AAED,eAAS,cAAc,MAAM;AAC3B,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC3D,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC3D,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAC3D,cAAM,KAAK,oBAAoB,KAAK,aAAa,IAAI,KAAK,CAAC;AAE3D,cAAM,OAAO,IAAIA,gBAAW;AAC5B,aAAK,OAAO,IAAI,EAAE;AAClB,aAAK,OAAO,IAAI,EAAE;AAClB,aAAK,YAAY,YAAY;AAE7B,eAAO;AAAA,MACR;AAID,eAAS,WAAW,MAAM,OAAO;AAC/B,gBAAQ,OAAO,OAAO,CAAA,GAAI,KAAK;AAE/B,YAAI,mBAAmB,CAAE;AAEzB,YAAI,KAAK,aAAa,OAAO,GAAG;AAC9B,gBAAM,iBAAiB,KACpB,aAAa,OAAO,EACpB,MAAM,IAAI,EACV,OAAO,OAAO,EACd,IAAI,CAAC,MAAM,EAAE,KAAI,CAAE;AAEtB,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,+BAAmB,OAAO,OAAO,kBAAkB,YAAY,MAAM,eAAe,CAAC,CAAC,CAAC;AAAA,UACxF;AAAA,QACF;AAED,YAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,6BAAmB,OAAO,OAAO,kBAAkB,YAAY,MAAM,KAAK,aAAa,IAAI,CAAC,CAAC;AAAA,QAC9F;AAED,iBAAS,SAAS,SAAS,QAAQ,gBAAgB;AACjD,cAAI,mBAAmB;AACrB,6BAAiB,SAAS,KAAK,GAAG;AAChC,kBAAI,EAAE,WAAW,KAAK;AAAG,wBAAQ,KAAK,yDAAyD;AAE/F,qBAAO;AAAA,YACR;AAEH,cAAI,KAAK,aAAa,OAAO;AAAG,kBAAM,MAAM,IAAI,eAAe,KAAK,aAAa,OAAO,CAAC;AACzF,cAAI,iBAAiB,OAAO;AAAG,kBAAM,MAAM,IAAI,eAAe,iBAAiB,OAAO,CAAC;AACvF,cAAI,KAAK,SAAS,KAAK,MAAM,OAAO,MAAM;AAAI,kBAAM,MAAM,IAAI,eAAe,KAAK,MAAM,OAAO,CAAC;AAAA,QACjG;AAED,iBAAS,MAAM,GAAG;AAChB,iBAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,oBAAoB,CAAC,CAAC,CAAC;AAAA,QACvD;AAED,iBAAS,SAAS,GAAG;AACnB,iBAAO,KAAK,IAAI,GAAG,oBAAoB,CAAC,CAAC;AAAA,QAC1C;AAED,iBAAS,QAAQ,MAAM;AACvB,iBAAS,gBAAgB,eAAe,KAAK;AAC7C,iBAAS,aAAa,UAAU;AAChC,iBAAS,WAAW,WAAW,KAAK;AACpC,iBAAS,UAAU,QAAQ;AAC3B,iBAAS,kBAAkB,iBAAiB,KAAK;AACjD,iBAAS,gBAAgB,eAAe,QAAQ;AAChD,iBAAS,mBAAmB,gBAAgB;AAC5C,iBAAS,kBAAkB,eAAe;AAC1C,iBAAS,qBAAqB,oBAAoB,QAAQ;AAC1D,iBAAS,cAAc,YAAY;AAEnC,eAAO;AAAA,MACR;AAID,eAAS,cAAc,GAAG,GAAG;AAC3B,eAAO,KAAK,IAAI;AAAA,MACjB;AAID,eAAS,YAAY,OAAO,OAAO,QAAQ;AACzC,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,IAAI,UAAU,oBAAoB,OAAO,KAAK;AAAA,QACrD;AAGD,cAAM,KAAK;AAAA,UACT,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,OAAO;AAAA,QACR;AAGD,cAAM,MAAM;AACZ,cAAM,MAAM;AACZ,cAAM,QAAQ;AACd,cAAM,MAAM;AAEZ,YAAI,QAAQ;AACZ,YAAI,YAAY;AAChB,YAAI,SAAS,IACX,WAAW;AACb,cAAM,SAAS,CAAE;AAEjB,iBAAS,iBAAiBK,UAAS,GAAG,SAAS;AAC7C,gBAAM,QAAQ,IAAI,YAAY,2BAA2BA,WAAU,gBAAgB,IAAI,GAAG;AAC1F,gBAAM,UAAU;AAChB,gBAAM;AAAA,QACP;AAED,iBAAS,YAAY;AACnB,cAAI,WAAW,IAAI;AACjB,gBAAI,aAAa;AAAI,qBAAO,KAAK,OAAO,MAAM,CAAC;AAAA;AAC1C,qBAAO,KAAK,OAAO,MAAM,IAAI,KAAK,IAAI,IAAI,OAAO,QAAQ,CAAC,CAAC;AAAA,UACjE;AAED,mBAAS;AACT,qBAAW;AAAA,QACZ;AAED,YAAI;AACJ,cAAM,SAAS,MAAM;AAErB,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,oBAAU,MAAM,CAAC;AAGjB,cAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,OAAO,SAAS,MAAM,KAAK,GAAG,MAAM,KAAK,OAAO,GAAG;AAC5F,oBAAQ;AACR,qBAAS;AACT,sBAAW;AACX;AAAA,UACD;AAGD,cAAI,UAAU,KAAK;AAEjB,gBAAI,GAAG,WAAW,KAAK,OAAO,GAAG;AAC/B;AAAA,YACD;AAGD,gBAAI,GAAG,MAAM,KAAK,OAAO,KAAK,GAAG,KAAK,KAAK,OAAO,GAAG;AACnD,sBAAQ;AACR,uBAAS;AACT;AAAA,YACD;AAED,gBAAI,GAAG,MAAM,KAAK,OAAO,GAAG;AAC1B,sBAAQ;AACR,uBAAS;AACT;AAAA,YACD;AAGD,gBAAI,GAAG,MAAM,KAAK,OAAO,GAAG;AAC1B,kBAAI,WAAW;AACb,iCAAiB,SAAS,GAAG,MAAM;AAAA,cACpC;AAED,0BAAY;AAAA,YACb;AAAA,UACF;AAGD,cAAI,UAAU,KAAK;AACjB,gBAAI,GAAG,MAAM,KAAK,OAAO,GAAG;AAC1B,wBAAU;AACV;AAAA,YACD;AAED,gBAAI,GAAG,MAAM,KAAK,OAAO,GAAG;AAC1B,wBAAU;AACV,sBAAQ;AACR;AAAA,YACD;AAED,gBAAI,GAAG,IAAI,KAAK,OAAO,GAAG;AACxB,sBAAQ;AACR;AAAA,YACD;AAGD,gBAAI,GAAG,KAAK,KAAK,OAAO,KAAK,OAAO,WAAW,KAAK,GAAG,KAAK,KAAK,OAAO,CAAC,CAAC,GAAG;AAC3E,+BAAiB,SAAS,GAAG,MAAM;AAAA,YACpC;AAAA,UACF;AAGD,cAAI,UAAU,OAAO;AACnB,gBAAI,GAAG,MAAM,KAAK,OAAO,GAAG;AAC1B,wBAAU;AACV;AAAA,YACD;AAED,gBAAI,GAAG,IAAI,KAAK,OAAO,GAAG;AACxB,sBAAQ;AACR;AAAA,YACD;AAGD,gBAAI,GAAG,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK;AAC/D,+BAAiB,SAAS,GAAG,MAAM;AAAA,YACpC;AAAA,UACF;AAGD,cAAI,UAAU,KAAK;AACjB,gBAAI,GAAG,MAAM,KAAK,OAAO,GAAG;AAC1B,0BAAY;AACZ;AAAA,YACD;AAED,gBAAI,GAAG,KAAK,KAAK,OAAO,GAAG;AACzB,kBAAI,aAAa,IAAI;AACnB,4BAAY;AACZ;AAAA,cACD;AAED,kBAAI,SAAS,WAAW,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AACnD,iCAAiB,SAAS,GAAG,MAAM;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAGD,cAAI,GAAG,WAAW,KAAK,OAAO,GAAG;AAC/B,sBAAW;AACX,oBAAQ;AACR,wBAAY;AAAA,UACb,WAAU,GAAG,MAAM,KAAK,OAAO,GAAG;AACjC,sBAAW;AACX,oBAAQ;AACR,wBAAY;AAAA,UACb,WAAU,GAAG,KAAK,KAAK,OAAO,GAAG;AAChC,sBAAW;AACX,oBAAQ;AACR,qBAAS;AAAA,UACV,WAAU,GAAG,MAAM,KAAK,OAAO,GAAG;AACjC,sBAAW;AACX,oBAAQ;AACR,qBAAS;AAAA,UACrB,OAAiB;AACL,6BAAiB,SAAS,GAAG,MAAM;AAAA,UACpC;AAAA,QACF;AAGD,kBAAW;AAEX,eAAO;AAAA,MACR;AAID,YAAM,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAGjD,YAAM,iBAAiB;AAAA,QACrB,IAAI;AAAA,UACF,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI,IAAI;AAAA,UACR,IAAI,KAAK;AAAA,UACT,IAAI,IAAI;AAAA,UACR,IAAI;AAAA,QACL;AAAA,QACD,IAAI;AAAA,UACF,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI,IAAI;AAAA,UACR,IAAI,KAAK;AAAA,UACT,IAAI,IAAI;AAAA,UACR,IAAI;AAAA,QACL;AAAA,QACD,IAAI;AAAA,UACF,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,QACL;AAAA,QACD,IAAI;AAAA,UACF,IAAI,OAAO;AAAA,UACX,IAAI,OAAO;AAAA,UACX,IAAI,IAAI;AAAA,UACR,IAAI;AAAA,UACJ,IAAI,IAAI;AAAA,UACR,IAAI;AAAA,QACL;AAAA,QACD,IAAI;AAAA,UACF,IAAI,OAAO;AAAA,UACX,IAAI,OAAO;AAAA,UACX,IAAI,IAAI;AAAA,UACR,IAAI,KAAK;AAAA,UACT,IAAI;AAAA,UACJ,IAAI;AAAA,QACL;AAAA,QACD,IAAI;AAAA,UACF,IAAI;AAAA,QACL;AAAA,MACF;AAED,eAAS,oBAAoB,QAAQ;AACnC,YAAI,UAAU;AAEd,YAAI,OAAO,WAAW,YAAY,kBAAkB,QAAQ;AAC1D,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,kBAAM,IAAI,MAAM,CAAC;AAEjB,gBAAI,OAAO,SAAS,CAAC,GAAG;AACtB,wBAAU;AACV,uBAAS,OAAO,UAAU,GAAG,OAAO,SAAS,EAAE,MAAM;AACrD;AAAA,YACD;AAAA,UACF;AAAA,QACF;AAED,YAAI,QAAQ;AAEZ,YAAI,YAAY,QAAQ,MAAM,gBAAgB,MAAM;AAGlD,kBAAQ,eAAe,IAAI,EAAE,MAAM,WAAW,IAAI,MAAM;AAAA,QAClE,OAAe;AACL,kBAAQ,eAAe,OAAO,EAAE,MAAM,WAAW;AAEjD,cAAI,QAAQ,GAAG;AAGb,oBAAQ,eAAe,OAAO,EAAE,IAAI,IAAI,MAAM;AAAA,UAC/C;AAAA,QACF;AAED,eAAO,QAAQ,WAAW,MAAM;AAAA,MACjC;AAID,eAAS,iBAAiB,MAAM;AAC9B,YACE,EACE,KAAK,aAAa,WAAW,KAC5B,KAAK,aAAa,UAAU,KAAK,aAAa,GAAG,KAAK,KAAK,aAAa,GAAG,KAE9E;AACA,iBAAO;AAAA,QACR;AAED,cAAM,YAAY,mBAAmB,IAAI;AAEzC,YAAI,eAAe,SAAS,GAAG;AAC7B,oBAAU,YAAY,eAAe,eAAe,SAAS,CAAC,CAAC;AAAA,QAChE;AAED,yBAAiB,KAAK,SAAS;AAC/B,uBAAe,KAAK,SAAS;AAE7B,eAAO;AAAA,MACR;AAED,eAAS,mBAAmB,MAAM;AAChC,cAAM,YAAY,IAAIC,cAAS;AAC/B,cAAMC,oBAAmB;AAEzB,YAAI,KAAK,aAAa,UAAU,KAAK,aAAa,GAAG,KAAK,KAAK,aAAa,GAAG,IAAI;AACjF,gBAAM,KAAK,oBAAoB,KAAK,aAAa,GAAG,CAAC;AACrD,gBAAM,KAAK,oBAAoB,KAAK,aAAa,GAAG,CAAC;AAErD,oBAAU,UAAU,IAAI,EAAE;AAAA,QAC3B;AAED,YAAI,KAAK,aAAa,WAAW,GAAG;AAClC,gBAAM,kBAAkB,KAAK,aAAa,WAAW,EAAE,MAAM,GAAG;AAEhE,mBAAS,SAAS,gBAAgB,SAAS,GAAG,UAAU,GAAG,UAAU;AACnE,kBAAM,gBAAgB,gBAAgB,MAAM,EAAE,KAAM;AAEpD,gBAAI,kBAAkB;AAAI;AAE1B,kBAAM,aAAa,cAAc,QAAQ,GAAG;AAC5C,kBAAM,cAAc,cAAc;AAElC,gBAAI,aAAa,KAAK,aAAa,aAAa;AAC9C,oBAAM,gBAAgB,cAAc,MAAM,GAAG,UAAU;AAEvD,oBAAM,QAAQ,YAAY,cAAc,MAAM,aAAa,CAAC,CAAC;AAE7D,cAAAA,kBAAiB,SAAU;AAE3B,sBAAQ,eAAa;AAAA,gBACnB,KAAK;AACH,sBAAI,MAAM,UAAU,GAAG;AACrB,0BAAM,KAAK,MAAM,CAAC;AAClB,wBAAI,KAAK;AAET,wBAAI,MAAM,UAAU,GAAG;AACrB,2BAAK,MAAM,CAAC;AAAA,oBACb;AAED,oBAAAA,kBAAiB,UAAU,IAAI,EAAE;AAAA,kBAClC;AAED;AAAA,gBAEF,KAAK;AACH,sBAAI,MAAM,UAAU,GAAG;AACrB,wBAAI,QAAQ;AACZ,wBAAI,KAAK;AACT,wBAAI,KAAK;AAGT,4BAAS,MAAM,CAAC,IAAI,KAAK,KAAM;AAE/B,wBAAI,MAAM,UAAU,GAAG;AAErB,2BAAK,MAAM,CAAC;AACZ,2BAAK,MAAM,CAAC;AAAA,oBACb;AAGD,mCAAe,gBAAgB,CAAC,IAAI,CAAC,EAAE;AACvC,mCAAe,aAAa,KAAK;AACjC,mCAAe,iBAAiB,gBAAgB,cAAc;AAC9D,mCAAe,gBAAgB,IAAI,EAAE;AACrC,oBAAAA,kBAAiB,iBAAiB,gBAAgB,cAAc;AAAA,kBACjE;AAED;AAAA,gBAEF,KAAK;AACH,sBAAI,MAAM,UAAU,GAAG;AACrB,0BAAM,SAAS,MAAM,CAAC;AACtB,wBAAI,SAAS;AAEb,wBAAI,MAAM,UAAU,GAAG;AACrB,+BAAS,MAAM,CAAC;AAAA,oBACjB;AAED,oBAAAA,kBAAiB,MAAM,QAAQ,MAAM;AAAA,kBACtC;AAED;AAAA,gBAEF,KAAK;AACH,sBAAI,MAAM,WAAW,GAAG;AACtB,oBAAAA,kBAAiB,IAAI,GAAG,KAAK,IAAK,MAAM,CAAC,IAAI,KAAK,KAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,kBAClF;AAED;AAAA,gBAEF,KAAK;AACH,sBAAI,MAAM,WAAW,GAAG;AACtB,oBAAAA,kBAAiB,IAAI,GAAG,GAAG,GAAG,KAAK,IAAK,MAAM,CAAC,IAAI,KAAK,KAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,kBAClF;AAED;AAAA,gBAEF,KAAK;AACH,sBAAI,MAAM,WAAW,GAAG;AACtB,oBAAAA,kBAAiB,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,kBACzF;AAED;AAAA,cACH;AAAA,YACF;AAED,sBAAU,YAAYA,iBAAgB;AAAA,UACvC;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAED,eAAS,cAAc,MAAM,GAAG;AAC9B,iBAAS,WAAW,IAAI;AACtB,iBAAO,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,aAAa,CAAC;AAExC,aAAG,IAAI,OAAO,GAAG,OAAO,CAAC;AAAA,QAC1B;AAED,iBAAS,qBAAqB,OAAO;AAInC,gBAAM,IAAI,MAAM;AAChB,gBAAM,IAAI,MAAM;AAEhB,gBAAM,WAAW,KAAK,IAAI,MAAM,SAAS;AACzC,gBAAM,WAAW,KAAK,IAAI,MAAM,SAAS;AAEzC,gBAAM,KAAK,IAAIC,cAAQ,IAAI,UAAU,IAAI,UAAU,CAAC;AACpD,gBAAM,KAAK,IAAIA,MAAAA,QAAQ,CAAC,IAAI,UAAU,IAAI,UAAU,CAAC;AAErD,gBAAM,KAAK,GAAG,aAAa,CAAC;AAC5B,gBAAM,KAAK,GAAG,aAAa,CAAC;AAE5B,gBAAM,KAAK,eAAe,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEnE,gBAAM,QAAQ,eAAe,KAAK,EAAE,EAAE,OAAQ;AAC9C,gBAAM,SAAS,eAAe,KAAK,KAAK,EAAE,UAAW;AACrD,gBAAM,KAAK,OAAO,SAAS,KAAK;AAChC,gBAAM,MAAM,GAAG;AAEf,gBAAM,KAAK,mBAAmB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACpD,gBAAM,UAAU,KAAK,KAAK,GAAG,GAAG;AAChC,gBAAM,UAAU,KAAK,KAAK,GAAG,GAAG;AAEhC,gBAAM,UAAU,IAAI;AACpB,gBAAM,UAAU,IAAI;AACpB,gBAAM,YAAY,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;AAEzC,gBAAM,iBAAiB,MAAM,YAAY,MAAM,gBAAgB,IAAI,KAAK,MAAM,OAAO;AAKrF,cAAI,CAAC,eAAe;AAClB,kBAAM,SAAS,eAAe,IAAI,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC;AAEvE,kBAAM,MAAM,eAAe,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AAEzE,kBAAM,OAAO,OAAO,SAAS,GAAG,EAAE,SAAS,EAAE;AAE7C,kBAAM,iBAAiB,CAAC,QAAQ;AAC9B,oBAAM,EAAE,GAAG,MAAM,GAAG,SAAS,IAAIA,MAAO,QAAC,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,aAAa,IAAI;AAE3F,qBAAO,KAAK,MAAM,MAAM,IAAI;AAAA,YAC7B;AAED,kBAAM,cAAc,eAAe,MAAM,WAAW;AACpD,kBAAM,YAAY,eAAe,MAAM,SAAS;AAEhD,gBAAI,mBAAmB,CAAC,GAAG;AACzB,oBAAM,aAAa,CAAC,MAAM;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAED,iBAAS,oBAAoB,OAAO;AAIlC,gBAAM,KAAK,mBAAmB,CAAC;AAC/B,gBAAM,KAAK,mBAAmB,CAAC;AAE/B,gBAAM,WAAW;AACjB,gBAAM,WAAW;AASjB,gBAAM,QACJ,KAAK,OAAO,UAAU,KAAK,MAAM,EAAE,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAE3G,gBAAM,aAAa;AAEnB,cAAI,mBAAmB,CAAC,GAAG;AACzB,kBAAM,eAAe;AACrB,kBAAM,aAAa;AACnB,kBAAM,aAAa,CAAC,MAAM;AAAA,UAC3B;AAAA,QACF;AAED,cAAM,WAAW,KAAK;AAEtB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,gBAAM,UAAU,SAAS,CAAC;AAC1B,gBAAM,SAAS,QAAQ;AAEvB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,QAAQ,OAAO,CAAC;AAEtB,gBAAI,MAAM,aAAa;AACrB,yBAAW,MAAM,EAAE;AACnB,yBAAW,MAAM,EAAE;AAAA,YACjC,WAAuB,MAAM,oBAAoB;AACnC,yBAAW,MAAM,EAAE;AACnB,yBAAW,MAAM,EAAE;AACnB,yBAAW,MAAM,EAAE;AACnB,yBAAW,MAAM,EAAE;AAAA,YACjC,WAAuB,MAAM,wBAAwB;AACvC,yBAAW,MAAM,EAAE;AACnB,yBAAW,MAAM,EAAE;AACnB,yBAAW,MAAM,EAAE;AAAA,YACjC,WAAuB,MAAM,gBAAgB;AAG/B,qBAAO,IAAI,MAAM,IAAI,MAAM,EAAE;AAC7B,yBAAW,MAAM;AACjB,oBAAM,KAAK,OAAO;AAClB,oBAAM,KAAK,OAAO;AAIlB,kBAAI,kBAAkB,CAAC,GAAG;AACxB,qCAAqB,KAAK;AAAA,cAC1C,OAAqB;AACL,oCAAoB,KAAK;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAED,eAAS,mBAAmB,GAAG;AAC7B,cAAM,KAAK,EAAE;AACb,eAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI;AAAA,MACxC;AAED,eAAS,kBAAkB,GAAG;AAC5B,cAAM,KAAK,EAAE;AACb,cAAM,WAAW,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAG7C,YAAI,aAAa;AAAG,iBAAO;AAE3B,cAAM,KAAK,mBAAmB,CAAC;AAC/B,cAAM,KAAK,mBAAmB,CAAC;AAE/B,eAAO,KAAK,IAAI,YAAY,KAAK,GAAG,IAAI,OAAO;AAAA,MAChD;AAED,eAAS,mBAAmB,GAAG;AAC7B,cAAM,KAAK,EAAE;AACb,eAAO,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAAA,MAC/C;AAED,eAAS,mBAAmB,GAAG;AAC7B,cAAM,KAAK,EAAE;AACb,eAAO,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAAA,MAC/C;AAYD,eAAS,mBAAmB,GAAG,GAAG,GAAG;AACnC,YAAI,KAAK,KAAK,IAAI,IAAI;AACtB,cAAM,KAAK,IAAI;AACf,cAAM,KAAK,IAAI;AACf,cAAM,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC;AAExC,YAAI,KAAK,GAAG;AACV,gBAAM,OAAO,KAAK;AAClB,cAAI,IAAI;AACR,gBAAM,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,QACpC,WAAmB,KAAK,GAAG;AACjB,gBAAM,OAAO,KAAK;AAAA,QAC5B,OAAe;AAGL,gBAAM,MAAM;AACZ,gBAAM,OAAO;AAAA,QACd;AAID,YAAI,KAAK,GAAG;AACV,eAAK,KAAK;AAAA,QACpB,OAAe;AACL,eAAK,KAAK;AAAA,QACX;AAED,YAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AAClC,cAAK,KAAK,IAAK;AACf,eAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAC5B,eAAK,IAAI;AAAA,QACV,WAAU,KAAK,IAAI,CAAC,MAAM,GAAG;AAC5B,eAAK;AACL,eAAK;AAAA,QACf,OAAe;AACL,cAAK,OAAO,KAAM;AAClB,eAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAC5B,eAAK,IAAI;AAAA,QACV;AAED,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,eAAK,CAAC;AACN,eAAK;AAAA,QACN;AAED,eAAO,EAAE,KAAK,KAAK,IAAI,GAAI;AAAA,MAC5B;AAID,YAAM,QAAQ,CAAE;AAChB,YAAM,cAAc,CAAE;AAEtB,YAAM,iBAAiB,CAAE;AAEzB,YAAM,iBAAiB,IAAIF,cAAS;AACpC,YAAM,iBAAiB,IAAIA,cAAS;AACpC,YAAM,iBAAiB,IAAIA,cAAS;AACpC,YAAM,iBAAiB,IAAIA,cAAS;AACpC,YAAM,SAAS,IAAIL,cAAS;AAC5B,YAAM,SAAS,IAAIO,cAAS;AAE5B,YAAM,mBAAmB,IAAIF,cAAS;AAEtC,YAAM,MAAM,IAAI,UAAS,EAAG,gBAAgB,MAAM,eAAe;AAEjE,gBAAU,IAAI,iBAAiB;AAAA,QAC7B,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,QACf,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,kBAAkB;AAAA,MAC1B,CAAO;AAED,YAAM,OAAO,EAAE,OAAc,KAAK,IAAI,gBAAiB;AAGvD,aAAO;AAAA,IACR;AAAA,IAED,OAAO,aAAa,WAAW;AAI7B,YAAM,YAAY;AAElB,YAAM,2BAA2B;AAAA,QAC/B,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAED,YAAM,iBAAiB;AAAA,QACrB,KAAK,yBAAyB;AAAA,QAC9B,GAAG;AAAA,MACJ;AAED,eAAS,qBAAqB,IAAI,IAAI,IAAI,IAAI;AAC5C,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,cAAM,KAAK,GAAG;AACd,cAAM,QAAQ,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK;AACvD,cAAM,QAAQ,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK;AACvD,cAAM,SAAS,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK;AACxD,cAAM,KAAK,OAAO;AAClB,cAAM,KAAK,OAAO;AAElB,YAAK,UAAU,KAAK,SAAS,KAAM,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAGzE,iBAAO;AAAA,QACR,WAAU,SAAS,KAAK,UAAU,GAAG;AAIpC,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,0BAAc,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;AAEvC,gBAAI,eAAe,OAAO,yBAAyB,QAAQ;AACzD,oBAAM,QAAQ,MAAM,IAAI,KAAK;AAC7B,qBAAO,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,eAAe,EAAG;AAAA,YACvD,WAAU,eAAe,OAAO,yBAAyB,SAAS;AACjE,oBAAM,IAAI,EAAE,KAAK,eAAe,KAAK,KAAK,KAAK,YAAY,EAAE;AAC7D,oBAAM,IAAI,EAAE,KAAK,eAAe,KAAK,KAAK,KAAK,YAAY,EAAE;AAC7D,qBAAO,EAAE,GAAM,GAAM,GAAG,eAAe,EAAG;AAAA,YAC3C;AAAA,UACF;AAED,iBAAO;AAAA,QACjB,OAAe;AAGL,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,0BAAc,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;AAEvC,gBAAI,eAAe,OAAO,yBAAyB,QAAQ;AACzD,oBAAM,QAAQ,MAAM,IAAI,KAAK;AAC7B,qBAAO,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,eAAe,EAAG;AAAA,YACvD;AAAA,UACF;AAED,gBAAM,IAAI,EAAE,KAAK,MAAM,KAAK,KAAK,YAAY,EAAE;AAC/C,gBAAM,IAAI,EAAE,KAAK,MAAM,KAAK,KAAK,YAAY,EAAE;AAC/C,iBAAO,EAAE,GAAM,GAAM,GAAG,GAAI;AAAA,QAC7B;AAAA,MACF;AAED,eAAS,cAAc,GAAG,WAAW,SAAS;AAC5C,cAAM,KAAK,QAAQ,IAAI,UAAU;AACjC,cAAM,KAAK,QAAQ,IAAI,UAAU;AACjC,cAAM,KAAK,EAAE,IAAI,UAAU;AAC3B,cAAM,KAAK,EAAE,IAAI,UAAU;AAC3B,cAAM,KAAK,KAAK,KAAK,KAAK;AAE1B,YAAI,EAAE,MAAM,UAAU,KAAK,EAAE,MAAM,UAAU,GAAG;AAC9C,yBAAe,MAAM,yBAAyB;AAC9C,yBAAe,IAAI;AACnB;AAAA,QACD;AAED,YAAI,EAAE,MAAM,QAAQ,KAAK,EAAE,MAAM,QAAQ,GAAG;AAC1C,yBAAe,MAAM,yBAAyB;AAC9C,yBAAe,IAAI;AACnB;AAAA,QACD;AAED,YAAI,KAAK,CAAC,OAAO,SAAS;AACxB,yBAAe,MAAM,yBAAyB;AAC9C;AAAA,QACD;AAED,YAAI,KAAK,OAAO,SAAS;AACvB,yBAAe,MAAM,yBAAyB;AAC9C;AAAA,QACD;AAED,YAAI,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAC9B,yBAAe,MAAM,yBAAyB;AAC9C;AAAA,QACD;AAED,YAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,GAAG;AAC/D,yBAAe,MAAM,yBAAyB;AAC9C;AAAA,QACD;AAED,YAAI;AAEJ,YAAI,OAAO,GAAG;AACZ,cAAI,KAAK;AAAA,QACnB,OAAe;AACL,cAAI,KAAK;AAAA,QACV;AAED,uBAAe,MAAM,yBAAyB;AAC9C,uBAAe,IAAI;AAAA,MACpB;AAED,eAAS,iBAAiB,OAAO,OAAO;AACtC,cAAM,mBAAmB,CAAE;AAC3B,cAAM,gBAAgB,CAAE;AAExB,iBAAS,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAS;AACjD,gBAAM,iBAAiB,MAAM,QAAQ,CAAC;AACtC,gBAAM,eAAe,MAAM,KAAK;AAEhC,mBAAS,SAAS,GAAG,SAAS,MAAM,QAAQ,UAAU;AACpD,kBAAM,iBAAiB,MAAM,SAAS,CAAC;AACvC,kBAAM,eAAe,MAAM,MAAM;AAEjC,kBAAM,eAAe,qBAAqB,gBAAgB,cAAc,gBAAgB,YAAY;AAEpG,gBACE,iBAAiB,QACjB,iBAAiB;AAAA,cACf,CAAC,MAAM,EAAE,KAAK,aAAa,IAAI,OAAO,WAAW,EAAE,KAAK,aAAa,IAAI,OAAO;AAAA,YAChG,MAAoB,QACN;AACA,+BAAiB,KAAK,YAAY;AAClC,4BAAc,KAAK,IAAIL,MAAO,QAAC,aAAa,GAAG,aAAa,CAAC,CAAC;AAAA,YAC/D;AAAA,UACF;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAED,eAAS,yBAAyB,UAAU,aAAa,OAAO;AAC9D,cAAM,SAAS,IAAIA,cAAS;AAC5B,oBAAY,UAAU,MAAM;AAE5B,cAAM,mBAAmB,CAAE;AAE3B,cAAM,QAAQ,CAAC,SAAS;AAItB,cAAI,KAAK,YAAY,cAAc,MAAM,GAAG;AAC1C,kBAAM,gBAAgB,iBAAiB,UAAU,KAAK,MAAM;AAE5D,0BAAc,QAAQ,CAAC,MAAM;AAC3B,+BAAiB,KAAK,EAAE,YAAY,KAAK,YAAY,MAAM,KAAK,MAAM,OAAO,EAAC,CAAE;AAAA,YAC9F,CAAa;AAAA,UACF;AAAA,QACX,CAAS;AAED,yBAAiB,KAAK,CAAC,IAAI,OAAO;AAChC,iBAAO,GAAG,MAAM,IAAI,GAAG,MAAM;AAAA,QACvC,CAAS;AAED,eAAO;AAAA,MACR;AAED,eAAS,SAAS,YAAY,UAAUQ,eAAcC,eAAc,WAAW;AAC7E,YAAI,cAAc,QAAQ,cAAc,UAAa,cAAc,IAAI;AACrE,sBAAY;AAAA,QACb;AAED,cAAM,oBAAoB,IAAIT,cAAS;AACvC,mBAAW,YAAY,UAAU,iBAAiB;AAElD,cAAM,WAAW;AAAA,UACf,IAAIA,cAAQQ,eAAc,kBAAkB,CAAC;AAAA,UAC7C,IAAIR,cAAQS,eAAc,kBAAkB,CAAC;AAAA,QAC9C;AAED,cAAM,wBAAwB,yBAAyB,UAAU,WAAW,aAAa,QAAQ;AAEjG,8BAAsB,KAAK,CAAC,IAAI,OAAO;AACrC,iBAAO,GAAG,MAAM,IAAI,GAAG,MAAM;AAAA,QACvC,CAAS;AAED,cAAM,oBAAoB,CAAE;AAC5B,cAAM,qBAAqB,CAAE;AAE7B,8BAAsB,QAAQ,CAACP,OAAM;AACnC,cAAIA,GAAE,eAAe,WAAW,YAAY;AAC1C,8BAAkB,KAAKA,EAAC;AAAA,UACpC,OAAiB;AACL,+BAAmB,KAAKA,EAAC;AAAA,UAC1B;AAAA,QACX,CAAS;AAED,cAAM,eAAe,kBAAkB,CAAC,EAAE,MAAM;AAGhD,cAAM,QAAQ,CAAE;AAChB,YAAI,IAAI;AAER,eAAO,IAAI,mBAAmB,UAAU,mBAAmB,CAAC,EAAE,MAAM,IAAI,cAAc;AACpF,cAAI,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC,MAAM,mBAAmB,CAAC,EAAE,YAAY;AACpF,kBAAM,IAAK;AAAA,UACvB,OAAiB;AACL,kBAAM,KAAK,mBAAmB,CAAC,EAAE,UAAU;AAAA,UAC5C;AAED;AAAA,QACD;AAED,cAAM,KAAK,WAAW,UAAU;AAEhC,YAAI,cAAc,WAAW;AAC3B,gBAAM,SAAS,MAAM,SAAS,MAAM,IAAI,OAAO;AAC/C,gBAAM,YAAY,MAAM,MAAM,SAAS,CAAC;AAExC,iBAAO,EAAE,YAAY,WAAW,YAAY,QAAgB,KAAK,UAAW;AAAA,QACtF,WAAmB,cAAc,WAAW;AAElC,cAAI,SAAS;AACb,cAAI,YAAY;AAChB,cAAI,cAAc;AAElB,mBAASA,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,kBAAM,aAAa,MAAMA,EAAC;AAC1B,gBAAI,QAAQ;AACV,4BAAc,SAAS,UAAU,EAAE;AACnC,uBAAS;AACT,0BAAY;AAAA,YACb,WAAU,gBAAgB,SAAS,UAAU,EAAE,MAAM;AACpD,4BAAc,SAAS,UAAU,EAAE;AACnC,uBAAS;AAAA,YACV;AAAA,UACF;AAED,iBAAO,EAAE,YAAY,WAAW,YAAY,QAAgB,KAAK,UAAW;AAAA,QACtF,OAAe;AACL,kBAAQ,KAAK,iBAAiB,YAAY,iCAAiC;AAAA,QAC5E;AAAA,MACF;AASD,UAAI,eAAe;AACnB,UAAI,eAAe,CAAC;AAEpB,UAAI,cAAc,UAAU,SAAS,IAAI,CAAC,MAAM;AAC9C,cAAM,SAAS,EAAE,UAAW;AAC5B,YAAI,OAAO,CAAC;AACZ,YAAI,OAAO;AACX,YAAI,OAAO,CAAC;AACZ,YAAI,OAAO;AAIX,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAMQ,KAAI,OAAO,CAAC;AAElB,cAAIA,GAAE,IAAI,MAAM;AACd,mBAAOA,GAAE;AAAA,UACV;AAED,cAAIA,GAAE,IAAI,MAAM;AACd,mBAAOA,GAAE;AAAA,UACV;AAED,cAAIA,GAAE,IAAI,MAAM;AACd,mBAAOA,GAAE;AAAA,UACV;AAED,cAAIA,GAAE,IAAI,MAAM;AACd,mBAAOA,GAAE;AAAA,UACV;AAAA,QACF;AAGD,YAAI,gBAAgB,MAAM;AACxB,yBAAe,OAAO;AAAA,QACvB;AAED,YAAI,gBAAgB,MAAM;AACxB,yBAAe,OAAO;AAAA,QACvB;AAED,eAAO;AAAA,UACL,QAAQ,EAAE;AAAA,UACV;AAAA,UACA,MAAMC,MAAAA,WAAW,YAAY,MAAM;AAAA,UACnC,YAAY;AAAA,UACZ,aAAa,IAAIC,MAAAA,KAAK,IAAIZ,MAAAA,QAAQ,MAAM,IAAI,GAAG,IAAIA,MAAO,QAAC,MAAM,IAAI,CAAC;AAAA,QACvE;AAAA,MACT,CAAO;AAED,oBAAc,YAAY,OAAO,CAAC,OAAO,GAAG,OAAO,SAAS,CAAC;AAE7D,eAAS,aAAa,GAAG,aAAa,YAAY,QAAQ,cAAc;AACtE,oBAAY,UAAU,EAAE,aAAa;AAAA,MACtC;AAGD,YAAM,UAAU,YAAY;AAAA,QAAI,CAAC,MAC/B;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,WAAW,UAAU,SAAS,MAAM,WAAW;AAAA,QAC1D;AAAA,MACF;AAED,YAAM,iBAAiB,CAAE;AACzB,kBAAY,QAAQ,CAAC,MAAM;AACzB,cAAM,WAAW,QAAQ,EAAE,UAAU;AAErC,YAAI,CAAC,SAAS,QAAQ;AACpB,gBAAM,QAAQ,IAAIa,YAAO;AACzB,gBAAM,SAAS,EAAE;AACjB,gBAAM,QAAQ,QAAQ,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU;AACtE,gBAAM,QAAQ,CAAC,MAAM;AACnB,kBAAM,OAAO,YAAY,EAAE,UAAU;AACrC,kBAAM,OAAO,IAAIV,WAAM;AACvB,iBAAK,SAAS,KAAK;AACnB,kBAAM,MAAM,KAAK,IAAI;AAAA,UACjC,CAAW;AACD,yBAAe,KAAK,KAAK;AAAA,QAC1B;AAAA,MACT,CAAO;AAED,aAAO;AAAA,IACR;AAAA,IAED,OAAO,eAAe,OAAO,OAAO,UAAU,SAAS,YAAY;AAQjE,cAAQ,UAAU,SAAY,QAAQ;AACtC,cAAQ,UAAU,SAAY,QAAQ;AACtC,iBAAW,aAAa,SAAY,WAAW;AAC/C,gBAAU,YAAY,SAAY,UAAU;AAC5C,mBAAa,eAAe,SAAY,aAAa;AAErD,aAAO;AAAA,QACL,aAAa;AAAA,QACb,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,kBAAkB;AAAA,MACnB;AAAA,IACF;AAAA,IAED,OAAO,eAAe,QAAQ,OAAO,cAAc,aAAa;AAS9D,YAAM,WAAW,CAAE;AACnB,YAAM,UAAU,CAAE;AAClB,YAAM,MAAM,CAAE;AAEd,UAAIR,WAAU,0BAA0B,QAAQ,OAAO,cAAc,aAAa,UAAU,SAAS,GAAG,MAAM,GAAG;AAC/G,eAAO;AAAA,MACR;AAED,YAAM,WAAW,IAAImB,qBAAgB;AACrC,eAAS,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AACzE,eAAS,aAAa,UAAU,IAAIA,MAAAA,uBAAuB,SAAS,CAAC,CAAC;AACtE,eAAS,aAAa,MAAM,IAAIA,MAAAA,uBAAuB,KAAK,CAAC,CAAC;AAE9D,aAAO;AAAA,IACR;AAAA,IAED,OAAO,0BAA0B,QAAQ,OAAO,cAAc,aAAa,UAAU,SAAS,KAAK,cAAc;AAQ/G,YAAM,WAAW,IAAIf,cAAS;AAC9B,YAAM,WAAW,IAAIA,cAAS;AAC9B,YAAM,WAAW,IAAIA,cAAS;AAC9B,YAAM,WAAW,IAAIA,cAAS;AAC9B,YAAM,WAAW,IAAIA,cAAS;AAC9B,YAAM,WAAW,IAAIA,cAAS;AAC9B,YAAM,WAAW,IAAIA,cAAS;AAC9B,YAAM,aAAa,IAAIA,cAAS;AAChC,YAAM,aAAa,IAAIA,cAAS;AAChC,YAAM,UAAU,IAAIA,cAAS;AAC7B,YAAM,UAAU,IAAIA,cAAS;AAC7B,YAAM,gBAAgB,IAAIA,cAAS;AACnC,YAAM,gBAAgB,IAAIA,cAAS;AACnC,YAAM,aAAa,IAAIA,cAAS;AAChC,YAAM,aAAa,IAAIA,cAAS;AAChC,YAAM,aAAa,IAAIA,cAAS;AAChC,YAAM,aAAa,IAAIA,cAAS;AAEhC,qBAAe,iBAAiB,SAAY,eAAe;AAC3D,oBAAc,gBAAgB,SAAY,cAAc;AACxD,qBAAe,iBAAiB,SAAY,eAAe;AAG3D,eAAS,uBAAuB,MAAM;AAEtC,YAAM,YAAY,OAAO;AAEzB,UAAI,YAAY;AAAG,eAAO;AAE1B,YAAM,WAAW,OAAO,CAAC,EAAE,OAAO,OAAO,YAAY,CAAC,CAAC;AAEvD,UAAI;AACJ,UAAI,gBAAgB,OAAO,CAAC;AAC5B,UAAI;AAEJ,YAAM,eAAe,MAAM,cAAc;AAEzC,YAAM,SAAS,KAAK,YAAY;AAChC,UAAI,KAAK,GACP;AAEF,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,0BAA0B;AAE9B,UAAI,cAAc;AAClB,UAAI,oBAAoB,eAAe;AACvC,UAAI,sBAAsB,eAAe;AAGzC,gBAAU,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,QAAQ,EAAE,eAAe,YAAY;AACrE,iBAAW,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,QAAQ;AACvC,iBAAW,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,QAAQ;AACvC,cAAQ,KAAK,UAAU;AACvB,cAAQ,KAAK,UAAU;AAEvB,eAAS,SAAS,GAAG,SAAS,WAAW,UAAU;AACjD,uBAAe,OAAO,MAAM;AAG5B,YAAI,WAAW,YAAY,GAAG;AAC5B,cAAI,UAAU;AAEZ,wBAAY,OAAO,CAAC;AAAA,UACrB;AAAM,wBAAY;AAAA,QAC7B,OAAe;AACL,sBAAY,OAAO,SAAS,CAAC;AAAA,QAC9B;AAGD,cAAM,UAAU;AAChB,kBAAU,eAAe,cAAc,OAAO;AAE9C,iBAAS,KAAK,OAAO,EAAE,eAAe,YAAY;AAClD,sBAAc,KAAK,YAAY,EAAE,IAAI,QAAQ;AAC7C,sBAAc,KAAK,YAAY,EAAE,IAAI,QAAQ;AAE7C,aAAK,KAAK;AAEV,4BAAoB;AAEpB,YAAI,cAAc,QAAW;AAE3B,oBAAU,cAAc,WAAW,QAAQ;AAE3C,mBAAS,KAAK,QAAQ,EAAE,eAAe,YAAY;AACnD,qBAAW,KAAK,YAAY,EAAE,IAAI,QAAQ;AAC1C,qBAAW,KAAK,YAAY,EAAE,IAAI,QAAQ;AAE1C,6BAAmB;AACnB,mBAAS,WAAW,WAAW,aAAa;AAC5C,cAAI,QAAQ,IAAI,QAAQ,IAAI,GAAG;AAC7B,+BAAmB;AAAA,UACpB;AAED,cAAI,WAAW;AAAG,sCAA0B;AAE5C,mBAAS,WAAW,WAAW,YAAY;AAC3C,mBAAS,UAAW;AACpB,gBAAM,MAAM,KAAK,IAAI,QAAQ,IAAI,QAAQ,CAAC;AAG1C,cAAI,MAAM,OAAO,SAAS;AAExB,kBAAM,YAAY,eAAe;AACjC,qBAAS,eAAe,CAAC,SAAS;AAClC,qBAAS,WAAW,cAAc,aAAa;AAC/C,qBAAS,KAAK,QAAQ,EAAE,UAAU,SAAS,EAAE,IAAI,QAAQ;AACzD,uBAAW,KAAK,QAAQ,EAAE,OAAQ;AAClC,kBAAM,eAAe,SAAS,OAAQ;AACtC,kBAAM,oBAAoB,SAAS,OAAQ;AAC3C,qBAAS,aAAa,iBAAiB;AACvC,qBAAS,WAAW,WAAW,YAAY;AAC3C,kBAAM,oBAAoB,SAAS,OAAQ;AAC3C,qBAAS,aAAa,iBAAiB;AAEvC,gBAAI,SAAS,IAAI,UAAU,IAAI,qBAAqB,SAAS,IAAI,UAAU,IAAI,mBAAmB;AAChG,kCAAoB;AAAA,YACrB;AAED,uBAAW,KAAK,QAAQ,EAAE,IAAI,YAAY;AAC1C,uBAAW,IAAI,YAAY;AAE3B,sBAAU;AAEV,gBAAI,mBAAmB;AACrB,kBAAI,kBAAkB;AACpB,2BAAW,KAAK,UAAU;AAC1B,8BAAc,KAAK,UAAU;AAAA,cAC7C,OAAqB;AACL,2BAAW,KAAK,UAAU;AAC1B,8BAAc,KAAK,UAAU;AAAA,cAC9B;AAAA,YACf,OAAmB;AAGL,mCAAsB;AAAA,YACvB;AAED,oBAAQ,MAAM,gBAAc;AAAA,cAC1B,KAAK;AACH,yCAAyB,kBAAkB,mBAAmB,EAAE;AAEhE;AAAA,cAEF,KAAK;AAGH,wDAAwC,kBAAkB,iBAAiB;AAI3E,oBAAI,kBAAkB;AACpB,qCAAmB,cAAc,eAAe,YAAY,IAAI,CAAC;AAAA,gBACnF,OAAuB;AACL,qCAAmB,cAAc,YAAY,eAAe,IAAI,CAAC;AAAA,gBAClE;AAED;AAAA,cAEF,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AACE,sBAAM,gBAAiB,eAAe,MAAM,mBAAoB;AAEhE,oBAAI,gBAAgB,GAAG;AAGrB,sBAAI,MAAM,mBAAmB,cAAc;AACzC,6CAAyB,kBAAkB,mBAAmB,EAAE;AAChE;AAAA,kBACpB,OAAyB;AAGL,4DAAwC,kBAAkB,iBAAiB;AAI3E,wBAAI,kBAAkB;AACpB,+BAAS,WAAW,YAAY,aAAa,EAAE,eAAe,aAAa,EAAE,IAAI,aAAa;AAC9F,+BAAS,WAAW,YAAY,UAAU,EAAE,eAAe,aAAa,EAAE,IAAI,UAAU;AAExF,gCAAU,eAAe,IAAI,CAAC;AAC9B,gCAAU,UAAU,IAAI,CAAC;AACzB,gCAAU,cAAc,IAAI,GAAG;AAE/B,gCAAU,cAAc,IAAI,GAAG;AAC/B,gCAAU,UAAU,IAAI,CAAC;AACzB,gCAAU,UAAU,IAAI,CAAC;AAEzB,gCAAU,cAAc,IAAI,GAAG;AAC/B,gCAAU,UAAU,IAAI,CAAC;AACzB,gCAAU,YAAY,IAAI,CAAC;AAAA,oBACjD,OAA2B;AACL,+BAAS,WAAW,YAAY,aAAa,EAAE,eAAe,aAAa,EAAE,IAAI,aAAa;AAC9F,+BAAS,WAAW,YAAY,UAAU,EAAE,eAAe,aAAa,EAAE,IAAI,UAAU;AAExF,gCAAU,eAAe,IAAI,CAAC;AAC9B,gCAAU,UAAU,IAAI,CAAC;AACzB,gCAAU,cAAc,IAAI,GAAG;AAE/B,gCAAU,cAAc,IAAI,GAAG;AAC/B,gCAAU,UAAU,IAAI,CAAC;AACzB,gCAAU,UAAU,IAAI,CAAC;AAEzB,gCAAU,cAAc,IAAI,GAAG;AAC/B,gCAAU,UAAU,IAAI,CAAC;AACzB,gCAAU,YAAY,IAAI,CAAC;AAAA,oBAC5B;AAAA,kBACF;AAAA,gBACnB,OAAuB;AAGL,sBAAI,mBAAmB;AAGrB,wBAAI,kBAAkB;AACpB,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAE3B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAAA,oBACjD,OAA2B;AACL,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAE3B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAAA,oBAC5B;AAED,wBAAI,kBAAkB;AACpB,iCAAW,KAAK,UAAU;AAAA,oBAChD,OAA2B;AACL,iCAAW,KAAK,UAAU;AAAA,oBAC3B;AAAA,kBACrB,OAAyB;AAGL,wBAAI,kBAAkB;AACpB,gCAAU,eAAe,IAAI,CAAC;AAC9B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,cAAc,IAAI,GAAG;AAE/B,gCAAU,cAAc,IAAI,GAAG;AAC/B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAAA,oBACjD,OAA2B;AACL,gCAAU,eAAe,IAAI,CAAC;AAC9B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,cAAc,IAAI,GAAG;AAE/B,gCAAU,cAAc,IAAI,GAAG;AAC/B,gCAAU,YAAY,IAAI,CAAC;AAC3B,gCAAU,YAAY,IAAI,CAAC;AAAA,oBAC5B;AAAA,kBACF;AAED,4BAAU;AAAA,gBACX;AAED;AAAA,YACH;AAAA,UACb,OAAiB;AAGL,iCAAsB;AAAA,UACvB;AAAA,QACX,OAAe;AAGL,+BAAsB;AAAA,QACvB;AAED,YAAI,CAAC,YAAY,WAAW,YAAY,GAAG;AAEzC,yBAAe,OAAO,CAAC,GAAG,SAAS,SAAS,kBAAkB,MAAM,EAAE;AAAA,QACvE;AAID,aAAK;AAEL,wBAAgB;AAEhB,mBAAW,KAAK,UAAU;AAC1B,mBAAW,KAAK,UAAU;AAAA,MAC3B;AAED,UAAI,CAAC,UAAU;AAEb,uBAAe,cAAc,eAAe,eAAe,kBAAkB,OAAO,EAAE;AAAA,MAC9F,WAAiB,qBAAqB,UAAU;AAGxC,YAAI,YAAY;AAChB,YAAI,YAAY;AAEhB,YAAI,4BAA4B,kBAAkB;AAChD,sBAAY;AACZ,sBAAY;AAAA,QACb;AAED,YAAI,kBAAkB;AACpB,cAAI,WAAW,yBAAyB;AACtC,sBAAU,QAAQ,UAAU,IAAI,CAAC;AACjC,sBAAU,QAAQ,UAAU,IAAI,CAAC;AAEjC,gBAAI,SAAS;AACX,wBAAU,QAAQ,UAAU,IAAI,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,QACX,OAAe;AACL,cAAI,WAAW,CAAC,yBAAyB;AACvC,sBAAU,QAAQ,UAAU,IAAI,CAAC;AACjC,sBAAU,QAAQ,UAAU,IAAI,CAAC;AAEjC,gBAAI,SAAS;AACX,wBAAU,QAAQ,UAAU,IAAI,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAED,aAAO;AAMP,eAAS,UAAU,IAAI,IAAI,QAAQ;AACjC,eAAO,WAAW,IAAI,EAAE;AACxB,eAAO,OAAO,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,EAAE,UAAW;AAAA,MACnD;AAED,eAAS,UAAU,UAAU,GAAG,GAAG;AACjC,YAAI,UAAU;AACZ,mBAAS,iBAAiB,IAAI,SAAS;AACvC,mBAAS,oBAAoB,CAAC,IAAI,SAAS;AAC3C,mBAAS,oBAAoB,CAAC,IAAI;AAElC,cAAI,SAAS;AACX,oBAAQ,iBAAiB,IAAI;AAC7B,oBAAQ,oBAAoB,CAAC,IAAI;AACjC,oBAAQ,oBAAoB,CAAC,IAAI;AAAA,UAClC;AAED,+BAAqB;AAErB,cAAI,KAAK;AACP,gBAAI,mBAAmB,IAAI;AAC3B,gBAAI,sBAAsB,CAAC,IAAI;AAE/B,mCAAuB;AAAA,UACxB;AAAA,QACF;AAED,uBAAe;AAAA,MAChB;AAED,eAAS,mBAAmB,QAAQ,IAAI,IAAI,GAAG,GAAG;AAIhD,iBAAS,KAAK,EAAE,EAAE,IAAI,MAAM,EAAE,UAAW;AACzC,iBAAS,KAAK,EAAE,EAAE,IAAI,MAAM,EAAE,UAAW;AAEzC,YAAI,QAAQ,KAAK;AACjB,cAAM,MAAM,SAAS,IAAI,QAAQ;AACjC,YAAI,KAAK,IAAI,GAAG,IAAI;AAAG,kBAAQ,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC;AAEtD,iBAAS;AAET,iBAAS,KAAK,EAAE;AAEhB,iBAAS,IAAI,GAAG,KAAK,eAAe,GAAG,IAAI,IAAI,KAAK;AAClD,mBAAS,KAAK,QAAQ,EAAE,aAAa,QAAQ,KAAK;AAElD,oBAAU,UAAU,GAAG,CAAC;AACxB,oBAAU,UAAU,GAAG,CAAC;AACxB,oBAAU,QAAQ,GAAG,GAAG;AAExB,mBAAS,KAAK,QAAQ;AAAA,QACvB;AAED,kBAAU,UAAU,GAAG,CAAC;AACxB,kBAAU,IAAI,GAAG,CAAC;AAClB,kBAAU,QAAQ,GAAG,GAAG;AAAA,MACzB;AAED,eAAS,uBAAuB;AAC9B,kBAAU,YAAY,IAAI,CAAC;AAC3B,kBAAU,YAAY,IAAI,CAAC;AAC3B,kBAAU,eAAe,IAAI,CAAC;AAE9B,kBAAU,YAAY,IAAI,CAAC;AAC3B,kBAAU,eAAe,IAAI,CAAC;AAC9B,kBAAU,eAAe,IAAI,CAAC;AAAA,MAC/B;AAED,eAAS,yBAAyBgB,mBAAkBC,oBAAmB,GAAG;AACxE,YAAIA,oBAAmB;AAGrB,cAAID,mBAAkB;AAGpB,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAE9B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAC9B,sBAAU,YAAY,IAAI,CAAC;AAI3B,sBAAU,eAAe,GAAG,CAAC;AAC7B,sBAAU,YAAY,GAAG,CAAC;AAC1B,sBAAU,YAAY,GAAG,GAAG;AAAA,UACxC,OAAiB;AAGL,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAE9B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAI9B,sBAAU,eAAe,GAAG,CAAC;AAC7B,sBAAU,YAAY,GAAG,CAAC;AAC1B,sBAAU,YAAY,GAAG,CAAC;AAAA,UAC3B;AAAA,QACX,OAAe;AAGL,cAAIA,mBAAkB;AACpB,sBAAU,eAAe,GAAG,CAAC;AAC7B,sBAAU,YAAY,GAAG,CAAC;AAC1B,sBAAU,cAAc,GAAG,GAAG;AAAA,UAC1C,OAAiB;AACL,sBAAU,eAAe,GAAG,CAAC;AAC7B,sBAAU,YAAY,GAAG,CAAC;AAC1B,sBAAU,cAAc,GAAG,GAAG;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAED,eAAS,wCAAwCA,mBAAkBC,oBAAmB;AACpF,YAAIA,oBAAmB;AACrB,cAAID,mBAAkB;AACpB,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAE9B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAC9B,sBAAU,YAAY,IAAI,CAAC;AAE3B,sBAAU,eAAe,IAAI,CAAC;AAC9B,sBAAU,cAAc,IAAI,GAAG;AAC/B,sBAAU,YAAY,IAAI,CAAC;AAE3B,sBAAU,cAAc,IAAI,GAAG;AAC/B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAAA,UACvC,OAAiB;AACL,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAE9B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,eAAe,IAAI,CAAC;AAE9B,sBAAU,eAAe,IAAI,CAAC;AAC9B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,cAAc,IAAI,GAAG;AAE/B,sBAAU,cAAc,IAAI,GAAG;AAC/B,sBAAU,YAAY,IAAI,CAAC;AAC3B,sBAAU,YAAY,IAAI,CAAC;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAED,eAAS,eAAe,QAAQ,IAAI,IAAIA,mBAAkB,OAAO,GAAG;AAIlE,gBAAQ,MAAM,eAAa;AAAA,UACzB,KAAK;AACH,gBAAI,OAAO;AACT,iCAAmB,QAAQ,IAAI,IAAI,GAAG,GAAG;AAAA,YACvD,OAAmB;AACL,iCAAmB,QAAQ,IAAI,IAAI,GAAG,GAAG;AAAA,YAC1C;AAED;AAAA,UAEF,KAAK;AACH,gBAAI,OAAO;AACT,uBAAS,WAAW,IAAI,MAAM;AAC9B,uBAAS,IAAI,SAAS,GAAG,CAAC,SAAS,CAAC;AAEpC,uBAAS,WAAW,UAAU,QAAQ,EAAE,IAAI,MAAM;AAClD,uBAAS,WAAW,UAAU,QAAQ,EAAE,IAAI,MAAM;AAGlD,kBAAIA,mBAAkB;AACpB,yBAAS,QAAQ,UAAU,IAAI,CAAC;AAChC,yBAAS,QAAQ,UAAU,IAAI,CAAC;AAChC,yBAAS,QAAQ,UAAU,IAAI,CAAC;AAAA,cAChD,OAAqB;AACL,yBAAS,QAAQ,UAAU,IAAI,CAAC;AAEhC,oBAAI,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,QAAQ,UAAU,IAAI,CAAC,IAAI,SAAS,QAAQ,UAAU,IAAI,CAAC;AAC3F,yBAAS,QAAQ,UAAU,IAAI,CAAC;AAAA,cACjC;AAAA,YACf,OAAmB;AACL,uBAAS,WAAW,IAAI,MAAM;AAC9B,uBAAS,IAAI,SAAS,GAAG,CAAC,SAAS,CAAC;AAEpC,uBAAS,WAAW,UAAU,QAAQ,EAAE,IAAI,MAAM;AAClD,uBAAS,WAAW,UAAU,QAAQ,EAAE,IAAI,MAAM;AAElD,oBAAM,KAAK,SAAS;AAGpB,kBAAIA,mBAAkB;AACpB,yBAAS,QAAQ,UAAU,KAAK,IAAI,CAAC;AACrC,yBAAS,QAAQ,UAAU,KAAK,IAAI,CAAC;AACrC,yBAAS,QAAQ,UAAU,KAAK,IAAI,CAAC;AAAA,cACrD,OAAqB;AACL,yBAAS,QAAQ,UAAU,KAAK,IAAI,CAAC;AACrC,yBAAS,QAAQ,UAAU,KAAK,IAAI,CAAC;AACrC,yBAAS,QAAQ,UAAU,KAAK,IAAI,CAAC;AAAA,cACtC;AAAA,YACF;AAED;AAAA,QAMH;AAAA,MACF;AAED,eAAS,uBAAuBE,SAAQ;AAItC,YAAI,YAAY;AAChB,iBAAS,IAAI,GAAG,IAAIA,QAAO,SAAS,GAAG,IAAI,GAAG,KAAK;AACjD,cAAIA,QAAO,CAAC,EAAE,WAAWA,QAAO,IAAI,CAAC,CAAC,IAAI,aAAa;AACrD,wBAAY;AACZ;AAAA,UACD;AAAA,QACF;AAED,YAAI,CAAC;AAAW,iBAAOA;AAEvB,cAAM,YAAY,CAAE;AACpB,kBAAU,KAAKA,QAAO,CAAC,CAAC;AAExB,iBAAS,IAAI,GAAG,IAAIA,QAAO,SAAS,GAAG,IAAI,GAAG,KAAK;AACjD,cAAIA,QAAO,CAAC,EAAE,WAAWA,QAAO,IAAI,CAAC,CAAC,KAAK,aAAa;AACtD,sBAAU,KAAKA,QAAO,CAAC,CAAC;AAAA,UACzB;AAAA,QACF;AAED,kBAAU,KAAKA,QAAOA,QAAO,SAAS,CAAC,CAAC;AAExC,eAAO;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAED,SAAOvB;AACT,GAAC;;"}
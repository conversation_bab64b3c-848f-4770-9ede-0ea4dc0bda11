{"version": 3, "file": "SSRPass.js", "sources": ["../../src/postprocessing/SSRPass.js"], "sourcesContent": ["import {\n  AddEquation,\n  Color,\n  NormalBlending,\n  DepthTexture,\n  SrcAlphaFactor,\n  OneMinusSrcAlphaFactor,\n  MeshNormalMaterial,\n  MeshBasicMaterial,\n  NearestFilter,\n  NoBlending,\n  ShaderMaterial,\n  UniformsUtils,\n  UnsignedShortType,\n  WebGLRenderTarget,\n  HalfFloatType,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { SSRShader } from '../shaders/SSRShader'\nimport { SSRBlurShader } from '../shaders/SSRShader'\nimport { SSRDepthShader } from '../shaders/SSRShader'\nimport { CopyShader } from '../shaders/CopyShader'\n\nconst SSRPass = /* @__PURE__ */ (() => {\n  class SSRPass extends Pass {\n    static OUTPUT = {\n      Default: 0,\n      SSR: 1,\n      Beauty: 3,\n      Depth: 4,\n      Normal: 5,\n      Metalness: 7,\n    }\n    constructor({ renderer, scene, camera, width, height, selects, bouncing = false, groundReflector }) {\n      super()\n\n      this.width = width !== undefined ? width : 512\n      this.height = height !== undefined ? height : 512\n\n      this.clear = true\n\n      this.renderer = renderer\n      this.scene = scene\n      this.camera = camera\n      this.groundReflector = groundReflector\n\n      this.opacity = SSRShader.uniforms.opacity.value\n      this.output = 0\n\n      this.maxDistance = SSRShader.uniforms.maxDistance.value\n      this.thickness = SSRShader.uniforms.thickness.value\n\n      this.tempColor = new Color()\n\n      this._selects = selects\n      this.selective = Array.isArray(this._selects)\n      Object.defineProperty(this, 'selects', {\n        get() {\n          return this._selects\n        },\n        set(val) {\n          if (this._selects === val) return\n          this._selects = val\n          if (Array.isArray(val)) {\n            this.selective = true\n            this.ssrMaterial.defines.SELECTIVE = true\n            this.ssrMaterial.needsUpdate = true\n          } else {\n            this.selective = false\n            this.ssrMaterial.defines.SELECTIVE = false\n            this.ssrMaterial.needsUpdate = true\n          }\n        },\n      })\n\n      this._bouncing = bouncing\n      Object.defineProperty(this, 'bouncing', {\n        get() {\n          return this._bouncing\n        },\n        set(val) {\n          if (this._bouncing === val) return\n          this._bouncing = val\n          if (val) {\n            this.ssrMaterial.uniforms['tDiffuse'].value = this.prevRenderTarget.texture\n          } else {\n            this.ssrMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          }\n        },\n      })\n\n      this.blur = true\n\n      this._distanceAttenuation = SSRShader.defines.DISTANCE_ATTENUATION\n      Object.defineProperty(this, 'distanceAttenuation', {\n        get() {\n          return this._distanceAttenuation\n        },\n        set(val) {\n          if (this._distanceAttenuation === val) return\n          this._distanceAttenuation = val\n          this.ssrMaterial.defines.DISTANCE_ATTENUATION = val\n          this.ssrMaterial.needsUpdate = true\n        },\n      })\n\n      this._fresnel = SSRShader.defines.FRESNEL\n      Object.defineProperty(this, 'fresnel', {\n        get() {\n          return this._fresnel\n        },\n        set(val) {\n          if (this._fresnel === val) return\n          this._fresnel = val\n          this.ssrMaterial.defines.FRESNEL = val\n          this.ssrMaterial.needsUpdate = true\n        },\n      })\n\n      this._infiniteThick = SSRShader.defines.INFINITE_THICK\n      Object.defineProperty(this, 'infiniteThick', {\n        get() {\n          return this._infiniteThick\n        },\n        set(val) {\n          if (this._infiniteThick === val) return\n          this._infiniteThick = val\n          this.ssrMaterial.defines.INFINITE_THICK = val\n          this.ssrMaterial.needsUpdate = true\n        },\n      })\n\n      // beauty render target with depth buffer\n\n      const depthTexture = new DepthTexture()\n      depthTexture.type = UnsignedShortType\n      depthTexture.minFilter = NearestFilter\n      depthTexture.magFilter = NearestFilter\n\n      this.beautyRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n        depthTexture: depthTexture,\n        depthBuffer: true,\n      })\n\n      //for bouncing\n      this.prevRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n      })\n\n      // normal render target\n\n      this.normalRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n      })\n\n      // metalness render target\n\n      this.metalnessRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        type: HalfFloatType,\n      })\n\n      // ssr render target\n\n      this.ssrRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n      })\n\n      this.blurRenderTarget = this.ssrRenderTarget.clone()\n      this.blurRenderTarget2 = this.ssrRenderTarget.clone()\n      // this.blurRenderTarget3 = this.ssrRenderTarget.clone();\n\n      // ssr material\n\n      this.ssrMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRShader.defines, {\n          MAX_STEP: Math.sqrt(this.width * this.width + this.height * this.height),\n        }),\n        uniforms: UniformsUtils.clone(SSRShader.uniforms),\n        vertexShader: SSRShader.vertexShader,\n        fragmentShader: SSRShader.fragmentShader,\n        blending: NoBlending,\n      })\n\n      this.ssrMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n      this.ssrMaterial.uniforms['tNormal'].value = this.normalRenderTarget.texture\n      this.ssrMaterial.defines.SELECTIVE = this.selective\n      this.ssrMaterial.needsUpdate = true\n      this.ssrMaterial.uniforms['tMetalness'].value = this.metalnessRenderTarget.texture\n      this.ssrMaterial.uniforms['tDepth'].value = this.beautyRenderTarget.depthTexture\n      this.ssrMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.ssrMaterial.uniforms['cameraFar'].value = this.camera.far\n      this.ssrMaterial.uniforms['thickness'].value = this.thickness\n      this.ssrMaterial.uniforms['resolution'].value.set(this.width, this.height)\n      this.ssrMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssrMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      // normal material\n\n      this.normalMaterial = new MeshNormalMaterial()\n      this.normalMaterial.blending = NoBlending\n\n      // metalnessOn material\n\n      this.metalnessOnMaterial = new MeshBasicMaterial({\n        color: 'white',\n      })\n\n      // metalnessOff material\n\n      this.metalnessOffMaterial = new MeshBasicMaterial({\n        color: 'black',\n      })\n\n      // blur material\n\n      this.blurMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n        vertexShader: SSRBlurShader.vertexShader,\n        fragmentShader: SSRBlurShader.fragmentShader,\n      })\n      this.blurMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n      this.blurMaterial.uniforms['resolution'].value.set(this.width, this.height)\n\n      // blur material 2\n\n      this.blurMaterial2 = new ShaderMaterial({\n        defines: Object.assign({}, SSRBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n        vertexShader: SSRBlurShader.vertexShader,\n        fragmentShader: SSRBlurShader.fragmentShader,\n      })\n      this.blurMaterial2.uniforms['tDiffuse'].value = this.blurRenderTarget.texture\n      this.blurMaterial2.uniforms['resolution'].value.set(this.width, this.height)\n\n      // // blur material 3\n\n      // this.blurMaterial3 = new ShaderMaterial({\n      //   defines: Object.assign({}, SSRBlurShader.defines),\n      //   uniforms: UniformsUtils.clone(SSRBlurShader.uniforms),\n      //   vertexShader: SSRBlurShader.vertexShader,\n      //   fragmentShader: SSRBlurShader.fragmentShader\n      // });\n      // this.blurMaterial3.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture;\n      // this.blurMaterial3.uniforms['resolution'].value.set(this.width, this.height);\n\n      // material for rendering the depth\n\n      this.depthRenderMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSRDepthShader.defines),\n        uniforms: UniformsUtils.clone(SSRDepthShader.uniforms),\n        vertexShader: SSRDepthShader.vertexShader,\n        fragmentShader: SSRDepthShader.fragmentShader,\n        blending: NoBlending,\n      })\n      this.depthRenderMaterial.uniforms['tDepth'].value = this.beautyRenderTarget.depthTexture\n      this.depthRenderMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.depthRenderMaterial.uniforms['cameraFar'].value = this.camera.far\n\n      // material for rendering the content of a render target\n\n      this.copyMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        transparent: true,\n        depthTest: false,\n        depthWrite: false,\n        blendSrc: SrcAlphaFactor,\n        blendDst: OneMinusSrcAlphaFactor,\n        blendEquation: AddEquation,\n        blendSrcAlpha: SrcAlphaFactor,\n        blendDstAlpha: OneMinusSrcAlphaFactor,\n        blendEquationAlpha: AddEquation,\n        // premultipliedAlpha:true,\n      })\n\n      this.fsQuad = new FullScreenQuad(null)\n\n      this.originalClearColor = new Color()\n    }\n\n    dispose() {\n      // dispose render targets\n\n      this.beautyRenderTarget.dispose()\n      this.prevRenderTarget.dispose()\n      this.normalRenderTarget.dispose()\n      this.metalnessRenderTarget.dispose()\n      this.ssrRenderTarget.dispose()\n      this.blurRenderTarget.dispose()\n      this.blurRenderTarget2.dispose()\n      // this.blurRenderTarget3.dispose();\n\n      // dispose materials\n\n      this.normalMaterial.dispose()\n      this.metalnessOnMaterial.dispose()\n      this.metalnessOffMaterial.dispose()\n      this.blurMaterial.dispose()\n      this.blurMaterial2.dispose()\n      this.copyMaterial.dispose()\n      this.depthRenderMaterial.dispose()\n\n      // dipsose full screen quad\n\n      this.fsQuad.dispose()\n    }\n\n    render(renderer, writeBuffer /*, readBuffer, deltaTime, maskActive */) {\n      // render beauty and depth\n\n      renderer.setRenderTarget(this.beautyRenderTarget)\n      renderer.clear()\n      if (this.groundReflector) {\n        this.groundReflector.visible = false\n        this.groundReflector.doRender(this.renderer, this.scene, this.camera)\n        this.groundReflector.visible = true\n      }\n\n      renderer.render(this.scene, this.camera)\n      if (this.groundReflector) this.groundReflector.visible = false\n\n      // render normals\n\n      this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 0, 0)\n\n      // render metalnesses\n\n      if (this.selective) {\n        this.renderMetalness(renderer, this.metalnessOnMaterial, this.metalnessRenderTarget, 0, 0)\n      }\n\n      // render SSR\n\n      this.ssrMaterial.uniforms['opacity'].value = this.opacity\n      this.ssrMaterial.uniforms['maxDistance'].value = this.maxDistance\n      this.ssrMaterial.uniforms['thickness'].value = this.thickness\n      this.renderPass(renderer, this.ssrMaterial, this.ssrRenderTarget)\n\n      // render blur\n\n      if (this.blur) {\n        this.renderPass(renderer, this.blurMaterial, this.blurRenderTarget)\n        this.renderPass(renderer, this.blurMaterial2, this.blurRenderTarget2)\n        // this.renderPass(renderer, this.blurMaterial3, this.blurRenderTarget3);\n      }\n\n      // output result to screen\n\n      switch (this.output) {\n        case SSRPass.OUTPUT.Default:\n          if (this.bouncing) {\n            this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n\n            if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n            else this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n            this.copyMaterial.blending = NormalBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n\n            this.copyMaterial.uniforms['tDiffuse'].value = this.prevRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n          } else {\n            this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n            if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n            else this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n            this.copyMaterial.blending = NormalBlending\n            this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n          }\n\n          break\n        case SSRPass.OUTPUT.SSR:\n          if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n          else this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          if (this.bouncing) {\n            if (this.blur) this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget2.texture\n            else this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n            this.copyMaterial.blending = NoBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n\n            this.copyMaterial.uniforms['tDiffuse'].value = this.ssrRenderTarget.texture\n            this.copyMaterial.blending = NormalBlending\n            this.renderPass(renderer, this.copyMaterial, this.prevRenderTarget)\n          }\n\n          break\n\n        case SSRPass.OUTPUT.Beauty:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSRPass.OUTPUT.Depth:\n          this.renderPass(renderer, this.depthRenderMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSRPass.OUTPUT.Normal:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.normalRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSRPass.OUTPUT.Metalness:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.metalnessRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        default:\n          console.warn('THREE.SSRPass: Unknown output type.')\n      }\n    }\n\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      // save original state\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor))\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor)\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n\n      // setup pass state\n      renderer.autoClear = false\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.material = passMaterial\n      this.fsQuad.render(renderer)\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor))\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor)\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.overrideMaterial = overrideMaterial\n      renderer.render(this.scene, this.camera)\n      this.scene.overrideMaterial = null\n\n      // restore original state\n\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderMetalness(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      this.originalClearColor.copy(renderer.getClearColor(this.tempColor))\n      const originalClearAlpha = renderer.getClearAlpha(this.tempColor)\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.traverseVisible((child) => {\n        child._SSRPassBackupMaterial = child.material\n        if (this._selects.includes(child)) {\n          child.material = this.metalnessOnMaterial\n        } else {\n          child.material = this.metalnessOffMaterial\n        }\n      })\n      renderer.render(this.scene, this.camera)\n      this.scene.traverseVisible((child) => {\n        child.material = child._SSRPassBackupMaterial\n      })\n\n      // restore original state\n\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    setSize(width, height) {\n      this.width = width\n      this.height = height\n\n      this.ssrMaterial.defines.MAX_STEP = Math.sqrt(width * width + height * height)\n      this.ssrMaterial.needsUpdate = true\n      this.beautyRenderTarget.setSize(width, height)\n      this.prevRenderTarget.setSize(width, height)\n      this.ssrRenderTarget.setSize(width, height)\n      this.normalRenderTarget.setSize(width, height)\n      this.metalnessRenderTarget.setSize(width, height)\n      this.blurRenderTarget.setSize(width, height)\n      this.blurRenderTarget2.setSize(width, height)\n      // this.blurRenderTarget3.setSize(width, height);\n\n      this.ssrMaterial.uniforms['resolution'].value.set(width, height)\n      this.ssrMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssrMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      this.blurMaterial.uniforms['resolution'].value.set(width, height)\n      this.blurMaterial2.uniforms['resolution'].value.set(width, height)\n    }\n  }\n\n  return SSRPass\n})()\n\nexport { SSRPass }\n"], "names": ["SSRPass"], "mappings": ";;;;;;;;;;AAuBK,MAAC,UAA2B,uBAAM;AACrC,QAAM,WAAN,cAAsB,KAAK;AAAA,IASzB,YAAY,EAAE,UAAU,OAAO,QAAQ,OAAO,QAAQ,SAAS,WAAW,OAAO,gBAAe,GAAI;AAClG,YAAO;AAEP,WAAK,QAAQ,UAAU,SAAY,QAAQ;AAC3C,WAAK,SAAS,WAAW,SAAY,SAAS;AAE9C,WAAK,QAAQ;AAEb,WAAK,WAAW;AAChB,WAAK,QAAQ;AACb,WAAK,SAAS;AACd,WAAK,kBAAkB;AAEvB,WAAK,UAAU,UAAU,SAAS,QAAQ;AAC1C,WAAK,SAAS;AAEd,WAAK,cAAc,UAAU,SAAS,YAAY;AAClD,WAAK,YAAY,UAAU,SAAS,UAAU;AAE9C,WAAK,YAAY,IAAI,MAAO;AAE5B,WAAK,WAAW;AAChB,WAAK,YAAY,MAAM,QAAQ,KAAK,QAAQ;AAC5C,aAAO,eAAe,MAAM,WAAW;AAAA,QACrC,MAAM;AACJ,iBAAO,KAAK;AAAA,QACb;AAAA,QACD,IAAI,KAAK;AACP,cAAI,KAAK,aAAa;AAAK;AAC3B,eAAK,WAAW;AAChB,cAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,iBAAK,YAAY;AACjB,iBAAK,YAAY,QAAQ,YAAY;AACrC,iBAAK,YAAY,cAAc;AAAA,UAC3C,OAAiB;AACL,iBAAK,YAAY;AACjB,iBAAK,YAAY,QAAQ,YAAY;AACrC,iBAAK,YAAY,cAAc;AAAA,UAChC;AAAA,QACF;AAAA,MACT,CAAO;AAED,WAAK,YAAY;AACjB,aAAO,eAAe,MAAM,YAAY;AAAA,QACtC,MAAM;AACJ,iBAAO,KAAK;AAAA,QACb;AAAA,QACD,IAAI,KAAK;AACP,cAAI,KAAK,cAAc;AAAK;AAC5B,eAAK,YAAY;AACjB,cAAI,KAAK;AACP,iBAAK,YAAY,SAAS,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AAAA,UAChF,OAAiB;AACL,iBAAK,YAAY,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AAAA,UACvE;AAAA,QACF;AAAA,MACT,CAAO;AAED,WAAK,OAAO;AAEZ,WAAK,uBAAuB,UAAU,QAAQ;AAC9C,aAAO,eAAe,MAAM,uBAAuB;AAAA,QACjD,MAAM;AACJ,iBAAO,KAAK;AAAA,QACb;AAAA,QACD,IAAI,KAAK;AACP,cAAI,KAAK,yBAAyB;AAAK;AACvC,eAAK,uBAAuB;AAC5B,eAAK,YAAY,QAAQ,uBAAuB;AAChD,eAAK,YAAY,cAAc;AAAA,QAChC;AAAA,MACT,CAAO;AAED,WAAK,WAAW,UAAU,QAAQ;AAClC,aAAO,eAAe,MAAM,WAAW;AAAA,QACrC,MAAM;AACJ,iBAAO,KAAK;AAAA,QACb;AAAA,QACD,IAAI,KAAK;AACP,cAAI,KAAK,aAAa;AAAK;AAC3B,eAAK,WAAW;AAChB,eAAK,YAAY,QAAQ,UAAU;AACnC,eAAK,YAAY,cAAc;AAAA,QAChC;AAAA,MACT,CAAO;AAED,WAAK,iBAAiB,UAAU,QAAQ;AACxC,aAAO,eAAe,MAAM,iBAAiB;AAAA,QAC3C,MAAM;AACJ,iBAAO,KAAK;AAAA,QACb;AAAA,QACD,IAAI,KAAK;AACP,cAAI,KAAK,mBAAmB;AAAK;AACjC,eAAK,iBAAiB;AACtB,eAAK,YAAY,QAAQ,iBAAiB;AAC1C,eAAK,YAAY,cAAc;AAAA,QAChC;AAAA,MACT,CAAO;AAID,YAAM,eAAe,IAAI,aAAc;AACvC,mBAAa,OAAO;AACpB,mBAAa,YAAY;AACzB,mBAAa,YAAY;AAEzB,WAAK,qBAAqB,IAAI,kBAAkB,KAAK,OAAO,KAAK,QAAQ;AAAA,QACvE,WAAW;AAAA,QACX,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,QACA,aAAa;AAAA,MACrB,CAAO;AAGD,WAAK,mBAAmB,IAAI,kBAAkB,KAAK,OAAO,KAAK,QAAQ;AAAA,QACrE,WAAW;AAAA,QACX,WAAW;AAAA,MACnB,CAAO;AAID,WAAK,qBAAqB,IAAI,kBAAkB,KAAK,OAAO,KAAK,QAAQ;AAAA,QACvE,WAAW;AAAA,QACX,WAAW;AAAA,QACX,MAAM;AAAA,MACd,CAAO;AAID,WAAK,wBAAwB,IAAI,kBAAkB,KAAK,OAAO,KAAK,QAAQ;AAAA,QAC1E,WAAW;AAAA,QACX,WAAW;AAAA,QACX,MAAM;AAAA,MACd,CAAO;AAID,WAAK,kBAAkB,IAAI,kBAAkB,KAAK,OAAO,KAAK,QAAQ;AAAA,QACpE,WAAW;AAAA,QACX,WAAW;AAAA,MACnB,CAAO;AAED,WAAK,mBAAmB,KAAK,gBAAgB,MAAO;AACpD,WAAK,oBAAoB,KAAK,gBAAgB,MAAO;AAKrD,WAAK,cAAc,IAAI,eAAe;AAAA,QACpC,SAAS,OAAO,OAAO,CAAA,GAAI,UAAU,SAAS;AAAA,UAC5C,UAAU,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,KAAK,SAAS,KAAK,MAAM;AAAA,QACjF,CAAS;AAAA,QACD,UAAU,cAAc,MAAM,UAAU,QAAQ;AAAA,QAChD,cAAc,UAAU;AAAA,QACxB,gBAAgB,UAAU;AAAA,QAC1B,UAAU;AAAA,MAClB,CAAO;AAED,WAAK,YAAY,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACtE,WAAK,YAAY,SAAS,SAAS,EAAE,QAAQ,KAAK,mBAAmB;AACrE,WAAK,YAAY,QAAQ,YAAY,KAAK;AAC1C,WAAK,YAAY,cAAc;AAC/B,WAAK,YAAY,SAAS,YAAY,EAAE,QAAQ,KAAK,sBAAsB;AAC3E,WAAK,YAAY,SAAS,QAAQ,EAAE,QAAQ,KAAK,mBAAmB;AACpE,WAAK,YAAY,SAAS,YAAY,EAAE,QAAQ,KAAK,OAAO;AAC5D,WAAK,YAAY,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAC3D,WAAK,YAAY,SAAS,WAAW,EAAE,QAAQ,KAAK;AACpD,WAAK,YAAY,SAAS,YAAY,EAAE,MAAM,IAAI,KAAK,OAAO,KAAK,MAAM;AACzE,WAAK,YAAY,SAAS,wBAAwB,EAAE,MAAM,KAAK,KAAK,OAAO,gBAAgB;AAC3F,WAAK,YAAY,SAAS,+BAA+B,EAAE,MAAM,KAAK,KAAK,OAAO,uBAAuB;AAIzG,WAAK,iBAAiB,IAAI,mBAAoB;AAC9C,WAAK,eAAe,WAAW;AAI/B,WAAK,sBAAsB,IAAI,kBAAkB;AAAA,QAC/C,OAAO;AAAA,MACf,CAAO;AAID,WAAK,uBAAuB,IAAI,kBAAkB;AAAA,QAChD,OAAO;AAAA,MACf,CAAO;AAID,WAAK,eAAe,IAAI,eAAe;AAAA,QACrC,SAAS,OAAO,OAAO,CAAA,GAAI,cAAc,OAAO;AAAA,QAChD,UAAU,cAAc,MAAM,cAAc,QAAQ;AAAA,QACpD,cAAc,cAAc;AAAA,QAC5B,gBAAgB,cAAc;AAAA,MACtC,CAAO;AACD,WAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,gBAAgB;AACpE,WAAK,aAAa,SAAS,YAAY,EAAE,MAAM,IAAI,KAAK,OAAO,KAAK,MAAM;AAI1E,WAAK,gBAAgB,IAAI,eAAe;AAAA,QACtC,SAAS,OAAO,OAAO,CAAA,GAAI,cAAc,OAAO;AAAA,QAChD,UAAU,cAAc,MAAM,cAAc,QAAQ;AAAA,QACpD,cAAc,cAAc;AAAA,QAC5B,gBAAgB,cAAc;AAAA,MACtC,CAAO;AACD,WAAK,cAAc,SAAS,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AACtE,WAAK,cAAc,SAAS,YAAY,EAAE,MAAM,IAAI,KAAK,OAAO,KAAK,MAAM;AAe3E,WAAK,sBAAsB,IAAI,eAAe;AAAA,QAC5C,SAAS,OAAO,OAAO,CAAA,GAAI,eAAe,OAAO;AAAA,QACjD,UAAU,cAAc,MAAM,eAAe,QAAQ;AAAA,QACrD,cAAc,eAAe;AAAA,QAC7B,gBAAgB,eAAe;AAAA,QAC/B,UAAU;AAAA,MAClB,CAAO;AACD,WAAK,oBAAoB,SAAS,QAAQ,EAAE,QAAQ,KAAK,mBAAmB;AAC5E,WAAK,oBAAoB,SAAS,YAAY,EAAE,QAAQ,KAAK,OAAO;AACpE,WAAK,oBAAoB,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAInE,WAAK,eAAe,IAAI,eAAe;AAAA,QACrC,UAAU,cAAc,MAAM,WAAW,QAAQ;AAAA,QACjD,cAAc,WAAW;AAAA,QACzB,gBAAgB,WAAW;AAAA,QAC3B,aAAa;AAAA,QACb,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,eAAe;AAAA,QACf,eAAe;AAAA,QACf,eAAe;AAAA,QACf,oBAAoB;AAAA;AAAA,MAE5B,CAAO;AAED,WAAK,SAAS,IAAI,eAAe,IAAI;AAErC,WAAK,qBAAqB,IAAI,MAAO;AAAA,IACtC;AAAA,IAED,UAAU;AAGR,WAAK,mBAAmB,QAAS;AACjC,WAAK,iBAAiB,QAAS;AAC/B,WAAK,mBAAmB,QAAS;AACjC,WAAK,sBAAsB,QAAS;AACpC,WAAK,gBAAgB,QAAS;AAC9B,WAAK,iBAAiB,QAAS;AAC/B,WAAK,kBAAkB,QAAS;AAKhC,WAAK,eAAe,QAAS;AAC7B,WAAK,oBAAoB,QAAS;AAClC,WAAK,qBAAqB,QAAS;AACnC,WAAK,aAAa,QAAS;AAC3B,WAAK,cAAc,QAAS;AAC5B,WAAK,aAAa,QAAS;AAC3B,WAAK,oBAAoB,QAAS;AAIlC,WAAK,OAAO,QAAS;AAAA,IACtB;AAAA,IAED,OAAO,UAAU,aAAsD;AAGrE,eAAS,gBAAgB,KAAK,kBAAkB;AAChD,eAAS,MAAO;AAChB,UAAI,KAAK,iBAAiB;AACxB,aAAK,gBAAgB,UAAU;AAC/B,aAAK,gBAAgB,SAAS,KAAK,UAAU,KAAK,OAAO,KAAK,MAAM;AACpE,aAAK,gBAAgB,UAAU;AAAA,MAChC;AAED,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AACvC,UAAI,KAAK;AAAiB,aAAK,gBAAgB,UAAU;AAIzD,WAAK,eAAe,UAAU,KAAK,gBAAgB,KAAK,oBAAoB,GAAG,CAAC;AAIhF,UAAI,KAAK,WAAW;AAClB,aAAK,gBAAgB,UAAU,KAAK,qBAAqB,KAAK,uBAAuB,GAAG,CAAC;AAAA,MAC1F;AAID,WAAK,YAAY,SAAS,SAAS,EAAE,QAAQ,KAAK;AAClD,WAAK,YAAY,SAAS,aAAa,EAAE,QAAQ,KAAK;AACtD,WAAK,YAAY,SAAS,WAAW,EAAE,QAAQ,KAAK;AACpD,WAAK,WAAW,UAAU,KAAK,aAAa,KAAK,eAAe;AAIhE,UAAI,KAAK,MAAM;AACb,aAAK,WAAW,UAAU,KAAK,cAAc,KAAK,gBAAgB;AAClE,aAAK,WAAW,UAAU,KAAK,eAAe,KAAK,iBAAiB;AAAA,MAErE;AAID,cAAQ,KAAK,QAAM;AAAA,QACjB,KAAK,SAAQ,OAAO;AAClB,cAAI,KAAK,UAAU;AACjB,iBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,iBAAK,aAAa,WAAW;AAC7B,iBAAK,WAAW,UAAU,KAAK,cAAc,KAAK,gBAAgB;AAElE,gBAAI,KAAK;AAAM,mBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,kBAAkB;AAAA;AAChF,mBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,gBAAgB;AACzE,iBAAK,aAAa,WAAW;AAC7B,iBAAK,WAAW,UAAU,KAAK,cAAc,KAAK,gBAAgB;AAElE,iBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AACrE,iBAAK,aAAa,WAAW;AAC7B,iBAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAAA,UACjG,OAAiB;AACL,iBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,iBAAK,aAAa,WAAW;AAC7B,iBAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF,gBAAI,KAAK;AAAM,mBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,kBAAkB;AAAA;AAChF,mBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,gBAAgB;AACzE,iBAAK,aAAa,WAAW;AAC7B,iBAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAAA,UACtF;AAED;AAAA,QACF,KAAK,SAAQ,OAAO;AAClB,cAAI,KAAK;AAAM,iBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,kBAAkB;AAAA;AAChF,iBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,gBAAgB;AACzE,eAAK,aAAa,WAAW;AAC7B,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF,cAAI,KAAK,UAAU;AACjB,gBAAI,KAAK;AAAM,mBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,kBAAkB;AAAA;AAChF,mBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AAC5E,iBAAK,aAAa,WAAW;AAC7B,iBAAK,WAAW,UAAU,KAAK,cAAc,KAAK,gBAAgB;AAElE,iBAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,gBAAgB;AACpE,iBAAK,aAAa,WAAW;AAC7B,iBAAK,WAAW,UAAU,KAAK,cAAc,KAAK,gBAAgB;AAAA,UACnE;AAED;AAAA,QAEF,KAAK,SAAQ,OAAO;AAClB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,eAAK,aAAa,WAAW;AAC7B,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF,KAAK,SAAQ,OAAO;AAClB,eAAK,WAAW,UAAU,KAAK,qBAAqB,KAAK,iBAAiB,OAAO,WAAW;AAE5F;AAAA,QAEF,KAAK,SAAQ,OAAO;AAClB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,eAAK,aAAa,WAAW;AAC7B,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF,KAAK,SAAQ,OAAO;AAClB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,sBAAsB;AAC1E,eAAK,aAAa,WAAW;AAC7B,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF;AACE,kBAAQ,KAAK,qCAAqC;AAAA,MACrD;AAAA,IACF;AAAA,IAED,WAAW,UAAU,cAAc,cAAc,YAAY,YAAY;AAEvE,WAAK,mBAAmB,KAAK,SAAS,cAAc,KAAK,SAAS,CAAC;AACnE,YAAM,qBAAqB,SAAS,cAAc,KAAK,SAAS;AAChE,YAAM,oBAAoB,SAAS;AAEnC,eAAS,gBAAgB,YAAY;AAGrC,eAAS,YAAY;AACrB,UAAI,eAAe,UAAa,eAAe,MAAM;AACnD,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,cAAc,CAAG;AACxC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,OAAO,WAAW;AACvB,WAAK,OAAO,OAAO,QAAQ;AAG3B,eAAS,YAAY;AACrB,eAAS,cAAc,KAAK,kBAAkB;AAC9C,eAAS,cAAc,kBAAkB;AAAA,IAC1C;AAAA,IAED,eAAe,UAAU,kBAAkB,cAAc,YAAY,YAAY;AAC/E,WAAK,mBAAmB,KAAK,SAAS,cAAc,KAAK,SAAS,CAAC;AACnE,YAAM,qBAAqB,SAAS,cAAc,KAAK,SAAS;AAChE,YAAM,oBAAoB,SAAS;AAEnC,eAAS,gBAAgB,YAAY;AACrC,eAAS,YAAY;AAErB,mBAAa,iBAAiB,cAAc;AAC5C,mBAAa,iBAAiB,cAAc;AAE5C,UAAI,eAAe,UAAa,eAAe,MAAM;AACnD,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,cAAc,CAAG;AACxC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,MAAM,mBAAmB;AAC9B,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AACvC,WAAK,MAAM,mBAAmB;AAI9B,eAAS,YAAY;AACrB,eAAS,cAAc,KAAK,kBAAkB;AAC9C,eAAS,cAAc,kBAAkB;AAAA,IAC1C;AAAA,IAED,gBAAgB,UAAU,kBAAkB,cAAc,YAAY,YAAY;AAChF,WAAK,mBAAmB,KAAK,SAAS,cAAc,KAAK,SAAS,CAAC;AACnE,YAAM,qBAAqB,SAAS,cAAc,KAAK,SAAS;AAChE,YAAM,oBAAoB,SAAS;AAEnC,eAAS,gBAAgB,YAAY;AACrC,eAAS,YAAY;AAErB,mBAAa,iBAAiB,cAAc;AAC5C,mBAAa,iBAAiB,cAAc;AAE5C,UAAI,eAAe,UAAa,eAAe,MAAM;AACnD,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,cAAc,CAAG;AACxC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,MAAM,gBAAgB,CAAC,UAAU;AACpC,cAAM,yBAAyB,MAAM;AACrC,YAAI,KAAK,SAAS,SAAS,KAAK,GAAG;AACjC,gBAAM,WAAW,KAAK;AAAA,QAChC,OAAe;AACL,gBAAM,WAAW,KAAK;AAAA,QACvB;AAAA,MACT,CAAO;AACD,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AACvC,WAAK,MAAM,gBAAgB,CAAC,UAAU;AACpC,cAAM,WAAW,MAAM;AAAA,MAC/B,CAAO;AAID,eAAS,YAAY;AACrB,eAAS,cAAc,KAAK,kBAAkB;AAC9C,eAAS,cAAc,kBAAkB;AAAA,IAC1C;AAAA,IAED,QAAQ,OAAO,QAAQ;AACrB,WAAK,QAAQ;AACb,WAAK,SAAS;AAEd,WAAK,YAAY,QAAQ,WAAW,KAAK,KAAK,QAAQ,QAAQ,SAAS,MAAM;AAC7E,WAAK,YAAY,cAAc;AAC/B,WAAK,mBAAmB,QAAQ,OAAO,MAAM;AAC7C,WAAK,iBAAiB,QAAQ,OAAO,MAAM;AAC3C,WAAK,gBAAgB,QAAQ,OAAO,MAAM;AAC1C,WAAK,mBAAmB,QAAQ,OAAO,MAAM;AAC7C,WAAK,sBAAsB,QAAQ,OAAO,MAAM;AAChD,WAAK,iBAAiB,QAAQ,OAAO,MAAM;AAC3C,WAAK,kBAAkB,QAAQ,OAAO,MAAM;AAG5C,WAAK,YAAY,SAAS,YAAY,EAAE,MAAM,IAAI,OAAO,MAAM;AAC/D,WAAK,YAAY,SAAS,wBAAwB,EAAE,MAAM,KAAK,KAAK,OAAO,gBAAgB;AAC3F,WAAK,YAAY,SAAS,+BAA+B,EAAE,MAAM,KAAK,KAAK,OAAO,uBAAuB;AAEzG,WAAK,aAAa,SAAS,YAAY,EAAE,MAAM,IAAI,OAAO,MAAM;AAChE,WAAK,cAAc,SAAS,YAAY,EAAE,MAAM,IAAI,OAAO,MAAM;AAAA,IAClE;AAAA,EACF;AA5gBD,MAAMA,WAAN;AACE,gBADIA,UACG,UAAS;AAAA,IACd,SAAS;AAAA,IACT,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,EACZ;AAsgBH,SAAOA;AACT,GAAC;"}
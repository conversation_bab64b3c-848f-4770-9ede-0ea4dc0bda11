{"version": 3, "file": "RaycasterHelper.cjs", "sources": ["../../src/helpers/RaycasterHelper.ts"], "sourcesContent": ["/**\n * from https://github.com/gsimone/things/tree/main/packages/three-raycaster-helper\n */\n\nimport {\n  BufferAttribute,\n  BufferGeometry,\n  Float32BufferAttribute,\n  InstancedMesh,\n  Intersection,\n  Line,\n  LineBasicMaterial,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  Raycaster,\n  SphereGeometry,\n  Vector3,\n} from 'three'\n\nconst _o = /* @__PURE__ */ new Object3D()\nconst _v = /* @__PURE__ */ new Vector3()\n\nclass RaycasterHelper extends Object3D {\n  raycaster: Raycaster\n  hits: Intersection[]\n\n  origin: Mesh<SphereGeometry, MeshBasicMaterial>\n  near: Line<BufferGeometry, LineBasicMaterial>\n  far: Line<BufferGeometry, LineBasicMaterial>\n\n  nearToFar: Line<BufferGeometry, LineBasicMaterial>\n  originToNear: Line<BufferGeometry, LineBasicMaterial>\n\n  hitPoints: InstancedMesh\n\n  colors = {\n    near: 0xffffff,\n    far: 0xffffff,\n    originToNear: 0x333333,\n    nearToFar: 0xffffff,\n    origin: [0x0eec82, 0xff005b],\n  }\n\n  constructor(raycaster: Raycaster, public numberOfHitsToVisualize = 20) {\n    super()\n    this.raycaster = raycaster\n\n    this.hits = []\n\n    this.origin = new Mesh(new SphereGeometry(0.04, 32), new MeshBasicMaterial())\n    this.origin.name = 'RaycasterHelper_origin'\n    this.origin.raycast = () => null\n\n    const size = 0.1\n    let geometry = new BufferGeometry()\n    // prettier-ignore\n    geometry.setAttribute( 'position', new Float32BufferAttribute( [\n              - size, size, 0,\n              size, size, 0,\n              size, - size, 0,\n              - size, - size, 0,\n              - size, size, 0\n          ], 3 ) );\n\n    this.near = new Line(geometry, new LineBasicMaterial())\n    this.near.name = 'RaycasterHelper_near'\n    this.near.raycast = () => null\n\n    this.far = new Line(geometry, new LineBasicMaterial())\n    this.far.name = 'RaycasterHelper_far'\n    this.far.raycast = () => null\n\n    this.nearToFar = new Line(new BufferGeometry(), new LineBasicMaterial())\n    this.nearToFar.name = 'RaycasterHelper_nearToFar'\n    this.nearToFar.raycast = () => null\n\n    this.nearToFar.geometry.setFromPoints([_v, _v])\n\n    this.originToNear = new Line(this.nearToFar.geometry.clone(), new LineBasicMaterial())\n    this.originToNear.name = 'RaycasterHelper_originToNear'\n    this.originToNear.raycast = () => null\n\n    this.hitPoints = new InstancedMesh(new SphereGeometry(0.04), new MeshBasicMaterial(), this.numberOfHitsToVisualize)\n    this.hitPoints.name = 'RaycasterHelper_hits'\n    this.hitPoints.raycast = () => null\n\n    this.add(this.nearToFar)\n    this.add(this.originToNear)\n\n    this.add(this.near)\n    this.add(this.far)\n\n    this.add(this.origin)\n    this.add(this.hitPoints)\n\n    this.setColors()\n  }\n\n  setColors = (colors?: Partial<typeof this.colors>) => {\n    const _colors = {\n      ...this.colors,\n      ...colors,\n    }\n\n    this.near.material.color.set(_colors.near)\n    this.far.material.color.set(_colors.far)\n    this.nearToFar.material.color.set(_colors.nearToFar)\n    this.originToNear.material.color.set(_colors.originToNear)\n  }\n\n  update = () => {\n    const origin = this.raycaster.ray.origin\n    const direction = this.raycaster.ray.direction\n\n    this.origin.position.copy(origin)\n\n    this.near.position.copy(origin).add(direction.clone().multiplyScalar(this.raycaster.near))\n\n    this.far.position.copy(origin).add(direction.clone().multiplyScalar(this.raycaster.far))\n\n    this.far.lookAt(origin)\n    this.near.lookAt(origin)\n\n    let pos = this.nearToFar.geometry.getAttribute('position') as BufferAttribute\n    pos.set([...this.near.position.toArray(), ...this.far.position.toArray()])\n    pos.needsUpdate = true\n\n    pos = this.originToNear.geometry.getAttribute('position') as BufferAttribute\n    pos.set([...origin.toArray(), ...this.near.position.toArray()])\n    pos.needsUpdate = true\n\n    /**\n     * Update hit points visualization\n     */\n    for (let i = 0; i < this.numberOfHitsToVisualize; i++) {\n      const hit = this.hits?.[i]\n\n      if (hit) {\n        const { point } = hit\n        _o.position.copy(point)\n        _o.scale.setScalar(1)\n      } else {\n        _o.scale.setScalar(0)\n      }\n\n      _o.updateMatrix()\n\n      this.hitPoints.setMatrixAt(i, _o.matrix)\n    }\n\n    this.hitPoints.instanceMatrix.needsUpdate = true\n\n    /**\n     * Update the color of the origin based on wether there are hits.\n     */\n    this.origin.material.color.set(this.hits.length > 0 ? this.colors.origin[0] : this.colors.origin[1])\n  }\n\n  dispose = () => {\n    this.origin.geometry.dispose()\n    this.origin.material.dispose()\n    this.near.geometry.dispose()\n    this.near.material.dispose()\n    this.far.geometry.dispose()\n    this.far.material.dispose()\n    this.nearToFar.geometry.dispose()\n    this.nearToFar.material.dispose()\n    this.originToNear.geometry.dispose()\n    this.originToNear.material.dispose()\n    this.hitPoints.dispose()\n  }\n}\n\nexport { RaycasterHelper }\n"], "names": ["Object3D", "Vector3", "<PERSON><PERSON>", "SphereGeometry", "MeshBasicMaterial", "BufferGeometry", "Float32BufferAttribute", "Line", "LineBasicMaterial", "In<PERSON>d<PERSON>esh"], "mappings": ";;;;;;;;;AAoBA,MAAM,yBAAyBA,MAAAA;AAC/B,MAAM,yBAAyBC,MAAAA;AAE/B,MAAM,wBAAwBD,MAAAA,SAAS;AAAA,EAqBrC,YAAY,WAA6B,0BAA0B,IAAI;AAC/D;AArBR;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAEA,kCAAS;AAAA,MACP,MAAM;AAAA,MACN,KAAK;AAAA,MACL,cAAc;AAAA,MACd,WAAW;AAAA,MACX,QAAQ,CAAC,QAAU,QAAQ;AAAA,IAAA;AA0D7B,qCAAY,CAAC,WAAyC;AACpD,YAAM,UAAU;AAAA,QACd,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MAAA;AAGL,WAAK,KAAK,SAAS,MAAM,IAAI,QAAQ,IAAI;AACzC,WAAK,IAAI,SAAS,MAAM,IAAI,QAAQ,GAAG;AACvC,WAAK,UAAU,SAAS,MAAM,IAAI,QAAQ,SAAS;AACnD,WAAK,aAAa,SAAS,MAAM,IAAI,QAAQ,YAAY;AAAA,IAAA;AAG3D,kCAAS,MAAM;;AACP,YAAA,SAAS,KAAK,UAAU,IAAI;AAC5B,YAAA,YAAY,KAAK,UAAU,IAAI;AAEhC,WAAA,OAAO,SAAS,KAAK,MAAM;AAEhC,WAAK,KAAK,SAAS,KAAK,MAAM,EAAE,IAAI,UAAU,MAAA,EAAQ,eAAe,KAAK,UAAU,IAAI,CAAC;AAEzF,WAAK,IAAI,SAAS,KAAK,MAAM,EAAE,IAAI,UAAU,MAAA,EAAQ,eAAe,KAAK,UAAU,GAAG,CAAC;AAElF,WAAA,IAAI,OAAO,MAAM;AACjB,WAAA,KAAK,OAAO,MAAM;AAEvB,UAAI,MAAM,KAAK,UAAU,SAAS,aAAa,UAAU;AACzD,UAAI,IAAI,CAAC,GAAG,KAAK,KAAK,SAAS,WAAW,GAAG,KAAK,IAAI,SAAS,QAAA,CAAS,CAAC;AACzE,UAAI,cAAc;AAElB,YAAM,KAAK,aAAa,SAAS,aAAa,UAAU;AACxD,UAAI,IAAI,CAAC,GAAG,OAAO,QAAQ,GAAG,GAAG,KAAK,KAAK,SAAS,QAAQ,CAAC,CAAC;AAC9D,UAAI,cAAc;AAKlB,eAAS,IAAI,GAAG,IAAI,KAAK,yBAAyB,KAAK;AAC/C,cAAA,OAAM,UAAK,SAAL,mBAAY;AAExB,YAAI,KAAK;AACD,gBAAA,EAAE,MAAU,IAAA;AACf,aAAA,SAAS,KAAK,KAAK;AACnB,aAAA,MAAM,UAAU,CAAC;AAAA,QAAA,OACf;AACF,aAAA,MAAM,UAAU,CAAC;AAAA,QACtB;AAEA,WAAG,aAAa;AAEhB,aAAK,UAAU,YAAY,GAAG,GAAG,MAAM;AAAA,MACzC;AAEK,WAAA,UAAU,eAAe,cAAc;AAK5C,WAAK,OAAO,SAAS,MAAM,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,OAAO,OAAO,CAAC,IAAI,KAAK,OAAO,OAAO,CAAC,CAAC;AAAA,IAAA;AAGrG,mCAAU,MAAM;AACT,WAAA,OAAO,SAAS;AAChB,WAAA,OAAO,SAAS;AAChB,WAAA,KAAK,SAAS;AACd,WAAA,KAAK,SAAS;AACd,WAAA,IAAI,SAAS;AACb,WAAA,IAAI,SAAS;AACb,WAAA,UAAU,SAAS;AACnB,WAAA,UAAU,SAAS;AACnB,WAAA,aAAa,SAAS;AACtB,WAAA,aAAa,SAAS;AAC3B,WAAK,UAAU;IAAQ;AA9HgB,SAAA,0BAAA;AAEvC,SAAK,YAAY;AAEjB,SAAK,OAAO;AAEP,SAAA,SAAS,IAAIE,MAAAA,KAAK,IAAIC,MAAAA,eAAe,MAAM,EAAE,GAAG,IAAIC,MAAA,kBAAA,CAAmB;AAC5E,SAAK,OAAO,OAAO;AACd,SAAA,OAAO,UAAU,MAAM;AAE5B,UAAM,OAAO;AACT,QAAA,WAAW,IAAIC,MAAAA;AAEV,aAAA,aAAc,YAAY,IAAIC,6BAAwB;AAAA,MACrD,CAAE;AAAA,MAAM;AAAA,MAAM;AAAA,MACd;AAAA,MAAM;AAAA,MAAM;AAAA,MACZ;AAAA,MAAM,CAAE;AAAA,MAAM;AAAA,MACd,CAAE;AAAA,MAAM,CAAE;AAAA,MAAM;AAAA,MAChB,CAAE;AAAA,MAAM;AAAA,MAAM;AAAA,IAAA,GACf,CAAE,CAAE;AAEb,SAAK,OAAO,IAAIC,MAAAA,KAAK,UAAU,IAAIC,yBAAmB;AACtD,SAAK,KAAK,OAAO;AACZ,SAAA,KAAK,UAAU,MAAM;AAE1B,SAAK,MAAM,IAAID,MAAAA,KAAK,UAAU,IAAIC,yBAAmB;AACrD,SAAK,IAAI,OAAO;AACX,SAAA,IAAI,UAAU,MAAM;AAEpB,SAAA,YAAY,IAAID,MAAK,KAAA,IAAIF,MAAAA,kBAAkB,IAAIG,yBAAmB;AACvE,SAAK,UAAU,OAAO;AACjB,SAAA,UAAU,UAAU,MAAM;AAE/B,SAAK,UAAU,SAAS,cAAc,CAAC,IAAI,EAAE,CAAC;AAEzC,SAAA,eAAe,IAAID,MAAA,KAAK,KAAK,UAAU,SAAS,MAAM,GAAG,IAAIC,MAAA,kBAAA,CAAmB;AACrF,SAAK,aAAa,OAAO;AACpB,SAAA,aAAa,UAAU,MAAM;AAE7B,SAAA,YAAY,IAAIC,MAAA,cAAc,IAAIN,MAAA,eAAe,IAAI,GAAG,IAAIC,MAAA,kBAAA,GAAqB,KAAK,uBAAuB;AAClH,SAAK,UAAU,OAAO;AACjB,SAAA,UAAU,UAAU,MAAM;AAE1B,SAAA,IAAI,KAAK,SAAS;AAClB,SAAA,IAAI,KAAK,YAAY;AAErB,SAAA,IAAI,KAAK,IAAI;AACb,SAAA,IAAI,KAAK,GAAG;AAEZ,SAAA,IAAI,KAAK,MAAM;AACf,SAAA,IAAI,KAAK,SAAS;AAEvB,SAAK,UAAU;AAAA,EACjB;AA2EF;;"}
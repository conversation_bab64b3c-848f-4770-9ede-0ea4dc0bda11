{"version": 3, "file": "NURBSSurface.js", "sources": ["../../src/curves/NURBSSurface.js"], "sourcesContent": ["import { Vector4 } from 'three'\nimport * as NURBSUtils from '../curves/NURBSUtils'\n\n/**\n * NURBS surface object\n *\n * Implementation is based on (x, y [, z=0 [, w=1]]) control points with w=weight.\n **/\n\nclass NURBSSurface {\n  constructor(degree1, degree2, knots1, knots2 /* arrays of reals */, controlPoints /* array^2 of Vector(2|3|4) */) {\n    this.degree1 = degree1\n    this.degree2 = degree2\n    this.knots1 = knots1\n    this.knots2 = knots2\n    this.controlPoints = []\n\n    const len1 = knots1.length - degree1 - 1\n    const len2 = knots2.length - degree2 - 1\n\n    // ensure Vector4 for control points\n    for (let i = 0; i < len1; ++i) {\n      this.controlPoints[i] = []\n      for (let j = 0; j < len2; ++j) {\n        const point = controlPoints[i][j]\n        this.controlPoints[i][j] = new Vector4(point.x, point.y, point.z, point.w)\n      }\n    }\n  }\n\n  getPoint(t1, t2, target) {\n    const u = this.knots1[0] + t1 * (this.knots1[this.knots1.length - 1] - this.knots1[0]) // linear mapping t1->u\n    const v = this.knots2[0] + t2 * (this.knots2[this.knots2.length - 1] - this.knots2[0]) // linear mapping t2->u\n\n    NURBSUtils.calcSurfacePoint(this.degree1, this.degree2, this.knots1, this.knots2, this.controlPoints, u, v, target)\n  }\n}\n\nexport { NURBSSurface }\n"], "names": ["NURBSUtils.calcSurfacePoint"], "mappings": ";;AASA,MAAM,aAAa;AAAA,EACjB,YAAY,SAAS,SAAS,QAAQ,QAA8B,eAA8C;AAChH,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,gBAAgB,CAAE;AAEvB,UAAM,OAAO,OAAO,SAAS,UAAU;AACvC,UAAM,OAAO,OAAO,SAAS,UAAU;AAGvC,aAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAC7B,WAAK,cAAc,CAAC,IAAI,CAAE;AAC1B,eAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAC7B,cAAM,QAAQ,cAAc,CAAC,EAAE,CAAC;AAChC,aAAK,cAAc,CAAC,EAAE,CAAC,IAAI,IAAI,QAAQ,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AAAA,EAED,SAAS,IAAI,IAAI,QAAQ;AACvB,UAAM,IAAI,KAAK,OAAO,CAAC,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,IAAI,KAAK,OAAO,CAAC;AACpF,UAAM,IAAI,KAAK,OAAO,CAAC,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,IAAI,KAAK,OAAO,CAAC;AAEpFA,qBAA4B,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,KAAK,eAAe,GAAG,GAAG,MAAM;AAAA,EACnH;AACH;"}
<template>
  <el-dialog
    v-model="showInstructions"
    title="使用说明"
    width="600px"
    :before-close="handleClose"
  >
    <div class="instructions-content">
      <h3>🎮 操作指南</h3>
      <el-divider />
      
      <div class="instruction-section">
        <h4>🖱️ 3D场景控制</h4>
        <ul>
          <li><strong>旋转视角</strong>: 按住鼠标左键拖拽</li>
          <li><strong>缩放</strong>: 滚动鼠标滚轮</li>
          <li><strong>平移</strong>: 按住鼠标右键拖拽</li>
          <li><strong>选择设备</strong>: 点击3D场景中的设备</li>
        </ul>
      </div>

      <div class="instruction-section">
        <h4>🏠 设备控制</h4>
        <ul>
          <li><strong>空调</strong>: 调节温度(16-30°C)、切换模式</li>
          <li><strong>风扇</strong>: 调节风速(1-3档)、观看旋转动画</li>
          <li><strong>灯光</strong>: 调节亮度(0-100%)、实时光照效果</li>
          <li><strong>窗帘</strong>: 调节开合度(0-100%)、快捷操作</li>
        </ul>
      </div>

      <div class="instruction-section">
        <h4>📱 界面功能</h4>
        <ul>
          <li><strong>设备列表</strong>: 左侧面板显示所有设备状态</li>
          <li><strong>快捷开关</strong>: 直接在列表中开关设备</li>
          <li><strong>控制面板</strong>: 点击设备后右侧弹出详细控制</li>
          <li><strong>批量操作</strong>: 一键控制所有同类设备</li>
        </ul>
      </div>

      <div class="instruction-section">
        <h4>✨ 特色功能</h4>
        <ul>
          <li><strong>实时同步</strong>: 设备状态变化立即反映到3D场景</li>
          <li><strong>视觉反馈</strong>: LED指示灯、光照效果、动画</li>
          <li><strong>选中高亮</strong>: 选中的设备会显示绿色线框</li>
          <li><strong>响应式设计</strong>: 支持桌面和移动端访问</li>
        </ul>
      </div>

      <el-alert
        title="提示"
        type="info"
        :closable="false"
        show-icon
      >
        这是一个演示项目，所有设备控制都是模拟的。在实际应用中，可以连接真实的智能家居API。
      </el-alert>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-checkbox v-model="dontShowAgain">不再显示</el-checkbox>
        <el-button type="primary" @click="handleClose">知道了</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'Instructions',
  data() {
    return {
      showInstructions: false,
      dontShowAgain: false
    }
  },
  mounted() {
    // 检查是否需要显示说明
    const hasShownInstructions = localStorage.getItem('smart-home-3d-instructions-shown')
    if (!hasShownInstructions) {
      setTimeout(() => {
        this.showInstructions = true
      }, 1000)
    }
  },
  methods: {
    handleClose() {
      this.showInstructions = false
      if (this.dontShowAgain) {
        localStorage.setItem('smart-home-3d-instructions-shown', 'true')
      }
    },
    
    show() {
      this.showInstructions = true
    }
  }
}
</script>

<style scoped>
.instructions-content {
  max-height: 500px;
  overflow-y: auto;
}

.instruction-section {
  margin-bottom: 20px;
}

.instruction-section h4 {
  color: #409EFF;
  margin-bottom: 10px;
  font-size: 16px;
}

.instruction-section ul {
  margin: 0;
  padding-left: 20px;
}

.instruction-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.instruction-section li strong {
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-alert {
  margin-top: 20px;
}
</style>

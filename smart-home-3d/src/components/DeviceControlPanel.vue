<template>
  <el-drawer
    v-model="devicesStore.showControlPanel"
    :title="device ? device.name : '设备控制'"
    direction="rtl"
    size="400px"
    @close="devicesStore.unselectDevice"
  >
    <div v-if="device" class="control-panel">
      <!-- 设备基本信息 -->
      <el-card class="device-info">
        <template #header>
          <div class="card-header">
            <span>{{ device.name }}</span>
            <el-tag :type="device.status === 'on' || device.status === 'open' ? 'success' : 'info'">
              {{ getStatusText(device) }}
            </el-tag>
          </div>
        </template>
        
        <!-- 设备开关 -->
        <div class="control-item">
          <span>电源开关</span>
          <el-switch
            :model-value="device.status === 'on' || device.status === 'open'"
            @change="toggleDevice"
            :active-text="device.type === 'curtain' ? '打开' : '开启'"
            :inactive-text="device.type === 'curtain' ? '关闭' : '关闭'"
          />
        </div>
      </el-card>

      <!-- 空调控制 -->
      <el-card v-if="device.type === 'air-conditioner'" class="device-controls">
        <template #header>
          <span>空调控制</span>
        </template>
        
        <div class="control-item">
          <span>温度设置</span>
          <div class="temperature-control">
            <el-button 
              :icon="Minus" 
              size="small" 
              @click="adjustTemperature(-1)"
              :disabled="device.status === 'off'"
            />
            <span class="temperature-display">{{ device.temperature }}°C</span>
            <el-button 
              :icon="Plus" 
              size="small" 
              @click="adjustTemperature(1)"
              :disabled="device.status === 'off'"
            />
          </div>
        </div>

        <div class="control-item">
          <span>运行模式</span>
          <el-select 
            :model-value="device.mode" 
            @change="setMode"
            :disabled="device.status === 'off'"
            style="width: 120px"
          >
            <el-option label="制冷" value="cool" />
            <el-option label="制热" value="heat" />
            <el-option label="送风" value="fan" />
            <el-option label="除湿" value="dry" />
          </el-select>
        </div>
      </el-card>

      <!-- 风扇控制 -->
      <el-card v-if="device.type === 'fan'" class="device-controls">
        <template #header>
          <span>风扇控制</span>
        </template>
        
        <div class="control-item">
          <span>风速档位</span>
          <el-radio-group 
            :model-value="device.speed" 
            @change="setFanSpeed"
            :disabled="device.status === 'off'"
          >
            <el-radio-button :label="1">低速</el-radio-button>
            <el-radio-button :label="2">中速</el-radio-button>
            <el-radio-button :label="3">高速</el-radio-button>
          </el-radio-group>
        </div>
      </el-card>

      <!-- 灯光控制 -->
      <el-card v-if="device.type === 'light'" class="device-controls">
        <template #header>
          <span>灯光控制</span>
        </template>
        
        <div class="control-item">
          <span>亮度调节</span>
          <el-slider
            :model-value="device.brightness"
            @change="setBrightness"
            :disabled="device.status === 'off'"
            :min="0"
            :max="100"
            show-input
            style="margin-top: 10px"
          />
        </div>
      </el-card>

      <!-- 窗帘控制 -->
      <el-card v-if="device.type === 'curtain'" class="device-controls">
        <template #header>
          <span>窗帘控制</span>
        </template>
        
        <div class="control-item">
          <span>开合程度</span>
          <el-slider
            :model-value="device.openness"
            @change="setCurtainOpenness"
            :min="0"
            :max="100"
            show-input
            style="margin-top: 10px"
          />
        </div>

        <div class="control-item">
          <span>快捷操作</span>
          <div class="curtain-buttons">
            <el-button size="small" @click="setCurtainOpenness(0)">完全关闭</el-button>
            <el-button size="small" @click="setCurtainOpenness(50)">半开</el-button>
            <el-button size="small" @click="setCurtainOpenness(100)">完全打开</el-button>
          </div>
        </div>
      </el-card>

      <!-- 设备状态信息 -->
      <el-card class="device-status">
        <template #header>
          <span>设备状态</span>
        </template>
        
        <el-descriptions :column="1" size="small">
          <el-descriptions-item label="设备ID">{{ device.id }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ getDeviceTypeText(device.type) }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">{{ getStatusText(device) }}</el-descriptions-item>
          <el-descriptions-item label="位置">
            X: {{ device.position.x }}, Y: {{ device.position.y }}, Z: {{ device.position.z }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </el-drawer>
</template>

<script>
import { computed } from 'vue'
import { useDevicesStore } from '../stores/devices.js'
import { Plus, Minus } from '@element-plus/icons-vue'

export default {
  name: 'DeviceControlPanel',
  components: {
    Plus,
    Minus
  },
  setup() {
    const devicesStore = useDevicesStore()
    
    const device = computed(() => devicesStore.selectedDevice)
    
    return {
      devicesStore,
      device,
      Plus,
      Minus
    }
  },
  methods: {
    toggleDevice() {
      if (this.device) {
        this.devicesStore.toggleDevice(this.device.id)
      }
    },

    adjustTemperature(delta) {
      if (this.device && this.device.type === 'air-conditioner') {
        const newTemp = this.device.temperature + delta
        this.devicesStore.setAirConditionerTemperature(this.device.id, newTemp)
      }
    },

    setMode(mode) {
      if (this.device && this.device.type === 'air-conditioner') {
        this.devicesStore.setAirConditionerMode(this.device.id, mode)
      }
    },

    setFanSpeed(speed) {
      if (this.device && this.device.type === 'fan') {
        this.devicesStore.setFanSpeed(this.device.id, speed)
      }
    },

    setBrightness(brightness) {
      if (this.device && this.device.type === 'light') {
        this.devicesStore.setLightBrightness(this.device.id, brightness)
      }
    },

    setCurtainOpenness(openness) {
      if (this.device && this.device.type === 'curtain') {
        this.devicesStore.setCurtainOpenness(this.device.id, openness)
      }
    },

    getStatusText(device) {
      switch (device.type) {
        case 'curtain':
          return device.status === 'open' ? '已打开' : '已关闭'
        default:
          return device.status === 'on' ? '已开启' : '已关闭'
      }
    },

    getDeviceTypeText(type) {
      const typeMap = {
        'air-conditioner': '空调',
        'fan': '电风扇',
        'light': '灯具',
        'curtain': '窗帘'
      }
      return typeMap[type] || type
    }
  }
}
</script>

<style scoped>
.control-panel {
  padding: 0;
}

.device-info,
.device-controls,
.device-status {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.control-item:last-child {
  margin-bottom: 0;
}

.temperature-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.temperature-display {
  min-width: 50px;
  text-align: center;
  font-weight: bold;
}

.curtain-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>

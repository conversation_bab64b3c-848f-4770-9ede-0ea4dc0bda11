<template>
  <div ref="sceneContainer" class="scene-container">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p>正在加载3D场景...</p>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { useDevicesStore } from '../stores/devices.js'

export default {
  name: 'Scene3D',
  setup() {
    const devicesStore = useDevicesStore()
    return { devicesStore }
  },
  data() {
    return {
      // 使用非响应式数据来避免Vue代理问题
      threeData: {
        scene: null,
        camera: null,
        renderer: null,
        controls: null,
        deviceMeshes: new Map(),
        raycaster: null,
        mouse: null,
        animationId: null,
        gltfLoader: null
      },
      loading: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initScene()
    })
  },
  beforeUnmount() {
    this.cleanup()
  },

  methods: {
    async initScene() {
      try {
        // 初始化Three.js对象，避免Vue响应式代理
        const THREE_OBJECTS = {
          scene: new THREE.Scene(),
          camera: null,
          renderer: null,
          controls: null,
          deviceMeshes: new Map(),
          raycaster: new THREE.Raycaster(),
          mouse: new THREE.Vector2(),
          animationId: null
        }

        // 将非响应式对象存储到组件实例
        Object.assign(this.threeData, THREE_OBJECTS)

        // 设置场景背景
        this.threeData.scene.background = new THREE.Color(0x87CEEB)

        // 创建相机
        const container = this.$refs.sceneContainer
        const width = container.clientWidth
        const height = container.clientHeight

        this.threeData.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
        this.threeData.camera.position.set(10, 8, 10)

        // 创建渲染器 - 禁用阴影以避免兼容性问题
        this.threeData.renderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true
        })
        this.threeData.renderer.setSize(width, height)
        // 暂时禁用阴影
        // this.threeData.renderer.shadowMap.enabled = true
        // this.threeData.renderer.shadowMap.type = THREE.PCFSoftShadowMap
        container.appendChild(this.threeData.renderer.domElement)

        // 添加轨道控制器
        this.threeData.controls = new OrbitControls(this.threeData.camera, this.threeData.renderer.domElement)
        this.threeData.controls.enableDamping = true
        this.threeData.controls.dampingFactor = 0.05
        this.threeData.controls.maxPolarAngle = Math.PI / 2.2

        // 添加光源
        this.addLights()

        // 创建房间
        await this.createRoom()

        // 创建设备
        await this.createDevices()

        // 添加事件监听
        this.addEventListeners()

        // 开始动画循环
        this.animate()

        this.loading = false

      } catch (error) {
        console.error('3D场景初始化失败:', error)
        this.loading = false
      }
    },

    addLights() {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.8)
      this.threeData.scene.add(ambientLight)

      // 主光源 - 简化版本，不使用阴影
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6)
      directionalLight.position.set(50, 50, 25)
      // 禁用阴影以避免兼容性问题
      // directionalLight.castShadow = true
      this.threeData.scene.add(directionalLight)
    },

    async createRoom() {
      // 创建地板
      const floorGeometry = new THREE.PlaneGeometry(20, 20)
      const floorMaterial = new THREE.MeshBasicMaterial({
        color: 0xf0f0f0
      })
      const floor = new THREE.Mesh(floorGeometry, floorMaterial)
      floor.rotation.x = -Math.PI / 2
      this.threeData.scene.add(floor)

      // 创建墙壁
      this.createWalls()

      // 添加一些家具
      this.createFurniture()
    },

    createWalls() {
      const wallMaterial = new THREE.MeshBasicMaterial({
        color: 0xe8e8e8,
        transparent: true,
        opacity: 0.7
      })

      // 后墙
      const backWallGeometry = new THREE.PlaneGeometry(20, 8)
      const backWall = new THREE.Mesh(backWallGeometry, wallMaterial)
      backWall.position.set(0, 4, -10)
      this.threeData.scene.add(backWall)

      // 左墙
      const leftWallGeometry = new THREE.PlaneGeometry(20, 8)
      const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      leftWall.position.set(-10, 4, 0)
      leftWall.rotation.y = Math.PI / 2
      this.threeData.scene.add(leftWall)

      // 添加窗户
      this.createWindows()
    },

    createWindows() {
      // 后墙窗户
      const windowGeometry = new THREE.PlaneGeometry(3, 2)
      const windowMaterial = new THREE.MeshBasicMaterial({
        color: 0x87CEEB,
        transparent: true,
        opacity: 0.6
      })
      const window1 = new THREE.Mesh(windowGeometry, windowMaterial)
      window1.position.set(-3, 3, -9.9)
      this.threeData.scene.add(window1)

      const window2 = new THREE.Mesh(windowGeometry, windowMaterial)
      window2.position.set(3, 3, -9.9)
      this.threeData.scene.add(window2)

      // 窗框
      const frameGeometry = new THREE.BoxGeometry(3.2, 2.2, 0.1)
      const frameMaterial = new THREE.MeshBasicMaterial({ color: 0x8B4513 })
      const frame1 = new THREE.Mesh(frameGeometry, frameMaterial)
      frame1.position.set(-3, 3, -9.95)
      this.threeData.scene.add(frame1)

      const frame2 = new THREE.Mesh(frameGeometry, frameMaterial)
      frame2.position.set(3, 3, -9.95)
      this.threeData.scene.add(frame2)

      // 添加外部景观
      this.createOutdoorScene()
    },

    createOutdoorScene() {
      // 创建一些远山
      for (let i = 0; i < 5; i++) {
        const mountainGeometry = new THREE.ConeGeometry(2 + Math.random() * 2, 3 + Math.random() * 2, 6)
        const mountainMaterial = new THREE.MeshBasicMaterial({
          color: 0x228B22,
          transparent: true,
          opacity: 0.3
        })
        const mountain = new THREE.Mesh(mountainGeometry, mountainMaterial)
        mountain.position.set(
          -10 + i * 5,
          1.5,
          -15 - Math.random() * 5
        )
        this.threeData.scene.add(mountain)
      }

      // 添加一些云朵
      for (let i = 0; i < 8; i++) {
        const cloudGroup = new THREE.Group()

        // 云朵由多个球体组成
        for (let j = 0; j < 4; j++) {
          const cloudGeometry = new THREE.SphereGeometry(0.5 + Math.random() * 0.3, 8, 8)
          const cloudMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.8
          })
          const cloudPart = new THREE.Mesh(cloudGeometry, cloudMaterial)
          cloudPart.position.set(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 0.5,
            (Math.random() - 0.5) * 1
          )
          cloudGroup.add(cloudPart)
        }

        cloudGroup.position.set(
          -15 + i * 4,
          5 + Math.random() * 2,
          -20 - Math.random() * 10
        )
        this.threeData.scene.add(cloudGroup)
      }
    },

    async createFurniture() {
      // 创建一个简单的沙发
      const sofaGeometry = new THREE.BoxGeometry(3, 1, 1.5)
      const sofaMaterial = new THREE.MeshBasicMaterial({ color: 0x8B4513 })
      const sofa = new THREE.Mesh(sofaGeometry, sofaMaterial)
      sofa.position.set(0, 0.5, 3)
      this.threeData.scene.add(sofa)

      // 创建茶几
      const tableGeometry = new THREE.BoxGeometry(2, 0.2, 1)
      const tableMaterial = new THREE.MeshBasicMaterial({ color: 0x654321 })
      const table = new THREE.Mesh(tableGeometry, tableMaterial)
      table.position.set(0, 0.4, 0)
      this.threeData.scene.add(table)

      // 尝试加载3D模型椅子
      try {
        const gltfLoader = new GLTFLoader()
        const gltf = await new Promise((resolve, reject) => {
          gltfLoader.load(
            '/models/simple-chair.gltf',
            resolve,
            undefined,
            reject
          )
        })

        const chair = gltf.scene
        chair.position.set(2, 0, 1)
        chair.scale.setScalar(0.5)
        this.threeData.scene.add(chair)
        console.log('椅子模型加载成功')
      } catch (error) {
        console.warn('椅子模型加载失败，使用备用几何体:', error)
        // 备用椅子
        const chairGeometry = new THREE.BoxGeometry(0.5, 1, 0.5)
        const chairMaterial = new THREE.MeshBasicMaterial({ color: 0x654321 })
        const chair = new THREE.Mesh(chairGeometry, chairMaterial)
        chair.position.set(2, 0.5, 1)
        this.threeData.scene.add(chair)
      }

      // 添加一些装饰物品
      this.createDecorations()
    },

    createDecorations() {
      // 创建一些植物
      this.createPlants()

      // 创建一些装饰球体
      for (let i = 0; i < 5; i++) {
        const sphereGeometry = new THREE.SphereGeometry(0.1, 8, 8)
        const sphereMaterial = new THREE.MeshBasicMaterial({
          color: Math.random() * 0xffffff
        })
        const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial)
        sphere.position.set(
          (Math.random() - 0.5) * 10,
          0.1,
          (Math.random() - 0.5) * 10
        )
        this.threeData.scene.add(sphere)
      }

      // 创建一些柱子
      for (let i = 0; i < 3; i++) {
        const pillarGeometry = new THREE.CylinderGeometry(0.1, 0.1, 2, 8)
        const pillarMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 })
        const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial)
        pillar.position.set(
          -8 + i * 8,
          1,
          -8
        )
        this.threeData.scene.add(pillar)
      }
    },

    createPlants() {
      // 创建简单的植物模型
      const plantPositions = [
        { x: -3, z: -3 },
        { x: 3, z: -3 },
        { x: -3, z: 4 },
        { x: 4, z: 2 }
      ]

      plantPositions.forEach(pos => {
        const plantGroup = new THREE.Group()

        // 花盆
        const potGeometry = new THREE.CylinderGeometry(0.3, 0.2, 0.4, 8)
        const potMaterial = new THREE.MeshBasicMaterial({ color: 0x8B4513 })
        const pot = new THREE.Mesh(potGeometry, potMaterial)
        pot.position.y = 0.2
        plantGroup.add(pot)

        // 植物茎
        const stemGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.8, 6)
        const stemMaterial = new THREE.MeshBasicMaterial({ color: 0x228B22 })
        const stem = new THREE.Mesh(stemGeometry, stemMaterial)
        stem.position.y = 0.8
        plantGroup.add(stem)

        // 叶子
        for (let i = 0; i < 4; i++) {
          const leafGeometry = new THREE.SphereGeometry(0.15, 6, 6)
          const leafMaterial = new THREE.MeshBasicMaterial({ color: 0x32CD32 })
          const leaf = new THREE.Mesh(leafGeometry, leafMaterial)
          leaf.position.set(
            Math.cos(i * Math.PI / 2) * 0.2,
            1.0 + Math.random() * 0.3,
            Math.sin(i * Math.PI / 2) * 0.2
          )
          leaf.scale.set(1, 0.5, 1)
          plantGroup.add(leaf)
        }

        plantGroup.position.set(pos.x, 0, pos.z)
        this.threeData.scene.add(plantGroup)
      })

      // 添加书架
      this.createBookshelf()
    },

    createBookshelf() {
      const bookshelfGroup = new THREE.Group()

      // 书架框架
      const shelfGeometry = new THREE.BoxGeometry(2, 3, 0.3)
      const shelfMaterial = new THREE.MeshBasicMaterial({ color: 0x8B4513 })
      const shelf = new THREE.Mesh(shelfGeometry, shelfMaterial)
      shelf.position.y = 1.5
      bookshelfGroup.add(shelf)

      // 书架隔板
      for (let i = 0; i < 4; i++) {
        const boardGeometry = new THREE.BoxGeometry(1.9, 0.05, 0.25)
        const boardMaterial = new THREE.MeshBasicMaterial({ color: 0x654321 })
        const board = new THREE.Mesh(boardGeometry, boardMaterial)
        board.position.y = 0.3 + i * 0.7
        bookshelfGroup.add(board)
      }

      // 添加书籍
      for (let shelf = 0; shelf < 3; shelf++) {
        for (let book = 0; book < 8; book++) {
          const bookGeometry = new THREE.BoxGeometry(0.05, 0.3, 0.2)
          const bookColors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0x54A0FF, 0x5F27CD]
          const bookMaterial = new THREE.MeshBasicMaterial({
            color: bookColors[book % bookColors.length]
          })
          const bookMesh = new THREE.Mesh(bookGeometry, bookMaterial)
          bookMesh.position.set(
            -0.8 + book * 0.2,
            0.5 + shelf * 0.7,
            0.05
          )
          bookshelfGroup.add(bookMesh)
        }
      }

      bookshelfGroup.position.set(-7, 0, -2)
      this.threeData.scene.add(bookshelfGroup)
    },

    async createDevices() {
      // 创建设备3D模型
      for (const device of this.devicesStore.devices) {
        const mesh = this.createDeviceMesh(device)
        if (mesh) {
          mesh.position.set(device.position.x, device.position.y, device.position.z)
          mesh.userData = { deviceId: device.id }
          this.threeData.scene.add(mesh)
          this.threeData.deviceMeshes.set(device.id, mesh)
        }
      }
    },

    createDeviceMesh(device) {
      let mesh

      switch (device.type) {
        case 'air-conditioner':
          mesh = this.createAirConditioner(device)
          break
        case 'fan':
          mesh = this.createFan(device)
          break
        case 'light':
          mesh = this.createLight(device)
          break
        case 'curtain':
          mesh = this.createCurtain(device)
          break
        default:
          mesh = this.createDefaultDevice(device)
      }

      return mesh
    },

    createAirConditioner(device) {
      const group = new THREE.Group()

      // 主体
      const mainGeometry = new THREE.BoxGeometry(1.5, 0.4, 0.8)
      const mainMaterial = new THREE.MeshBasicMaterial({
        color: device.status === 'on' ? 0x4CAF50 : 0xcccccc
      })
      const mainBody = new THREE.Mesh(mainGeometry, mainMaterial)
      group.add(mainBody)

      // 前面板
      const panelGeometry = new THREE.BoxGeometry(1.4, 0.35, 0.05)
      const panelMaterial = new THREE.MeshBasicMaterial({
        color: device.status === 'on' ? 0x2E7D32 : 0x999999
      })
      const panel = new THREE.Mesh(panelGeometry, panelMaterial)
      panel.position.z = 0.425
      group.add(panel)

      group.userData.device = device
      return group
    },

    createFan(device) {
      const group = new THREE.Group()

      // 底座
      const baseGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.1, 16)
      const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x444444 })
      const base = new THREE.Mesh(baseGeometry, baseMaterial)
      group.add(base)

      // 支柱
      const poleGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1.2, 8)
      const poleMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 })
      const pole = new THREE.Mesh(poleGeometry, poleMaterial)
      pole.position.y = 0.6
      group.add(pole)

      // 扇叶组
      const bladeGroup = new THREE.Group()
      for (let i = 0; i < 3; i++) {
        const bladeGeometry = new THREE.BoxGeometry(0.6, 0.02, 0.1)
        const bladeMaterial = new THREE.MeshBasicMaterial({
          color: 0xcccccc,
          transparent: true,
          opacity: 0.8
        })
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial)
        blade.position.x = 0.3
        blade.rotation.y = (i * Math.PI * 2) / 3
        bladeGroup.add(blade)
      }
      bladeGroup.position.y = 1.2
      group.add(bladeGroup)

      group.userData.blades = bladeGroup
      group.userData.device = device
      return group
    },

    createLight(device) {
      const group = new THREE.Group()

      // 灯泡 - 使用基础材质避免光照计算
      const bulbGeometry = new THREE.SphereGeometry(0.15, 16, 16)
      const bulbMaterial = new THREE.MeshBasicMaterial({
        color: device.status === 'on' ? 0xffff88 : 0xcccccc
      })
      const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial)
      group.add(bulb)

      group.userData.device = device
      return group
    },

    createCurtain(device) {
      const group = new THREE.Group()

      // 窗帘轨道
      const railGeometry = new THREE.BoxGeometry(2, 0.05, 0.05)
      const railMaterial = new THREE.MeshBasicMaterial({ color: 0x8B4513 })
      const rail = new THREE.Mesh(railGeometry, railMaterial)
      rail.position.y = 1
      group.add(rail)

      // 窗帘布料
      const curtainGeometry = new THREE.PlaneGeometry(1.8, 1.8)
      const curtainMaterial = new THREE.MeshBasicMaterial({
        color: 0x8B4513,
        side: THREE.DoubleSide
      })
      const curtain = new THREE.Mesh(curtainGeometry, curtainMaterial)
      curtain.position.y = 0.1

      // 根据开合度调整窗帘
      const openness = device.openness / 100
      curtain.scale.x = 1 - openness * 0.8

      group.add(curtain)
      group.userData.curtain = curtain
      group.userData.device = device
      return group
    },

    createDefaultDevice(device) {
      const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5)
      const material = new THREE.MeshBasicMaterial({
        color: parseInt(device.color.replace('#', '0x'))
      })
      return new THREE.Mesh(geometry, material)
    },

    addEventListeners() {
      this.$refs.sceneContainer.addEventListener('click', this.onMouseClick)
      window.addEventListener('resize', this.onWindowResize)
    },

    onMouseClick(event) {
      const rect = this.$refs.sceneContainer.getBoundingClientRect()
      this.threeData.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      this.threeData.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      this.threeData.raycaster.setFromCamera(this.threeData.mouse, this.threeData.camera)
      const intersects = this.threeData.raycaster.intersectObjects(this.threeData.scene.children, true)

      if (intersects.length > 0) {
        let clickedObject = intersects[0].object

        // 向上查找包含deviceId的对象
        while (clickedObject && !clickedObject.userData.deviceId) {
          clickedObject = clickedObject.parent
        }

        if (clickedObject && clickedObject.userData.deviceId) {
          this.devicesStore.selectDevice(clickedObject.userData.deviceId)
        }
      }
    },

    onWindowResize() {
      const container = this.$refs.sceneContainer
      const width = container.clientWidth
      const height = container.clientHeight

      this.threeData.camera.aspect = width / height
      this.threeData.camera.updateProjectionMatrix()
      this.threeData.renderer.setSize(width, height)
    },

    animate() {
      this.threeData.animationId = requestAnimationFrame(this.animate)

      if (this.threeData.controls) {
        this.threeData.controls.update()
      }

      this.updateDeviceVisuals()

      if (this.threeData.renderer && this.threeData.scene && this.threeData.camera) {
        this.threeData.renderer.render(this.threeData.scene, this.threeData.camera)
      }
    },

    updateDeviceVisuals() {
      this.devicesStore.devices.forEach(device => {
        const mesh = this.threeData.deviceMeshes.get(device.id)
        if (!mesh) return

        // 更新设备状态
        switch (device.type) {
          case 'fan':
            if (device.status === 'on' && mesh.userData.blades) {
              mesh.userData.blades.rotation.y += 0.1 * device.speed
            }
            break

          case 'light':
            // 更新灯光状态
            const bulb = mesh.children.find(child => child.geometry && child.geometry.type === 'SphereGeometry')
            if (bulb) {
              bulb.material.color.setHex(device.status === 'on' ? 0xffff88 : 0xcccccc)
            }
            break

          case 'curtain':
            if (mesh.userData.curtain) {
              const openness = device.openness / 100
              mesh.userData.curtain.scale.x = 1 - openness * 0.8
            }
            break
        }
      })
    },

    cleanup() {
      if (this.threeData.animationId) {
        cancelAnimationFrame(this.threeData.animationId)
      }

      if (this.threeData.controls) {
        this.threeData.controls.dispose()
      }

      if (this.threeData.renderer) {
        this.threeData.renderer.dispose()
      }

      // 移除事件监听器
      if (this.$refs.sceneContainer) {
        this.$refs.sceneContainer.removeEventListener('click', this.onMouseClick)
      }
      window.removeEventListener('resize', this.onWindowResize)
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #f0f0f0;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content p {
  margin: 0;
  font-size: 16px;
}
</style>
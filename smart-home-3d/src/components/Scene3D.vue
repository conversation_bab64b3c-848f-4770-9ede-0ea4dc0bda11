<template>
  <div ref="sceneContainer" class="scene-container"></div>
</template>

<script>
import * as THREE from 'three'
import { useDevicesStore } from '../stores/devices.js'

export default {
  name: 'Scene3D',
  setup() {
    const devicesStore = useDevicesStore()
    return { devicesStore }
  },
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      deviceMeshes: new Map(),
      raycaster: new THREE.Raycaster(),
      mouse: new THREE.Vector2(),
      animationId: null
    }
  },
  mounted() {
    this.initScene()
    this.createRoom()
    this.createDevices()
    this.addEventListeners()
    this.animate()
  },
  beforeUnmount() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    this.removeEventListeners()
    if (this.renderer) {
      this.renderer.dispose()
    }
  },
  methods: {
    initScene() {
      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      this.camera = new THREE.PerspectiveCamera(
        75,
        this.$refs.sceneContainer.clientWidth / this.$refs.sceneContainer.clientHeight,
        0.1,
        1000
      )
      this.camera.position.set(5, 5, 5)
      this.camera.lookAt(0, 0, 0)

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true })
      this.renderer.setSize(
        this.$refs.sceneContainer.clientWidth,
        this.$refs.sceneContainer.clientHeight
      )
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      this.$refs.sceneContainer.appendChild(this.renderer.domElement)

      // 添加光源
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(10, 10, 5)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      this.scene.add(directionalLight)
    },

    createRoom() {
      // 创建地板
      const floorGeometry = new THREE.PlaneGeometry(10, 8)
      const floorMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff })
      const floor = new THREE.Mesh(floorGeometry, floorMaterial)
      floor.rotation.x = -Math.PI / 2
      floor.receiveShadow = true
      this.scene.add(floor)

      // 创建墙壁
      const wallMaterial = new THREE.MeshLambertMaterial({ color: 0xe0e0e0 })
      
      // 后墙
      const backWallGeometry = new THREE.PlaneGeometry(10, 4)
      const backWall = new THREE.Mesh(backWallGeometry, wallMaterial)
      backWall.position.set(0, 2, -4)
      this.scene.add(backWall)

      // 左墙
      const leftWallGeometry = new THREE.PlaneGeometry(8, 4)
      const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      leftWall.position.set(-5, 2, 0)
      leftWall.rotation.y = Math.PI / 2
      this.scene.add(leftWall)

      // 右墙
      const rightWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      rightWall.position.set(5, 2, 0)
      rightWall.rotation.y = -Math.PI / 2
      this.scene.add(rightWall)
    },

    createDevices() {
      this.devicesStore.devices.forEach(device => {
        this.createDeviceMesh(device)
      })
    },

    createDeviceMesh(device) {
      let geometry, material, mesh

      switch (device.type) {
        case 'air-conditioner':
          geometry = new THREE.BoxGeometry(1, 0.3, 0.8)
          material = new THREE.MeshLambertMaterial({ color: device.color })
          mesh = new THREE.Mesh(geometry, material)
          break

        case 'fan':
          geometry = new THREE.CylinderGeometry(0.3, 0.3, 0.1, 8)
          material = new THREE.MeshLambertMaterial({ color: device.color })
          mesh = new THREE.Mesh(geometry, material)
          break

        case 'light':
          geometry = new THREE.SphereGeometry(0.2, 16, 16)
          material = new THREE.MeshLambertMaterial({ 
            color: device.status === 'on' ? 0xffff00 : device.color,
            emissive: device.status === 'on' ? 0x444400 : 0x000000
          })
          mesh = new THREE.Mesh(geometry, material)
          break

        case 'curtain':
          geometry = new THREE.PlaneGeometry(0.1, 2)
          material = new THREE.MeshLambertMaterial({ color: device.color })
          mesh = new THREE.Mesh(geometry, material)
          break

        default:
          geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5)
          material = new THREE.MeshLambertMaterial({ color: device.color })
          mesh = new THREE.Mesh(geometry, material)
      }

      mesh.position.set(device.position.x, device.position.y, device.position.z)
      mesh.castShadow = true
      mesh.receiveShadow = true
      mesh.userData = { deviceId: device.id }

      this.scene.add(mesh)
      this.deviceMeshes.set(device.id, mesh)
    },

    addEventListeners() {
      this.$refs.sceneContainer.addEventListener('click', this.onMouseClick)
      window.addEventListener('resize', this.onWindowResize)
    },

    removeEventListeners() {
      if (this.$refs.sceneContainer) {
        this.$refs.sceneContainer.removeEventListener('click', this.onMouseClick)
      }
      window.removeEventListener('resize', this.onWindowResize)
    },

    onMouseClick(event) {
      const rect = this.$refs.sceneContainer.getBoundingClientRect()
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      this.raycaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.raycaster.intersectObjects(this.scene.children)

      if (intersects.length > 0) {
        const clickedObject = intersects[0].object
        if (clickedObject.userData.deviceId) {
          this.devicesStore.selectDevice(clickedObject.userData.deviceId)
        }
      }
    },

    onWindowResize() {
      if (!this.$refs.sceneContainer) return
      
      const width = this.$refs.sceneContainer.clientWidth
      const height = this.$refs.sceneContainer.clientHeight

      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },

    animate() {
      this.animationId = requestAnimationFrame(this.animate)
      this.updateDeviceVisuals()
      this.renderer.render(this.scene, this.camera)
    },

    updateDeviceVisuals() {
      this.devicesStore.devices.forEach(device => {
        const mesh = this.deviceMeshes.get(device.id)
        if (!mesh) return

        // 更新设备视觉状态
        switch (device.type) {
          case 'light':
            mesh.material.color.setHex(device.status === 'on' ? 0xffff00 : device.color.replace('#', '0x'))
            mesh.material.emissive.setHex(device.status === 'on' ? 0x444400 : 0x000000)
            break

          case 'fan':
            if (device.status === 'on') {
              mesh.rotation.y += 0.1 * device.speed
            }
            break

          case 'curtain':
            // 根据开合度调整窗帘的缩放
            mesh.scale.x = 1 - (device.openness / 100) * 0.8
            break
        }

        // 选中状态的高亮效果
        if (this.devicesStore.selectedDevice && this.devicesStore.selectedDevice.id === device.id) {
          mesh.material.emissive.setHex(0x333333)
        } else if (device.type !== 'light' || device.status !== 'on') {
          mesh.material.emissive.setHex(0x000000)
        }
      })
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
}
</style>

<template>
  <div ref="sceneContainer" class="scene-container"></div>
</template>

<script>
import * as THREE from 'three'
import { useDevicesStore } from '../stores/devices.js'

export default {
  name: 'Scene3D',
  setup() {
    const devicesStore = useDevicesStore()
    return { devicesStore }
  },
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      deviceMeshes: new Map(),
      raycaster: new THREE.Raycaster(),
      mouse: new THREE.Vector2(),
      animationId: null
    }
  },
  mounted() {
    try {
      this.initScene()
      this.createDevices()
      this.addEventListeners()
      this.animate()
    } catch (error) {
      console.error('Error initializing 3D scene:', error)
    }
  },
  beforeUnmount() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    this.removeEventListeners()
    if (this.renderer) {
      this.renderer.dispose()
    }
  },
  methods: {
    initScene() {
      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      this.camera = new THREE.PerspectiveCamera(
        75,
        this.$refs.sceneContainer.clientWidth / this.$refs.sceneContainer.clientHeight,
        0.1,
        1000
      )
      this.camera.position.set(5, 5, 5)
      this.camera.lookAt(0, 0, 0)

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true })
      this.renderer.setSize(
        this.$refs.sceneContainer.clientWidth,
        this.$refs.sceneContainer.clientHeight
      )
      this.$refs.sceneContainer.appendChild(this.renderer.domElement)

      // 添加简单的光源
      const ambientLight = new THREE.AmbientLight(0x404040, 1.0)
      this.scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5)
      directionalLight.position.set(5, 5, 5)
      this.scene.add(directionalLight)

      // 创建简单的地板
      const floorGeometry = new THREE.PlaneGeometry(10, 10)
      const floorMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc })
      const floor = new THREE.Mesh(floorGeometry, floorMaterial)
      floor.rotation.x = -Math.PI / 2
      this.scene.add(floor)
    },



    createDevices() {
      // 创建所有设备模型
      this.devicesStore.devices.forEach(device => {
        this.createDeviceMesh(device)
      })
    },

    createDeviceMesh(device) {
      let mesh

      // 创建简单的几何体
      switch (device.type) {
        case 'air-conditioner':
          mesh = this.createAirConditioner(device)
          break
        case 'fan':
          mesh = this.createFan(device)
          break
        case 'light':
          mesh = this.createLight(device)
          break
        case 'curtain':
          mesh = this.createCurtain(device)
          break
        default:
          mesh = this.createDefaultDevice(device)
      }

      mesh.position.set(device.position.x, device.position.y, device.position.z)
      mesh.userData = { deviceId: device.id }

      this.scene.add(mesh)
      this.deviceMeshes.set(device.id, mesh)
    },

    createAirConditioner(device) {
      const geometry = new THREE.BoxGeometry(1, 0.3, 0.6)
      const material = new THREE.MeshLambertMaterial({
        color: device.status === 'on' ? 0x4CAF50 : 0xcccccc
      })
      const mesh = new THREE.Mesh(geometry, material)
      mesh.userData.type = 'air-conditioner'
      return mesh
    },

    createFan() {
      const group = new THREE.Group()

      // 底座
      const baseGeometry = new THREE.CylinderGeometry(0.2, 0.3, 0.1)
      const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 })
      const base = new THREE.Mesh(baseGeometry, baseMaterial)
      group.add(base)

      // 扇叶
      const bladeGroup = new THREE.Group()
      for (let i = 0; i < 3; i++) {
        const bladeGeometry = new THREE.BoxGeometry(0.4, 0.02, 0.08)
        const bladeMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc })
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial)
        blade.position.x = 0.2
        blade.rotation.y = (i * Math.PI * 2) / 3
        bladeGroup.add(blade)
      }
      bladeGroup.position.y = 0.1
      group.add(bladeGroup)

      group.userData.blades = bladeGroup
      group.userData.type = 'fan'
      return group
    },

    createLight(device) {
      const geometry = new THREE.SphereGeometry(0.1, 16, 16)
      const material = new THREE.MeshLambertMaterial({
        color: device.status === 'on' ? 0xffff00 : 0xcccccc,
        emissive: device.status === 'on' ? 0x222200 : 0x000000
      })
      const mesh = new THREE.Mesh(geometry, material)
      mesh.userData.type = 'light'
      return mesh
    },

    createCurtain(device) {
      const geometry = new THREE.PlaneGeometry(1.5, 1.5)
      const material = new THREE.MeshLambertMaterial({
        color: device.color.replace('#', '0x'),
        side: THREE.DoubleSide
      })
      const mesh = new THREE.Mesh(geometry, material)

      // 根据开合度调整窗帘
      const openness = device.openness / 100
      mesh.scale.x = 1 - openness * 0.8

      mesh.userData.type = 'curtain'
      return mesh
    },







    createDefaultDevice(device) {
      const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5)
      const material = new THREE.MeshLambertMaterial({
        color: device.color.replace('#', '0x')
      })
      return new THREE.Mesh(geometry, material)
    },

    addEventListeners() {
      this.$refs.sceneContainer.addEventListener('click', this.onMouseClick)
      window.addEventListener('resize', this.onWindowResize)
    },

    removeEventListeners() {
      if (this.$refs.sceneContainer) {
        this.$refs.sceneContainer.removeEventListener('click', this.onMouseClick)
      }
      window.removeEventListener('resize', this.onWindowResize)
    },

    onMouseClick(event) {
      const rect = this.$refs.sceneContainer.getBoundingClientRect()
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      this.raycaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.raycaster.intersectObjects(this.scene.children, true)

      if (intersects.length > 0) {
        const clickedObject = intersects[0].object
        // 查找包含deviceId的父对象
        let deviceObject = clickedObject
        while (deviceObject && !deviceObject.userData.deviceId) {
          deviceObject = deviceObject.parent
        }

        if (deviceObject && deviceObject.userData.deviceId) {
          this.devicesStore.selectDevice(deviceObject.userData.deviceId)
        }
      }
    },

    onWindowResize() {
      if (!this.$refs.sceneContainer) return

      const width = this.$refs.sceneContainer.clientWidth
      const height = this.$refs.sceneContainer.clientHeight

      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },

    animate() {
      this.animationId = requestAnimationFrame(this.animate)
      this.updateDeviceVisuals()
      this.renderer.render(this.scene, this.camera)
    },

    updateDeviceVisuals() {
      this.devicesStore.devices.forEach(device => {
        const mesh = this.deviceMeshes.get(device.id)
        if (!mesh) return

        // 更新设备视觉状态
        switch (device.type) {
          case 'air-conditioner':
            // 更新空调颜色
            if (mesh.material) {
              mesh.material.color.setHex(device.status === 'on' ? 0x4CAF50 : 0xcccccc)
            }
            break

          case 'fan':
            // 风扇扇叶旋转
            if (device.status === 'on' && mesh.userData.blades) {
              mesh.userData.blades.rotation.y += 0.1 * device.speed
            }
            break

          case 'light':
            // 更新灯光
            if (mesh.material) {
              mesh.material.color.setHex(device.status === 'on' ? 0xffff00 : 0xcccccc)
              mesh.material.emissive.setHex(device.status === 'on' ? 0x222200 : 0x000000)
            }
            break

          case 'curtain':
            // 更新窗帘开合度
            const openness = device.openness / 100
            mesh.scale.x = 1 - openness * 0.8
            break
        }

        // 选中状态的高亮效果
        if (this.devicesStore.selectedDevice && this.devicesStore.selectedDevice.id === device.id) {
          if (mesh.material && !mesh.userData.originalColor) {
            mesh.userData.originalColor = mesh.material.color.getHex()
            mesh.material.color.setHex(0x00ff00)
          }
        } else {
          if (mesh.material && mesh.userData.originalColor !== undefined) {
            mesh.material.color.setHex(mesh.userData.originalColor)
            mesh.userData.originalColor = undefined
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
}
</style>

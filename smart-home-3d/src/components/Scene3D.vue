<template>
  <div ref="sceneContainer" class="scene-container">
    <div class="device-grid">
      <div
        v-for="device in devicesStore.devices"
        :key="device.id"
        class="device-card"
        :class="{ active: device.status === 'on' || device.status === 'open' }"
        @click="devicesStore.selectDevice(device.id)"
      >
        <div class="device-icon">{{ getDeviceIcon(device.type) }}</div>
        <div class="device-name">{{ device.name }}</div>
        <div class="device-status">
          {{ device.status === 'on' || device.status === 'open' ? '开启' : '关闭' }}
        </div>
        <div class="device-details">
          <span v-if="device.type === 'air-conditioner'">{{ device.temperature }}°C</span>
          <span v-if="device.type === 'fan'">档位 {{ device.speed }}</span>
          <span v-if="device.type === 'light'">{{ device.brightness }}%</span>
          <span v-if="device.type === 'curtain'">{{ device.openness }}%</span>
        </div>
      </div>
    </div>

    <div class="scene-info">
      <h3>🏠 智能家居控制中心</h3>
      <p>点击设备卡片进行控制</p>
      <div class="stats">
        <span>总设备: {{ devicesStore.devices.length }}</span>
        <span>活跃设备: {{ activeDevicesCount }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useDevicesStore } from '../stores/devices.js'

export default {
  name: 'Scene3D',
  setup() {
    const devicesStore = useDevicesStore()

    const activeDevicesCount = computed(() =>
      devicesStore.devices.filter(device =>
        device.status === 'on' || device.status === 'open'
      ).length
    )

    return {
      devicesStore,
      activeDevicesCount
    }
  },
  methods: {
    getDeviceIcon(type) {
      const icons = {
        'air-conditioner': '❄️',
        'fan': '🌀',
        'light': '💡',
        'curtain': '🪟'
      }
      return icons[type] || '📱'
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
}

.scene-info {
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.scene-info h3 {
  font-size: 24px;
  margin-bottom: 10px;
}

.scene-info p {
  opacity: 0.9;
  margin-bottom: 15px;
}

.stats {
  display: flex;
  gap: 20px;
  justify-content: center;
  font-size: 14px;
  opacity: 0.8;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  max-width: 800px;
  width: 100%;
}

.device-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.device-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.device-card.active {
  border-color: #67C23A;
  background: linear-gradient(135deg, #f0fff4 0%, #e8f5e8 100%);
}

.device-icon {
  font-size: 48px;
  margin-bottom: 15px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.device-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.device-status {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.device-card.active .device-status {
  color: #67C23A;
  font-weight: 500;
}

.device-details {
  font-size: 12px;
  color: #C0C4CC;
  padding: 8px;
  background: #F5F7FA;
  border-radius: 6px;
}

.device-card.active .device-details {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

@media (max-width: 768px) {
  .device-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }

  .device-card {
    padding: 15px;
  }

  .device-icon {
    font-size: 36px;
    margin-bottom: 10px;
  }

  .stats {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
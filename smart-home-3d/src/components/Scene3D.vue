<template>
  <div ref="sceneContainer" class="scene-container"></div>
</template>

<script>
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { useDevicesStore } from '../stores/devices.js'
import modelLoader from '../utils/modelLoader.js'

export default {
  name: 'Scene3D',
  setup() {
    const devicesStore = useDevicesStore()
    return { devicesStore }
  },
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      deviceMeshes: new Map(),
      raycaster: new THREE.Raycaster(),
      mouse: new THREE.Vector2(),
      animationId: null,
      gltfLoader: new GLTFLoader(),
      loadedModels: new Map()
    }
  },
  mounted() {
    try {
      this.initScene()
      this.createRoom()
      this.createDevices()
      this.addEventListeners()
      this.animate()
    } catch (error) {
      console.error('Error initializing 3D scene:', error)
    }
  },
  beforeUnmount() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    this.removeEventListeners()
    if (this.controls) {
      this.controls.dispose()
    }
    if (this.renderer) {
      this.renderer.dispose()
    }
  },
  methods: {
    initScene() {
      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      this.camera = new THREE.PerspectiveCamera(
        75,
        this.$refs.sceneContainer.clientWidth / this.$refs.sceneContainer.clientHeight,
        0.1,
        1000
      )
      this.camera.position.set(8, 6, 8)
      this.camera.lookAt(0, 0, 0)

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true })
      this.renderer.setSize(
        this.$refs.sceneContainer.clientWidth,
        this.$refs.sceneContainer.clientHeight
      )
      // 暂时禁用阴影以避免兼容性问题
      // this.renderer.shadowMap.enabled = true
      // this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      this.$refs.sceneContainer.appendChild(this.renderer.domElement)

      // 添加轨道控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      this.controls.maxPolarAngle = Math.PI / 2.2 // 限制垂直旋转角度
      this.controls.minDistance = 3
      this.controls.maxDistance = 20

      // 添加简化的光源
      const ambientLight = new THREE.AmbientLight(0x404040, 0.8)
      this.scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6)
      directionalLight.position.set(10, 10, 5)
      // 暂时禁用阴影
      // directionalLight.castShadow = true
      this.scene.add(directionalLight)
    },

    createRoom() {
      // 创建地板
      const floorGeometry = new THREE.PlaneGeometry(12, 10)
      const floorTexture = this.createFloorTexture()
      const floorMaterial = new THREE.MeshLambertMaterial({
        map: floorTexture,
        color: 0xf5f5dc
      })
      const floor = new THREE.Mesh(floorGeometry, floorMaterial)
      floor.rotation.x = -Math.PI / 2
      this.scene.add(floor)

      // 创建墙壁
      const wallMaterial = new THREE.MeshLambertMaterial({ color: 0xf0f0f0 })

      // 后墙
      const backWallGeometry = new THREE.PlaneGeometry(12, 4)
      const backWall = new THREE.Mesh(backWallGeometry, wallMaterial)
      backWall.position.set(0, 2, -5)
      this.scene.add(backWall)

      // 左墙
      const leftWallGeometry = new THREE.PlaneGeometry(10, 4)
      const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      leftWall.position.set(-6, 2, 0)
      leftWall.rotation.y = Math.PI / 2
      this.scene.add(leftWall)

      // 右墙
      const rightWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      rightWall.position.set(6, 2, 0)
      rightWall.rotation.y = -Math.PI / 2
      this.scene.add(rightWall)

      // 添加一些家具
      this.createFurniture()
    },

    createFloorTexture() {
      const canvas = document.createElement('canvas')
      canvas.width = 512
      canvas.height = 512
      const context = canvas.getContext('2d')

      // 创建木地板纹理
      context.fillStyle = '#deb887'
      context.fillRect(0, 0, 512, 512)

      // 添加木纹
      for (let i = 0; i < 512; i += 64) {
        context.fillStyle = '#cd853f'
        context.fillRect(i, 0, 2, 512)
      }

      const texture = new THREE.CanvasTexture(canvas)
      texture.wrapS = THREE.RepeatWrapping
      texture.wrapT = THREE.RepeatWrapping
      texture.repeat.set(4, 4)

      return texture
    },

    createFurniture() {
      // 沙发
      const sofaGroup = new THREE.Group()

      // 沙发底座
      const sofaBaseGeometry = new THREE.BoxGeometry(2.5, 0.4, 1)
      const sofaBaseMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 })
      const sofaBase = new THREE.Mesh(sofaBaseGeometry, sofaBaseMaterial)
      sofaBase.position.y = 0.2
      sofaGroup.add(sofaBase)

      // 沙发靠背
      const sofaBackGeometry = new THREE.BoxGeometry(2.5, 0.8, 0.2)
      const sofaBack = new THREE.Mesh(sofaBackGeometry, sofaBaseMaterial)
      sofaBack.position.set(0, 0.6, -0.4)
      sofaGroup.add(sofaBack)

      // 沙发扶手
      const armGeometry = new THREE.BoxGeometry(0.2, 0.6, 1)
      const leftArm = new THREE.Mesh(armGeometry, sofaBaseMaterial)
      leftArm.position.set(-1.15, 0.5, 0)
      sofaGroup.add(leftArm)

      const rightArm = new THREE.Mesh(armGeometry, sofaBaseMaterial)
      rightArm.position.set(1.15, 0.5, 0)
      sofaGroup.add(rightArm)

      sofaGroup.position.set(0, 0, 2)
      this.scene.add(sofaGroup)

      // 茶几
      const tableGroup = new THREE.Group()

      // 桌面
      const tableTopGeometry = new THREE.BoxGeometry(1.5, 0.1, 0.8)
      const tableTopMaterial = new THREE.MeshPhongMaterial({ color: 0x654321 })
      const tableTop = new THREE.Mesh(tableTopGeometry, tableTopMaterial)
      tableTop.position.y = 0.4
      tableGroup.add(tableTop)

      // 桌腿
      const legGeometry = new THREE.BoxGeometry(0.05, 0.4, 0.05)
      const legMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 })

      const positions = [
        [-0.7, 0.2, -0.35],
        [0.7, 0.2, -0.35],
        [-0.7, 0.2, 0.35],
        [0.7, 0.2, 0.35]
      ]

      positions.forEach(pos => {
        const leg = new THREE.Mesh(legGeometry, legMaterial)
        leg.position.set(...pos)
        tableGroup.add(leg)
      })

      tableGroup.position.set(0, 0, 0.5)
      this.scene.add(tableGroup)

      // 电视
      const tvGroup = new THREE.Group()

      // 电视屏幕
      const screenGeometry = new THREE.BoxGeometry(2, 1.2, 0.1)
      const screenMaterial = new THREE.MeshPhongMaterial({ color: 0x000000 })
      const screen = new THREE.Mesh(screenGeometry, screenMaterial)
      screen.position.y = 1.5
      tvGroup.add(screen)

      // 电视边框
      const frameGeometry = new THREE.BoxGeometry(2.2, 1.4, 0.05)
      const frameMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 })
      const frame = new THREE.Mesh(frameGeometry, frameMaterial)
      frame.position.set(0, 1.5, -0.025)
      tvGroup.add(frame)

      tvGroup.position.set(0, 0, -4.9)
      this.scene.add(tvGroup)
    },

    createDevices() {
      // 创建所有设备模型
      this.devicesStore.devices.forEach(device => {
        this.createDeviceMesh(device)
      })
    },

    createDeviceMesh(device) {
      let mesh

      // 直接使用简化的几何体模型
      mesh = this.createSimpleMesh(device)

      mesh.position.set(device.position.x, device.position.y, device.position.z)
      mesh.userData = { deviceId: device.id }

      this.scene.add(mesh)
      this.deviceMeshes.set(device.id, mesh)
    },

    createSimpleMesh(device) {
      // 使用简化的几何体模型
      const group = new THREE.Group()

      switch (device.type) {
        case 'air-conditioner':
          return this.createSimpleAirConditioner(device)
        case 'fan':
          return this.createSimpleFan(device)
        case 'light':
          return this.createSimpleLight(device)
        case 'curtain':
          return this.createSimpleCurtain(device)
        default:
          return this.createDefaultDevice(device)
      }
    },

    createSimpleAirConditioner(device) {
      const group = new THREE.Group()

      // 主体
      const mainGeometry = new THREE.BoxGeometry(1.2, 0.4, 0.8)
      const mainMaterial = new THREE.MeshPhongMaterial({
        color: 0xf0f0f0,
        shininess: 30
      })
      const mainBody = new THREE.Mesh(mainGeometry, mainMaterial)
      mainBody.position.y = 0.2
      group.add(mainBody)

      // 前面板
      const panelGeometry = new THREE.BoxGeometry(1.15, 0.35, 0.05)
      const panelMaterial = new THREE.MeshPhongMaterial({
        color: device.status === 'on' ? 0x4CAF50 : 0x666666
      })
      const panel = new THREE.Mesh(panelGeometry, panelMaterial)
      panel.position.set(0, 0.2, 0.425)
      group.add(panel)

      // 存储面板用于状态更新
      group.userData.panel = panel

      return group
    },

    createSimpleFan(device) {
      const group = new THREE.Group()

      // 底座
      const baseGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.1, 16)
      const baseMaterial = new THREE.MeshPhongMaterial({ color: 0x444444 })
      const base = new THREE.Mesh(baseGeometry, baseMaterial)
      base.position.y = 0.05
      group.add(base)

      // 支柱
      const poleGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1, 8)
      const poleMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 })
      const pole = new THREE.Mesh(poleGeometry, poleMaterial)
      pole.position.y = 0.6
      group.add(pole)

      // 扇叶
      const bladeGroup = new THREE.Group()
      for (let i = 0; i < 3; i++) {
        const bladeGeometry = new THREE.BoxGeometry(0.6, 0.02, 0.1)
        const bladeMaterial = new THREE.MeshPhongMaterial({
          color: 0xcccccc,
          transparent: true,
          opacity: 0.8
        })
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial)
        blade.position.x = 0.3
        blade.rotation.y = (i * Math.PI * 2) / 3
        bladeGroup.add(blade)
      }
      bladeGroup.position.y = 1.1
      group.add(bladeGroup)

      // 存储扇叶组用于动画
      group.userData.blades = bladeGroup

      return group
    },

    createSimpleLight(device) {
      const group = new THREE.Group()

      // 灯泡
      const bulbGeometry = new THREE.SphereGeometry(0.15, 16, 16)
      const bulbMaterial = new THREE.MeshPhongMaterial({
        color: device.status === 'on' ? 0xffff88 : 0xcccccc,
        emissive: device.status === 'on' ? 0x444400 : 0x000000,
        transparent: true,
        opacity: 0.9
      })
      const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial)
      group.add(bulb)

      // 存储灯泡用于状态更新
      group.userData.bulb = bulb

      return group
    },

    createSimpleCurtain(device) {
      const group = new THREE.Group()

      // 窗帘轨道
      const railGeometry = new THREE.BoxGeometry(2, 0.05, 0.05)
      const railMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 })
      const rail = new THREE.Mesh(railGeometry, railMaterial)
      rail.position.y = 1
      group.add(rail)

      // 窗帘布料
      const curtainGeometry = new THREE.PlaneGeometry(1.8, 1.8)
      const curtainMaterial = new THREE.MeshLambertMaterial({
        color: device.color.replace('#', '0x'),
        side: THREE.DoubleSide
      })
      const curtain = new THREE.Mesh(curtainGeometry, curtainMaterial)
      curtain.position.y = 0.1
      curtain.position.z = -0.1

      // 根据开合度调整窗帘
      const openness = device.openness / 100
      curtain.scale.x = 1 - openness * 0.8

      group.add(curtain)
      group.userData.curtain = curtain

      return group
    },







    createDefaultDevice(device) {
      const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5)
      const material = new THREE.MeshPhongMaterial({
        color: device.color.replace('#', '0x')
      })
      return new THREE.Mesh(geometry, material)
    },

    addEventListeners() {
      this.$refs.sceneContainer.addEventListener('click', this.onMouseClick)
      window.addEventListener('resize', this.onWindowResize)
    },

    removeEventListeners() {
      if (this.$refs.sceneContainer) {
        this.$refs.sceneContainer.removeEventListener('click', this.onMouseClick)
      }
      window.removeEventListener('resize', this.onWindowResize)
    },

    onMouseClick(event) {
      const rect = this.$refs.sceneContainer.getBoundingClientRect()
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      this.raycaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.raycaster.intersectObjects(this.scene.children)

      if (intersects.length > 0) {
        const clickedObject = intersects[0].object
        if (clickedObject.userData.deviceId) {
          this.devicesStore.selectDevice(clickedObject.userData.deviceId)
        }
      }
    },

    onWindowResize() {
      if (!this.$refs.sceneContainer) return
      
      const width = this.$refs.sceneContainer.clientWidth
      const height = this.$refs.sceneContainer.clientHeight

      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },

    animate() {
      this.animationId = requestAnimationFrame(this.animate)
      this.controls.update() // 更新轨道控制器
      this.updateDeviceVisuals()
      this.renderer.render(this.scene, this.camera)
    },

    updateDeviceVisuals() {
      this.devicesStore.devices.forEach(device => {
        const mesh = this.deviceMeshes.get(device.id)
        if (!mesh) return

        // 更新设备视觉状态
        switch (device.type) {
          case 'air-conditioner':
            // 更新空调面板
            if (mesh.userData.panel) {
              mesh.userData.panel.material.color.setHex(device.status === 'on' ? 0x4CAF50 : 0x666666)
            }
            break

          case 'fan':
            // 风扇扇叶旋转
            if (device.status === 'on' && mesh.userData.blades) {
              mesh.userData.blades.rotation.y += 0.1 * device.speed
            }
            break

          case 'light':
            // 更新灯泡
            if (mesh.userData.bulb) {
              mesh.userData.bulb.material.color.setHex(device.status === 'on' ? 0xffff88 : 0xcccccc)
              mesh.userData.bulb.material.emissive.setHex(device.status === 'on' ? 0x444400 : 0x000000)
            }

            // 处理点光源
            if (device.status === 'on' && !mesh.userData.light) {
              const pointLight = new THREE.PointLight(0xffff88, 1, 5)
              pointLight.position.set(0, 0, 0)
              mesh.add(pointLight)
              mesh.userData.light = pointLight
            } else if (device.status === 'off' && mesh.userData.light) {
              mesh.remove(mesh.userData.light)
              mesh.userData.light = null
            }
            break

          case 'curtain':
            // 更新窗帘开合度
            if (mesh.userData.curtain) {
              const openness = device.openness / 100
              mesh.userData.curtain.scale.x = 1 - openness * 0.8
            }
            break
        }

        // 选中状态的高亮效果
        if (this.devicesStore.selectedDevice && this.devicesStore.selectedDevice.id === device.id) {
          this.addSelectionHighlight(mesh)
        } else {
          this.removeSelectionHighlight(mesh)
        }
      })
    },

    addSelectionHighlight(mesh) {
      // 添加选中高亮效果
      if (!mesh.userData.outline) {
        const outlineGeometry = new THREE.BoxGeometry(1.5, 1.5, 1.5)
        const outlineMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ff00,
          transparent: true,
          opacity: 0.3,
          wireframe: true
        })
        const outline = new THREE.Mesh(outlineGeometry, outlineMaterial)
        mesh.add(outline)
        mesh.userData.outline = outline
      }
    },

    removeSelectionHighlight(mesh) {
      // 移除选中高亮效果
      if (mesh.userData.outline) {
        mesh.remove(mesh.userData.outline)
        mesh.userData.outline = null
      }
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
}
</style>

<template>
  <div ref="sceneContainer" class="scene-container"></div>
</template>

<script>
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { useDevicesStore } from '../stores/devices.js'
import modelLoader from '../utils/modelLoader.js'

export default {
  name: 'Scene3D',
  setup() {
    const devicesStore = useDevicesStore()
    return { devicesStore }
  },
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      deviceMeshes: new Map(),
      raycaster: new THREE.Raycaster(),
      mouse: new THREE.Vector2(),
      animationId: null,
      gltfLoader: new GLTFLoader(),
      loadedModels: new Map()
    }
  },
  mounted() {
    this.initScene()
    this.createRoom()
    this.createDevices()
    this.addEventListeners()
    this.animate()
  },
  beforeUnmount() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    this.removeEventListeners()
    if (this.controls) {
      this.controls.dispose()
    }
    if (this.renderer) {
      this.renderer.dispose()
    }
  },
  methods: {
    initScene() {
      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      this.camera = new THREE.PerspectiveCamera(
        75,
        this.$refs.sceneContainer.clientWidth / this.$refs.sceneContainer.clientHeight,
        0.1,
        1000
      )
      this.camera.position.set(8, 6, 8)
      this.camera.lookAt(0, 0, 0)

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true })
      this.renderer.setSize(
        this.$refs.sceneContainer.clientWidth,
        this.$refs.sceneContainer.clientHeight
      )
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      this.$refs.sceneContainer.appendChild(this.renderer.domElement)

      // 添加轨道控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      this.controls.maxPolarAngle = Math.PI / 2.2 // 限制垂直旋转角度
      this.controls.minDistance = 3
      this.controls.maxDistance = 20

      // 添加光源
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(10, 10, 5)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      this.scene.add(directionalLight)
    },

    createRoom() {
      // 创建地板
      const floorGeometry = new THREE.PlaneGeometry(12, 10)
      const floorTexture = this.createFloorTexture()
      const floorMaterial = new THREE.MeshLambertMaterial({
        map: floorTexture,
        color: 0xf5f5dc
      })
      const floor = new THREE.Mesh(floorGeometry, floorMaterial)
      floor.rotation.x = -Math.PI / 2
      floor.receiveShadow = true
      this.scene.add(floor)

      // 创建墙壁
      const wallMaterial = new THREE.MeshLambertMaterial({ color: 0xf0f0f0 })

      // 后墙
      const backWallGeometry = new THREE.PlaneGeometry(12, 4)
      const backWall = new THREE.Mesh(backWallGeometry, wallMaterial)
      backWall.position.set(0, 2, -5)
      this.scene.add(backWall)

      // 左墙
      const leftWallGeometry = new THREE.PlaneGeometry(10, 4)
      const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      leftWall.position.set(-6, 2, 0)
      leftWall.rotation.y = Math.PI / 2
      this.scene.add(leftWall)

      // 右墙
      const rightWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      rightWall.position.set(6, 2, 0)
      rightWall.rotation.y = -Math.PI / 2
      this.scene.add(rightWall)

      // 添加一些家具
      this.createFurniture()
    },

    createFloorTexture() {
      const canvas = document.createElement('canvas')
      canvas.width = 512
      canvas.height = 512
      const context = canvas.getContext('2d')

      // 创建木地板纹理
      context.fillStyle = '#deb887'
      context.fillRect(0, 0, 512, 512)

      // 添加木纹
      for (let i = 0; i < 512; i += 64) {
        context.fillStyle = '#cd853f'
        context.fillRect(i, 0, 2, 512)
      }

      const texture = new THREE.CanvasTexture(canvas)
      texture.wrapS = THREE.RepeatWrapping
      texture.wrapT = THREE.RepeatWrapping
      texture.repeat.set(4, 4)

      return texture
    },

    createFurniture() {
      // 沙发
      const sofaGroup = new THREE.Group()

      // 沙发底座
      const sofaBaseGeometry = new THREE.BoxGeometry(2.5, 0.4, 1)
      const sofaBaseMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 })
      const sofaBase = new THREE.Mesh(sofaBaseGeometry, sofaBaseMaterial)
      sofaBase.position.y = 0.2
      sofaGroup.add(sofaBase)

      // 沙发靠背
      const sofaBackGeometry = new THREE.BoxGeometry(2.5, 0.8, 0.2)
      const sofaBack = new THREE.Mesh(sofaBackGeometry, sofaBaseMaterial)
      sofaBack.position.set(0, 0.6, -0.4)
      sofaGroup.add(sofaBack)

      // 沙发扶手
      const armGeometry = new THREE.BoxGeometry(0.2, 0.6, 1)
      const leftArm = new THREE.Mesh(armGeometry, sofaBaseMaterial)
      leftArm.position.set(-1.15, 0.5, 0)
      sofaGroup.add(leftArm)

      const rightArm = new THREE.Mesh(armGeometry, sofaBaseMaterial)
      rightArm.position.set(1.15, 0.5, 0)
      sofaGroup.add(rightArm)

      sofaGroup.position.set(0, 0, 2)
      sofaGroup.castShadow = true
      sofaGroup.receiveShadow = true
      this.scene.add(sofaGroup)

      // 茶几
      const tableGroup = new THREE.Group()

      // 桌面
      const tableTopGeometry = new THREE.BoxGeometry(1.5, 0.1, 0.8)
      const tableTopMaterial = new THREE.MeshPhongMaterial({ color: 0x654321 })
      const tableTop = new THREE.Mesh(tableTopGeometry, tableTopMaterial)
      tableTop.position.y = 0.4
      tableGroup.add(tableTop)

      // 桌腿
      const legGeometry = new THREE.BoxGeometry(0.05, 0.4, 0.05)
      const legMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 })

      const positions = [
        [-0.7, 0.2, -0.35],
        [0.7, 0.2, -0.35],
        [-0.7, 0.2, 0.35],
        [0.7, 0.2, 0.35]
      ]

      positions.forEach(pos => {
        const leg = new THREE.Mesh(legGeometry, legMaterial)
        leg.position.set(...pos)
        tableGroup.add(leg)
      })

      tableGroup.position.set(0, 0, 0.5)
      tableGroup.castShadow = true
      tableGroup.receiveShadow = true
      this.scene.add(tableGroup)

      // 电视
      const tvGroup = new THREE.Group()

      // 电视屏幕
      const screenGeometry = new THREE.BoxGeometry(2, 1.2, 0.1)
      const screenMaterial = new THREE.MeshPhongMaterial({ color: 0x000000 })
      const screen = new THREE.Mesh(screenGeometry, screenMaterial)
      screen.position.y = 1.5
      tvGroup.add(screen)

      // 电视边框
      const frameGeometry = new THREE.BoxGeometry(2.2, 1.4, 0.05)
      const frameMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 })
      const frame = new THREE.Mesh(frameGeometry, frameMaterial)
      frame.position.set(0, 1.5, -0.025)
      tvGroup.add(frame)

      tvGroup.position.set(0, 0, -4.9)
      tvGroup.castShadow = true
      tvGroup.receiveShadow = true
      this.scene.add(tvGroup)
    },

    async createDevices() {
      // 并行加载所有设备模型
      const devicePromises = this.devicesStore.devices.map(device =>
        this.createDeviceMesh(device)
      )

      try {
        await Promise.all(devicePromises)
        console.log('All device models loaded successfully')
      } catch (error) {
        console.error('Error loading some device models:', error)
      }
    },

    async createDeviceMesh(device) {
      let mesh

      // 尝试加载在线模型，如果失败则使用备用模型
      try {
        mesh = await modelLoader.loadModel(device.type)
        if (!mesh) {
          throw new Error('Model not found')
        }
      } catch (error) {
        console.warn(`Failed to load online model for ${device.type}, using fallback:`, error)
        mesh = this.createFallbackMesh(device)
      }

      mesh.position.set(device.position.x, device.position.y, device.position.z)
      mesh.castShadow = true
      mesh.receiveShadow = true
      mesh.userData = { deviceId: device.id }

      // 确保所有子对象也设置阴影
      mesh.traverse((child) => {
        if (child.isMesh) {
          child.castShadow = true
          child.receiveShadow = true
        }
      })

      this.scene.add(mesh)
      this.deviceMeshes.set(device.id, mesh)
    },

    createFallbackMesh(device) {
      // 使用备用的几何体模型
      switch (device.type) {
        case 'air-conditioner':
          return this.createAirConditioner(device)
        case 'fan':
          return this.createFan(device)
        case 'light':
          return this.createLight(device)
        case 'curtain':
          return this.createCurtain(device)
        default:
          return this.createDefaultDevice(device)
      }
    },

    createAirConditioner(device) {
      const group = new THREE.Group()

      // 主体 - 更圆润的设计
      const mainGeometry = new THREE.BoxGeometry(1.2, 0.4, 0.8)
      // 圆角效果
      mainGeometry.parameters = { width: 1.2, height: 0.4, depth: 0.8 }
      const mainMaterial = new THREE.MeshPhongMaterial({
        color: 0xf8f8f8,
        shininess: 100,
        specular: 0x111111
      })
      const mainBody = new THREE.Mesh(mainGeometry, mainMaterial)
      mainBody.position.y = 0.2
      group.add(mainBody)

      // 前面板 - 带LED指示灯
      const panelGeometry = new THREE.BoxGeometry(1.15, 0.35, 0.05)
      const panelMaterial = new THREE.MeshPhongMaterial({
        color: device.status === 'on' ? 0x4CAF50 : 0x666666,
        emissive: device.status === 'on' ? 0x002200 : 0x000000
      })
      const panel = new THREE.Mesh(panelGeometry, panelMaterial)
      panel.position.set(0, 0.2, 0.425)
      group.add(panel)

      // LED指示灯
      const ledGeometry = new THREE.SphereGeometry(0.03, 8, 8)
      const ledMaterial = new THREE.MeshPhongMaterial({
        color: device.status === 'on' ? 0x00ff00 : 0x333333,
        emissive: device.status === 'on' ? 0x004400 : 0x000000
      })
      const led = new THREE.Mesh(ledGeometry, ledMaterial)
      led.position.set(0.4, 0.3, 0.45)
      group.add(led)

      // 出风口 - 更精细的设计
      for (let i = 0; i < 5; i++) {
        const ventGeometry = new THREE.BoxGeometry(0.9, 0.03, 0.02)
        const ventMaterial = new THREE.MeshPhongMaterial({
          color: 0x222222,
          shininess: 50
        })
        const vent = new THREE.Mesh(ventGeometry, ventMaterial)
        vent.position.set(0, 0.05 + i * 0.06, 0.45)
        group.add(vent)
      }

      // 侧面散热格栅
      for (let side = 0; side < 2; side++) {
        for (let i = 0; i < 8; i++) {
          const grillGeometry = new THREE.BoxGeometry(0.02, 0.3, 0.03)
          const grillMaterial = new THREE.MeshPhongMaterial({ color: 0x444444 })
          const grill = new THREE.Mesh(grillGeometry, grillMaterial)
          grill.position.set(
            side === 0 ? -0.61 : 0.61,
            0.2,
            -0.3 + i * 0.08
          )
          group.add(grill)
        }
      }

      // 存储面板用于状态更新
      group.userData.panel = panel
      group.userData.led = led

      return group
    },

    createFan() {
      const group = new THREE.Group()

      // 底座 - 更重的设计
      const baseGeometry = new THREE.CylinderGeometry(0.35, 0.45, 0.15, 16)
      const baseMaterial = new THREE.MeshPhongMaterial({
        color: 0x2c3e50,
        shininess: 80
      })
      const base = new THREE.Mesh(baseGeometry, baseMaterial)
      base.position.y = 0.075
      group.add(base)

      // 底座装饰环
      const ringGeometry = new THREE.TorusGeometry(0.4, 0.02, 8, 16)
      const ringMaterial = new THREE.MeshPhongMaterial({ color: 0x34495e })
      const ring = new THREE.Mesh(ringGeometry, ringMaterial)
      ring.position.y = 0.15
      ring.rotation.x = Math.PI / 2
      group.add(ring)

      // 支柱 - 更粗壮
      const poleGeometry = new THREE.CylinderGeometry(0.06, 0.08, 1.2, 12)
      const poleMaterial = new THREE.MeshPhongMaterial({
        color: 0x34495e,
        shininess: 60
      })
      const pole = new THREE.Mesh(poleGeometry, poleMaterial)
      pole.position.y = 0.75
      group.add(pole)

      // 电机外壳
      const motorGeometry = new THREE.CylinderGeometry(0.18, 0.18, 0.25, 16)
      const motorMaterial = new THREE.MeshPhongMaterial({
        color: 0x2c3e50,
        shininess: 100
      })
      const motor = new THREE.Mesh(motorGeometry, motorMaterial)
      motor.position.y = 1.35
      group.add(motor)

      // 电机前盖
      const frontCoverGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.05, 16)
      const frontCoverMaterial = new THREE.MeshPhongMaterial({ color: 0x1a252f })
      const frontCover = new THREE.Mesh(frontCoverGeometry, frontCoverMaterial)
      frontCover.position.y = 1.475
      group.add(frontCover)

      // 扇叶组
      const bladeGroup = new THREE.Group()
      for (let i = 0; i < 4; i++) {
        // 扇叶形状 - 更真实的弧形
        const bladeShape = new THREE.Shape()
        bladeShape.moveTo(0, 0)
        bladeShape.quadraticCurveTo(0.3, 0.1, 0.7, 0.05)
        bladeShape.quadraticCurveTo(0.8, 0, 0.7, -0.05)
        bladeShape.quadraticCurveTo(0.3, -0.1, 0, 0)

        const bladeGeometry = new THREE.ExtrudeGeometry(bladeShape, {
          depth: 0.02,
          bevelEnabled: true,
          bevelThickness: 0.005,
          bevelSize: 0.005
        })

        const bladeMaterial = new THREE.MeshPhongMaterial({
          color: 0xe8e8e8,
          shininess: 120,
          transparent: true,
          opacity: 0.9
        })

        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial)
        blade.rotation.y = (i * Math.PI * 2) / 4
        blade.rotation.z = Math.PI / 12 // 轻微倾斜
        bladeGroup.add(blade)
      }

      bladeGroup.position.y = 1.5
      group.add(bladeGroup)

      // 中心螺丝
      const screwGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.08, 8)
      const screwMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 })
      const screw = new THREE.Mesh(screwGeometry, screwMaterial)
      screw.position.y = 1.54
      group.add(screw)

      // 存储扇叶组用于动画
      group.userData.blades = bladeGroup

      return group
    },

    createLight(device) {
      const group = new THREE.Group()

      // 灯座/底座
      const baseGeometry = new THREE.CylinderGeometry(0.12, 0.15, 0.08, 16)
      const baseMaterial = new THREE.MeshPhongMaterial({
        color: 0x8B4513,
        shininess: 60
      })
      const base = new THREE.Mesh(baseGeometry, baseMaterial)
      base.position.y = -0.04
      group.add(base)

      // 灯杆
      const poleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.3, 8)
      const poleMaterial = new THREE.MeshPhongMaterial({
        color: 0x654321,
        shininess: 80
      })
      const pole = new THREE.Mesh(poleGeometry, poleMaterial)
      pole.position.y = 0.15
      group.add(pole)

      // 灯泡 - 更真实的形状
      const bulbGeometry = new THREE.SphereGeometry(0.12, 16, 16)
      // 压扁底部
      const bulbPositions = bulbGeometry.attributes.position
      for (let i = 0; i < bulbPositions.count; i++) {
        const y = bulbPositions.getY(i)
        if (y < -0.05) {
          bulbPositions.setY(i, -0.05)
        }
      }
      bulbGeometry.attributes.position.needsUpdate = true

      const bulbMaterial = new THREE.MeshPhongMaterial({
        color: device.status === 'on' ? 0xfff8dc : 0xe0e0e0,
        emissive: device.status === 'on' ? 0x332200 : 0x000000,
        transparent: true,
        opacity: 0.95,
        shininess: 100
      })
      const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial)
      bulb.position.y = 0.3
      group.add(bulb)

      // 灯罩 - 更精美的设计
      const shadeGeometry = new THREE.ConeGeometry(0.28, 0.35, 12, 1, false)
      const shadeMaterial = new THREE.MeshPhongMaterial({
        color: 0xf5f5dc,
        transparent: true,
        opacity: 0.8,
        side: THREE.DoubleSide,
        shininess: 30
      })
      const shade = new THREE.Mesh(shadeGeometry, shadeMaterial)
      shade.position.y = 0.475
      shade.rotation.x = Math.PI
      group.add(shade)

      // 灯罩边缘装饰
      const rimGeometry = new THREE.TorusGeometry(0.28, 0.01, 8, 16)
      const rimMaterial = new THREE.MeshPhongMaterial({
        color: 0xd4af37,
        shininess: 100
      })
      const rim = new THREE.Mesh(rimGeometry, rimMaterial)
      rim.position.y = 0.3
      rim.rotation.x = Math.PI / 2
      group.add(rim)

      // 开关按钮
      const switchGeometry = new THREE.CylinderGeometry(0.015, 0.015, 0.03, 8)
      const switchMaterial = new THREE.MeshPhongMaterial({
        color: device.status === 'on' ? 0x00ff00 : 0x666666,
        emissive: device.status === 'on' ? 0x002200 : 0x000000
      })
      const switchButton = new THREE.Mesh(switchGeometry, switchMaterial)
      switchButton.position.set(0.08, 0.1, 0)
      switchButton.rotation.z = Math.PI / 2
      group.add(switchButton)

      // 存储组件用于状态更新
      group.userData.bulb = bulb
      group.userData.switch = switchButton

      return group
    },

    createCurtain(device) {
      const group = new THREE.Group()

      // 窗帘轨道 - 更精细的设计
      const railGeometry = new THREE.BoxGeometry(2.2, 0.08, 0.08)
      const railMaterial = new THREE.MeshPhongMaterial({
        color: 0x8B4513,
        shininess: 60
      })
      const rail = new THREE.Mesh(railGeometry, railMaterial)
      rail.position.y = 1
      group.add(rail)

      // 轨道支架
      for (let i = 0; i < 3; i++) {
        const bracketGeometry = new THREE.BoxGeometry(0.05, 0.15, 0.1)
        const bracketMaterial = new THREE.MeshPhongMaterial({ color: 0x654321 })
        const bracket = new THREE.Mesh(bracketGeometry, bracketMaterial)
        bracket.position.set(-0.8 + i * 0.8, 1.075, 0)
        group.add(bracket)
      }

      // 窗帘环
      for (let i = 0; i < 8; i++) {
        const ringGeometry = new THREE.TorusGeometry(0.03, 0.008, 6, 12)
        const ringMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 })
        const ring = new THREE.Mesh(ringGeometry, ringMaterial)
        ring.position.set(-0.7 + i * 0.2, 0.95, 0)
        ring.rotation.x = Math.PI / 2
        group.add(ring)
      }

      // 左侧窗帘
      const leftCurtainGeometry = new THREE.PlaneGeometry(0.9, 1.8)
      const curtainMaterial = new THREE.MeshLambertMaterial({
        color: device.color.replace('#', '0x'),
        side: THREE.DoubleSide
      })

      // 添加窗帘纹理
      const canvas = document.createElement('canvas')
      canvas.width = 256
      canvas.height = 512
      const context = canvas.getContext('2d')
      context.fillStyle = device.color
      context.fillRect(0, 0, 256, 512)

      // 添加垂直条纹
      context.fillStyle = 'rgba(0,0,0,0.1)'
      for (let i = 0; i < 256; i += 16) {
        context.fillRect(i, 0, 2, 512)
      }

      const curtainTexture = new THREE.CanvasTexture(canvas)
      curtainMaterial.map = curtainTexture

      const leftCurtain = new THREE.Mesh(leftCurtainGeometry, curtainMaterial)
      leftCurtain.position.y = 0.1
      leftCurtain.position.z = -0.05

      // 右侧窗帘
      const rightCurtain = new THREE.Mesh(leftCurtainGeometry, curtainMaterial.clone())
      rightCurtain.position.y = 0.1
      rightCurtain.position.z = -0.05

      // 根据开合度调整窗帘位置
      const openness = device.openness / 100
      leftCurtain.position.x = -0.45 - openness * 0.5
      rightCurtain.position.x = 0.45 + openness * 0.5

      group.add(leftCurtain)
      group.add(rightCurtain)

      // 窗帘绑带
      if (openness > 0.3) {
        for (let side = 0; side < 2; side++) {
          const tieGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.3, 8)
          const tieMaterial = new THREE.MeshPhongMaterial({
            color: parseInt(device.color.replace('#', '0x')) * 0.8
          })
          const tie = new THREE.Mesh(tieGeometry, tieMaterial)
          tie.position.set(
            side === 0 ? leftCurtain.position.x + 0.3 : rightCurtain.position.x - 0.3,
            0.3,
            -0.03
          )
          tie.rotation.z = Math.PI / 2
          group.add(tie)
        }
      }

      // 存储窗帘用于状态更新
      group.userData.leftCurtain = leftCurtain
      group.userData.rightCurtain = rightCurtain

      return group
    },

    createDefaultDevice(device) {
      const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5)
      const material = new THREE.MeshPhongMaterial({
        color: device.color.replace('#', '0x')
      })
      return new THREE.Mesh(geometry, material)
    },

    addEventListeners() {
      this.$refs.sceneContainer.addEventListener('click', this.onMouseClick)
      window.addEventListener('resize', this.onWindowResize)
    },

    removeEventListeners() {
      if (this.$refs.sceneContainer) {
        this.$refs.sceneContainer.removeEventListener('click', this.onMouseClick)
      }
      window.removeEventListener('resize', this.onWindowResize)
    },

    onMouseClick(event) {
      const rect = this.$refs.sceneContainer.getBoundingClientRect()
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      this.raycaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.raycaster.intersectObjects(this.scene.children)

      if (intersects.length > 0) {
        const clickedObject = intersects[0].object
        if (clickedObject.userData.deviceId) {
          this.devicesStore.selectDevice(clickedObject.userData.deviceId)
        }
      }
    },

    onWindowResize() {
      if (!this.$refs.sceneContainer) return
      
      const width = this.$refs.sceneContainer.clientWidth
      const height = this.$refs.sceneContainer.clientHeight

      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },

    animate() {
      this.animationId = requestAnimationFrame(this.animate)
      this.controls.update() // 更新轨道控制器
      this.updateDeviceVisuals()
      this.renderer.render(this.scene, this.camera)
    },

    updateDeviceVisuals() {
      this.devicesStore.devices.forEach(device => {
        const mesh = this.deviceMeshes.get(device.id)
        if (!mesh) return

        // 更新设备视觉状态
        switch (device.type) {
          case 'air-conditioner':
            // 更新空调面板和LED
            if (mesh.userData.panel) {
              mesh.userData.panel.material.color.setHex(device.status === 'on' ? 0x4CAF50 : 0x666666)
              mesh.userData.panel.material.emissive.setHex(device.status === 'on' ? 0x002200 : 0x000000)
            }
            if (mesh.userData.led) {
              mesh.userData.led.material.color.setHex(device.status === 'on' ? 0x00ff00 : 0x333333)
              mesh.userData.led.material.emissive.setHex(device.status === 'on' ? 0x004400 : 0x000000)
            }
            break

          case 'fan':
            // 风扇扇叶旋转
            if (device.status === 'on' && mesh.userData.blades) {
              mesh.userData.blades.rotation.y += 0.15 * device.speed
            }
            break

          case 'light':
            // 更新灯泡
            if (mesh.userData.bulb) {
              mesh.userData.bulb.material.color.setHex(device.status === 'on' ? 0xfff8dc : 0xe0e0e0)
              mesh.userData.bulb.material.emissive.setHex(device.status === 'on' ? 0x332200 : 0x000000)
            }

            // 更新开关按钮
            if (mesh.userData.switch) {
              mesh.userData.switch.material.color.setHex(device.status === 'on' ? 0x00ff00 : 0x666666)
              mesh.userData.switch.material.emissive.setHex(device.status === 'on' ? 0x002200 : 0x000000)
            }

            // 处理点光源
            if (device.status === 'on' && !mesh.userData.light) {
              const pointLight = new THREE.PointLight(0xfff8dc, 1.5, 6)
              pointLight.position.set(0, 0.3, 0)
              pointLight.castShadow = true
              pointLight.shadow.mapSize.width = 1024
              pointLight.shadow.mapSize.height = 1024
              mesh.add(pointLight)
              mesh.userData.light = pointLight
            } else if (device.status === 'off' && mesh.userData.light) {
              mesh.remove(mesh.userData.light)
              mesh.userData.light = null
            }

            // 根据亮度调整光源强度
            if (mesh.userData.light) {
              mesh.userData.light.intensity = (device.brightness / 100) * 1.5
            }
            break

          case 'curtain':
            // 更新窗帘开合度
            if (mesh.userData.leftCurtain && mesh.userData.rightCurtain) {
              const openness = device.openness / 100
              mesh.userData.leftCurtain.position.x = -0.45 - openness * 0.5
              mesh.userData.rightCurtain.position.x = 0.45 + openness * 0.5
            }
            break
        }

        // 选中状态的高亮效果
        if (this.devicesStore.selectedDevice && this.devicesStore.selectedDevice.id === device.id) {
          this.addSelectionHighlight(mesh)
        } else {
          this.removeSelectionHighlight(mesh)
        }
      })
    },

    addSelectionHighlight(mesh) {
      // 添加选中高亮效果
      if (!mesh.userData.outline) {
        const outlineGeometry = new THREE.BoxGeometry(1.5, 1.5, 1.5)
        const outlineMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ff00,
          transparent: true,
          opacity: 0.3,
          wireframe: true
        })
        const outline = new THREE.Mesh(outlineGeometry, outlineMaterial)
        mesh.add(outline)
        mesh.userData.outline = outline
      }
    },

    removeSelectionHighlight(mesh) {
      // 移除选中高亮效果
      if (mesh.userData.outline) {
        mesh.remove(mesh.userData.outline)
        mesh.userData.outline = null
      }
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
}
</style>

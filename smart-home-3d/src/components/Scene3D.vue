<template>
  <div ref="sceneContainer" class="scene-container">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p>正在加载3D场景...</p>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { useDevicesStore } from '../stores/devices.js'

export default {
  name: 'Scene3D',
  setup() {
    const devicesStore = useDevicesStore()
    return { devicesStore }
  },
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      deviceMeshes: new Map(),
      raycaster: new THREE.Raycaster(),
      mouse: new THREE.Vector2(),
      animationId: null,
      loading: true,
      gltfLoader: new GLTFLoader()
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initScene()
    })
  },
  beforeUnmount() {
    this.cleanup()
  },

  methods: {
    async initScene() {
      try {
        // 创建场景
        this.scene = new THREE.Scene()
        this.scene.background = new THREE.Color(0x87CEEB) // 天空蓝

        // 创建相机
        const container = this.$refs.sceneContainer
        const width = container.clientWidth
        const height = container.clientHeight

        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
        this.camera.position.set(10, 8, 10)

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true
        })
        this.renderer.setSize(width, height)
        this.renderer.shadowMap.enabled = true
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
        container.appendChild(this.renderer.domElement)

        // 添加轨道控制器
        this.controls = new OrbitControls(this.camera, this.renderer.domElement)
        this.controls.enableDamping = true
        this.controls.dampingFactor = 0.05
        this.controls.maxPolarAngle = Math.PI / 2.2

        // 添加光源
        this.addLights()

        // 创建房间
        await this.createRoom()

        // 创建设备
        await this.createDevices()

        // 添加事件监听
        this.addEventListeners()

        // 开始动画循环
        this.animate()

        this.loading = false

      } catch (error) {
        console.error('3D场景初始化失败:', error)
        this.loading = false
      }
    },

    addLights() {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)

      // 主光源 - 太阳光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
      directionalLight.position.set(50, 50, 25)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      directionalLight.shadow.camera.near = 0.5
      directionalLight.shadow.camera.far = 500
      directionalLight.shadow.camera.left = -50
      directionalLight.shadow.camera.right = 50
      directionalLight.shadow.camera.top = 50
      directionalLight.shadow.camera.bottom = -50
      this.scene.add(directionalLight)
    },

    async createRoom() {
      // 创建地板
      const floorGeometry = new THREE.PlaneGeometry(20, 20)
      const floorMaterial = new THREE.MeshLambertMaterial({
        color: 0xf0f0f0,
        transparent: true,
        opacity: 0.8
      })
      const floor = new THREE.Mesh(floorGeometry, floorMaterial)
      floor.rotation.x = -Math.PI / 2
      floor.receiveShadow = true
      this.scene.add(floor)

      // 创建墙壁
      this.createWalls()

      // 添加一些家具
      this.createFurniture()
    },

    createWalls() {
      const wallMaterial = new THREE.MeshLambertMaterial({
        color: 0xe8e8e8,
        transparent: true,
        opacity: 0.7
      })

      // 后墙
      const backWallGeometry = new THREE.PlaneGeometry(20, 8)
      const backWall = new THREE.Mesh(backWallGeometry, wallMaterial)
      backWall.position.set(0, 4, -10)
      this.scene.add(backWall)

      // 左墙
      const leftWallGeometry = new THREE.PlaneGeometry(20, 8)
      const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial)
      leftWall.position.set(-10, 4, 0)
      leftWall.rotation.y = Math.PI / 2
      this.scene.add(leftWall)
    },

    createFurniture() {
      // 创建一个简单的沙发
      const sofaGeometry = new THREE.BoxGeometry(3, 1, 1.5)
      const sofaMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 })
      const sofa = new THREE.Mesh(sofaGeometry, sofaMaterial)
      sofa.position.set(0, 0.5, 3)
      sofa.castShadow = true
      sofa.receiveShadow = true
      this.scene.add(sofa)

      // 创建茶几
      const tableGeometry = new THREE.BoxGeometry(2, 0.2, 1)
      const tableMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 })
      const table = new THREE.Mesh(tableGeometry, tableMaterial)
      table.position.set(0, 0.4, 0)
      table.castShadow = true
      table.receiveShadow = true
      this.scene.add(table)
    },

    async createDevices() {
      // 创建设备3D模型
      for (const device of this.devicesStore.devices) {
        const mesh = this.createDeviceMesh(device)
        if (mesh) {
          mesh.position.set(device.position.x, device.position.y, device.position.z)
          mesh.userData = { deviceId: device.id }
          mesh.castShadow = true
          mesh.receiveShadow = true
          this.scene.add(mesh)
          this.deviceMeshes.set(device.id, mesh)
        }
      }
    },

    createDeviceMesh(device) {
      let mesh

      switch (device.type) {
        case 'air-conditioner':
          mesh = this.createAirConditioner(device)
          break
        case 'fan':
          mesh = this.createFan(device)
          break
        case 'light':
          mesh = this.createLight(device)
          break
        case 'curtain':
          mesh = this.createCurtain(device)
          break
        default:
          mesh = this.createDefaultDevice(device)
      }

      return mesh
    },

    createAirConditioner(device) {
      const group = new THREE.Group()

      // 主体
      const mainGeometry = new THREE.BoxGeometry(1.5, 0.4, 0.8)
      const mainMaterial = new THREE.MeshLambertMaterial({
        color: device.status === 'on' ? 0x4CAF50 : 0xcccccc
      })
      const mainBody = new THREE.Mesh(mainGeometry, mainMaterial)
      group.add(mainBody)

      // 前面板
      const panelGeometry = new THREE.BoxGeometry(1.4, 0.35, 0.05)
      const panelMaterial = new THREE.MeshLambertMaterial({
        color: device.status === 'on' ? 0x2E7D32 : 0x999999
      })
      const panel = new THREE.Mesh(panelGeometry, panelMaterial)
      panel.position.z = 0.425
      group.add(panel)

      group.userData.device = device
      return group
    },

    createFan(device) {
      const group = new THREE.Group()

      // 底座
      const baseGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.1, 16)
      const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 })
      const base = new THREE.Mesh(baseGeometry, baseMaterial)
      group.add(base)

      // 支柱
      const poleGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1.2, 8)
      const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 })
      const pole = new THREE.Mesh(poleGeometry, poleMaterial)
      pole.position.y = 0.6
      group.add(pole)

      // 扇叶组
      const bladeGroup = new THREE.Group()
      for (let i = 0; i < 3; i++) {
        const bladeGeometry = new THREE.BoxGeometry(0.6, 0.02, 0.1)
        const bladeMaterial = new THREE.MeshLambertMaterial({
          color: 0xcccccc,
          transparent: true,
          opacity: 0.8
        })
        const blade = new THREE.Mesh(bladeGeometry, bladeMaterial)
        blade.position.x = 0.3
        blade.rotation.y = (i * Math.PI * 2) / 3
        bladeGroup.add(blade)
      }
      bladeGroup.position.y = 1.2
      group.add(bladeGroup)

      group.userData.blades = bladeGroup
      group.userData.device = device
      return group
    },

    createLight(device) {
      const group = new THREE.Group()

      // 灯泡
      const bulbGeometry = new THREE.SphereGeometry(0.15, 16, 16)
      const bulbMaterial = new THREE.MeshLambertMaterial({
        color: device.status === 'on' ? 0xffff88 : 0xcccccc,
        emissive: device.status === 'on' ? 0x444400 : 0x000000
      })
      const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial)
      group.add(bulb)

      // 如果灯是开着的，添加点光源
      if (device.status === 'on') {
        const pointLight = new THREE.PointLight(0xffff88, 1, 5)
        pointLight.position.set(0, 0, 0)
        group.add(pointLight)
        group.userData.light = pointLight
      }

      group.userData.device = device
      return group
    },

    createCurtain(device) {
      const group = new THREE.Group()

      // 窗帘轨道
      const railGeometry = new THREE.BoxGeometry(2, 0.05, 0.05)
      const railMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 })
      const rail = new THREE.Mesh(railGeometry, railMaterial)
      rail.position.y = 1
      group.add(rail)

      // 窗帘布料
      const curtainGeometry = new THREE.PlaneGeometry(1.8, 1.8)
      const curtainMaterial = new THREE.MeshLambertMaterial({
        color: 0x8B4513,
        side: THREE.DoubleSide
      })
      const curtain = new THREE.Mesh(curtainGeometry, curtainMaterial)
      curtain.position.y = 0.1

      // 根据开合度调整窗帘
      const openness = device.openness / 100
      curtain.scale.x = 1 - openness * 0.8

      group.add(curtain)
      group.userData.curtain = curtain
      group.userData.device = device
      return group
    },

    createDefaultDevice(device) {
      const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5)
      const material = new THREE.MeshLambertMaterial({
        color: parseInt(device.color.replace('#', '0x'))
      })
      return new THREE.Mesh(geometry, material)
    },

    addEventListeners() {
      this.$refs.sceneContainer.addEventListener('click', this.onMouseClick)
      window.addEventListener('resize', this.onWindowResize)
    },

    onMouseClick(event) {
      const rect = this.$refs.sceneContainer.getBoundingClientRect()
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      this.raycaster.setFromCamera(this.mouse, this.camera)
      const intersects = this.raycaster.intersectObjects(this.scene.children, true)

      if (intersects.length > 0) {
        let clickedObject = intersects[0].object

        // 向上查找包含deviceId的对象
        while (clickedObject && !clickedObject.userData.deviceId) {
          clickedObject = clickedObject.parent
        }

        if (clickedObject && clickedObject.userData.deviceId) {
          this.devicesStore.selectDevice(clickedObject.userData.deviceId)
        }
      }
    },

    onWindowResize() {
      const container = this.$refs.sceneContainer
      const width = container.clientWidth
      const height = container.clientHeight

      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },

    animate() {
      this.animationId = requestAnimationFrame(this.animate)

      if (this.controls) {
        this.controls.update()
      }

      this.updateDeviceVisuals()

      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera)
      }
    },

    updateDeviceVisuals() {
      this.devicesStore.devices.forEach(device => {
        const mesh = this.deviceMeshes.get(device.id)
        if (!mesh) return

        // 更新设备状态
        switch (device.type) {
          case 'fan':
            if (device.status === 'on' && mesh.userData.blades) {
              mesh.userData.blades.rotation.y += 0.1 * device.speed
            }
            break

          case 'light':
            // 更新灯光状态
            const bulb = mesh.children.find(child => child.geometry.type === 'SphereGeometry')
            if (bulb) {
              bulb.material.color.setHex(device.status === 'on' ? 0xffff88 : 0xcccccc)
              bulb.material.emissive.setHex(device.status === 'on' ? 0x444400 : 0x000000)
            }
            break

          case 'curtain':
            if (mesh.userData.curtain) {
              const openness = device.openness / 100
              mesh.userData.curtain.scale.x = 1 - openness * 0.8
            }
            break
        }
      })
    },

    cleanup() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
      }

      if (this.controls) {
        this.controls.dispose()
      }

      if (this.renderer) {
        this.renderer.dispose()
      }

      // 移除事件监听器
      if (this.$refs.sceneContainer) {
        this.$refs.sceneContainer.removeEventListener('click', this.onMouseClick)
      }
      window.removeEventListener('resize', this.onWindowResize)
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #f0f0f0;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content p {
  margin: 0;
  font-size: 16px;
}
</style>
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

// 免费3D模型资源URL - 使用可访问的公开资源
const MODEL_URLS = {
  // 使用Three.js官方示例模型（这些是可靠的）
  'chair': 'https://threejs.org/examples/models/gltf/DamagedHelmet/DamagedHelmet.gltf',
  'helmet': 'https://threejs.org/examples/models/gltf/DamagedHelmet/DamagedHelmet.gltf',

  // 使用一些开源的简单模型（如果可用）
  'simple-box': '/models/simple-box.gltf',

  // 暂时禁用在线模型加载，使用本地几何体
  'air-conditioner': null,
  'fan': null,
  'light': null,
  'curtain': null
}

class ModelLoader {
  constructor() {
    this.loader = new GLTFLoader()
    this.loadedModels = new Map()
    this.loadingPromises = new Map()
  }

  // 加载模型
  async loadModel(modelType, url = null) {
    const modelUrl = url || MODEL_URLS[modelType]
    
    if (!modelUrl) {
      console.warn(`No URL found for model type: ${modelType}`)
      return null
    }

    // 如果已经加载过，直接返回克隆
    if (this.loadedModels.has(modelType)) {
      return this.cloneModel(this.loadedModels.get(modelType))
    }

    // 如果正在加载，等待加载完成
    if (this.loadingPromises.has(modelType)) {
      await this.loadingPromises.get(modelType)
      return this.cloneModel(this.loadedModels.get(modelType))
    }

    // 开始加载
    const loadingPromise = this.loadGLTF(modelUrl)
    this.loadingPromises.set(modelType, loadingPromise)

    try {
      const gltf = await loadingPromise
      this.loadedModels.set(modelType, gltf.scene)
      this.loadingPromises.delete(modelType)
      return this.cloneModel(gltf.scene)
    } catch (error) {
      console.error(`Failed to load model ${modelType}:`, error)
      this.loadingPromises.delete(modelType)
      return null
    }
  }

  // 加载GLTF文件
  loadGLTF(url) {
    return new Promise((resolve, reject) => {
      this.loader.load(
        url,
        (gltf) => resolve(gltf),
        (progress) => {
          console.log(`Loading progress: ${(progress.loaded / progress.total * 100)}%`)
        },
        (error) => reject(error)
      )
    })
  }

  // 克隆模型
  cloneModel(model) {
    const cloned = model.clone()
    
    // 确保材质也被克隆
    cloned.traverse((child) => {
      if (child.isMesh) {
        child.material = child.material.clone()
        child.castShadow = true
        child.receiveShadow = true
      }
    })
    
    return cloned
  }

  // 创建简单的几何体模型作为备用
  createFallbackModel(deviceType) {
    const group = new THREE.Group()
    
    switch (deviceType) {
      case 'air-conditioner':
        return this.createAirConditionerModel()
      case 'fan':
        return this.createFanModel()
      case 'light':
        return this.createLightModel()
      case 'curtain':
        return this.createCurtainModel()
      default:
        const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5)
        const material = new THREE.MeshPhongMaterial({ color: 0x888888 })
        return new THREE.Mesh(geometry, material)
    }
  }

  createAirConditionerModel() {
    const group = new THREE.Group()
    
    // 主体
    const mainGeometry = new THREE.BoxGeometry(1.2, 0.4, 0.8)
    const mainMaterial = new THREE.MeshPhongMaterial({ 
      color: 0xf0f0f0,
      shininess: 30
    })
    const mainBody = new THREE.Mesh(mainGeometry, mainMaterial)
    mainBody.position.y = 0.2
    group.add(mainBody)

    // 前面板
    const panelGeometry = new THREE.BoxGeometry(1.15, 0.35, 0.05)
    const panelMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 })
    const panel = new THREE.Mesh(panelGeometry, panelMaterial)
    panel.position.set(0, 0.2, 0.425)
    group.add(panel)

    // 出风口
    for (let i = 0; i < 3; i++) {
      const ventGeometry = new THREE.BoxGeometry(0.8, 0.05, 0.02)
      const ventMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 })
      const vent = new THREE.Mesh(ventGeometry, ventMaterial)
      vent.position.set(0, 0.1 + i * 0.1, 0.45)
      group.add(vent)
    }

    return group
  }

  createFanModel() {
    const group = new THREE.Group()
    
    // 底座
    const baseGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.1, 16)
    const baseMaterial = new THREE.MeshPhongMaterial({ color: 0x444444 })
    const base = new THREE.Mesh(baseGeometry, baseMaterial)
    base.position.y = 0.05
    group.add(base)

    // 支柱
    const poleGeometry = new THREE.CylinderGeometry(0.05, 0.05, 1, 8)
    const poleMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 })
    const pole = new THREE.Mesh(poleGeometry, poleMaterial)
    pole.position.y = 0.6
    group.add(pole)

    // 电机
    const motorGeometry = new THREE.SphereGeometry(0.15, 16, 16)
    const motorMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 })
    const motor = new THREE.Mesh(motorGeometry, motorMaterial)
    motor.position.y = 1.1
    group.add(motor)

    // 扇叶
    const bladeGroup = new THREE.Group()
    for (let i = 0; i < 3; i++) {
      const bladeGeometry = new THREE.BoxGeometry(0.6, 0.02, 0.1)
      const bladeMaterial = new THREE.MeshPhongMaterial({ 
        color: 0xcccccc,
        transparent: true,
        opacity: 0.8
      })
      const blade = new THREE.Mesh(bladeGeometry, bladeMaterial)
      blade.position.x = 0.3
      blade.rotation.y = (i * Math.PI * 2) / 3
      bladeGroup.add(blade)
    }
    bladeGroup.position.y = 1.1
    group.add(bladeGroup)

    group.userData.blades = bladeGroup
    return group
  }

  createLightModel() {
    const group = new THREE.Group()
    
    // 灯泡
    const bulbGeometry = new THREE.SphereGeometry(0.15, 16, 16)
    const bulbMaterial = new THREE.MeshPhongMaterial({ 
      color: 0xcccccc,
      transparent: true,
      opacity: 0.9
    })
    const bulb = new THREE.Mesh(bulbGeometry, bulbMaterial)
    group.add(bulb)

    // 灯罩
    const shadeGeometry = new THREE.ConeGeometry(0.25, 0.3, 8, 1, true)
    const shadeMaterial = new THREE.MeshPhongMaterial({ 
      color: 0xffffff,
      transparent: true,
      opacity: 0.7,
      side: THREE.DoubleSide
    })
    const shade = new THREE.Mesh(shadeGeometry, shadeMaterial)
    shade.position.y = 0.25
    shade.rotation.x = Math.PI
    group.add(shade)

    return group
  }

  createCurtainModel() {
    const group = new THREE.Group()
    
    // 窗帘轨道
    const railGeometry = new THREE.BoxGeometry(2, 0.05, 0.05)
    const railMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 })
    const rail = new THREE.Mesh(railGeometry, railMaterial)
    rail.position.y = 1
    group.add(rail)

    // 窗帘布料
    const curtainGeometry = new THREE.PlaneGeometry(1.8, 1.8)
    const curtainMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x909399,
      side: THREE.DoubleSide
    })
    const curtain = new THREE.Mesh(curtainGeometry, curtainMaterial)
    curtain.position.y = 0.1
    curtain.position.z = -0.1
    
    group.add(curtain)
    group.userData.curtain = curtain

    return group
  }

  // 清理资源
  dispose() {
    this.loadedModels.clear()
    this.loadingPromises.clear()
  }
}

export default new ModelLoader()

<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <el-header class="app-header">
      <div class="header-content">
        <h1 class="app-title">
          <el-icon><House /></el-icon>
          智能家居3D控制中心
        </h1>
        <div class="header-stats">
          <el-statistic
            title="在线设备"
            :value="devicesStore.devices.length"
            suffix="台"
          />
          <el-statistic
            title="活跃设备"
            :value="devicesStore.activeDevices.length"
            suffix="台"
          />
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-container class="main-container">
      <!-- 左侧设备列表 -->
      <el-aside width="350px" class="sidebar">
        <DeviceList />
      </el-aside>

      <!-- 中间3D场景 -->
      <el-main class="scene-main">
        <el-card class="scene-card">
          <template #header>
            <div class="scene-header">
              <span>3D场景视图</span>
              <div class="scene-controls">
                <el-tooltip content="使用说明" placement="bottom">
                  <el-button :icon="QuestionFilled" size="small" circle @click="showInstructions" />
                </el-tooltip>
                <el-tooltip content="点击设备进行控制" placement="bottom">
                  <el-button :icon="InfoFilled" size="small" circle />
                </el-tooltip>
              </div>
            </div>
          </template>
          <div class="scene-wrapper">
            <Scene3D />
          </div>
        </el-card>
      </el-main>
    </el-container>

    <!-- 设备控制面板 -->
    <DeviceControlPanel />

    <!-- 使用说明 -->
    <Instructions ref="instructions" />
  </div>
</template>

<script>
import { useDevicesStore } from './stores/devices.js'
import Scene3D from './components/Scene3D.vue'
import DeviceList from './components/DeviceList.vue'
import DeviceControlPanel from './components/DeviceControlPanel.vue'
import { House, InfoFilled } from '@element-plus/icons-vue'

export default {
  name: 'App',
  components: {
    Scene3D,
    DeviceList,
    DeviceControlPanel,
    House,
    InfoFilled
  },
  setup() {
    const devicesStore = useDevicesStore()

    return {
      devicesStore,
      House,
      InfoFilled
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#app {
  height: 100vh;
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 40px;
}

.header-stats .el-statistic {
  color: white;
}

.header-stats .el-statistic__head {
  color: rgba(255, 255, 255, 0.8);
}

.header-stats .el-statistic__content {
  color: white;
}

.main-container {
  flex: 1;
  background-color: #f5f7fa;
}

.sidebar {
  padding: 20px;
  background-color: white;
  border-right: 1px solid #e4e7ed;
}

.scene-main {
  padding: 20px;
}

.scene-card {
  height: 100%;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scene-controls {
  display: flex;
  gap: 8px;
}

.scene-wrapper {
  height: calc(100vh - 200px);
  min-height: 500px;
}
</style>

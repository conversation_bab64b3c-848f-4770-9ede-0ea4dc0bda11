import { defineStore } from 'pinia'

export const useDevicesStore = defineStore('devices', {
  state: () => ({
    devices: [
      {
        id: 'ac-1',
        name: '客厅空调',
        type: 'air-conditioner',
        status: 'off',
        temperature: 26,
        mode: 'cool', // cool, heat, fan, dry
        position: { x: -4, y: 1.5, z: -4 },
        color: '#409EFF'
      },
      {
        id: 'fan-1',
        name: '客厅电风扇',
        type: 'fan',
        status: 'off',
        speed: 1, // 1-3
        position: { x: 3, y: 0, z: 3 },
        color: '#67C23A'
      },
      {
        id: 'light-1',
        name: '客厅主灯',
        type: 'light',
        status: 'off',
        brightness: 80, // 0-100
        position: { x: 0, y: 3, z: 0 },
        color: '#E6A23C'
      },
      {
        id: 'light-2',
        name: '角落台灯',
        type: 'light',
        status: 'off',
        brightness: 60,
        position: { x: 4, y: 1.5, z: 3 },
        color: '#E6A23C'
      },
      {
        id: 'light-3',
        name: '入口灯',
        type: 'light',
        status: 'off',
        brightness: 90,
        position: { x: -4, y: 3, z: 2 },
        color: '#E6A23C'
      },
      {
        id: 'curtain-1',
        name: '客厅窗帘',
        type: 'curtain',
        status: 'closed',
        openness: 0, // 0-100 (0=关闭, 100=完全打开)
        position: { x: -5.8, y: 1.5, z: -2 },
        color: '#909399'
      },
      {
        id: 'curtain-2',
        name: '侧面窗帘',
        type: 'curtain',
        status: 'closed',
        openness: 0,
        position: { x: 5.8, y: 1.5, z: 2 },
        color: '#909399'
      }
    ],
    selectedDevice: null,
    showControlPanel: false
  }),

  getters: {
    getDeviceById: (state) => (id) => {
      return state.devices.find(device => device.id === id)
    },
    
    getDevicesByType: (state) => (type) => {
      return state.devices.filter(device => device.type === type)
    },

    activeDevices: (state) => {
      return state.devices.filter(device => device.status !== 'off')
    }
  },

  actions: {
    // 切换设备开关状态
    toggleDevice(deviceId) {
      const device = this.getDeviceById(deviceId)
      if (device) {
        if (device.type === 'curtain') {
          device.status = device.status === 'closed' ? 'open' : 'closed'
          device.openness = device.status === 'open' ? 100 : 0
        } else {
          device.status = device.status === 'off' ? 'on' : 'off'
        }
      }
    },

    // 更新设备属性
    updateDevice(deviceId, updates) {
      const device = this.getDeviceById(deviceId)
      if (device) {
        Object.assign(device, updates)
      }
    },

    // 选择设备
    selectDevice(deviceId) {
      this.selectedDevice = this.getDeviceById(deviceId)
      this.showControlPanel = true
    },

    // 取消选择设备
    unselectDevice() {
      this.selectedDevice = null
      this.showControlPanel = false
    },

    // 设置空调温度
    setAirConditionerTemperature(deviceId, temperature) {
      const device = this.getDeviceById(deviceId)
      if (device && device.type === 'air-conditioner') {
        device.temperature = Math.max(16, Math.min(30, temperature))
      }
    },

    // 设置空调模式
    setAirConditionerMode(deviceId, mode) {
      const device = this.getDeviceById(deviceId)
      if (device && device.type === 'air-conditioner') {
        device.mode = mode
      }
    },

    // 设置风扇速度
    setFanSpeed(deviceId, speed) {
      const device = this.getDeviceById(deviceId)
      if (device && device.type === 'fan') {
        device.speed = Math.max(1, Math.min(3, speed))
      }
    },

    // 设置灯光亮度
    setLightBrightness(deviceId, brightness) {
      const device = this.getDeviceById(deviceId)
      if (device && device.type === 'light') {
        device.brightness = Math.max(0, Math.min(100, brightness))
      }
    },

    // 设置窗帘开合度
    setCurtainOpenness(deviceId, openness) {
      const device = this.getDeviceById(deviceId)
      if (device && device.type === 'curtain') {
        device.openness = Math.max(0, Math.min(100, openness))
        device.status = openness > 50 ? 'open' : 'closed'
      }
    }
  }
})
